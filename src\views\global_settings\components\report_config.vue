<template>
  <div v-loading="formLoading">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-divider content-position="left">
          <span class="setting_block_title">日报配置</span>
        </el-divider>
        <el-card class="h-250px">
          <el-form ref="dailyReportFormRef" :model="reportData.dailyReport" label-width="80px" :rules="dailyFormRules">
            <el-form-item label="医院名称" prop="HOSPITAL_NAME">
              <el-input v-model="reportData.dailyReport.HOSPITAL_NAME" placeholder="XX日报" />
            </el-form-item>
            <el-form-item label="医院分组" prop="Part_ID">
              <el-input v-model="reportData.dailyReport.Part_ID" placeholder="请输入医院分组，例如：40" />
            </el-form-item>
            <el-form-item label="代理" prop="proxy">
              <el-input v-model="reportData.dailyReport.PROXY" placeholder="代理地址" />
            </el-form-item>
            <el-form-item label="推送方式">
              <el-switch
                v-model="reportData.dailyReport.dailyPushType"
                class="ml-2"
                inline-prompt
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                active-text="kafka"
                inactive-text="http"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-divider content-position="left">
          <span class="setting_block_title">周报配置</span>
        </el-divider>
        <el-card class="h-250px">
          <el-form
            :model="reportData.weeklyReport"
            label-width="80px"
            ref="weeklyReportFormRef"
            :rules="dailyFormRules"
          >
            <el-form-item label="周报地址" prop="weekly_url">
              <el-input
                v-model="reportData.weeklyReport.weekly_url"
                placeholder="请输入周报url地址"
                type="textarea"
                rows="4"
                resize="none"
              />
            </el-form-item>
            <el-form-item label="ES版本" prop="kbn_version">
              <el-input placeholder="请输入elasticsearch版本" v-model="reportData.weeklyReport.kbn_version" />
            </el-form-item>
            <el-form-item label="推送方式" prop="push_type">
              <el-switch
                v-model="reportData.weeklyReport.push_type"
                class="ml-2"
                inline-prompt
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                active-text="企业微信"
                inactive-text="本地推送"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { addEnvConfigInfo } from "@/api/modules/configuration/env_config";
import { defineProps, defineEmits, ref, watch } from "vue";
const formLoading = ref(false);
const reportData = reactive({
  dailyReport: {
    HOSPITAL_NAME: "",
    PROXY: "",
    Part_ID: "40",
    dailyPushType: false,

    PUSH_METHOD: "",
  },
  weeklyReport: {
    weekly_url: "",
    weekly_id: "",
    push_type: false,
    kbn_version: "",
  },
});
const dailyFormRules = {
  HOSPITAL_NAME: [{ required: true, message: "请输入名称", trigger: "blur" }],
  Part_ID: [{ required: true, message: "请输入医院分组", trigger: "blur" }],
  weekly_url: [{ required: true, message: "请输入周报url地址", trigger: "blur" }],
  weekly_id: [{ required: true, message: "请输入周报id", trigger: "blur" }],
};

const weeklyReportFormRef = ref(null);
const dailyReportFormRef = ref(null);
const emits = defineEmits(["next-step"]);
function saveReportconfig() {
  Promise.all([weeklyReportFormRef.value.validate(), dailyReportFormRef.value.validate()])
    .then(() => {
      if (reportData.dailyReport.dailyPushType) {
        reportData.dailyReport.PUSH_METHOD = "kafka";
      } else {
        reportData.dailyReport.PUSH_METHOD = "http";
      }
      formLoading.value = true;
      Promise.all([
        addEnvConfigInfo({
          type: "daily",
          data_resource: reportData.dailyReport,
        }),
        addEnvConfigInfo({
          type: "weekly",
          data_resource: reportData.weeklyReport,
        }),
      ])
        .then(() => {
          formLoading.value = false;
          emits("next-step");
        })
        .catch(() => {
          emits("next-step");
          formLoading.value = false;
          // ElMessage.error("请检查表单数据");
        });
    })
    .catch(() => {
      ElMessage.error("请检查表单数据");
    });
}
defineExpose({ saveReportconfig, formLoading });
</script>

<style scoped></style>
