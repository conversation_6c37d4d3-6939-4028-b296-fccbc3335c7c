<script setup>
import { onMounted, reactive } from "vue";
import { usePagination } from "@/utils/composables";
import { queryAllTriggerRecordInfo, batchDelRecord } from "@/api/modules/self_healing/slef_cure";
import { pageChangeNum } from "@/views/homepage/components/utils";
import { goBack } from "@/utils";
import { ArrowLeftBold } from "@element-plus/icons-vue";
const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();

const resultStatus = {
  success: "执行成功",
  fail: "执行失败",
  wait: "等待执行",
  running: "执行中",
};
const data = reactive({
  dataList: [],
  allList: [],
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },
  search: {
    searchName: "",
    searchStatus: 0,
    isSearch: false,
  },
  formModeProps: {
    showMode: false,
  },
  content: {},
  dialogTitle: "",
});
onMounted(() => {
  getData();
});

//获取列表数据
function getData() {
  queryAllTriggerRecordInfo()
    .then((res) => {
      data.dataList = res.data;
      data.allList = res.data;
    })
    .finally(() => {
      changePageNum(data.allList);
    });
}

function changePageNum(lists) {
  let res = pageChangeNum(lists, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
}
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      queryData();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）

function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      queryData();
    } else {
      changePageNum(data.allList);
    }
  });
}
//筛选方法
function queryData() {
  let searchName = data.search.searchName;
  let searchStatus = data.search.searchStatus;
  let list = data.allList;
  if (searchName == "" && searchStatus == 0) {
    getData();
    data.search.isSearch = false;
    return;
  } else {
    if (searchName != "") {
      list = data.allList.filter((item) => {
        return item.host_name.toLowerCase().includes(searchName.toLowerCase());
      });
    }
    if (searchStatus != 0) {
      list = list.filter((item) => {
        return (
          (searchStatus == 1 && item.resolved === "success") ||
          (searchStatus == 3 && item.resolved === "wait") ||
          (searchStatus == 2 && item.resolved === "fail") ||
          (searchStatus == 4 && item.resolved === "running")
        );
      });
    }
    data.search.isSearch = true;
    changePageNum(list);
  }
}

function viewResultInfo(row) {
  ElMessageBox.alert(`${row.execution_information}`, "详情", {
    // if you want to disable its autofocus
    // autofocus: false,
    confirmButtonText: "确认",
  });
}
function delRecord() {
  let list = [];
  ElMessageBox.confirm("是否删除选中出发记录？", "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    data.batch.selectionDataList.forEach((item) => {
      list.push(item.id);
    });
    batchDelRecord(list).then((res) => {
      if (res.status_code == 200) {
        getData();
      }
    });
  });
}
</script>
<template>
  <div>
    <div>
      <page-main title="自愈触发记录">
        <template #title>
          <div class="flex items-center">
            <el-icon class="cursor-pointer" @click="goBack()"><ArrowLeftBold /></el-icon>
            <span class="ml-10px">自愈触发记录</span>
          </div>
        </template>
        <div class="flex">
          <el-space wrap>
            <el-form label-suffix=":">
              <el-form-item label="主机名" class="form_css">
                <el-input
                  placeholder="请输入主机名称进行查询"
                  v-model="data.search.searchName"
                  clearable
                  @keyup.enter="queryData"
                />
              </el-form-item>
            </el-form>
            <el-form label-suffix=":">
              <el-form-item label="状态" class="form_css">
                <el-select
                  style="width: 200px; margin-left: 10px"
                  v-model="data.search.searchStatus"
                  @change="queryData"
                >
                  <el-option label="全部" :value="0" />
                  <el-option :label="resultStatus.success" :value="1" />
                  <el-option :label="resultStatus.fail" :value="2" />
                  <el-option :label="resultStatus.wait" :value="3" />
                  <el-option :label="resultStatus.running" :value="4" />
                </el-select>
              </el-form-item>
            </el-form>
            <el-button type="primary" @click="queryData()">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:search" />
                </el-icon>
              </template>
              筛选
            </el-button>
            <el-button :disabled="!data.batch.selectionDataList.length" type="danger" @click="delRecord()">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:search" />
                </el-icon>
              </template>
              批量删除
            </el-button>
          </el-space>
          <el-space wrap class="ml-20px"></el-space>
        </div>
        <el-table
          :data="data.dataList"
          border
          class="list-table"
          stripe
          highlight-current-row
          @selection-change="data.batch.selectionDataList = $event"
        >
          <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
          <el-table-column label="主机" prop="host_name" />
          <el-table-column label="触发事件" prop="event_name" />
          <el-table-column label="执行功能包" prop="script_name" />
          <el-table-column label="状态" prop="resolved">
            <template #default="scoped">
              <el-tag v-if="scoped.row.resolved == 'success'" type="success">{{ resultStatus.success }}</el-tag>
              <el-tag v-else-if="scoped.row.resolved == 'wait'" type="info">
                {{ resultStatus.wait }}
              </el-tag>
              <el-tag v-else-if="scoped.row.resolved == 'running'" type="warning">
                {{ resultStatus.running }}
              </el-tag>
              <el-tag v-else type="danger">
                {{ resultStatus.fail }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="115px" align="center">
            <template #default="scoped">
              <el-button type="primary" plain v-if="scoped.row.resolved == 'fail'" @click="viewResultInfo(scoped.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pagination.page"
          :total="pagination.total"
          :page-size="pagination.size"
          :page-sizes="pagination.sizes"
          :layout="pagination.layout"
          :hide-on-single-page="false"
          class="paginationTable"
          background
          @size-change="sizeChange"
          @current-change="currentChange"
        />
      </page-main>
    </div>
  </div>
</template>
<style scoped>
.form_css {
  margin-bottom: 1px;
}
</style>
