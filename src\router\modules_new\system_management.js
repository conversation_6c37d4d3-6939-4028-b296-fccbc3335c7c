const Layout = () => import("@/layout/index.vue");
let children = [
  {
    path: "businessSoftInformation",
    name: "cmdb_business_soft_information",
    component: () => import("@/views/model_configuration/cmdb_business_soft/informationBusinessSoft.vue"),
    meta: {
      title: "应用服务",
      auth: ["admin", "cmdb_business_soft_information.browse"],
    },
    children: [
      {
        path: "detailBusiness",
        name: "cmdb_detailBusiness",
        component: () => import("@/views/homepage/cmdb_business/informationBusiness.vue"),
        meta: {
          sidebar: false,
          title: "业务详情",
          activeMenu: "/system_management/businessSoftInformation",
        },
      },
      {
        path: "create_business_soft",
        name: "cmdb_business_soft",
        component: () => import("@/views/model_configuration/cmdb_business_soft/createBusinessSoft.vue"),
        meta: {
          title: "新增应用服务",
          sidebar: false,
          activeMenu: "/system_management/businessSoftInformation",
        },
      },
    ],
  },
  {
    path: "task_list",
    name: "task_list",
    component: () => import("@/views/scheduled_task/list.vue"),
    meta: {
      title: "定时任务",
      sidebar: true,
      auth: ["admin", "task_list.browse"],
    },
  },
  {
    path: "script_managment_center",
    name: "script_managment_center",
    component: () => import("@/views/script_managment/index.vue"),
    meta: {
      title: "平台脚本",
      auth: ["admin", "script_managment_center.browse"],
    },
  },
  {
    path: "monitorTemplateList",
    name: "monitor_template_list",
    component: () => import("@/views/template_configuration/monitor_template/list.vue"),
    meta: {
      title: "监控模板",
      auth: ["admin", "monitor_template_list.browse"],
    },
  },

  {
    path: "information",
    name: "cmdb_information",
    component: () => import("@/views/model_configuration/cmdb_soft/information.vue"),
    meta: {
      title: "软件管理",
      auth: ["admin", "cmdb_information.browse"],
    },
    children: [
      {
        path: "create",
        name: "create_soft",
        component: () => import("@/views/model_configuration/cmdb_soft/create.vue"),
        meta: {
          title: "新增软件",
          sidebar: false,
          activeMenu: "/system_management/information",
        },
      },
    ],
  },
  {
    path: "dictionary",
    name: "dictionary",
    component: () => import("@/views/dictionary/index.vue"),
    meta: {
      title: "字典管理",
      auth: ["admin", "dictionary.browse"],
    },
  },
  {
    path: "basic_configuration",
    name: "basic_configuration",
    component: () => import("@/views/personal/configuration.vue"),
    meta: {
      title: "基础配置",
      auth: ["admin", "basic_configuration.browse"],
    },
    children: [
      {
        path: "logstashCreate",
        name: "logstashCreate",
        component: () => import("@/views/personal/components/logstash_conf/detail.vue"),
        meta: {
          title: "新增logstash",
          sidebar: false,
          activeMenu: "/system_management/basic_configuration",
        },
      },
      {
        path: "logstashEdit",
        name: "logstashEdit",
        component: () => import("@/views/personal/components/logstash_conf/detail.vue"),
        meta: {
          title: "编辑logstash",
          sidebar: false,
          activeMenu: "/system_management/basic_configuration",
        },
      },
      {
        path: "db4bixEdit",
        name: "db4bixEdit",
        component: () => import("@/views/personal/components/db4bix_conf/detail.vue"),
        meta: {
          title: "编辑db4bix",
          sidebar: false,
          activeMenu: "/system_management/basic_configuration",
        },
      },
      {
        path: "db4bixCreate",
        name: "db4bixCreate",
        component: () => import("@/views/personal/components/db4bix_conf/detail.vue"),
        meta: {
          title: "新增db4bix",
          sidebar: false,
          activeMenu: "/system_management/basic_configuration",
        },
      },
    ],
  },

  {
    path: "user_list",
    name: "user_list",
    component: () => import("@/views/user_manage/list.vue"),
    meta: {
      title: "账号管理",
      auth: ["admin", "user_list.browse"],
    },
    children: [
      {
        path: "create_user",
        name: "create_user",
        component: () => import("@/views/user_manage/detail.vue"),
        meta: {
          title: "新增用户",
          sidebar: false,
          activeMenu: "/system_management/user_list",
        },
      },
      {
        path: "edit_user",
        name: "edit_user",
        component: () => import("@/views/user_manage/detail.vue"),
        meta: {
          title: "编辑用户",
          sidebar: false,
          activeMenu: "/system_management/user_list",
        },
      },
    ],
  },
  // {
  //   path: "campus",
  //   name: "campus",
  //   component: () => import("@/views/campus/index.vue"),
  //   meta: {
  //     title: "机构管理",
  //     auth: ["admin", "personal.browse"],
  //   },
  // },
];
export default {
  path: "/system_management",
  component: Layout,
  redirect: "/system_management/monitorTemplateList",
  name: "system_management",
  meta: {
    auth: ["admin", "system_management.browse"],
    title: "系统管理",
    icon: "operatingSystem",
  },
  children,
};
