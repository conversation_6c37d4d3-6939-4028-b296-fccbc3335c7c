<script setup>
import {
  GetAllPlaybook,
  GetPlaybookParams,
  SumbitTask,
} from "@/api/modules/auto_operation_maintenance/operationMaintenance";
import { getResource } from "@/api/modules/scheduled_task/scheduled";
import PageMain from "@/components/PageMain/index.vue";
import FormMode from "../FormMode/index.vue";
import { useRoute } from "vue-router";
import Result from "../FormMode/index.vue";
import { Edit, Select, CircleCheckFilled } from "@element-plus/icons-vue";
import useTaskJournalStore from "@/store/modules/taskJournal";
import dayjs from "@/utils/dayjs";
import { EndFlagWord } from "@/utils/websocket";
import { goBack } from "@/utils";
import { ArrowLeftBold } from "@element-plus/icons-vue";
let step = ref(0);
const loading = ref(false);
const route = useRoute();
const centerDialogVisible = ref(false);
let task_id = route.query.task_id;
let ip = route.query.ip;
const group = route.query.group;
const selectedPlaybook = ref({});
const data = reactive({
  loading: false,
  playbookOption: [],
  playbookGroup: [],
  hostList: [],
  form: {},
  content: "",
  sub_title: "",
  filterList: [],
  playbookList: [],
  playbook_var: {},
  extra_params: [],
  select_host: [],
  submit_details: {},
  resultTitle: "",
  icon: "",
  formModeProps: {
    showMode: false,
  },
});

const props = {
  multiple: true,
};

onMounted(() => {
  GetAllPlaybook().then((res) => {
    if (res.status_code == 200) {
      let parseResult = JSON.parse(res.data);
      let playbookList = [];
      data.playbookList = parseResult;
      selectedPlaybook.value = parseResult.find((item) => {
        return item.id == task_id;
      });

      parseResult.forEach((element) => {
        if (element.status == true) {
          playbookList.push(element);
        }
      });

      let tempList = [];
      let playbookTypes = [];
      playbookList.forEach((item) => {
        if (playbookTypes.indexOf(item.type) == -1) {
          tempList.push({
            label: item.type,
            category: item.category,
            options: [
              {
                value: item.id,
                label: item.describe,
              },
            ],
          });
          playbookTypes.push(item.type);
        } else {
          let index = playbookTypes.indexOf(item.type);
          tempList[index].options.push({
            value: item.id,
            label: item.name,
          });
        }
      });
      data.playbookOption = tempList;
      console.log(tempList);
      for (var i = 0; i < data.playbookOption.length; i++) {
        if (data.playbookOption[i].label == "") {
          data.playbookOption.splice(data.playbookOption.indexOf(data.playbookOption[i]), 1);
        }
      }
      data.playbookGroup = data.playbookOption.reduce((acc, item) => {
        const category = item.category;
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category] = [...acc[category], ...item.options];
        return acc;
      }, {});

      if (task_id) {
        task_id = parseInt(task_id);
        data.form.playbook = task_id;
        getPlaybookParams(task_id);
      }
    }
  });
  getResourceList();
});

function getResourceList() {
  getResource("all").then((res) => {
    let hostList = res.data;
    hostList.forEach((element) => {
      element.group_name = element.group_name.replace(/'/g, '"');
    });
    // hostList = hostList.filter((obj) => obj.type !== "network");
    data.filterList = hostList;
    if (ip && group) {
      const list = data.filterList.filter((obj) => obj.ip == ip && obj.group_name == group);
      data.playbookOption = data.playbookOption.filter((obj) =>
        list.some((item) => obj.label.includes(item.category) || obj.label == "监控系统")
      );
      data.playbookGroup = data.playbookOption.reduce((acc, item) => {
        const category = item.category;
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category] = [...acc[category], ...item.options];
        return acc;
      }, {});
    }
    data.hostList = hostList.reduce((acc, curr) => {
      const groupIndex = acc.findIndex((item) => item.name === curr.group_name);
      if (groupIndex > -1) {
        const ipIndex = acc[groupIndex].items.findIndex((item) => item.name === curr.ip);
        if (ipIndex === -1) {
          acc[groupIndex].items.push({
            name: curr.ip,
            id: curr.id,
            group: curr.group_name,
          });
        }
      } else {
        acc.push({
          name: curr.group_name,
          items: [{ name: curr.ip, id: curr.id, group: curr.group_name }],
        });
      }
      return acc;
    }, []);
    data.hostList = convertToSelectOptions(data.hostList);
    data.select_host = [findValueForIP(ip, group, data.hostList)];
  });
}

function findValueForIP(ipAddress, groups, cascadingSelectorData) {
  for (const group of cascadingSelectorData) {
    for (const item of group.children) {
      if (item.label === ipAddress && item.group.toLowerCase() == groups.toLowerCase()) {
        return [group.label, item.label];
      }
    }
  }
  return [];
}
function convertToSelectOptions(data) {
  return data.map((group, index) => {
    return {
      value: group.name,
      label: group.name,
      children: group.items.map((item, itemIndex) => {
        return {
          value: item.name,
          label: item.name,
          group: item.group,
        };
      }),
    };
  });
}
function changeHostSelect(event) {
  if (event.length != 0) {
    let frist_group_name = event[0][0];
    let flag = false;
    let frist_group = [];

    event.forEach((item) => {
      if (item[0] != frist_group_name) {
        flag = true;
      } else {
        frist_group.push(item);
      }
    });

    if (flag) {
      ElMessage.warning({
        message: "禁止跨组执行任务",
        center: true,
      });
      data.select_host = frist_group;
    }
  }
}

// 这里的数据结构可能有点怪，这与当时设计有关，当时想着的可以同时执行多个任务所以这么设计了，对单任务而言，这个数据结构的设计就十分多余了，后期如果需要维护的话，希望把这部分的代码重新设计
function getPlaybookParams(event) {
  let playbook = data.playbookList.filter((item) => {
    return item.id == event;
  });
  let type = playbook[0].type;
  let describe = playbook[0].describe;
  //   let is_roles = playbook[0].is_roles;
  let path = playbook[0].playbook_router;
  let name = playbook[0].name;
  GetPlaybookParams({ role_router: path }).then((res) => {
    if (res.status_code == 200) {
      let result = JSON.parse(res.data);
      data.playbook_var = {};
      data.form.var_dict = {};
      Object.keys(result).forEach((key) => {
        let message = key.split("~");
        if (Object.keys(data.playbook_var).length == 0) {
          data.playbook_var = {
            //   is_roles: message[0],
            path: path,
            var_dict: [{ key: message[0], value: result[key] }],
            type: type,
            describe: describe,
            name: name,
          };
        } else {
          data.playbook_var["var_dict"].push({
            key: message[0],
            value: result[key],
          });
        }

        data.form.var_dict[message[0]] = result[key];
      });
      // 假如这个脚本没有任何参数的情况下
      data.playbook_var =
        Object.keys(data.playbook_var).length == 0
          ? {
              // is_roles: is_roles,
              path: path,
              var_dict: [],
              type: type,
              describe: describe,
              name: name,
            }
          : data.playbook_var;
      data.extra_params = data.playbook_var["var_dict"] == undefined ? [] : data.playbook_var["var_dict"];
    }
  });
}

function saveParams(row) {
  if (row.value != "" || row.value != undefined) {
    data.extra_params[
      data.extra_params.findIndex((item) => {
        return item.key == row.key;
      })
    ].value = row.value;
    row.isEdit = false;
  } else {
    ElMessage.warning({
      message: "额外参数是必填部分",
      center: true,
    });
  }
}
// 校验表单中是否存在没有填写的数据
function checkForm() {
  let flag = false;
  if (data.extra_params.length != 0) {
    data.extra_params.forEach((item) => {
      if (item.value == "" || item.value == undefined) {
        flag = true;
      }
    });
  }

  if (data.select_host.length == 0) {
    ElMessage.warning({
      message: "请选择需要执行的ip",
      center: true,
    });
  } else if (Object.keys(data.playbook_var).length == 0) {
    ElMessage.warning({
      message: "请选择需要执行的脚本",
      center: true,
    });
  } else if (flag) {
    ElMessage.warning({
      message: "额外参数中存在未填写的参数",
      center: true,
    });
  } else {
    step.value = 1;
  }
}

// 提交任务执行
function submit() {
  loading.value = true;

  let ips = [];
  data.select_host.forEach((item) => {
    ips.push(item[1]);
  });
  ips = [...new Set(ips)];
  let var_dict = {};
  data.extra_params.forEach((item) => {
    var_dict[item.key] = item.value;
  });

  let selectedPlaybook = data.playbookList.find((item) => {
    return item.id == data.form.playbook;
  });
  var_dict["ips"] = ips;
  let params = {
    var_dict: var_dict,
    role_router: selectedPlaybook.playbook_router,
  };
  data.icon = "executing";
  data.resultTitle = "任务执行中";
  SumbitTask(params)
    .then((res) => {
      if (res.data.websocket_url) {
        step.value = 2;
        data.sub_title = "";
        data.content = "";
        // 增加ws任务
        addwebsocketdispatch(res.data.websocket_url);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

const taskJournalStore = useTaskJournalStore();

/**
 * 新增ws连接
 * @param wsUrl
 */
async function addwebsocketdispatch(wsUrl) {
  // 增加任务缓存
  const taskItem = {
    wsUrl,
    name: data.playbook_var.name,
    dispatchName: dayjs().valueOf(),
    wsStatus: "wait",
  };
  taskJournalStore.addTask(taskItem);
  loopGetTask(wsUrl);
}

// 轮询当前的wsurl的storage,如果最后一条信息是结束标志，就把页面状态换成成功
const loopInterval = ref();
function loopGetTask(wsUrl) {
  loopInterval.value = setInterval(() => {
    data.content = [];
    let content = "";
    const list = taskJournalStore.getTaskJournalList(wsUrl);
    list.forEach((item) => {
      content += item.content + "\n";
    });
    data.content = content;
    if (list[list.length - 1]?.content == EndFlagWord) {
      data.icon = "success";
      data.resultTitle = "任务执行结束";
      clearInterval(loopInterval.value);
    }
  }, 1000);
}
</script>

<template>
  <div>
    <page-main title="作业执行">
      <template #title>
        <div class="flex items-center">
          <el-icon class="cursor-pointer" @click="goBack()"><ArrowLeftBold /></el-icon>
          <span class="ml-10px">作业执行</span>
        </div>
      </template>
      <el-row type="flex" justify="center">
        <el-col :md="4" :sm="6">
          <el-steps
            direction="vertical"
            :active="step"
            finish-status="success"
            align-center
            style="margin: 18px 0 40px"
          >
            <el-step title="填写任务" :icon="Edit" description="填写任务创建信息" />
            <el-step title="提交" :icon="Select" description="确认任务信息" />
            <el-step title="任务执行" :icon="CircleCheckFilled" description="执行任务" />
          </el-steps>
        </el-col>
        <el-col :md="14" :sm="18">
          <el-form :model="data.form" label-width="100px">
            <div v-if="step === 0">
              <div>
                <div class="vertical-line"></div>
                <span class="sub-title">填写基础信息</span>
              </div>
              <el-form-item style="padding-top: 18px" label="选择服务器" prop="ip">
                <el-cascader
                  v-model="data.select_host"
                  placeholder="选择服务器"
                  :options="data.hostList"
                  :props="props"
                  filterable
                  collapse-tags
                  collapse-tags-tooltip
                  clearable
                  @change="changeHostSelect"
                  :disabled="ip !== undefined"
                />
              </el-form-item>
              <el-form-item label="选择执行任务">
                <el-select
                  style="width: 215px"
                  v-model="data.form.playbook"
                  placeholder="选择执行的任务"
                  @change="getPlaybookParams"
                >
                  <el-option-group v-for="(val, key, index) in data.playbookGroup" :key="index" :label="key">
                    <el-option v-for="item in val" :key="item.value" :label="item.label" :value="item.value" />
                  </el-option-group>
                </el-select>
              </el-form-item>
              <el-form ref="extra_params_table" label-position="top" label-width="300px">
                <page-main title="额外参数">
                  <el-table :data="data.extra_params" style="width: 100%">
                    <el-table-column label="参数名">
                      <template #default="scope">
                        <span>{{ scope.row.key }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="参数值">
                      <template #default="scope">
                        <el-input v-if="scope.row.isEdit" v-model="scope.row.value" size="small" />
                        <span v-else>{{ scope.row.value }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200" align="center">
                      <template #default="scope">
                        <template v-if="scope.row.isEdit">
                          <el-button type="primary" plain size="small" @click="saveParams(scope.row)">保存</el-button>
                        </template>
                        <template v-else>
                          <el-button type="primary" plain size="small" @click="scope.row.isEdit = true">编辑</el-button>
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                </page-main>
              </el-form>
              <el-form-item>
                <div style="padding-left: 270px">
                  <el-button type="primary" @click="checkForm">下一步</el-button>
                </div>
              </el-form-item>
            </div>
            <div v-else-if="step === 1">
              <div>
                <div class="vertical-line"></div>
                <span class="sub-title">请确认服务器信息</span>
              </div>
              <div>
                <el-row style="padding: 15px" v-for="(item, index) in data.select_host" :key="index">
                  <el-col :span="12">
                    <div style="display: flex; align-items: center">
                      <el-icon size="40px">
                        <svg-icon name="ip_address" />
                      </el-icon>
                      <span style="font-size: 18px; font-weight: bold; padding-left: 18px">服务器：{{ item[1] }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div style="display: flex; align-items: center">
                      <el-icon size="40px">
                        <svg-icon name="group" />
                      </el-icon>
                      <span style="font-size: 18px; font-weight: bold; padding-left: 18px">
                        所属群组：{{ item[0] }}
                      </span>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <div>
                <div class="vertical-line"></div>
                <span class="sub-title">请确认任务名称</span>
              </div>
              <div style="display: flex; align-items: center; padding: 18px">
                <el-icon size="40px">
                  <svg-icon name="task" />
                </el-icon>
                <span style="font-size: 18px; font-weight: bold; padding-left: 18px">
                  执行任务名称：{{ data.playbook_var.name }}
                </span>
              </div>

              <el-form-item>
                <el-button @click="step = 0">上一步</el-button>
                <el-button type="primary" :loading="loading" @click="submit">提 交</el-button>
              </el-form-item>
            </div>
            <div v-else-if="step === 2">
              <div>
                <div class="vertical-line"></div>
                <span class="sub-title">任务执行</span>
              </div>
              <el-result :sub-title="data.sub_title">
                <template #icon>
                  <el-icon size="64px" :class="{ 'is-loading': data.icon == 'executing' }">
                    <svg-icon :name="data.icon" />
                  </el-icon>
                </template>
                <template #title>
                  <span style="font-size: 30px; font-weight: bold">{{ data.resultTitle }}</span>
                </template>
                <template #extra>
                  <el-button @click="step = 0">再次执行任务</el-button>
                  <el-button type="primary" @click="centerDialogVisible = true">查看返回结果</el-button>
                </template>
              </el-result>
            </div>
          </el-form>
        </el-col>
      </el-row>
      <Result v-model="centerDialogVisible" :content="data.content"></Result>
    </page-main>

    <FormMode v-model="data.formModeProps.showMode" :content="data.submit_details" />
  </div>
</template>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}

.dialog {
  height: 50%;
  overflow-y: auto;

  ul {
    width: 100%;
    height: 100%;

    li {
      white-space: nowrap;
      width: 100%;
      // line-height: 10%;
      overflow-x: auto;
    }
  }
}

.margin-top {
  min-width: 550px;
  padding-bottom: 10px;
}

ul {
  list-style: none;
}

.el-notification.right {
  width: 250px;
}

.el-form-item__content {
  margin-left: 0px;
}

.vertical-line {
  border-left: 4px solid #337ecc;
  /* 2px 宽度的加粗直线，黑色 */
  height: 13px;
  /* 设置高度 */
  display: inline-block;
}

.sub-title {
  display: inline-block;
  /* 设置为内联块级元素 */
  margin-left: 5px;
  /* 添加一些左边距以分隔线和文本 */
  font-size: 14px;
  font-weight: bold;
}
</style>
