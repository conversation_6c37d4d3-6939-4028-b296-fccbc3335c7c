<template>
  <div class="p-20px h-[100vh-30px] overflow-auto">
    <div class="w-full bg-[#fff] mb-20px p-20px rounded-4px yw-all">
      <div class="yw-all-warp">
        <div class="yw-all-item" @click="jumpToURL('biz_hub')">
          <div class="yw-all-item-left">
            <div class="text-18px">业务系统</div>
            <div class="flex justify-start items-end">
              <div class="text-34px mr-18px font-bold">{{ data.businessSystemNum }}</div>
              <div class="mb-4px">个</div>
            </div>
          </div>
          <svg-icon name="ApplicationIndex" class="yw-all-item-icon"></svg-icon>
        </div>
        <div class="yw-all-item" @click="jumpToURL('cmdb_server_information')">
          <div class="yw-all-item-left">
            <div class="text-18px">服务器</div>
            <div class="flex justify-start items-end">
              <div class="text-34px mr-18px font-bold">{{ data.serverNum }}</div>
              <div class="mb-4px">台</div>
            </div>
          </div>
          <svg-icon name="farm" class="yw-all-item-icon"></svg-icon>
        </div>
        <div class="yw-all-item" @click="jumpToURL('cmdb_database_information')">
          <div class="yw-all-item-left">
            <div class="text-18px">数据库</div>
            <div class="flex justify-start items-end">
              <div class="text-34px mr-18px font-bold">{{ data.databaseNum }}</div>
              <div class="mb-4px">个</div>
            </div>
          </div>
          <svg-icon name="database2" class="yw-all-item-icon"></svg-icon>
        </div>

        <div class="yw-all-item" @click="jumpToURL('cmdb_network_information')">
          <div class="yw-all-item-left">
            <div class="text-18px">网络设备</div>
            <div class="flex justify-start items-end">
              <div class="text-34px mr-18px font-bold">{{ data.networkDeviceNum }}</div>
              <div class="mb-4px">个</div>
            </div>
          </div>
          <svg-icon name="network_info" class="yw-all-item-icon"></svg-icon>
        </div>
        <div class="yw-all-item" @click="jumpToURL('problem_list')">
          <div class="yw-all-item-left">
            <div class="text-18px">发现报警</div>
            <div class="flex justify-start items-end">
              <div class="text-34px mr-18px font-bold">{{ data.warningList.length || 0 }}</div>
              <div class="mb-4px">个</div>
            </div>
          </div>
          <svg-icon name="danger-sign" class="yw-all-item-icon"></svg-icon>
        </div>
      </div>
    </div>

    <div class="flex flex-row min-h-[300px]">
      <div class="w-80% min-w-900px flex-shrink-0 bg-[#fff] p-20px mr-20px rounded-4px h-[calc(100vh-330px)]">
        <div class="mb-10px">活动预警</div>
        <div class="h-[calc(100%-40px)]">
          <el-table
            :cell-style="tableCellstyle"
            v-loading="data.warningListLoading"
            :data="data.warningList"
            table-layout="auto"
            class="!w-99% !h-[100%]"
          >
            <el-table-column prop="lastchange" label="时间"></el-table-column>
            <el-table-column prop="priority" label="级别" align="center"></el-table-column>
            <el-table-column prop="hostname" label="主机名">
              <template #default="scoped">
                <el-text tag="ins" @click="$onContextMenu($event, scoped.row)">
                  {{ scoped.row.hostname }}
                </el-text>
              </template>
            </el-table-column>

            <el-table-column prop="description" label="问题" />
          </el-table>
        </div>
      </div>

      <div class="flex-1 p-20px bg-[#fff] rounded-4px">
        <div class="mb-10px">快捷操作</div>
        <div class="quick_operation_button_group">
          <el-button type="primary" @click="jumpToURL('latest')" v-auth="['admin', 'latest.browse']">
            最新数据
          </el-button>
          <el-button
            type="primary"
            @click="jumpToURL('analysis_platform')"
            v-auth="['admin', 'analysis_platform.browse']"
          >
            智联面板
          </el-button>
          <el-button type="primary" @click="jumpToURL('screen_player')">数字大屏</el-button>

          <el-button type="primary" @click="jumpToURL('daily_list')">日报</el-button>
          <el-button type="primary" @click="jumpToURL('weekly_list')">周报</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getZabbixHistoricalAlarmInformation } from "@/api/modules/cache";
import { getInfrastructureSummary } from "@/api/modules/home_page";
import { tableCellstyle } from "@/utils/alarm";
const router = useRouter();
const data = reactive({
  businessSystemNum: 0, // 业务数量
  alarmNum: 0, // 预警数量
  networkDeviceNum: 0, // 网络设备数据
  serverNum: 0, // 服务器数量
  databaseNum: 0, // 数据库数量
  warningList: [], // 预警列表
  warningListLoading: false,
});

/**
 *
 * @param name
 */
const jumpToURL = (name) => {
  router.push({ name });
};

/**
 * 获取活动告警信息
 */
function getWarringData() {
  data.warningListLoading = true;
  // 参数type
  getZabbixHistoricalAlarmInformation("")
    // ZabbixWarning(alarmInfoEnum["unresolved"])
    .then((res) => {
      let list = res.data;
      list.sort(function (a, b) {
        var dateA = new Date(a.lastchange);
        var dateB = new Date(b.lastchange);
        return dateB - dateA;
      });
      data.warningList = list;
    })
    .catch(() => {})
    .finally(() => {
      data.warningListLoading = false;
    });
}

/**
 *  页面加载时 获取统计信息
 */
function getObtainCount() {
  getInfrastructureSummary()
    .then((res) => {
      data.businessSystemNum = res.data.business_count || 0;
      data.serverNum = res.data.server_count || 0;
      data.databaseNum = res.data.database_count || 0;
      data.networkDeviceNum = res.data.network_device_count || 0;
    })
    .finally(() => {});
}

/**
 * 页面加载时
 */
onMounted(() => {
  getObtainCount(); //获取统计数量
  getWarringData(); //获取活动告警信息
});
</script>
<style scoped lang="scss">
.yw-all {
  .yw-header {
    margin-bottom: 20px;
  }
  .yw-all-warp {
    height: 140px;
    display: flex;
    flex-wrap: nowrap;

    .yw-all-item {
      cursor: pointer;
      border-radius: 8px;
      padding: 20px;
      width: 20%;
      height: 100%;
      margin-right: 18px;
      background: #f5f5f5;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      & :nth-child(5) {
        margin-right: 0;
      }

      .yw-all-item-left {
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        height: 100%;
      }
      .yw-all-item-icon {
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
      }
    }
  }
}
.quick_operation_button_group {
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
  }
  .el-button + .el-button {
    margin-left: 0;
  }
}
</style>
