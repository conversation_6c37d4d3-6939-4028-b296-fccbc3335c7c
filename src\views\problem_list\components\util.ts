import { getDecimalPlaces } from '@/utils/decimal'
import { loadingShow } from '@/components/SpinkitLoading/index'
// 单位的转换 轴线上显示的val是后端返回的原始值，所以这里计算字节单位的时候，直接除以1024
export function _unit_conversion(unit, val) {
  console.log(unit, val, 1222222)

  // if (['B', 'Bps', 'bps', 'KB', 'MB', 'GB', 'TB'].includes(unit)) {
  //   const valNum = Number(val)

  //   const valMath = Math.abs(valNum)
  //   let pers = valNum.toFixed(2)
  //   if (valMath > 1024 ** 4) {
  //     pers = `${(valMath / 1024 ** 4).toFixed(2)} TB`
  //   } else if (valMath > 1024 ** 3) {
  //     pers = `${(valMath / 1024 ** 3).toFixed(2)} GB`
  //   } else if (valMath > 1024 ** 2) {
  //     pers = `${(valMath / 1024 ** 2).toFixed(2)} MB`
  //   } else if (valMath > 1024) {
  //     pers = `${(valMath / 1024).toFixed(2)} KB`
  //   } else {
  //     pers = pers + 'B'
  //   }
  //   return valNum >= 0 ? pers : '-' + pers
  // }

  if (!isNaN(Number(val)) && parseFloat(val) === Number(val)) {
    console.log(333333)

    return parseFloat(val).toFixed(getDecimalPlaces(val) < 4 ? getDecimalPlaces(val) + 1 : 4) + unit
  }
  return val + unit
}

export function compareByx(a, b) {
  return new Date(a.clock) - new Date(b.clock)
}

// 查找历史数据里面的最大值和最小值
export function findMinMaxOriginalDataArrays(data) {
  // 初始化最大值和最小值及其对应的数组
  let maxArray = null
  let minArray = null
  let maxOriginalData = -Infinity
  let minOriginalData = Infinity

  // 使用 reduce 遍历数组
  const result = data.reduce((acc, item) => {
    // 更新最大值
    if (item.original_data > maxOriginalData) {
      maxOriginalData = item.original_data
      maxArray = item
    }

    // 更新最小值
    if (item.original_data < minOriginalData) {
      minOriginalData = item.original_data
      minArray = item
    }

    return acc // 返回累加器，这里我们不需要累加器，所以直接返回
  }, {})

  return { maxArray, minArray }
}

export function loading() {
  // 提供 3 个参数
  loadingShow({
    type: 'chase',
    size: 50,
    color: '#FFFFFF'
  })
}

export function checkValue(value) {
  if (!isNaN(parseFloat(value)) && isFinite(value)) {
    return !isNaN(value)
  }
}
