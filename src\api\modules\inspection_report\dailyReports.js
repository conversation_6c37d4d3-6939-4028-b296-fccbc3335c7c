import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取当前已生成html的所有的日报列表
 * @returns
 */
export function getAllDailyReports() {
  return api.get("/daily_inspection/get_all_daily_report/");
}

/**
 * 批量推送日报
 * @param {*} data
 * @returns
 */
export function pushSelectedDailyReports(data) {
  return api.post("/daily_inspection/bulk_push_daily_reports/", JSON.stringify(data));
}

/**
 * 查看日报内容
 * @param {*} data
 * @returns
 */
export function viewDailyReport(data) {
  return api.get(`/daily_inspection/get_daily_report/${data}/`);
}

/**
 * 获取当前日报状态
 * @param {*} data
 * @returns
 */
export function getCurrnetDailyReport(data) {
  return api.post("/daily_inspection/get_current_daily_report_status/", JSON.stringify(data));
}

/**
 *推送当前状态日报
 * @returns
 */
export function pushCurrentDailyReport() {
  return api.get("/daily_inspection/push_current_status_daily_report/");
}
