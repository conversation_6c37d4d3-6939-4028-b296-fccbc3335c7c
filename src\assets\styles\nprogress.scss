#nprogress {
    pointer-events: none;
    .bar {
        position: fixed;
        z-index: 1031;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: var(--g-nprogress-color);
    }
    .peg {
        display: block;
        position: absolute;
        right: 0;
        width: 100px;
        height: 100%;
        opacity: 1;
        transform: rotate(3deg) translate(0, -4px);
        box-shadow: 0 0 10px var(--g-nprogress-color), 0 0 5px var(--g-nprogress-color);
    }
    .spinner {
        display: block;
        position: fixed;
        z-index: 1031;
        top: 11px;
        right: 14px;
        .spinner-icon {
            width: 18px;
            height: 18px;
            box-sizing: border-box;
            border: solid 2px transparent;
            border-radius: 50%;
            border-top-color: var(--g-nprogress-color);
            border-left-color: var(--g-nprogress-color);
            animation: nprogress-spinner 400ms linear infinite;
        }
    }
}
.nprogress-custom-parent {
    overflow: hidden;
    position: relative;
    #nprogress .spinner,
    #nprogress .bar {
        position: absolute;
    }
}
@keyframes nprogress-spinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
@keyframes nprogress-spinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
