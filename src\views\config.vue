<route>
{
    name: 'config',
    meta: {
        title: "配置管理",
        constant: true,
        layout: false
    }
}
</route>
<script setup>
import ConfigForm from "@/containers/config/index.vue";
</script>

<template>
  <div style="padding: 10px">
    <div style="display: flex; justify-content: center; align-items: center; margin-top: 20px">
      <h2>请完成初始化配置</h2>
    </div>

    <ConfigForm :firstConfig="true"></ConfigForm>
  </div>
</template>
<style lang="scss" scoped></style>
