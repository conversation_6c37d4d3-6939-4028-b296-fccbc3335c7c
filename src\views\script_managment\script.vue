<script setup>
import { onMounted, reactive } from "vue";
import { usePagination } from "@/utils/composables";
import { ElMessage, ElMessageBox } from "element-plus";
import { getScriptList, uploadScript, delScript, scriptDeployment } from "@/api/modules/script_managment/index";
import { showNotification } from "@/plugins/element-ui/index";
import { deepClone } from "@/utils/";
import DeployDialog from "./components/deployDialog.vue";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const uploadRef = ref();
const data = reactive({
  dataList: [],
  allList: [],
  batch: {
    enable: true,
    selectionDataList: [],
  },
  addDialog: false,
  addTitle: "上传脚本",
  search: {
    searchName: "",
  },
  selectTaskObject: {},
  file: {},
  deployDialog: false,
});
onMounted(() => {
  getData();
});
//获取所有功能包信息
function getData() {
  getScriptList().then((res) => {
    data.allList = res.data;
    paging(data.allList);
  });
}
//分页
function paging(list) {
  let params = getParams();
  let dataList = data_Filter(list, params);
  data.dataList = dataList.list;
  pagination.value.total = dataList.total;
}
//查询功能包
function queryData() {
  if (data.search.searchName == "") {
    getData();
    return;
  }
  data.dataList = data.allList.filter((item) => {
    return item.name.toLowerCase().includes(data.search.searchName.toLowerCase());
  });
  paging(data.dataList);
}
function beforeUpload(file) {
  const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
  const whiteList = ["zip"];
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElMessage.warning({
      message: "上传的文件只允许是zip文件",
      center: true,
    });
    return false;
  }
}
//筛选数据
function data_Filter(dataList, params) {
  let list = deepClone(dataList);

  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });

  pageList.forEach((item) => {
    item.params = item.params;
  });
  return {
    list: pageList,
    total: list.length,
  };
}
function uploadYML(file) {
  data.addDialog = false;
  let forms = new FormData();
  forms.append("scriptFile", file.file);
  const notification = showNotification("提示", "文件正在上传中", "info");
  uploadScript(forms)
    .then((res) => {
      notification.close();
      if (res.status_code == 200) {
        showNotification("上传成功", res.message, "success");
        getData();
        return;
      }
    })
    .catch((res) => {
      showNotification("上传失败", res.message, "error");
      uploadRef.value.clearFiles(file);
    });
}
function deploy(item) {
  data.deployDialog = true;
  data.selectTaskObject = item;
}
function delDeploy() {
  let list = [];
  ElMessageBox.confirm("是否删除选中脚本包？", "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    data.batch.selectionDataList.forEach((item) => {
      list.push({ id: item.id, file_name: item.file_name });
    });
    delScript(list).then((res) => {
      if (res.status_code == 200) {
        if (res.data.length !== 0) {
          ElMessage({
            message: `删除失败的脚本有${res.data.join("，")}`,
            type: "error",
          });
        }

        getData();
      }
    });
  });
}
const submitTask = (value) => {
  scriptDeployment(value).then((res) => {
    getData();
  });
};
</script>
<template>
  <div>
    <div class="flex justify-between">
      <el-space wrap>
        <el-button type="primary" @click="data.addDialog = true">
          <template #icon>
            <el-icon>
              <svg-icon name="ep:upload" />
            </el-icon>
          </template>
          上传脚本
        </el-button>
        <el-button :disabled="!data.batch.selectionDataList.length" type="danger" @click="delDeploy">
          批量删除
        </el-button>
      </el-space>
      <el-space>
        <div class="w-220px">
          <el-input v-model="data.search.searchName" placeholder="输入脚本名称" clearable @keyup.enter="queryData" />
        </div>
        <el-button type="primary" @click="queryData()">
          <template #icon>
            <el-icon>
              <svg-icon name="ep:search" />
            </el-icon>
          </template>
          筛选
        </el-button>
      </el-space>
    </div>

    <el-divider />
    <div v-if="data.dataList.length != 0">
      <el-table
        :data="data.dataList"
        border
        class="list-table"
        stripe
        highlight-current-row
        @selection-change="data.batch.selectionDataList = $event"
      >
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column label="脚本名称" prop="name" />
        <el-table-column label="文件名称" prop="file_name" />
        <el-table-column label="文件类型" prop="file_type" />
        <el-table-column label="版本号">
          <template #default="scoped">
            <el-tag type="primary" plain>
              {{ scoped.row.version }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="文件目录" prop="deploy_path" />
        <el-table-column label="状态">
          <template #default="scoped">
            <el-tag v-if="scoped.row.deploy_status == 0" type="info">暂未部署</el-tag>
            <el-tag v-else-if="scoped.row.deploy_status == 1" type="success">部署成功</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="115px" align="center">
          <template #default="scoped">
            <el-button type="primary" plain @click="deploy(scoped.row)">部署</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
    <div v-if="data.dataList.length == 0">
      <el-empty :image-size="120" />
    </div>
    <el-dialog v-model="data.addDialog" :title="data.addTitle">
      <div style="margin-top: 10px">
        <el-form label-position="right" label-width="120px" label-suffix="：">
          <el-form-item label="脚本文件">
            <file-upload
              ref="uploadRef"
              class="upload-demo"
              :max="1"
              :ext="['zip']"
              :size="500"
              :auto-upload="true"
              type="file"
              accept=".zip"
              action=""
              :before-upload="beforeUpload"
              :http-request="uploadYML"
            />
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
    <DeployDialog
      v-if="data.deployDialog"
      v-model="data.deployDialog"
      @submitTask="submitTask"
      :item="data.selectTaskObject"
    ></DeployDialog>
  </div>
</template>
<style scoped lang="scss">
.text {
  max-width: 80%;
  @include text-overflow;
}

.button_css {
  float: right;
  position: absolute;
  top: 20px;
  right: 10px;
}

.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70vh;
}

.page-content {
  flex-direction: column;
  align-items: center;
}

.form_css {
  margin-bottom: 1px;
}
</style>
