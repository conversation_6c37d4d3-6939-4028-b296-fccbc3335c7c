const Layout = () => import("@/layout/index.vue");
let children = [
  {
    path: "publish",
    name: "publish",
    component: () => import("@/views/automatic_operation/publish/index.vue"),
    meta: {
      title: "运维工具",
      auth: ["admin", "publish.browse"],
    },
    children: [
      {
        path: "1",
        name: "publish_hidden",
        meta: { sidebar: false },
      },
      {
        path: "task",
        name: "task",
        component: () => import("@/views/automatic_operation/exec_playbook/list.vue"),
        meta: {
          title: "执行记录",
          sidebar: false,
          activeMenu: "/aotumation/publish",
          auth: ["admin", "publish.task_list"],
        },
      },
      {
        path: "task_managment",
        name: "task_managment",
        component: () => import("@/views/function_list/playbook_management/index.vue"),
        meta: {
          title: "功能包管理",
          sidebar: false,
          activeMenu: "/aotumation/publish",
          auth: ["admin", "publish.task_managment"],
        },
      },
      {
        path: "create_task",
        name: "create_task",
        component: () => import("@/views/automatic_operation/exec_playbook/components/DetailForm/createTask.vue"),
        meta: {
          title: "作业执行",
          sidebar: false,
          activeMenu: "/aotumation/publish",
          auth: ["admin", "publish.task_create"],
        },
      },
      {
        path: "runnerlog",
        name: "runnerlog",
        component: () => import("@/views/personal/runnerlog.vue"),
        meta: {
          title: "运维工具日志",
          sidebar: false,
          activeMenu: "/aotumation/publish",
          auth: ["admin", "publish.runnerlog"],
        },
      },
    ],
  },

  {
    path: "self_healing",
    name: "self_healing",
    component: () => import("@/views/automatic_operation/self_cure/cure_function_package/index.vue"),
    meta: {
      title: "自愈中心",
      auth: ["admin", "self_healing.browse"],
    },
    children: [
      {
        path: "1",
        name: "self_healing_hidden",
        meta: { sidebar: false },
      },
      {
        path: "cure_relation",
        name: "cure_relation",
        component: () => import("@/views/automatic_operation/self_cure/cure_relation/index.vue"),
        meta: {
          title: "自愈关系",
          sidebar: false,
          activeMenu: "/aotumation/self_healing",
          auth: ["admin", "self_healing.browse"],
        },
      },
      {
        path: "cure_trigger_record",
        name: "cure_trigger_record",
        component: () => import("@/views/automatic_operation/self_cure/cure_trigger_record/recordIndex.vue"),
        meta: {
          title: "触发记录",
          sidebar: false,
          activeMenu: "/aotumation/self_healing",
          auth: ["admin", "self_healing.browse"],
        },
      },
    ],
  },
];
export default {
  path: "/aotumation",
  component: Layout,
  redirect: "/aotumation/publish",
  name: "aotumation",
  meta: {
    auth: ["admin", "aotumation.browse"],
    title: "运维管理",
    icon: "buslog",
  },
  children,
};
