<script setup name="IframeLayout">
import storage from '@/utils/storage'
import { useRouter, useRoute } from 'vue-router';


let url = storage.local.get('VITE_APP_PLAYER')
const iframeRef = ref()
const iframe = ref({
    loading: true,
    src: ''
})
const data = reactive({
    dataList: [],
});
onMounted(() => {
    getData()
})
function getData() {
    iframe.value.src = url
    iframeRef.value.onload = () => {
        iframe.value.loading = false
    }
}

</script>

<template>
    <div>
        <div v-loading="iframe.loading" class="iframe">
            <iframe ref="iframeRef" :src="iframe.src" frameborder="0" />
            <!-- <el-button class="back-button" :icon="ArrowLeft" @click="goBack" circle></el-button> -->
        </div>
    </div>
</template>

<style lang="scss" scoped>
.iframe,
iframe {
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
    padding-bottom: 32px;
}

.back-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
}
</style>
