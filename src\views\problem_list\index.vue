<script setup name="ProblemListIndex">
import {
  getZabbixHistoricalAlarmInformation,
  getZabbixHistoricalCloseAlarmInformationn,
  ZabbixWarning,
} from "@/api/modules/cache";
import { saveHandProblem } from "@/api/modules/problem";
import { usePagination } from "@/utils/composables";
import { deepClone } from "@/utils/";
import { tableCellstyle } from "@/utils/alarm";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
const route = useRoute();

// 定义localStorage的key
const STORAGE_KEY = "problem_list_search_condition";

const timerInterval = 60000;
const activeName = ref("unresolved");
const priorityOption = [
  { label: "灾难", value: "灾难" },
  { label: "严重", value: "严重" },
  { label: "一般严重", value: "一般严重" },
  { label: "警告", value: "警告" },
  { label: "信息", value: "信息" },
];

// 获取告警级别对应的类型和颜色
function getPriorityStyle(priority) {
  switch (priority) {
    case "灾难":
      return { type: "danger", color: "#E45959" };
    case "严重":
      return { type: "danger", color: "#E97659" };
    case "一般严重":
      return { type: "warning", color: "#FFA059" };
    case "警告":
      return { type: "warning", color: "#FFC859" };
    case "信息":
      return { type: "info", color: "#7499FF" };
    default:
      return { type: "info", color: "#909399" };
  }
}

const data = reactive({
  allList: [],
  tableList: [],
  tableloading: false,
  search: { hostname: "", priority: [] },

  dialogProblem: false,
  gridData: [],
  type: "",
  problemForm: {
    solver: "",
    time: "",
    hostip: "",
    triggerid: "",
    hostname: "",
    description: "",
    priority: "",
  },
  showEdit: false,
});

// 监听搜索条件变化，更新localStorage
watch(
  () => data.search,
  (newVal) => {
    localStorage.setItem(
      STORAGE_KEY,
      JSON.stringify({
        hostname: newVal.hostname,
        priority: newVal.priority,
        activeName: activeName.value,
      })
    );
  },
  { deep: true }
);

// 监听标签页变化
watch(
  () => activeName.value,
  (newVal) => {
    localStorage.setItem(
      STORAGE_KEY,
      JSON.stringify({
        hostname: data.search.hostname,
        priority: data.search.priority,
        activeName: newVal,
      })
    );
  }
);

onMounted(() => {
  // 从localStorage获取筛选条件
  const savedSearch = localStorage.getItem(STORAGE_KEY);
  if (savedSearch) {
    try {
      const parsedSearch = JSON.parse(savedSearch);
      data.search.hostname = parsedSearch.hostname || "";
      data.search.priority = parsedSearch.priority || [];
      if (parsedSearch.activeName) {
        activeName.value = parsedSearch.activeName;
      }
    } catch (e) {
      console.error("解析保存的筛选条件出错", e);
    }
  }

  if (route.query.hostname && !data.search.hostname) {
    data.search.hostname = route.query.hostname;
  }

  data.type =
    activeName.value === "resolved" ? "historical_has_been_cleared_alarm_information" : "historical_alarm_information";

  if (activeName.value === "resolved") {
    getData(getZabbixHistoricalCloseAlarmInformationn);
  } else {
    getData(getZabbixHistoricalAlarmInformation);
  }

  // const timer = setInterval(getData, timerInterval);
  // onBeforeUnmount(() => {
  //   clearInterval(timer);
  // });
});

// 组件卸载时清除localStorage
onUnmounted(() => {
  localStorage.removeItem(STORAGE_KEY);
});

function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.hostname || data.search.priority.length > 0) {
      searchHost();
    } else {
      paging(data.allList);
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page) {
  onCurrentChange(page).then(() => {
    if (data.search.hostname || data.search.priority.length > 0) {
      searchHost();
    } else {
      paging(data.allList);
    }
  });
}
function getData(Fun) {
  data.tableloading = true;
  // getZabbixHistoricalAlarmInformation(data.type)
  Fun()
    .then((res) => {
      let list = res.data;
      list.sort(function (a, b) {
        var dateA = new Date(a.lastchange);
        var dateB = new Date(b.lastchange);
        return dateB - dateA;
      });
      data.allList = list;
      // paging(data.allList);
      searchHost();
    })
    .finally(() => {
      data.tableloading = false;
    })
    .catch(() => {
      data.allList = [];
      data.tableList = [];
    });
}

function paging(list) {
  let params = getParams();
  let dataList = data_Filter(list, params);
  data.tableList = dataList.list;
  pagination.value.total = dataList.total;
}

function searchHost() {
  let results = data.allList;
  data.tableloading = true;
  if (data.search.hostname) {
    results = data.allList.filter((item) => {
      // 根据关键词进行模糊匹配
      return item.hostname.includes(data.search.hostname);
    });
  }
  // 严重性筛选
  if (data.search.priority.length > 0) {
    results = results.filter((item) => {
      return data.search.priority.includes(item.priority);
    });
  }
  data.tableloading = false;
  paging(results);
}

// 时间排序
function timeSort(array, timeProp) {
  let arrays = array.slice().sort((a, b) => new Date(b[timeProp]) - new Date(a[timeProp]));
  return arrays;
}

function lastWarning(item) {
  data.dialogProblem = true;
  // 确保获取到的是历史告警数组
  if (Array.isArray(item)) {
    data.gridData = item; // 直接是数组
  } else if (item && Array.isArray(item.histroy_list)) {
    data.gridData = item.histroy_list; // 是对象，需要获取其histroy_list属性
    
    // 添加父级告警的priority作为默认值
    data.gridData.forEach(record => {
      // 如果历史记录中没有priority，使用父告警的priority
      if (!record.priority && item.priority) {
        record.priority = item.priority;
      }
    });
  } else {
    data.gridData = []; // 兜底处理，确保是空数组
}
}

function data_Filter(dataList, params) {
  console.log(dataList);
  let list = deepClone(dataList);

  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });

  pageList.forEach((item) => {
    item.params = item.params;
  });
  return {
    list: pageList,
    total: list.length,
  };
}

function lastData(row) {
  router.push({
    name: "latest",
    query: {
      hostip: row.hostip,
      hostid: row.hostid,
    },
  });
}

const handleClick = (tab) => {
  if (tab === "resolved") {
    //已解决
    data.type = "historical_has_been_cleared_alarm_information";
    getData(getZabbixHistoricalCloseAlarmInformationn);
  } else if (tab === "unresolved") {
    //未解决
    data.type = "historical_alarm_information";
    getData(getZabbixHistoricalAlarmInformation);
  }
};

// 获取时间轴圆圈颜色 (根据告警状态)
function getTimelineDotColor(status) {
  if (status === '未解决') {
    return { background: "#F56C6C" };  // 未解决：红色
  } else {
    return { background: "#67C23A" };  // 已解决：绿色
  }
}
</script>

<template>
  <div>
    <page-main title="问题清单">
      <div class="mb-10px flex justify-between items-center">
        <div>
          <el-space wrap>
            <div class="w-350px">
              <el-input
                v-model="data.search.hostname"
                placeholder="输入主机名进行筛选,区分大小写"
                clearable
                @keyup.enter="searchHost()"
              />
            </div>
            <div class="w-250px">
              <el-select v-model="data.search.priority" placeholder="请选择严重性级别" clearable multiple collapse-tags>
                <el-option :label="item.label" :value="item.value" v-for="(item, index) in priorityOption" />
              </el-select>
            </div>
            <el-button type="primary" @click="searchHost()">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:search" />
                </el-icon>
              </template>
              筛选
            </el-button>
          </el-space>
        </div>
      </div>
      <el-tabs v-model="activeName" type="card" @tab-change="handleClick">
        <el-tab-pane label="未解决" name="unresolved">
          <el-table
            :cell-style="tableCellstyle"
            v-loading="data.tableloading"
            :data="data.tableList"
            table-layout="auto"
            border
          >
            <el-table-column prop="hostname" label="主机名">
              <template #default="scoped">
                <el-text tag="ins" @click="$onContextMenu($event, scoped.row)">{{ scoped.row.hostname }}</el-text>
              </template>
            </el-table-column>
            <el-table-column prop="hostip" label="IP" />
            <el-table-column prop="description" label="问题" />
            <el-table-column prop="lastchange" label="时间"></el-table-column>
            <el-table-column prop="priority" label="级别" align="center"></el-table-column>
            <el-table-column label="状态" align="center">
              <template #default="scope">
                <div>
                  <el-tag v-if="scope.row.status === '已解决'" type="success">{{ scope.row.status }}</el-tag>
                  <el-tag v-else-if="scope.row.status === '未解决'" type="danger">{{ scope.row.status }}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="查看历史告警" align="center">
              <template #default="scope">
                <div>
                  <el-button
                    type="primary"
                    round
                    v-if="scope.row.histroy_list && scope.row.histroy_list.length != 0"
                    @click="lastWarning(scope.row)"
                  >
                    查看
                  </el-button>
                  <div v-else>暂无历史告警</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="查看最新数据" align="center">
              <template #default="scope">
                <div>
                  <el-button type="primary" round @click="lastData(scope.row)">查看</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="已解决" name="resolved">
          <el-table
            :cell-style="tableCellstyle"
            v-loading="data.tableloading"
            :data="data.tableList"
            table-layout="auto"
            border
          >
            <el-table-column prop="hostname" label="主机名">
              <template #default="scoped">
                <el-text tag="ins" @click="$onContextMenu($event, scoped.row)">{{ scoped.row.hostname }}</el-text>
              </template>
            </el-table-column>
            <el-table-column prop="hostip" label="IP" />
            <el-table-column prop="description" label="问题" />
            <el-table-column prop="lastchange" label="时间"></el-table-column>
            <el-table-column prop="priority" label="级别" align="center"></el-table-column>
            <el-table-column label="状态" align="center">
              <template #default="scope">
                <div>
                  <el-tag v-if="scope.row.status === '已解决'" type="success">{{ scope.row.status }}</el-tag>
                  <el-tag v-else-if="scope.row.status === '未解决'" type="danger">{{ scope.row.status }}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="查看历史告警" align="center">
              <template #default="scope">
                <div>
                  <el-button
                    type="primary"
                    round
                    v-if="scope.row.histroy_list && scope.row.histroy_list.length != 0"
                    @click="lastWarning(scope.row)"
                  >
                    查看
                  </el-button>
                  <div v-else>暂无历史告警</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="查看最新数据" align="center">
              <template #default="scope">
                <div>
                  <el-button type="primary" round @click="lastData(scope.row)">查看</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>
    <el-dialog v-model="data.dialogProblem" title="历史告警" width="70%">
      <div class="history-timeline-container">
        <!-- 时间轴样式 -->
        <div class="custom-timeline">
          <div v-if="data.gridData.length === 0" class="empty-timeline">
            <el-empty description="暂无历史告警记录"></el-empty>
          </div>
          <div v-else class="timeline-wrapper">
            <div 
            v-for="(item, index) in data.gridData"
            :key="index"
              class="timeline-item"
              :class="[
                item.status === '未解决' ? 'left-item' : 'right-item'
              ]"
            >
              <div class="timeline-dot" :style="getTimelineDotColor(item.status)"></div>
              <div class="timeline-content">
                <div class="alert-status-icon" :class="item.status === '已解决' ? 'resolved' : 'unresolved'">
                  <el-icon v-if="item.status === '已解决'">
                    <svg-icon name="resolved" />
                  </el-icon>
                  <el-icon v-else>
                    <svg-icon name="unresolved" />
                  </el-icon>
                </div>
                <div class="alert-header">
                  <div class="alert-priority">
                  <el-tag 
                    :type="getPriorityStyle(item.priority).type"
                      :style="{ backgroundColor: getPriorityStyle(item.priority).color }"
                    effect="dark"
                    size="small"
                  >
                      {{ item.priority || "未知" }}
                  </el-tag>
                    <span class="alert-time">{{ item.startime }}</span>
                  </div>
                  <div class="alert-status">
                  <el-tag 
                    :type="item.status === '已解决' ? 'success' : 'danger'"
                    size="small"
                      effect="light"
                  >
                      {{ item.status || "未知" }}
                  </el-tag>
                  </div>
                </div>
                <div class="alert-info">
                  <div class="alert-desc">
                    持续时间: {{ item.duration ? `${item.duration[0]}${item.duration[1]}${item.duration[2]}${item.duration[3]}` : '未知' }}
                  </div>
                </div>
                <div class="alert-footer">
                  <div class="alert-time-range">
                    <span class="time-label">开始时间：</span>{{ item.startime }}
                    <span class="time-separator">~</span>
                    <span class="time-label">结束时间：</span>{{ item.endtime || '进行中' }}
                  </div>
                  <div class="alert-tags" v-if="item.tags && item.tags.length > 0">
                  <el-tag 
                    v-for="(tag, tagIndex) in item.tags" 
                    :key="tagIndex"
                      effect="plain"
                    size="small"
                      style="margin: 3px 3px"
                  >
                    {{ tag.tag }}:{{ tag.value }}
                  </el-tag>
                </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
// 历史告警时间轴样式
.history-timeline-container {
  padding: 20px;
  
  // 自定义时间轴样式
  .custom-timeline {
    position: relative;
    padding: 20px 0;
    
    /* 时间轴固定中心线 */
    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      width: 4px;
      height: 100%; /* 使用100% */
      background: linear-gradient(to bottom, #e4e7ed, #409EFF, #e4e7ed); 
      transform: translateX(-50%);
      z-index: 0;
    }
    
    .timeline-wrapper {
      position: relative;
      max-height: 500px;
      overflow-y: auto;
      padding-right: 10px;
      overflow-x: hidden; /* 防止水平滚动 */
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }

      .timeline-item {
        position: relative;
        margin-bottom: 30px;
        width: 45%;
  
        &.left-item {
          margin-right: 55%;
    
          .timeline-dot {
            right: -44px;
            z-index: 2; /* 确保点在时间线之上 */
          }
    
          .timeline-content {
            &::after {
              right: -10px;
              border-left-color: #fff;
            }
          }
        }
        
        &.right-item {
          margin-left: 55%;
          
          .timeline-dot {
            left: -44px;
            z-index: 2; /* 确保点在时间线之上 */
          }
    
          .timeline-content {
            &::after {
              left: -10px;
              border-right-color: #fff;
              transform: rotate(180deg);
            }
          }
        }
        
        .timeline-dot {
          position: absolute;
          top: 20px;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: 3px solid #fff; /* 加粗边框 */
          z-index: 2; /* 确保在时间线之上 */
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
        }
  
        .timeline-content {
          background: #fff;
          padding: 15px 15px 15px 55px; /* 增加左侧padding留出更大图标位置 */
          border-radius: 8px;
          box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
          position: relative;
          transition: all 0.3s ease;
          border: 1px solid #f0f0f0;
      
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
          }
          
          &::after {
            content: '';
            position: absolute;
            top: 20px;
            width: 0;
            height: 0;
            border: 10px solid transparent;
            z-index: 1; /* 确保箭头在适当位置 */
          }
  
          .alert-status-icon {
            position: absolute;
            left: 10px;
            top: 10px;
            width: 38px;
            height: 38px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            
            .el-icon {
              font-size: 22px;
              
              &.resolved {
                color: #67C23A;
              }
              
              &.unresolved {
                color: #F56C6C;
              }
            }
            
            &.resolved {
              color: #67C23A;
            }
            
            &.unresolved {
              color: #F56C6C;
            }
          }
          
          .alert-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            
            .alert-priority {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .alert-time {
                color: #909399;
                font-size: 12px;
              }
            }
          }
          
          .alert-info {
            margin: 10px 0;
            
            .alert-desc {
              color: #606266;
              font-size: 13px;
              line-height: 1.5;
            }
          }
          
          .alert-footer {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 10px;
            
            .alert-time-range {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              gap: 8px;
              color: #606266;
              font-size: 13px;
              
              .time-label {
                color: #909399;
              }
              
              .time-separator {
                color: #DCDFE6;
                margin: 0 4px;
              }
            }
            
            .alert-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 5px;
            }
          }
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .timeline-item {
      width: 100% !important;
      margin: 0 0 20px 40px !important;
      
      .timeline-dot {
        left: -30px !important;
        right: auto !important;
      }
      
      .timeline-content {
        &::after {
          left: -10px !important;
          right: auto !important;
          border-right-color: #fff !important;
          transform: rotate(180deg) !important;
        }
      }
    }
    
    .custom-timeline::before {
      left: 20px !important;
      transform: none !important;
    }
  }
  
  :deep(.el-timeline) {
    padding: 0;

    .el-timeline-item {
      .el-timeline-item__wrapper {
        padding-left: 28px;
      }

      .el-timeline-item__timestamp {
        color: #909399;
        font-size: 13px;
        margin-bottom: 8px;
      }

      .el-timeline-item__tail {
        border-left-style: dashed;
      }
    }
  }

  .timeline-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .timeline-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .timeline-card-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .duration-text {
          color: #606266;
          font-size: 14px;
        }
      }

      .timeline-card-right {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .timeline-tag {
          margin: 0;
        }
      }
    }

    .timeline-card-time {
      color: #606266;
      font-size: 13px;

      .time-range {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .time-label {
          color: #909399;
        }

        .time-separator {
          color: #dcdfe6;
          margin: 0 4px;
        }
      }
    }
  }
}
</style>
