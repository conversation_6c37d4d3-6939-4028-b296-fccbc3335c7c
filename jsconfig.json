{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "strict": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": false, "baseUrl": "./", "sourceMap": true, "isolatedModules": true, "lib": ["esnext", "dom"], "types": ["vite/client"], "paths": {"@/*": ["src/*"]}, "jsx": "preserve", "checkJs": true, "noImplicitAny": false}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "./types/auto-imports.d.ts"], "exclude": ["/dist/**", "node_modules"]}