import api from "@/plugins/axios";

export interface BatchTriggersDetail {
  trigger_ids: string[];
  enabled: boolean; // true 表示启用, false 表示停用
}
/**
 *  批量启用或停用多个触发器。
 * @param data
 * @returns
 */
export function setTriggersStatusBatch(data: { trigger_ids: string; enabled: BatchTriggersDetail }) {
  return api.post(`/zabbix_alarm_configuration_management/set_trigger_status/`, data);
}

/**
 * 获取单个触发器的详细配置信息，用于用户界面进行调整
 */
export function getTriggersDetailById(trigger_id: string) {
  return api.get(`/zabbix_alarm_configuration_management/get_trigger_information/?trigger_id=${trigger_id}`);
}

/**
 * 更新触发器的设置，包括固定值、宏值、告警等级以及应用范围
 */
export function updateTriggersDetailById(data) {
  return api.post(`/zabbix_alarm_configuration_management/update_zabbix_trigger_config/`, data);
}
