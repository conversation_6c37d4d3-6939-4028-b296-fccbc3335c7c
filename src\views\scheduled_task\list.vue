<route lang="yaml">
meta:
  enabled: false
</route>

<script setup>
import {  ElMessage } from "element-plus";
import { ref } from "@vue/reactivity";
import {
  getAllScheduledTasks,
  recoverScheduledTaskByName,
  stopScheduledTaskByName,
} from "@/api/modules/scheduled_task/scheduled";

import { usePagination } from "@/utils/composables";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();

const router = useRouter();
const timerInterval = 900000;
const data = ref({
  loading: false,
  // 表格是否自适应高度
  tableAutoHeight: false,
  /**
   * 详情展示模式
   * router 路由跳转
   * dialog 对话框
   * drawer 抽屉
   */
  formMode: "router",
  // 详情
  formModeProps: {
    visible: false,
    id: "",
  },
  // 搜索
  search: {
    name: "",
  },
  searchFold: true,
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },
  // 列表数据
  dataList: [],
});

onMounted(() => {
  getDataList();
  const timer = setInterval(getDataList, timerInterval);
  onBeforeUnmount(() => {
    clearInterval(timer);
  });
});

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => queryData());
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => queryData());
}

function getDataList() {
  getAllScheduledTasks().then((res) => {
    data.value.dataList = res.data;
    pagination.value.total = res.data.length;
  });
}
//更改定时任务状态
function onEdit(row) {
  //调用禁用方法
  //console.log(row);
  if (row.Job_state == "success") {
    stopScheduledTaskByName(row.function_name).then((res) => {
      getDataList();
    });
  } else {
    recoverScheduledTaskByName(row.function_name).then((res) => {
      if (res.data == "success") {
        getDataList();
      } else {
        ElMessage.warning("启动失败!");
      }
    });
  }
}
function formatPeriod(row) {
  if (row.name === "周报脚本定时任务") {
    return "每周一早上六点执行";
  } else if (row.name == "日报脚本定时任务") {
    return "每日早上六点执行";
  } else {
    return "每隔三天早上七点执行";
  }
}
</script>

<template>
  <div>
    <page-main title="定时任务">
      <el-table
        ref="tableRef"
        v-loading="data.loading"
        class="list-table"
        :data="data.dataList"
        border
        stripe
        highlight-current-row
      >
        <el-table-column label="定时任务" prop="name" />
        <!-- <el-table-column label="周期时间" prop="name" :formatter="formatPeriod" /> -->
        <el-table-column label="状态" prop="Job_state" width="100px" align="center" fixed="right">
          <template #default="scope">
            <el-tag v-if="scope.row.Job_state == 'success'" type="success" size="small">启用</el-tag>
            <el-tag v-else-if="scope.row.Job_state == 'waiting'" type="info" size="small">等待执行</el-tag>
            <el-tag v-else-if="scope.row.Job_state == 'error'" type="warning" size="small">启动失败</el-tag>
            <el-tag v-else type="warning" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200px" align="center" fixed="right">
          <template #default="scope">
            <div v-if="scope.row.Job_state == 'success'">
              <el-button type="primary" size="small" plain @click="onEdit(scope.row)">禁用</el-button>
            </div>
            <div v-else-if="scope.row.Job_state == 'waiting'">
              <el-button
                type="primary"
                size="small"
                plain
                :disabled="scope.row.Job_state === 'waiting'"
                @click="onEdit(scope.row)"
              >
                启用
              </el-button>
            </div>
            <div v-else>
              <el-button type="primary" size="small" plain @click="onEdit(scope.row)">启用</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="pagination"
        background
        @current-change="currentChange"
        @size-change="sizeChange"
      />
    </page-main>
    <!-- <FormMode
      v-if="['dialog', 'drawer'].includes(data.formMode)"
      :id="data.formModeProps.id"
      v-model="data.formModeProps.visible"
      :mode="data.formMode"
      @success="getDataList"
    /> -->
  </div>
</template>

<style lang="scss" scoped>
.absolute-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 0;
  }

  .page-main {
    // 让 page-main 的高度自适应
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;

    .search-container {
      margin-bottom: 0;
    }
  }
}

.page-main {
  .search-form {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: -18px;

    :deep(.el-form-item) {
      flex: 1 1 300px;

      &:last-child {
        margin-left: auto;

        .el-form-item__content {
          justify-content: flex-end;
        }
      }
    }
  }

  .el-divider {
    margin-inline: -20px;
    width: calc(100% + 40px);
  }
}
</style>
