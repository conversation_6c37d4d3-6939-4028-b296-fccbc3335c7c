import api from '@/plugins/axios/index'
import axios from 'axios'
/**
 * 上传图片
 * @returns 
 */
export function uploadBusinessIcon(data) {//上传图标
  return api.post('/home_page/upload_business_icon/', data)
}
export function businessTopology(data) {//cmdb业务视图总览获取
  return api.get('/home_page/business_topology/?id=' + data)
}
export function getDatabase(data) {//cmdb业务视图总览获取
  return api.post('/home_page/analysis_database/', JSON.stringify(data))
}

export function getIisAnalysis(data) {//cmdb业务视图总览获取
  return api.post('/home_page/analysis_iis/', JSON.stringify(data))
}
export function getBusinessAnalysis(data) {//业务分析量获取
  return api.get(`/business/business_system_online_count/?business_system_type=${data.type}&past_days=${data.past_days}`)
}
/**
 * 编辑业务拓扑节点
 * @param {Object} data 业务节点数据
 * @returns {Promise}
 */
export function editBusinessTopology(data) {
  return api.post('/home_page/edit_business_topology', JSON.stringify(data))
}

// /**
//  * 删除业务拓扑节点
//  * @param {Object} data 包含要删除的业务ID
//  * @returns {Promise}
//  */
// export function deleteBusinessTopology(data) {
//   return api.post('/home_page/delete_business_topology', JSON.stringify(data))
// }

// /**
//  * 获取服务器信息
//  * @param {Object} data 包含要查询的业务ID
//  * @returns {Promise}
//  */
// export function getBusinessServers(data) {
//   return api.post('/home_page/server_performance_comparison/', JSON.stringify(data))
// }