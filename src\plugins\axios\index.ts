import axios from "axios";
import * as qs from "qs";
// 改造空间很富余，后期对报错进行改造
import router from "@/router/index";
import { Toast } from "../element-ui";
import { getBaseUrl } from "@/utils/axios";

const api = axios.create({
  baseURL: getBaseUrl(),
  timeout: 1800000,
  withCredentials: true,
  headers: { "content-type": "application/json" },
});

// 在全局请求之前进行拦截，处理请求信息
api.interceptors.request.use(
  (config: any) => {
    // 如果有data并且不是FormData，则使用qs进行字符串化
    if (config.data && !(config.data instanceof FormData)) {
      config.body = qs.stringify(config.data);
    }
    // 不要在这里设置responseType，除非明确需要
    // response 类型应在每个请求中根据需要单独设置
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    // mock数据特殊处理吧，直接返回，结构是{status:1,data:[]},有的还没有status
    if (response.config.baseURL == "/mock/") {
      return Promise.resolve(response.data);
    }
    // 对于下载请求，直接返回原始响应
    if (response.config.responseType === "blob") {
      return response;
    }
    // 网络错误
    const code = response.status;
    if ((code >= 200 && code < 300) || code === 304) {
      if (response.data.message == "授权文件未启动") {
        Toast.error(response.data);
      } else if (response.data.message == "授权文件路径错误") {
        Toast.error(response.data);
      } else if (response.data.message == "非本机授权文件，请检查是否服务器硬件发生变化。") {
        router.push({
          name: "error",
        });
      } else if (response.data.message == "不是针对联壹一体化运维平台的授权文件") {
        router.push({
          name: "error_product",
        });
      } else if (response.data.message == "产品已过期！") {
        router.push({
          name: "error_time",
        });
      } else {
        const message = response.data.message;
        // 后端统一配置{data:[],message:"",status_code:""},接口成功只需要处理data里面的数据
        // 400： 是告警错误，就是操作不当
        // 500: 属于程序内部出现异常，自定义异常
        if (response.data.status_code == 400) {
          Toast.warning(message + `：【${response.data.data["异常信息是"]}】`);
          return Promise.reject(response.data);
        } else if (response.data.status_code == 500) {
          Toast.error(message + `：【${response.data.data["异常信息是"]}】`);
          return Promise.reject(response.data);
        } else if (response.data.status_code == 200) {
          // 基于问题所有接口都提示的话，提示显示太多了，特此处理 ，get请求不提示
          if (response.config.method != "get") {
            Toast.success(message);
          }
        }
        return Promise.resolve(response.data);
      }
    } else {
      // 响应错误逻辑处理 5xx 4xx 等等
      let message = "";
      if (response.data.status_code === 400) {
        message = "请求错误，请检查参数";
      } else if (response.data.status_code === 401) {
        message = "未授权访问";
      } else if (response.data.status_code === 403) {
        message = "禁止访问";
      } else if (response.data.status_code === 404) {
        message = `请求地址出错: ${response.config.url}`;
      } else if (response.data.status_code === 500) {
        message = "服务器内部错误";
      } else {
        message = "未知错误";
      }
      Toast.error(message);

      return Promise.reject(response);
    }
  },
  (error) => {
    let message = error.message;
    if (message == "Network Error") {
      message = "后端网络故障";
    } else if (message.includes("timeout")) {
      message = "接口请求超时";
    } else if (message.includes("Request failed with status code")) {
      message = "接口" + message.substr(message.length - 3) + "异常,服务器异常";
    }
    Toast.error(message);
    return Promise.reject(error);
  }
);

export default api;
