<script setup>
import { getRunnerLog, clearLog } from "@/api/modules/ansible";

import { Viewer } from "@bytemd/vue-next";
import gfm from "@bytemd/plugin-gfm";
import gfmLocale from "@bytemd/plugin-gfm/lib/locales/zh_Hans.json";
import highlight from "@bytemd/plugin-highlight";
import "bytemd/dist/index.css";
import "highlight.js/styles/vs.css";
import { goBack } from "@/utils";
import { ArrowLeftBold } from "@element-plus/icons-vue";
const plugins = [
  gfm({
    locale: gfmLocale,
  }),
  highlight({}),
];
const data = reactive({
  log: "",
});
const activeName = ref("inter");
const contentLoading = ref(false);
const cleatLoading = ref(false);

onMounted(() => {
  getDataList(activeName.value);
});

function getDataList(tabName) {
  contentLoading.value = true;
  data.log = "";
  getRunnerLog({ log_name: tabName })
    .then((res) => {
      data.log = res.data;
    })
    .finally(() => {
      contentLoading.value = false;
    });
}

/**
 *  清除日志
 */
function clear(param) {
  ElMessageBox.confirm("确认要一键清空ansible执行日志吗？", "警告", {
    confirmButtonText: "是",
    cancelButtonText: "否",
    type: "warning",
  }).then(() => {
    cleatLoading.value = true;
    clearLog({ log_name: param })
      .then((res) => {
        getDataList(activeName.value);
      })
      .finally(() => {
        cleatLoading.value = false;
      });
  });
}

/**
 * tabs切换
 * @param {*} tabName
 */
function handleClick(tabName) {
  getDataList(tabName);
}
</script>

<template>
  <div>
    <page-main title="查看ansible执行的日志">
      <template #title>
        <div class="flex items-center">
          <el-icon class="cursor-pointer" @click="goBack()"><ArrowLeftBold /></el-icon>
          <span class="ml-10px">查看ansible执行的日志</span>
        </div>
      </template>
      <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
        <el-tab-pane label="功能日志" name="inter">
          <el-button size="default" type="primary" round @click="clear('inter')" :loading="cleatLoading">
            清空功能日志
          </el-button>
        </el-tab-pane>
        <el-tab-pane label="功能包日志" name="runner">
          <el-button size="default" type="primary" round @click="clear('runner')" :loading="cleatLoading">
            清空功能包日志
          </el-button>
        </el-tab-pane>
      </el-tabs>
      <div v-loading="contentLoading" class="bytemd1">
        <Viewer :value="'```sh\n' + data.log + '```'" :plugins="plugins" class="viewer" />
      </div>
    </page-main>
  </div>
</template>
<style lang="scss">
.bytemd1 {
  min-height: 50vh;
  pre code.hljs {
    white-space: break-spaces;
    word-break: break-words;
  }
}
</style>
