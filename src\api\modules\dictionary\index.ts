import api from "@/plugins/axios";

// 字典和字典数据项目在一个接口处理
/**
 * 获取字典列表
 * @returns
 */
export function apiDictionarylist() {
  return api.get("dictionary/get_all_dictionary/");
}

// 创建、修改、删除 字典
export function apiDictionaryeAddUpdate(data: any) {
  return api.post("dictionary/create_or_update_dictionary/", data);
}

// 删除字典
export function apiDictionarydelete(id: number | string) {
  return api.post("dictionary/delete_dictionary/?id=" + id);
}
