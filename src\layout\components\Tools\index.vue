<script setup name="Tools">
import { useI18n } from "vue-i18n";

import useSettingsStore from "@/store/modules/settings";
import useWarningListStore from "@/store/modules/warningList";
import useUserStore from "@/store/modules/user";
import useNotificationStore from "@/store/modules/notification";
import useDateTimeStore from "@/store/modules/datetime";
import eventBus from "@/utils/eventBus";
import { useMainPage } from "@/utils/composables";
import storage from "@/utils/storage";
import { license_info } from "@/store/modules/license";
const router = useRouter();
const settingsStore = useSettingsStore();
const userStore = useUserStore();
const timeStore = useDateTimeStore();
const notifucationStore = useNotificationStore();

const login_account = computed(() => {
  const userName = JSON.parse(storage.local.get("user") || "")["full_name"];
  return storage.local.get("account") + `(${userName})`;
});
notifucationStore.init();
const { t } = useI18n();
const warningList = useWarningListStore();
warningList.init();
const generateI18nTitle = inject("generateI18nTitle");

function userCommand(command) {
  switch (command) {
    case "dashboard":
      router.push({
        name: "dashboard",
      });
      break;
    case "setting":
      router.push({
        name: "personalEditPassword",
      });
      break;
    case "configuration":
      router.push({
        name: "configuration",
      });
      break;
    case "runnerlog":
      router.push({
        name: "runnerlog",
      });
      break;
    case "logout":
      userStore.logout().then(() => {
        // location.reload()
        router
          .push({
            name: "login",
          })
          .then(() => {
            location.reload();
          });
      });
      break;
  }
}
function day() {
  if (license_info.value.days.leftDays != "") {
    return license_info.value.days.leftDays + "天";
  } else {
    return license_info.value.days.leftDays;
  }
}
const host_num = "当前授权主机数：" + license_info.value.license.HostNums;
const over_time = "授权使用至" + license_info.value.license.over_time;
const over_day = "授权剩余天数：" + day();
const name = license_info.value.license.oragnName;
const version = license_info.value.license.PackageNames;
function toggle1() {
  useMainPage().maximize(!settingsStore.mainPageMaximizeStatus);
}
const time = reactive({
  time: [],
  shortcuts: [],
  disabledDate: "",
});

time.time = [new Date(timeStore.begin), new Date(timeStore.over)];

time.shortcuts = timeStore.shortcuts;
time.disabledDate = timeStore.disabledDate;
function selectTime() {
  storage.local.set("begin", new Date(time.time[0]).getTime());
  storage.local.set("over", new Date(time.time[1]).getTime());
  timeStore.setBegin(time.time[0]);
  timeStore.setOver(time.time[1]);
}

function toggleColorScheme(event) {
  document.documentElement.style.setProperty("--x", event.clientX + "px");
  document.documentElement.style.setProperty("--y", event.clientY + "px");
  if (document.startViewTransition) {
    document.startViewTransition(() => {
      settingsStore.setColorScheme(settingsStore.app.colorScheme === "dark" ? "light" : "dark");
    });
  } else {
    settingsStore.setColorScheme(settingsStore.app.colorScheme === "dark" ? "light" : "dark");
  }
}
</script>

<template>
  <div class="tools">
    <div class="oragnName">
      <h3>{{ name }}</h3>
    </div>
    <div class="flex items-center flex-shrink-0">
      <div class="buttons">
        <span class="popper-el-data-span">
          <el-date-picker
            style="width: 355px; padding: 0 5px"
            v-model="time.time"
            type="datetimerange"
            :clearable="false"
            :shortcuts="time.shortcuts"
            :disabled-date="time.disabledDate"
            range-separator="到"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="selectTime()"
            size="small"
          />
        </span>
        <el-popover trigger="hover" :width="270">
          <div>
            <div class="popover-content">
              <h3>消息通知</h3>
            </div>
            <el-divider class="message-divider" />
            <div class="scrollable-content">
              <el-row v-for="(item, index) in warningList.message" :key="index">
                <el-col :span="4">
                  <el-icon :size="35"><svg-icon name="warining-tip" /></el-icon>
                </el-col>
                <el-col :span="20">
                  <div>
                    <span style="font-size: 14px">{{ item.message }}</span>
                  </div>
                  <!-- <div style="text-align: right">
                                      <el-tag v-if="item.status==='success'" type="success">执行成功</el-tag>
                                      <el-tag v-else type="danger">执行失败</el-tag>
                                    </div> -->
                </el-col>
                <el-divider class="message-divider" />
              </el-row>
            </div>
            <el-divider class="message-divider" />
            <router-link v-slot="{ navigate }" :to="{ name: 'personalNotification' }" custom>
              <div class="message-footer" @click="navigate">进入消息列表</div>
            </router-link>
          </div>
          <template #reference>
            <el-badge :value="warningList.total" :is-dot="warningList.total > 99" :hidden="warningList.total === 0">
              <el-icon :size="30" class="item">
                <svg-icon name="warning-message" />
              </el-icon>
            </el-badge>
          </template>
        </el-popover>
        <el-popover trigger="hover" :width="270">
          <h3 style="text-align: center">{{ name }}</h3>
          <p style="text-align: center" class="font-bold">{{ version }}</p>
          <p style="text-align: center">{{ host_num }}</p>
          <p style="text-align: center">{{ over_time }}</p>
          <p style="text-align: center">{{ over_day }}</p>
          <template #reference>
            <el-icon class="item">
              <svg-icon name="i-ep:warning" />
            </el-icon>
          </template>
        </el-popover>

        <span class="item" @click="toggle1" v-if="settingsStore.toolbar.enableFullscreen">
          <el-icon>
            <svg-icon name="ep:full-screen"></svg-icon>
          </el-icon>
        </span>
        <span v-if="settingsStore.toolbar.enableNavSearch" class="item" @click="eventBus.emit('global-search-toggle')">
          <el-icon>
            <svg-icon name="ep:search" />
          </el-icon>
        </span>

        <span v-if="settingsStore.toolbar.enablePageReload" class="item" @click="useMainPage().reload()">
          <el-icon>
            <svg-icon name="ep:refresh" />
          </el-icon>
        </span>
        <span v-if="settingsStore.toolbar.enableColorScheme" class="item" @click="toggleColorScheme">
          <el-icon>
            <svg-icon :name="settingsStore.app.colorScheme === 'light' ? 'i-ri:sun-line' : 'i-ri:moon-line'" />
          </el-icon>
        </span>
        <span
          v-if="settingsStore.toolbar.enableAppSetting"
          class="item"
          @click="eventBus.emit('global-app-setting-toggle')"
        >
          <el-icon>
            <svg-icon name="ph:user-bold" />
          </el-icon>
        </span>
      </div>
      <el-dropdown class="user-container" size="default" @command="userCommand">
        <div class="user-wrapper">
          <el-avatar size="small">
            <el-icon style="font-size: 22px">
              <svg-icon name="avatar" />
            </el-icon>
          </el-avatar>
          {{ login_account }}
          <el-icon>
            <svg-icon name="i-ep:caret-bottom" />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="user-dropdown">
            <Auth :value="['admin']">
              <el-dropdown-item v-if="settingsStore.dashboard.enable" command="dashboard">
                {{ generateI18nTitle("route.dashboard", settingsStore.dashboard.title) }}
              </el-dropdown-item>
            </Auth>
            <el-dropdown-item command="setting">
              {{ t("app.profile") }}
            </el-dropdown-item>
            <Auth :value="['admin', 'publish.runnerlog']">
              <el-dropdown-item command="runnerlog">查看日志</el-dropdown-item>
            </Auth>
            <el-dropdown-item divided command="logout">
              {{ t("app.logout") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tools {
  display: flex;
  align-items: center;
  padding: 0 20px;
  white-space: nowrap;

  .oragnName {
    // padding-right: 400px;
    position: absolute;
    left: 0;
    right: 0;
    h3 {
      margin: 0 auto;
      width: 500px;
      text-align: center;
    }
  }

  .buttons {
    margin-right: 20px;

    .item {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 26px;
      width: 34px;
      cursor: pointer;
      vertical-align: middle;

      .el-icon {
        color: var(--el-text-color-primary);
        transition: var(--el-transition-color);
      }

      .el-badge {
        display: flex;
        align-items: center;
      }
    }
  }

  :deep(.language-container) {
    font-size: 16px;
  }

  :deep(.user-container) {
    display: inline-block;
    height: 24px;
    line-height: 24px;
    cursor: pointer;

    .user-wrapper {
      .el-avatar {
        vertical-align: middle;
        margin-top: -2px;
        margin-right: 4px;
      }
    }
  }
}

.scrollable-content {
  height: 200px;
  /* 设置内容区域的固定高度 */
  overflow-y: auto;
  /* 当内容超出高度时显示垂直滚动条 */
  display: flex;
  flex-direction: column;
}

.message-footer {
  margin-top: auto;
  /* 将footer推到底部 */
  background-color: #f0f0f0;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  cursor: pointer;
}

.popover-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 30px;
}

.message-divider {
  margin: 0px;
}
</style>
<style lang="scss">
.popper-el-data-span {
  .el-date-editor {
    width: 300px !important;
    .el-range-separator {
      flex: none;
    }
  }
}
</style>
