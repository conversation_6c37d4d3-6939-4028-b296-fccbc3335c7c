<template>
  <div>
    <el-drawer
      v-model="myVisible"
      size="1000px"
      :title="data.opt == 'update' ? '编辑业务信息' : '新增业务信息'"
      @close="handleDrawerClose"
      lock-scroll
      close-on-click-modal
    >
      <el-form label-width="150px" :model="data.businessForm">
        <div class="divider_css">
          <el-divider>基础信息</el-divider>
        </div>

        <el-form-item label="业务" prop="businessName">
          <!-- <el-input placeholder="请输入业务名称" style="width: 500px" v-model="data.businessForm.businessName" /> -->
          <el-select
            filterable
            clearable
            v-model="data.businessForm.businessName"
            placeholder="请选择业务"
            style="width: 500px"
          >
            <el-option v-for="item in data.businessNameOptions" :key="item.id" :label="item.key" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="业务分组" prop="belong">
          <el-input placeholder="输入产品分组" style="width: 500px" v-model="data.businessForm.belong" />
        </el-form-item>
        <el-row>
          <el-form-item label="服务器">
            <el-select
              v-model="data.businessForm.selectedServer"
              multiple
              filterable
              placeholder="请选择服务器"
              class="custom-select"
              style="width: 500px"
            >
              <el-option
                v-for="(item, index) in data.serverList"
                :key="index"
                :label="item.server_name"
                :value="item.id"
              />
            </el-select>
            <div class="float-right ml-10">
              <el-button type="primary" @click="getSoftList">确认</el-button>
              <el-button type="primary" plain @click="createResuorce('Server')">新增服务器</el-button>
            </div>
          </el-form-item>
        </el-row>
        <div class="divider_css">
          <el-divider>
            <span>绑定数据库</span>
            --
            <el-button size="small" @click="createResuorce('DB')" type="primary" plain>新增</el-button>
          </el-divider>
        </div>
        <el-form-item>
          <el-table :data="data.dataList" class="table_class" row-key="id" border default-expand-all>
            <el-table-column prop="server_name" label="服务器列表" />
            <el-table-column label="数据库">
              <template #default="scope">
                <el-checkbox-group v-model="data.businessForm.selectedDB">
                  <el-checkbox
                    v-for="(item, index) in scope.row.database_software_list"
                    :value="item.relation_id"
                    :key="index"
                  >
                    {{ item.software_name }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <div class="divider_css">
          <el-divider>
            <span>绑定中间件</span>
            --
            <el-button size="small" @click="createResuorce('Mid')" type="primary" plain>新增</el-button>
          </el-divider>
        </div>
        <el-form-item>
          <el-table :data="data.dataList" class="table_class" row-key="id" border default-expand-all>
            <el-table-column prop="server_name" label="服务器列表" />
            <el-table-column label="中间件">
              <template #default="scope">
                <el-checkbox-group v-model="data.businessForm.selectedMid">
                  <el-checkbox
                    v-for="(item, index) in scope.row.middleware_software_list"
                    :value="item.relation_id"
                    :key="index"
                  >
                    {{ item.software_name }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <div class="divider_css">
          <el-divider>
            <span>绑定业务应用</span>
            --
            <el-button size="small" @click="createResuorce('Soft')" type="primary" plain>新增</el-button>
          </el-divider>
        </div>

        <el-form-item>
          <el-table :data="data.dataList" class="table_class" row-key="id" border default-expand-all>
            <el-table-column prop="server_name" label="服务器列表" />
            <el-table-column label="业务应用">
              <template #default="scope">
                <el-checkbox-group v-model="data.businessForm.selectedBusiness">
                  <el-checkbox
                    v-for="(item, index) in scope.row.business_app_list"
                    :value="item.relation_id"
                    :key="index"
                  >
                    {{ item.software_name }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <div class="divider_css">
          <el-divider>
            <span>绑定集群</span>
            --
            <el-button size="small" @click="createResuorce('Cluster')" type="primary" plain>新增</el-button>
          </el-divider>
        </div>
        <el-form-item>
          <el-table :data="data.dataList" class="table_class" row-key="id" border default-expand-all>
            <el-table-column prop="server_name" label="服务器列表" />
            <el-table-column label="集群">
              <template #default="scope">
                <el-checkbox-group v-model="data.businessForm.configuration.clsuter">
                  <el-checkbox v-for="(item, index) in scope.row.cluster" :key="index" :label="item.relation_id">
                    {{ item.software_name }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <div class="button_class">
        <el-button @click="createBusiness" type="primary" :loading="data.createBusinessLoading">提交</el-button>
        <el-button @click="returnInformation">取消</el-button>
      </div>
    </el-drawer>
    <el-dialog v-model="showCreateDB" width="60%" :destroy-on-close="true" :show-close="false">
      <CreateDBForm
        :page="'business'"
        @closeDialog="closeDialog('DB')"
        @getResource="reloadResource('DB')"
      ></CreateDBForm>
    </el-dialog>
    <el-dialog v-model="showCreateMid" width="60%" :destroy-on-close="true" :show-close="false">
      <CreateMid :page="'business'" @closeDialog="closeDialog('Mid')" @getResource="reloadResource('Mid')"></CreateMid>
    </el-dialog>
    <el-dialog v-model="showCreateSoft" width="60%" :destroy-on-close="true" :show-close="false">
      <CreateBusinessSoft
        :page="'business'"
        @closeDialog="closeDialog('Soft')"
        @getResource="reloadResource('Soft')"
      ></CreateBusinessSoft>
    </el-dialog>
    <el-dialog v-model="showCreateServer" width="60%" :destroy-on-close="true" :show-close="false">
      <CreateServer
        :page="'business'"
        @closeDialog="closeDialog('Server')"
        @getResource="reloadResource('Server')"
      ></CreateServer>
    </el-dialog>
    <el-dialog v-model="showCreateCluster" width="60%" :destroy-on-close="true" :show-close="false">
      <CreateCluster
        :page="'business'"
        @closeDialog="closeDialog('Cluster')"
        @getResource="reloadResource('Cluster')"
      ></CreateCluster>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { reactive, onMounted, ref, computed, watch } from "vue";
import {
  cmdbBusinessGetBelongSoftware,
  cmdbBusinessAddSoftMessage,
  cmdbGetBusinessMessageById,
  getBusinessNameList,
} from "@/api/modules/cmdb/business";
import { getServerNameList } from "@/api/modules/cmdb/server";
import useSettingsStore from "@/store/modules/settings";
import CreateDBForm from "@/views/homepage/cmdb_database/createDatabase.vue";
import CreateMid from "@/views/homepage/cmdb_middleware/createMiddleware.vue";
import CreateBusinessSoft from "@/views/model_configuration/cmdb_business_soft/createBusinessSoft.vue";
import CreateServer from "@/views/homepage/cmdb_server/createServer.vue";
import CreateCluster from "@/views/homepage/cmdb_cluster/createCluster.vue";

const settingsStore = useSettingsStore();
const showCreateDB = ref(false);
const showCreateMid = ref(false);
const showCreateSoft = ref(false);
const showCreateServer = ref(false);
const showCreateCluster = ref(false);
const props = defineProps({
  businessId: {
    type: Number,
    default: 0,
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
  edges: {
    type: Array,
    default: [],
  },
});
const emit = defineEmits(["requestCompleted", "update:modelValue", "close-drawer"]);
let myVisible = computed({
  get: function () {
    return props.modelValue;
  },
  set: function (val) {
    emit("update:modelValue", val);
  },
});
const data = reactive({
  title: "",
  opt: "",
  edges: [],
  dataList: [],
  businessForm: {
    business_id: 0,
    businessName: "",
    belong: "中联产品",
    selectedServer: [],
    selectedDB: [],
    selectedMid: [],
    selectedBusiness: [],
    configuration: {
      nodes: [],
      edges: [],
      clsuter: [],
    },
  },
  //业务名称下拉列表
  businessNameOptions: [],

  serverList: [],
  showForm: false,
  showMidForm: false,
  showBSoft: false,

  createBusinessLoading: false,
});
onMounted(() => {
  getServerList();
  getBusinessNameOptions();
});
watch(
  () => props.businessId,
  (newVal, oldVal) => {
    if (newVal != 0 && newVal != undefined) {
      data.opt = "update";
      getData();
    }
  }
);
watch(
  () => props.edges,
  (newValue, oldValue) => {
    data.edges = newValue;
  }
);
function getData() {
  data.businessForm.business_id = props.businessId;
  cmdbGetBusinessMessageById(props.businessId).then((res) => {
    data.dataList = res.data.dataList;
    data.businessForm.businessName = res.data.businessName;
    data.businessForm.selectedServer = res.data.selectedServer;
    data.businessForm.selectedDB = res.data.selectedDB;
    data.businessForm.belong = res.data.belong;
    data.businessForm.selectedMid = res.data.selectedMid;
    data.businessForm.selectedBusiness = res.data.selectedBusiness;
    data.businessForm.configuration = res.data.configuration;
  });
}

// 获取业务名称下拉列表
function getBusinessNameOptions() {
  getBusinessNameList({ dictionary_code: "product_indexes" }).then((res) => {
    data.businessNameOptions = res.data;
  });
}

function getServerList() {
  getServerNameList().then((res) => {
    data.serverList = res.data;
  });
}
function getSoftList() {
  if (data.businessForm.selectedServer.length == 0) {
    return;
  }
  cmdbBusinessGetBelongSoftware(data.businessForm.selectedServer).then((res) => {
    data.dataList = res.data;
  });
}
function createBusiness() {
  const select = [
    ...data.businessForm.selectedDB,
    ...data.businessForm.selectedMid,
    ...data.businessForm.selectedBusiness,
  ];
  data.businessForm.configuration.nodes = [];
  data.dataList.forEach((element) => {
    const database = element.database_software_list.map((item) => item.relation_id);
    const sofeware = element.business_app_list.map((item) => item.relation_id);
    const middleware = element.middleware_software_list.map((item) => item.relation_id);
    const list = [...database, ...sofeware, ...middleware];
    const filterSelect = list.filter((item) => select.includes(item));
    data.businessForm.configuration.nodes.push({ server_id: element.server_id, select: filterSelect });
  });
  data.businessForm.configuration.edges = data.edges;
  data.createBusinessLoading = true;
  cmdbBusinessAddSoftMessage(data.businessForm)
    .then((res) => {
      emit("requestCompleted");
      emit("update:modelValue");
    })
    .finally(() => {
      data.createBusinessLoading = false;
    });
}
function returnInformation() {
  myVisible.value = false;
}
function handleDrawerClose() {
  data.businessForm = {
    business_id: 0,
    businessName: "",
    belong: "中联产品",
    selectedServer: [],
    selectedDB: [],
    selectedMid: [],
    selectedBusiness: [],
    configuration: {
      nodes: [],
      edges: [],
      clsuter: [],
    },
  };
  data.dataList = [];
  emit("close-drawer");
}

function createResuorce(item) {
  switch (item) {
    case "DB":
      showCreateDB.value = true;
      break;
    case "Soft":
      showCreateSoft.value = true;
      break;
    case "Mid":
      showCreateMid.value = true;
      break;
    case "Server":
      showCreateServer.value = true;
      break;
    case "Cluster":
      showCreateCluster.value = true;
      break;
  }
}
function closeDialog(item) {
  switch (item) {
    case "Soft":
      showCreateSoft.value = false;
      break;
    case "DB":
      showCreateDB.value = false;
      break;
    case "Mid":
      showCreateMid.value = false;
      break;
    case "Server":
      showCreateServer.value = false;
      break;
    case "Cluster":
      showCreateCluster.value = false;
      break;
  }
}
function reloadResource(item) {
  switch (item) {
    case "Server":
      getServerList();
      showCreateServer.value = false;
      break;
    case "DB":
      getSoftList();
      showCreateDB.value = false;
    case "Mid":
      getSoftList();
      showCreateMid.value = false;
    case "Soft":
      getSoftList();
      showCreateSoft.value = false;
    case "Cluster":
      getSoftList();
      showCreateCluster.value = false;
      break;
  }
}
</script>
<style lang="scss" scoped>
.button_class {
  display: flex;
  justify-content: center;
}

.table_class {
  width: 80%;
  margin-bottom: 20px;
}

.divider_css {
  :deep(.el-divider--horizontal) {
    margin: 20px 0;
  }
}
</style>
