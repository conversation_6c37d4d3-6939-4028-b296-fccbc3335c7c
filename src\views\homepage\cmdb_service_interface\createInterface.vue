<script setup>
import { computed, onMounted, reactive, ref } from "vue";
import { getServerNameList } from "@/api/modules/cmdb/server";
import { GetZabbixTemplate } from "@/api/modules/zabbix_api_management/zabbix";
import { createServiceInterface, updateServiceInterface } from "@/api/modules/cmdb/resource";
import { GetModelDetailById } from "@/api/modules/basic_crud/crud";

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(["closeDialog", "getResource", "update:modelValue"]);

const formRef = ref();
const data = reactive({
  title: "新增服务接口",
  serviceForm: {
    zabbix_id: 0,
    template_list: [],
    service_ip: "",
    service_name:"",
    service_url:"",
    extra_parameters: [
      { label: "short_name", key: "服务索引", value: "" },
    ],
  },
  serverList: [],
  service_template_list: [],
  cloneButton: false,
  submitLoading: false,
  formRules: {
    service_ip: [{ required: true, message: "请选择服务器", trigger: "blur" }],
    service_name: [{ required: true, message: "请输入服务名", trigger: "blur" }],
    service_url: [{ required: true, message: "请输入接口地址", trigger: "blur" }],
    // template_list: [{ required: true, message: "请选择监控模板", trigger: "blur" }]
  },
});
onMounted(() => {
  if (props.modelValue) {
    getInterface();
    data.title = "编辑服务接口";
    data.cloneButton = true;
  }
  getServer();
  getTemplate();
});
//编辑服务接口时获取信息
function getInterface() {
  GetModelDetailById(props.modelValue).then((res) => {
    // 从extra_parameters中获取WSNAME和URL
    const extraParams = res.data.extra_parameters;
    data.serviceForm.service_name = extraParams.WSNAME || "";
    data.serviceForm.service_url = extraParams.URL || "";
    
    // 过滤掉WSNAME和URL，保留其他参数
    data.serviceForm.extra_parameters = extraParams.configuration.filter(
      item => !["WSNAME", "URL"].includes(item.label)
    );
    
    data.serviceForm.template_list = res.data.zabbix_template_name;
    data.serviceForm.zabbix_id = res.data.id;
    data.serviceForm.service_ip = res.data.ip;
  });
}

function getServer() {
  getServerNameList().then((res) => {
    data.serverList = res.data;
  });
}
function getTemplate() {
  GetZabbixTemplate().then((res) => {
    data.service_template_list = filterTemp(res.data);
    // data.template_list = res.data;
  });
}
//筛选监控模板
function filterTemp(list) {
  return list.filter((item) => item.name.includes("Webservice"));
}

function removeExtraParams(index) {
  data.serviceForm.extra_parameters.splice(index, 1);
}
function addParams(data) {
  data.isEdit = false;
  if (data.label == "" || data.key == "" || data.value == "") {
    ElMessage.warning({
      message: "自定义字段不能置空",
      center: true,
    });
    data.label = "待填写";
    data.key = "待填写";
    data.value = "待填写";
  }
}
//创建和编辑服务接口
function addServiceInterface() {
  const result = (res) => {
    if (res.status_code == 200) {
      emit("getResource");
      emit("closeDialog");
    }
    data.submitLoading = false;
  };

  formRef.value.validate((valide) => {
    if (valide) {
      data.submitLoading = true;
      
      // 构建提交的数据结构
      const submitData = {
        ...data.serviceForm,
        extra_parameters: [
          ...data.serviceForm.extra_parameters,
          {
            label: "WSNAME",
            key: "服务名",
            value: data.serviceForm.service_name,
            isEdit: false
          },
          {
            label: "URL",
            key: "服务地址",
            value: data.serviceForm.service_url,
            isEdit: false
          }
        ]
      };
      
      // 删除不需要提交的字段
      delete submitData.service_name;
      delete submitData.service_url;

      //修改服务接口
      if (props.modelValue != 0) {
        updateServiceInterface(submitData).then(result);
      } else {
        //创建服务接口
        createServiceInterface(submitData).then(result);
      }
    }
  });
}

function handleClone() {
  emit("update:modelValue", 0);
  data.serviceForm.zabbix_id = 0;
  data.cloneButton = false;
}

function returnPrePage() {
  emit("closeDialog");
}
</script>
<template>
  <div>
    <el-row justify="center" type="flex">
      <el-col>
        <el-form label-width="100px" :rules="data.formRules" ref="formRef" :model="data.serviceForm">
          <el-form-item label="服务器" prop="service_ip">
            <el-select filterable v-model="data.serviceForm.service_ip">
              <el-option
                v-for="item in data.serverList"
                :label="item.server_name"
                :value="item.server_ip"
                :key="item.server_name"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="监控模板" prop="template_list">
            <el-select v-model="data.serviceForm.template_list" filterable multiple placeholder="请选择监控模板">
              <el-option
                v-for="(item, index) in data.service_template_list"
                :label="item.name"
                :value="item.name"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="服务名" prop="service_name">
            <el-input v-model="data.serviceForm.service_name" placeholder="请输入服务名"/>
          </el-form-item>
          <el-form-item label="接口地址" prop="service_url">
            <el-input v-model="data.serviceForm.service_url" placeholder="请输入接口地址"/>
          </el-form-item>
          <el-form-item label="参数">
            <el-table :data="data.serviceForm.extra_parameters">
              <el-table-column label="名称(EN)">
                <template #default="scoped">
                  <el-input v-if="scoped.row.isEdit && !scoped.row.label" v-model="scoped.row.label" size="small" />
                  <span v-else>{{ scoped.row.label }}</span>
                </template>
              </el-table-column>
              <el-table-column label="名称(CN)">
                <template #default="scoped">
                  <el-input v-if="scoped.row.isEdit && !scoped.row.key" v-model="scoped.row.key" size="small" />
                  <span v-else>{{ scoped.row.key }}</span>
                </template>
              </el-table-column>
              <el-table-column label="值">
                <template #default="scoped">
                  <el-input v-if="scoped.row.isEdit" v-model="scoped.row.value" size="small" />
                  <span v-else>{{ scoped.row.value }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center">
                <template #default="scope">
                  <template v-if="scope.row.isEdit">
                    <el-button type="primary" plain size="small" @click="addParams(scope.row)">保存</el-button>
                  </template>
                  <template v-else>
                    <el-button type="primary" plain size="small" @click="scope.row.isEdit = true">编辑</el-button>
                    <el-popconfirm
                      title="是否要删除此行？"
                      style="margin-left: 10px"
                      @confirm="removeExtraParams(scope.$index, scope.row)"
                    >
                      <template #reference>
                        <el-button type="danger" plain size="small">删除</el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <div class="button_css">
          <el-button type="primary" @click="addServiceInterface" :loading="data.submitLoading">提交</el-button>
          <el-button type="primary" plain v-if="data.cloneButton" @click="handleClone">克隆</el-button>
          <el-button @click="returnPrePage">取消</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<style scoped>
.button_css {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
/* @import "../../components/form.css"; */
</style>
