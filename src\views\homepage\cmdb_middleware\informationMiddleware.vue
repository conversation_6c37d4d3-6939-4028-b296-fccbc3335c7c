<script setup name="HomepageInformation">
import { onMounted, reactive, ref, onUnmounted, watch } from "vue";
import { usePagination } from "@/utils/composables";
import { cmdbGetResourceList, updateResourceAsset } from "@/api/modules/cmdb/resource";
import { ElMessage } from "element-plus";
import { ArrowDown, View, Refresh } from "@element-plus/icons-vue";

import { showNotification } from "@/plugins/element-ui/index";
import {
  goToPageAssetDetail,
  pageChangeNum,
  deleteResource,
  addZabbixForResource,
  showConfirmationDialog,
} from "../components/utils";
import { ResourceTypeEnum } from "../cmdb_asset/components/constants";
import SoftAsset from "@/views/model_configuration/cmdb_soft/components/detail.vue";
import SocketUtil from "@/utils/cmdb_utils";
import ZabbixResult from "@/containers/zabbix_resulte_dialog/index.vue";
import MoreOperations from "../components/more_operations.vue";
import CreateMid from "@/views/homepage/cmdb_middleware/createMiddleware.vue";

const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();
const router = useRouter();
const zabbixLoading = ref(false);
const zabbixResultDialog = ref(false);
const zabbixResultData = ref([]);

// 定义localStorage的key
const STORAGE_KEY = "cmdb_middleware_search_condition";

const data = reactive({
  serverLoading: false,
  title: "",
  assetTitle: "",
  softId: 0,
  dataList: [],
  allList: [],
  template_list: [],
  search: {
    searchName: "",
    isSearch: false,
  },
  showAsset: false,
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },
  drawer: false,
  router_id: 0,
});

// 监听搜索条件变化，更新localStorage
watch(
  () => data.search,
  (newVal) => {
    if (newVal.searchName) {
      localStorage.setItem(
        STORAGE_KEY,
        JSON.stringify({
          searchName: newVal.searchName,
          isSearch: newVal.isSearch,
        })
      );
    }
  },
  { deep: true }
);

onMounted(() => {
  data.title = ResourceTypeEnum.middleware;

  // 从localStorage获取筛选条件
  const savedSearch = localStorage.getItem(STORAGE_KEY);
  if (savedSearch) {
    try {
      const parsedSearch = JSON.parse(savedSearch);
      data.search.searchName = parsedSearch.searchName || "";
      data.search.isSearch = parsedSearch.isSearch || false;
    } catch (e) {
      console.error("解析保存的筛选条件出错", e);
    }
  }

  getData();
});

// 组件卸载时清除localStorage
onUnmounted(() => {
  localStorage.removeItem(STORAGE_KEY);
});

//后端采集socket通信
SocketUtil.socket.onmessage = (recv) => {
  let res = JSON.parse(recv.data);
  switch (res.message.status_code) {
    case 200:
      showNotification("采集成功", res.message.message, "success");
      break;
    case 500:
      showNotification("采集失败", res.message.message, "error");
      break;
  }
};
//加载初始化数据
function getData() {
  cmdbGetResourceList(data.title).then((res) => {
    res.data.map((item) => {
      item.iscluster = false;
      if (item.children) {
        item.iscluster = true;
        item.children.map((itemC) => {
          itemC.isclusterNode = true;
        });
      }
    });
    data.dataList = res.data;
    data.allList = res.data;

    // 如果有筛选条件，应用筛选
    if (data.search.isSearch && data.search.searchName) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

//分页
function changePageNum(lists) {
  let res = pageChangeNum(lists, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
  data.serverLoading = false;
}

const drawerTitle = computed(() => {
  return data.router_id === 0 ? "新增中间件" : "编辑中间件";
});

//添加页面跳转
function create() {
  data.drawer = true;
  data.router_id = 0;
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

//筛选函数
function queryData() {
  if (data.search.searchName === "") {
    getData();
    data.search.isSearch = false;
  }
  if (data.search.searchName !== "") {
    searchByName();
    data.search.isSearch = true;
  }
}

function searchByName() {
  let list = [];
  const ipRegex = /^[\d.]+$/;
  if (ipRegex.test(data.search.searchName.trim())) {
    data.allList.filter((item) => {
      if (item.ip.trim().indexOf(data.search.searchName.trim()) != -1) {
        list.push(item);
      }
    });
  } else {
    data.allList.filter((item) => {
      if (item.databaseSoft.trim().toLowerCase().indexOf(data.search.searchName.trim().toLowerCase()) != -1) {
        list.push(item);
      }
    });
  }

  changePageNum(list);
}

// 跳转详情
function jumpAssetDetail(row, isMonitoring = false, item = data.title) {
  const queryParams = { item, id: row.zabbix_id, ip: row.ip, relation_id: row.relation_id };
  if (item == ResourceTypeEnum.server && !row.iscluster && !row.isclusterNode) {
    queryParams.id = row.server_zabbix_id;
  }
  goToPageAssetDetail(queryParams, isMonitoring);
}

function editDatabase(row) {
  data.router_id = row.relation_id;
  data.drawer = true;
}

//批量删除
async function batchDel() {
  let list = [];
  data.batch.selectionDataList.forEach((item) => {
    list.push({ relation_id: item.relation_id, zabbix_id: item.zabbix_id });
  });
  const result = await deleteResource(ResourceTypeEnum.middleware, list);
  if (!result) {
    return;
  }
  getData();
}
//更新详情
function refreshAssetMiddleware(row) {
  updateResourceAsset({
    type: data.title,
    relation_id: row.relation_id,
  }).then((res) => {
    showNotification("提示", res.message, "info");
  });
}
//单个主机安装
function installZabbix(item) {
  if (item.template_list.length == 0) {
    ElMessage.error("中间件未配置监控模板，请配置！");
    return;
  }
  addZabbix([item]);
}
function batchInstall() {
  let list = [];
  list = data.allList.filter((item) => item.status == "offmonitor");
  if (list.length == 0) {
    ElMessage.error("请检查是否有未安装监控服务的设备");
    return;
  }
  let list_template = list.filter((item) => {
    return item.template_list.length == 0;
  });
  if (list_template.length != 0) {
    let serverName = list_template.map((item) => item.name);
    showNotification("注意", "以下中间件没有添加监控模板：[" + serverName.join(",") + "],请注意！", "error");
    return;
  }

  addZabbix(list);
}
//zabbix-agent安装

async function addZabbix(list) {
  let ip_information_list = [];
  list.forEach((item) => {
    ip_information_list.push({
      ip: item.ip,
      type: "middleware",
      group_name: item.group_name,
      id: item.zabbix_id,
    });
  });
  let params = {
    resource_type_name: "middleware",
    ip_information_list,
  };
  const confirm = await showConfirmationDialog();
  if (confirm) {
    zabbixLoading.value = true;
    zabbixResultDialog.value = true;
    zabbixResultData.value = [];
    try {
      zabbixResultData.value = await addZabbixForResource(params);
    } catch (error) {}
    zabbixLoading.value = false;
    getData();
  } else {
    ElMessage.info("已取消监控安装!");
  }
}

// 是否可以被选择
const selectable = (row) => {
  return !row.isclusterNode && !row.iscluster;
};
</script>
<template>
  <div>
    <!-- 服务器展示 -->
    <page-main :title="data.title">
      <div class="flex justify-between">
        <el-space wrap>
          <el-button type="primary" @click="create">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            新增
          </el-button>
          <el-button type="primary" @click="batchInstall">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:coordinate" />
              </el-icon>
            </template>
            一键安装所有
          </el-button>
          <el-button-group v-if="data.batch.enable">
            <el-button type="danger" :disabled="!data.batch.selectionDataList.length" @click="batchDel()">
              删除
            </el-button>
          </el-button-group>
        </el-space>
        <el-space wrap>
          <div class="w-220px">
            <el-input
              v-model="data.search.searchName"
              placeholder="请输入ip和安装软件检索"
              clearable
              @keyup.enter="queryData"
            />
          </div>
          <el-button type="primary" @click="queryData()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>

      <el-table
        v-loading="data.serverLoading"
        class="list-table"
        :data="data.dataList"
        border
        stripe
        highlight-current-row
        @selection-change="data.batch.selectionDataList = $event"
        row-key="zabbix_id"
        default-expand-all
      >
        <el-table-column v-if="data.batch.enable" type="selection" :selectable="selectable" align="center" fixed />
        <el-table-column label="IP" prop="ip">
          <template #default="scope">
            <!-- <el-link @click="jumpAssetDetail(scope.row, false)" type="primary">{{ scope.row.ip }}</el-link> -->
            <el-link
              @click="jumpAssetDetail(scope.row, false)"
              type="primary"
              v-if="!scope.row.iscluster && !scope.row.isclusterNode"
            >
              {{ scope.row.ip }}
            </el-link>
            <el-link v-else-if="scope.row.iscluster" @click="jumpAssetDetail(scope.row, false)" type="primary">
              {{ scope.row.ip }}
            </el-link>
            <span v-else>{{ scope.row.ip }}</span>
          </template>
        </el-table-column>

        <el-table-column label="类型" prop="type" width="108px" align="center">
          <template #default="scoped">
            <el-tag :type="scoped.row.iscluster ? 'warning' : 'success'">
              {{ scoped.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="软件" prop="databaseSoft"></el-table-column>
        <el-table-column label="服务器" prop="databaseServer">
          <template #default="scope">
            <el-link @click="jumpAssetDetail(scope.row, false, ResourceTypeEnum.server)" type="primary">
              {{ scope.row.databaseServer }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="监控状态" width="105px" align="center">
          <template #default="scoped">
            <template v-if="!scoped.row.isclusterNode">
              <el-tag v-if="scoped.row.status == 'online'">已启用</el-tag>
              <el-tag v-else-if="scoped.row.status == 'offmonitor'" type="info" effect="dark">未监控</el-tag>
              <el-tag v-else type="danger">停用的</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="在线状态" prop="type" width="108px" align="center">
          <template #default="scoped">
            <el-tag :type="scoped.row.isnline ? 'success' : 'danger'">
              {{ scoped.row.isnline ? "在线" : "离线" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scoped">
            <template v-if="!scoped.row.iscluster && !scoped.row.isclusterNode">
              <el-button type="primary" plain @click="editDatabase(scoped.row)">编辑</el-button>
              <MoreOperations>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :icon="View"
                    @click="installZabbix(scoped.row)"
                    v-if="scoped.row.status == 'offmonitor'"
                  >
                    安装监控
                  </el-dropdown-item>
                  <el-dropdown-item
                    :icon="View"
                    @click="jumpAssetDetail(scoped.row, true)"
                    v-if="scoped.row.status == 'online' || scoped.row.status == 'offline'"
                  >
                    最新数据
                  </el-dropdown-item>
                  <el-dropdown-item :icon="Refresh" @click="refreshAssetMiddleware(scoped.row)">
                    更新详情
                  </el-dropdown-item>
                </el-dropdown-menu>
              </MoreOperations>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>
    <el-drawer v-model="data.drawer" size="50%" :destroy-on-close="true" :close-on-click-modal="false">
      <template #header>
        <h4>{{ drawerTitle }}</h4>
      </template>
      <el-divider class="cancel_top" />
      <CreateMid v-model="data.router_id" @closeDialog="data.drawer = false" @getResource="getData()"></CreateMid>
    </el-drawer>
    <ZabbixResult v-model="zabbixResultDialog" :stepData="zabbixResultData" :loading="zabbixLoading"></ZabbixResult>
  </div>
</template>
<style scoped>
.cancel_top {
  margin-top: 0px;
}
</style>
