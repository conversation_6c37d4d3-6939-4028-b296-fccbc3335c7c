<script setup>
import { onMounted, reactive } from "vue";
import { usePagination } from "@/utils/composables";
import { ElMessage, ElMessageBox } from "element-plus";
import { uploadCurePackage, getAllPackageInfo, deleteCurePackage } from "@/api/modules/self_healing/slef_cure";
import { showNotification } from "@/plugins/element-ui/index";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const img = new URL("../../../../assets/images/pakeage.png", import.meta.url).href;
const uploadRef = ref();
const data = reactive({
  dataList: [],
  allList: [],
  batch: {
    enable: true,
    selectionDataList: [],
  },
  addDialog: false,
  addTitle: "上传自愈功能包",
  search: {
    searchName: "",
  },
  file: {},
});
onMounted(() => {
  getData();
});
//获取所有功能包信息
function getData() {
  getAllPackageInfo().then((res) => {
    data.dataList = res.data;
    data.allList = res.data;
  });
}

//单个功能包删除
function delOnlyOne(item) {
  ElMessageBox.confirm("是否删除选中功能包？", "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ElMessageBox.confirm("是否删除绑定关系？", "注意", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = { file_name: item.packageName, should_delete_relation: true };
          deleteCurePackage(params).then((res) => {
            getData();
          });
        })
        .catch((res) => {
          let params = { file_name: item.packageName, should_delete_relation: false };
          deleteCurePackage(params).then((res) => {
            getData();
          });
          // showNotification("提示", res.message, "error");
        });
    })
    .catch(() => {
      ElMessage.info("取消");
    });
}
//查询功能包
function queryData() {
  if (data.search.searchName == "") {
    getData();
    return;
  }
  data.dataList = data.allList.filter((item) => {
    return item.packageName.toLowerCase().includes(data.search.searchName.toLowerCase());
  });
}
function beforeUpload(file) {
  const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
  const whiteList = ["zip"];
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElMessage.warning({
      message: "上传的文件只允许是zip文件",
      center: true,
    });
    return false;
  }
}
function uploadYML(file) {
  data.addDialog = false;
  let forms = new FormData();

  forms.append("file", file.file);
  const notification = showNotification("提示", "文件正在上传中", "info");
  uploadCurePackage(forms)
    .then((res) => {
      notification.close();
      if (res.status_code == 200) {
        showNotification("上传成功", res.message, "success");
        getData();
        return;
      }
    })
    .catch((res) => {
      showNotification("上传失败", res.message, "error");
      uploadRef.value.clearFiles(file);
    });
}
function handleFileChange(file) {
  data.file = file;
}
</script>
<template>
  <div>
    <page-main title="自愈功能包管理">
      <div class="flex justify-between">
        <el-space wrap>
          <router-link to="/aotumation/self_healing/cure_relation">
            <el-button>自愈关系</el-button>
          </router-link>
          <router-link to="/aotumation/self_healing/cure_trigger_record">
            <el-button>触发记录</el-button>
          </router-link>
          <el-button type="primary" @click="data.addDialog = true">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:upload" />
              </el-icon>
            </template>
            上传自愈功能包
          </el-button>
        </el-space>
        <el-space>
          <div class="w-220px">
            <el-input
              v-model="data.search.searchName"
              placeholder="输入功能包名称"
              clearable
              @keyup.enter="queryData"
            />
          </div>
          <el-button type="primary" @click="queryData()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>

      <el-divider />
      <div v-if="data.dataList.length != 0">
        <el-row :gutter="16">
          <el-col v-for="(item, index) in data.dataList" :key="index" :span="4">
            <el-card shadow="always" class="custom-card">
              <div class="card-image">
                <img :src="img" alt="Package Image" />
              </div>
              <div class="card-content">
                <div class="text">
                  <el-tooltip :content="item.packageName" placement="top">
                    <span class="package-name">{{ item.packageName }}</span>
                  </el-tooltip>
                  <el-tooltip :content="item.version" placement="top">
                    <span class="version">版本: {{ item.version }}</span>
                  </el-tooltip>
                  <el-tooltip :content="item.associatedTriggers.join(',')" placement="top">
                    <span class="version">触发器: {{ item.associatedTriggers.join(",") }}</span>
                  </el-tooltip>
                </div>
                <el-button type="danger" size="small" class="delete-button" @click="delOnlyOne(item)">
                  <el-icon>
                    <delete />
                  </el-icon>
                  <span>删除</span>
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div v-if="data.dataList.length == 0">
        <el-empty :image-size="120" />
      </div>
    </page-main>
    <el-dialog v-model="data.addDialog" :title="data.addTitle">
      <div style="margin-top: 10px">
        <el-form label-position="right" label-width="120px" label-suffix="：">
          <el-form-item label="功能包文件">
            <file-upload
              ref="uploadRef"
              class="upload-demo"
              :max="1"
              :ext="['zip']"
              :size="500"
              :auto-upload="true"
              type="file"
              accept=".zip"
              action=""
              :before-upload="beforeUpload"
              :http-request="uploadYML"
            />
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">
.text {
  max-width: 80%;
  @include text-overflow;
}

.button_css {
  float: right;
  position: absolute;
  top: 20px;
  right: 10px;
}

.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70vh;
}

.page-content {
  flex-direction: column;
  align-items: center;
}

.custom-card {
  border-radius: 12px;
  border: none;
  overflow: hidden;
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.custom-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-image {
  width: 100%;
  height: 120px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.card-content {
  padding: 16px;
  text-align: center;
}

.package-name,
.version {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  /* 确保宽度占满父容器 */
}

.package-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.version {
  font-size: 14px;
  color: #909399;
  margin-bottom: 16px;
}

.delete-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #ff4d4f;
  border-color: #ff4d4f;
  color: white;
}

.delete-button:hover {
  background-color: #ff7875;
  border-color: #ff7875;
}
</style>
