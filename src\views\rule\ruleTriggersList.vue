<template>
  <page-main :title="'主机：' + data.host_name + ' 触发器列表'">
    <template #title>
      <div class="flex items-center">
        <el-icon class="cursor-pointer" @click="goBack()"><ArrowLeftBold /></el-icon>
        <span class="ml-10px">主机: {{ data.host_name }} 触发器列表</span>
      </div>
    </template>
    <search-bar>
      <el-form ref="form" label-width="80px" label-suffix="：" @submit.native.prevent>
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="名称" width="100">
              <el-input
                v-model="data.search.trigger_name"
                placeholder="输入触发器进行筛选,区分大小写"
                clearable
                @keyup.enter="searchItem()"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="严重性">
              <el-select
                v-model="data.search.priority"
                placeholder="严重性"
                style="width: 340px"
                collapse-tags
                collapse-tags-tooltip
                clearable
              >
                <el-option
                  v-for="item in SeverityDescriptionsNames"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-button type="primary" @click="searchItem()">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:search" />
                </el-icon>
              </template>
              筛选
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </search-bar>

    <div>
      <el-space wrap>
        <div>
          <el-button
            type="primary"
            @click="batchCutMonitoringDisable(MonitoringStatus.enable)"
            :disabled="!data.batch.selectionDataList.length"
          >
            <template #icon>
              <el-icon><Unlock /></el-icon>
            </template>
            启用
          </el-button>
          <el-button
            type="primary"
            @click="batchCutMonitoringDisable(MonitoringStatus.disable)"
            :disabled="!data.batch.selectionDataList.length"
          >
            <template #icon>
              <el-icon><Lock /></el-icon>
            </template>
            停用
          </el-button>
        </div>
      </el-space>
    </div>

    <el-table
      v-loading="data.tableLoading"
      :data="data.tableData"
      style="width: 100%"
      stripe
      @selection-change="data.batch.selectionDataList = $event"
    >
      <el-table-column type="selection" align="center" fixed />
      <el-table-column prop="priority" label="告警等级" width="120">
        <template #default="scope">
          <div
            class="rounded-4px m-4px px-8px py-4px inline-flex"
            :style="`background-color:${severityDescriptionsAndColors[SeverityDescriptions[scope.row.priority]].color}`"
            size="small"
          >
            {{ scope.row.priority }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="trigger_name" label="名称(点击修改)">
        <template #default="scope">
          <el-link @click="handleEditItem(scope.row)" underline type="primary">{{ scope.row.trigger_name }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="expression" label="表达式"></el-table-column>

      <el-table-column prop="enabled" label="状态(点击切换发布状态)" width="140">
        <template #default="scope">
          <el-button
            :type="scope.row.enabled ? 'success' : 'danger'"
            size="small"
            plain
            @click="cutMonitoringDisable(scope.row)"
          >
            {{ scope.row.enabled == true ? "已启用" : "已停用" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pagination.page"
      :total="pagination.total"
      :page-size="pagination.size"
      :page-sizes="pagination.sizes"
      :layout="pagination.layout"
      :hide-on-single-page="false"
      class="paginationTable"
      background
      @size-change="sizeChange"
      @current-change="currentChange"
    />
    <ruleTriggersDetailDialog
      v-model="data.showDetailDialog"
      :trigger_id="data.triggersDetail?.trigger_id"
      @response="getHostTriggersList()"
    ></ruleTriggersDetailDialog>
  </page-main>
</template>

<script setup lang="ts">
import { getHostsTriggers, MonitoringStatus } from "@/api/hosts/hosts";
import { ArrowLeftBold, Lock, Unlock } from "@element-plus/icons-vue";
import ruleTriggersDetailDialog from "./ruleTriggersDetailDialog.vue";
import { setTriggersStatusBatch } from "@/api/hosts/triggers";
import { SeverityDescriptions, severityDescriptionsAndColors, SeverityDescriptionsNames } from "@/utils/alarm";
import { goBack } from "@/utils";
import { usePagination } from "@/utils/composables";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
const route = useRoute();
const data = reactive({
  host_name: "" as any,
  host_id: "" as any,
  tableLoading: false,
  tableData: [],
  allData: [],
  batch: { selectionDataList: [] },
  search: {
    trigger_name: "",
    priority: "",
  },
  showDetailDialog: false,
  triggersDetail: {},
});
const routerParams = route.params;
const routerQuery = route.query;

/**
 *
 * @param enabled
 */
function batchCutMonitoringDisable(enabled) {
  ElMessageBox.confirm(enabled ? " 启用所选的触发器?" : " 停用所选的触发器??")
    .then(() => {
      const idList = data.batch.selectionDataList.map((item) => item.trigger_id);
      setTriggersstatusBatchFun(idList, enabled);
    })
    .catch(() => {});
}

function cutMonitoringDisable(row) {
  setTriggersstatusBatchFun([row.trigger_id], !row.enabled);
}

function searchItem() {
  getHostTriggersList();
}
/**
 *  停启用函数
 * @param list
 * @param enabled
 */
function setTriggersstatusBatchFun(list, enabled) {
  const params = {
    trigger_ids: list,
    enabled: enabled,
  };
  setTriggersStatusBatch(params)
    .then((res) => {
      getHostTriggersList();
    })
    .catch((error) => {
      console.log(error);
    });
}
/**
 * 获取主机触发器项列表
 */
function getHostTriggersList() {
  data.tableLoading = true;
  const search = data.search.trigger_name ? data.search.trigger_name : "";
  const priority = data.search.priority ? data.search.priority : "";
  getHostsTriggers({ host_id: data.host_id, trigger_name: search, priority })
    .then((res) => {
      data.allData = res.data;
      paging();
    })
    .finally(() => {
      data.tableLoading = false;
    });
}

/**
 * 点击触发器行 查看详情
 * @param row
 */
const handleEditItem = (row) => {
  data.showDetailDialog = true;
  data.triggersDetail = row;
};

// 每页条数改变
function sizeChange(size) {
  onSizeChange(size).then(() => {
    searchItem();
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.trigger_name != "") {
      searchItem();
    } else {
      paging();
    }
  });
}
function paging() {
  let params = getParams();
  let res = dataFilter(data.allData, params);
  data.tableData = res.list;
  pagination.value.total = res.total;
  data.tableLoading = false;
}
function dataFilter(dataList, params) {
  let list = [];
  dataList.forEach((element) => {
    list.push(element);
  });

  if (params.item_name != "" && params.item_name != undefined) {
    list = dataList.filter((item) => {
      return item ? item.item_name.includes(params.item_name) : true;
    });
  }

  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });
  return {
    list: pageList,
    total: list.length,
  };
}
onMounted(() => {
  if (!routerQuery.host_name || !routerParams.id) {
    router.push({
      name: "ruleHostList",
    });
  } else {
    data.host_name = routerQuery.host_name;
    data.host_id = routerParams.id;
    getHostTriggersList();
  }
});
</script>

<style lang="scss" scoped></style>
