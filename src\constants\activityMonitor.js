// activityMonitor.js
import router from "@/router";
import useUserStore from "@/store/modules/user";
const idleTime = 30 * 60 * 1000; // 30分钟无活动则视为闲置
let idleTimer;

export function startActivityMonitor() {
  resetIdleTimer();
  // 监听用户活动
  window.addEventListener('mousemove', resetIdleTimer);
  window.addEventListener('keypress', resetIdleTimer);
}

function resetIdleTimer() {
  clearTimeout(idleTimer);
  idleTimer = setTimeout(logoutUser, idleTime);
}

// 需注意：该退出只是前端清除缓存，登录页不需要退出了，以及大屏播放也不需要退出
function logoutUser() {
  if (!['/login', '/visualization/screen_player'].includes(router.currentRoute._value.path)) {

    // 执行登出逻辑，例如清除本地存储的 token，重定向到登录页面等
    const userStore = useUserStore();
    userStore.logout();
    ElMessageBox.alert('30分钟未使用,已自动退出', '提醒', {
      confirmButtonText: '确定', callback: (action) => {
        router.push({
          name: "login",
        }).then(() => { location.reload(); });

      },
    })
  }
}