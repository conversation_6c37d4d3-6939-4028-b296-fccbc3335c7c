<script setup>
import { reactive, computed, onMounted, ref, h } from "vue";
import { ElMessageBox } from "element-plus";
const emit = defineEmits(["modelValue", "closeInformation", "resetGraph", "look"]);
import {Folder,Location}from '@element-plus/icons-vue'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    default: {},
  },
});
const data = reactive({
  item: {},
  showProblemDetails: false,
  selectedProblem: null,
  tabActive: 'basic',
  childNodeProblemDialog: false,
  selectedChildNode: null,
});

// 判断节点类型 - 我们将始终把节点当作集群处理
const isCluster = computed(() => {
  // 默认都作为集群处理，除非明确标识为子节点
  return true;
});

const isServiceNode = computed(() => {
  return data.item && data.item.type === 'service';
});

const isDatabaseNode = computed(() => {
  return data.item && (data.item.type === 'oracle' || data.item.type === 'pg');
});

// 获取节点类型的中文名称
function getTypeLabel(type) {
  const typeMap = {
    'service': '业务',
    'oracle': 'Oracle数据库',
    'pg': 'PostgreSQL数据库',
    'nginx': 'Nginx中间件',
    'iis': 'IIS中间件',
    'origin': 'Origin',
    'interface': '接口',
    'firewall': '防火墙',
    'switch': '交换机',
  };
  return typeMap[type] || type || '未知';
}

// 获取状态信息
function getStatusInfo(status) {
  const statusMap = {
    'success': { label: '正常运行', class: 'success' },
    'danger': { label: '故障', class: 'danger' },
    'warning': { label: '警告', class: 'warning' },
  };
  return statusMap[status] || { label: '未知', class: 'info' };
}

// 处理节点问题数据
function processProblemData(problemData) {
  if (!problemData) return [];
  
  // 如果是嵌套数组，进行扁平化处理
  if (Array.isArray(problemData) && problemData.length > 0 && Array.isArray(problemData[0])) {
    return problemData[0];
  }
  
  // 如果是数组，直接返回
  if (Array.isArray(problemData)) {
  return problemData;
  }
  
  // 如果是对象，转为数组
  if (typeof problemData === 'object' && !Array.isArray(problemData)) {
    return [problemData];
  }
  
  return [];
}

// 根据优先级获取样式类名
function getPriorityClass(priority) {
  if (!priority) return 'priority-default';
  
  if (priority === '灾难') return 'priority-disaster';
  if (priority === '严重' || priority.includes('严重')) return 'priority-danger';
  if (priority === '警告' || priority.includes('警告')) return 'priority-warning';
  if (priority === '信息' || priority.includes('信息')) return 'priority-info';
  
  return 'priority-default';
}

// 根据优先级获取图标
function getPriorityIcon(priority) {
  if (!priority) return 'el-icon-info';
  
  if (priority === '灾难') return 'el-icon-warning';
  if (priority === '严重' || priority.includes('严重')) return 'el-icon-error';
  if (priority === '警告' || priority.includes('警告')) return 'el-icon-warning';
  if (priority === '信息' || priority.includes('信息')) return 'el-icon-info';
  
  return 'el-icon-info';
}

// 格式化时间显示
function formatDateTime(dateTimeStr) {
  if (!dateTimeStr) return '';
  
  try {
    const date = new Date(dateTimeStr);
    if (isNaN(date.getTime())) {
      return dateTimeStr;
    }
    
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  } catch (e) {
    return dateTimeStr;
  }
}

// 显示问题详情
function showProblemDetail(problem) {
  data.selectedProblem = problem;
  data.showProblemDetails = true;
}

// 处理端口信息
function processPortInfo(relationList) {
  if (!relationList || !Array.isArray(relationList)) return [];
  
  return relationList.map(item => {
    if (!item.port) {
      if (item.database_type === "pg") {
        item.port = "5432";
      } else if (item.database_type === "oracle") {
        item.port = "1521";
      } else {
        item.port = "";
      }
    }
    return item;
  });
}

// 按时间排序问题数据
function sortProblemsByTime(problems) {
  if (!problems || !Array.isArray(problems) || problems.length === 0) return [];
  
  return [...problems].sort((a, b) => {
    const dateA = a.lastchange ? new Date(a.lastchange).getTime() : 0;
    const dateB = b.lastchange ? new Date(b.lastchange).getTime() : 0;
    return dateB - dateA; // 降序排列，最新的问题在前
  });
}

// 根据优先级获取警报类型
function getAlertType(priority) {
  if (!priority) return { type: 'info', color: '#909399' };
  
  if (priority === '灾难') return { type: 'danger', color: '#E45959' };
  if (priority === '严重') return { type: 'danger', color: '#E97659' };
  if (priority === '一般严重') return { type: 'warning', color: '#FFA059' };
  if (priority === '警告') return { type: 'warning', color: '#FFC859' };
  if (priority === '信息') return { type: 'info', color: '#7499FF' };
  
  // 处理包含关键词的情况
  if (priority.includes('严重')) return { type: 'danger', color: '#E97659' };
  if (priority.includes('警告')) return { type: 'warning', color: '#FFC859' };
  if (priority.includes('信息')) return { type: 'info', color: '#7499FF' };
  
  return { type: 'info', color: '#909399' };
}

onMounted(() => {
  data.item = { ...props.item };
  
  // 处理关系数据
  if (data.item.relationDatabaseList) {
    data.item.relationDatabaseList = processPortInfo(data.item.relationDatabaseList);
  }
  if (data.item.relationServiceList) {
    data.item.relationServiceList = processPortInfo(data.item.relationServiceList);
  }
  
  // 处理子节点问题数据
  if (data.item.cluster && Array.isArray(data.item.cluster)) {
    data.item.cluster.forEach(node => {
      if (node.problem) {
        node.problem = processProblemData(node.problem);
      } else {
        node.problem = [];
      }
    });
  }
  
  // 处理节点自身的问题数据
  if (data.item.problem) {
    data.item.problem = processProblemData(data.item.problem);
  } else {
    data.item.problem = [];
  }
});

const showDialog = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("modelValue", val);
  },
});

function submit() {
    emit("resetGraph");
}

// 点击子节点显示详情
function showNodeDetail(node) {
  if (node && node.problem && node.problem.length > 0) {
    data.selectedChildNode = {
      name: node.name || node.ip,
      ip: node.ip,
      type: node.type,
      status: node.status,
      problem: sortProblemsByTime(node.problem), // 按时间排序
      productName: node.productName || data.item.productName,
      instanceName: node.instanceName
    };
    // 打开对话框
    data.childNodeProblemDialog = true;
  }
}
</script>
<template>
  <div>
    <el-drawer
      size="600"
      v-model="showDialog"
      direction="rtl"
      @close="$emit('closeInformation')"
      :show-close="false"
      :close-on-click-modal="false"
      class="business-information-drawer"
    >
      <template #header>
        <div class="drawer-header">
          <div class="title-container">
            <h3>{{ data.item.productName || data.item.serviceName || '节点详情' }}</h3>
            <el-tag 
              :type="data.item.status === 'success' ? 'success' : data.item.status === 'warning' ? 'warning' : 'danger'"
              class="status-tag"
              effect="dark"
              size="small"
            >
              {{ getStatusInfo(data.item.status).label }}
            </el-tag>
          </div>
          <div class="type-info">
            <span class="ip-display" v-if="data.item.ip">
              <i class="el-icon-connection"></i> {{ data.item.ip }}
            </span>
          </div>
        </div>
      </template>
      
      <template #default>
        <!-- 节点概览信息卡片 -->
        <div class="overview-card">
          <div class="overview-item" v-if="data.item.groupName">
            <div class="overview-icon"><el-icon class="el-icon-collection">
              <svg-icon name="topology-service-none" />
    </el-icon></div>
            <div class="overview-content">
              <div class="overview-label">业务分组</div>
              <div class="overview-value">{{ data.item.groupName }}</div>
                  </div>
                  </div>
                  
          <div class="overview-item" v-if="data.item.instanceName">
            <div class="overview-icon"><el-icon class="el-icon-monitor">
      <Folder />
    </el-icon></div>
            <div class="overview-content">
              <div class="overview-label">实例名称</div>
              <div class="overview-value">{{ data.item.instanceName }}</div>
                  </div>
                  </div>
                  
          <div class="overview-item" v-if="data.item.serverUrl">
            <div class="overview-icon"><el-icon class="el-icon-link">
      <Location />
    </el-icon></div>
            <div class="overview-content">
              <div class="overview-label">服务地址</div>
              <div class="overview-value">{{ data.item.serverUrl }}</div>
                  </div>
                  </div>
                  
          <div class="overview-item" v-if="data.item.cluster && data.item.cluster.length">
            <div class="overview-icon"><el-icon class="el-icon-s-grid">
              <svg-icon name="server" />
    </el-icon></div>
            <div class="overview-content">
              <div class="overview-label">服务器数量</div>
              <div class="overview-value">{{ data.item.cluster.length }} 个</div>
                  </div>
                  </div>
                </div>
        
        <!-- 首先展示问题信息时间轴 -->
        <div v-if="data.item.problem && processProblemData(data.item.problem).length > 0" class="problem-timeline-container">
          <div class="section-header">
            <i class="el-icon-warning-outline"></i>
            <span>检测到 {{ processProblemData(data.item.problem).length }} 个问题</span>
                  </div>
          
          <el-timeline class="custom-timeline">
                <el-timeline-item
                  v-for="(problem, index) in sortProblemsByTime(processProblemData(data.item.problem))"
                  :key="index"
                  :timestamp="formatDateTime(problem.lastchange)"
                  :type="problem.priority && problem.priority.includes('严重') ? 'danger' : 
                         problem.priority && problem.priority.includes('警告') ? 'warning' : 'info'"
                  :color="getAlertType(problem.priority).color"
                  placement="top"
                >
                  <el-card class="problem-card" shadow="hover" @click="showProblemDetail(problem)">
                    <div class="problem-card-header">
                      <span 
                        class="problem-priority" 
                        :style="{ backgroundColor: getAlertType(problem.priority).color }"
                      >
                        {{ problem.priority }}
                      </span>
                      <span class="problem-status">{{ problem.status || '未解决' }}</span>
                    </div>
                    <div class="problem-description">{{ problem.description }}</div>
                    <div class="problem-host" v-if="problem.hostname || problem.hostip">
                      <el-tag size="small" type="info" effect="plain">
                        {{ problem.hostname || problem.hostip }}
                      </el-tag>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
        </div>
        <div v-else class="no-problems-container">
          <div class="empty-state">
            <i class="el-icon-circle-check"></i>
            <span>一切正常，暂无问题记录</span>
          </div>
        </div>
        
        <!-- 基本信息 -->
        <div class="section-container">
          <div class="section-header">
            <i class="el-icon-document"></i>
            <span>基本信息</span>
              </div>
          
          <div class="info-content">
            <el-row :gutter="20">
              <el-col :span="12" v-if="data.item.productName">
                <div class="info-item">
                  <span class="info-label">产品名称</span>
                  <span class="info-value">{{ data.item.productName }}</span>
            </div>
              </el-col>
              
              <el-col :span="12" v-if="data.item.currentVersion">
                <div class="info-item">
                  <span class="info-label">版本号</span>
                  <span class="info-value">
                    <el-tag size="small" effect="plain">{{ data.item.currentVersion }}</el-tag>
                  </span>
                </div>
              </el-col>
              
              <el-col :span="24" v-if="data.item.installDirForDisplay">
                <div class="info-item">
                  <span class="info-label">安装目录</span>
                  <span class="info-value info-path">{{ data.item.installDirForDisplay }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        
        <!-- 显示子节点信息 -->
        <div v-if="data.item.cluster && data.item.cluster.length" class="section-container">
          <div class="section-header">
            <i class="el-icon-s-operation"></i>
            <span>服务器</span>
          </div>
          
          <div class="node-grid">
            <div 
              v-for="(node, index) in data.item.cluster" 
              :key="index" 
              class="node-card" 
              @click="showNodeDetail(node)"
              :class="{'has-problems': node.problem && node.problem.length}"
            >
              <div class="node-status-indicator" :class="node.status"></div>
              <div class="node-content">
                <div class="node-header">
                  <span class="node-name">{{ node.name || node.ip }}</span>
                  <el-tag 
                    size="mini" 
                    :type="node.status === 'success' ? 'success' : node.status === 'warning' ? 'warning' : 'danger'"
                  >
                    {{ getStatusInfo(node.status).label }}
                  </el-tag>
                </div>
                <div class="node-details">
                  <div class="node-type">
                    <i class="el-icon-cpu"></i>
                    {{ getTypeLabel(node.type) }}
                  </div>
                  <div class="node-ip">
                    <i class="el-icon-connection"></i>
                    {{ node.ip }}
                  </div>
                </div>
                <div v-if="node.problem && node.problem.length" class="node-problem-count">
                  <i class="el-icon-warning-outline"></i>
                  {{ node.problem.length }} 个问题
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 依赖服务信息 -->
        <div v-if="data.item.relationServiceList && data.item.relationServiceList.length > 0" class="section-container">
          <div class="section-header">
            <i class="el-icon-share"></i>
            <span>依赖服务</span>
          </div>
          
          <div class="relation-list">
            <div class="relation-item" v-for="(item, index) in data.item.relationServiceList" :key="index">
              <div class="relation-icon service-icon">
                <i class="el-icon-s-platform"></i>
              </div>
              <div class="relation-info">
                <div class="relation-name">{{ item.serviceName }}</div>
                <div class="relation-address">{{ item.ip }}{{ item.port ? ':' + item.port : '' }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 依赖数据库信息 -->
        <div v-if="data.item.relationDatabaseList && data.item.relationDatabaseList.length > 0" class="section-container">
          <div class="section-header">
            <i class="el-icon-coin"></i>
            <span>依赖数据库</span>
          </div>
          
          <div class="relation-list">
            <div class="relation-item" v-for="(item, index) in data.item.relationDatabaseList" :key="index">
              <div class="relation-icon database-icon">
                <el-icon class="el-icon-stopwatch"><svg-icon name="database" /></el-icon>
              </div>
              <div class="relation-info">
                <div class="relation-name">
                  {{ item.serviceName }}
                  <el-tag size="mini" effect="plain">{{ item.database_type }}</el-tag>
                </div>
                <div class="relation-address">{{ item.ip }}{{ item.port ? ':' + item.port : '' }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>
      
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="$emit('closeInformation')" plain>关闭</el-button>
          <el-button type="primary" @click="submit">确定</el-button>
        </div>
      </template>
    </el-drawer>
    
    <!-- 问题详情对话框 -->
    <el-dialog
      v-model="data.showProblemDetails"
      title="问题详情"
      width="600px"
      destroy-on-close
      :close-on-click-modal="true"
      class="problem-detail-dialog"
    >
      <div v-if="data.selectedProblem" class="problem-detail">
        <div class="problem-detail-header">
          <div class="problem-header-info">
            <span class="detail-host-name" v-if="data.selectedProblem.hostname">
              <i class="el-icon-monitor"></i> {{ data.selectedProblem.hostname }}
            </span>
            <span class="detail-host-ip">
              <i class="el-icon-connection"></i> {{ data.selectedProblem.hostip || '未知IP' }}
            </span>
          </div>
          
          <el-tag 
            :type="data.selectedProblem.status === '已解决' ? 'success' : 'danger'"
            :style="{ backgroundColor: data.selectedProblem.status === '已解决' ? '#52c41a' : '#f5222d' }"
            effect="dark"
            size="small"
            class="problem-status-tag"
          >
            {{ data.selectedProblem.status || '未解决' }}
          </el-tag>
        </div>

        <el-timeline class="detail-timeline">
          <el-timeline-item
            timestamp=""
            :type="data.selectedProblem.priority && data.selectedProblem.priority.includes('严重') ? 'danger' : 
                   data.selectedProblem.priority && data.selectedProblem.priority.includes('警告') ? 'warning' : 'info'"
            :color="getAlertType(data.selectedProblem.priority).color"
            size="large"
          >
            <div class="timeline-time">{{ formatDateTime(data.selectedProblem.lastchange) }}</div>
            <div class="timeline-content">
              <div class="timeline-priority">
                <span 
                  class="problem-priority-tag" 
                  :style="{ backgroundColor: getAlertType(data.selectedProblem.priority).color }"
                >
                  {{ data.selectedProblem.priority }}
                </span>
              </div>
              <div class="timeline-description">{{ data.selectedProblem.description }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="data.showProblemDetails = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 子节点问题详情对话框 -->
    <el-dialog
      v-model="data.childNodeProblemDialog"
      :title="`${data.selectedChildNode?.name || data.selectedChildNode?.ip || '节点'} - 问题详情`"
      width="650px"
      destroy-on-close
      :close-on-click-modal="true"
      class="child-node-problem-dialog"
    >
      <div v-if="data.selectedChildNode" class="child-node-detail">
        <!-- 子节点基本信息 -->
        <div class="child-node-header">
          <div class="node-info">
            <div class="node-type">
              <i class="el-icon-cpu"></i>
              {{ getTypeLabel(data.selectedChildNode.type) }}
            </div>
            <div class="node-ip">
              <i class="el-icon-connection"></i>
              {{ data.selectedChildNode.ip }}
            </div>
          </div>
          <el-tag 
            :type="data.selectedChildNode.status === 'success' ? 'success' : 
                  data.selectedChildNode.status === 'warning' ? 'warning' : 'danger'"
            :style="{ backgroundColor: data.selectedChildNode.status === 'success' ? '#52c41a' : 
                      data.selectedChildNode.status === 'warning' ? '#faad14' : '#f5222d' }"
            effect="dark"
            size="small"
          >
            {{ getStatusInfo(data.selectedChildNode.status).label }}
          </el-tag>
        </div>
        
        <!-- 问题计数 -->
        <div class="problem-count">
          发现 <span class="count-number">{{ data.selectedChildNode.problem.length }}</span> 个问题
        </div>
        
        <!-- 问题时间轴 -->
        <el-timeline class="child-node-timeline">
          <el-timeline-item
            v-for="(problem, index) in data.selectedChildNode.problem"
            :key="index"
            :timestamp="formatDateTime(problem.lastchange)"
            :type="problem.priority && problem.priority.includes('严重') ? 'danger' : 
                  problem.priority && problem.priority.includes('警告') ? 'warning' : 'info'"
            :color="getAlertType(problem.priority).color"
            placement="top"
          >
            <el-card class="timeline-problem-card" shadow="hover">
              <div class="timeline-problem-header">
                <span 
                  class="problem-priority" 
                  :style="{ backgroundColor: getAlertType(problem.priority).color }"
                >
                  {{ problem.priority }}
                </span>
                <span class="problem-status">{{ problem.status || '未解决' }}</span>
              </div>
              <div class="timeline-problem-description">{{ problem.description }}</div>
              <div class="timeline-problem-host" v-if="problem.hostname || problem.hostip">
                <el-tag size="small" type="info" effect="plain">
                  {{ problem.hostname || problem.hostip }}
                </el-tag>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="data.childNodeProblemDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style scoped>
/* 抽屉基本样式 */
:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fc;
}

:deep(.el-drawer__body) {
  padding: 0;
  overflow-y: auto;
  background-color: #f8f9fc;
}

.drawer-header {
  width: 100%;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.title-container h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.status-tag {
  font-weight: 500;
  border-radius: 4px;
}

.type-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.type-tag {
  font-weight: normal;
  border-radius: 4px;
}

.ip-display {
  color: #606266;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 节点概览卡片 */
.overview-card {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
  background-color: white;
  border-radius: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.overview-item {
  display: flex;
  align-items: flex-start;
  min-width: 250px;
  flex: 1;
  padding: 10px 16px;
}

.overview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: #f0f5ff;
  color: #1890ff;
  border-radius: 8px;
  margin-right: 12px;
  font-size: 18px;
}

.overview-content {
  flex: 1;
}

.overview-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.overview-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  word-break: break-all;
}

/* 各区块通用样式 */
.section-container {
  margin-bottom: 16px;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #303133;
  font-size: 15px;
}

.section-header i {
  font-size: 18px;
  margin-right: 8px;
  color: #1890ff;
}

/* 基本信息样式 */
.info-content {
  padding: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.info-label {
  font-size: 13px;
  color: #909399;
  margin-bottom: 8px;
}

.info-value {
  color: #303133;
  font-size: 14px;
  word-break: break-all;
}

.info-path {
  background-color: #f5f7fa;
  padding: 6px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

/* 节点网格样式 */
.node-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  padding: 16px;
}

.node-card {
  position: relative;
  border-radius: 8px;
  background-color: white;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

.node-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.node-status-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.node-status-indicator.success {
  background-color: #52c41a;
}

.node-status-indicator.warning {
  background-color: #faad14;
}

.node-status-indicator.danger {
  background-color: #f5222d;
}

.node-content {
  padding-left: 8px;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.node-name {
  font-weight: 600;
  color: #303133;
  font-size: 15px;
}

.node-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.node-type, .node-ip {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #606266;
}

.node-problem-count {
  padding: 6px 0;
  color: #f56c6c;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
}

.has-problems {
  background-color: #fff8f8;
}

/* 问题时间轴样式 */
.problem-timeline-container {
  background-color: white;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.custom-timeline {
  padding: 16px;
}

:deep(.el-timeline-item__tail) {
  border-left: 2px solid #e4e7ed;
}

:deep(.el-timeline-item__node) {
  z-index: 1;
}

:deep(.el-timeline-item__content) {
  margin-left: 25px;
}

.problem-card {
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 8px;
  border: none;
}

.problem-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.problem-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.problem-priority {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.problem-status {
  font-size: 13px;
  color: #909399;
}

.problem-description {
  margin-bottom: 10px;
  line-height: 1.5;
  color: #303133;
}

.problem-host {
  text-align: right;
}

.priority-disaster {
  background-color: #722ed1;
  color: white;
}

.priority-danger {
  background-color: #f5222d;
  color: white;
}

.priority-warning {
  background-color: #faad14;
  color: white;
}

.priority-info {
  background-color: #1890ff;
  color: white;
}

.priority-default {
  background-color: #8c8c8c;
  color: white;
}

/* 暂无问题时样式 */
.no-problems-container {
  background-color: white;
  padding: 40px 0;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #67c23a;
}

.empty-state i {
  font-size: 50px;
}

.empty-state span {
  font-size: 16px;
}

/* 依赖服务和数据库列表 */
.relation-list {
  padding: 16px;
}

.relation-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fc;
  border-radius: 6px;
  margin-bottom: 12px;
}

.relation-item:last-child {
  margin-bottom: 0;
}

.relation-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.service-icon {
  background-color: #1890ff;
}

.database-icon {
  background-color: #13c2c2;
}

.relation-info {
  flex: 1;
}

.relation-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.relation-address {
  font-size: 13px;
  color: #606266;
}

/* 页脚样式 */
.drawer-footer {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background-color: white;
}

/* 问题详情对话框样式 */
.problem-detail-dialog :deep(.el-dialog__header) {
  padding: 20px;
  background-color: #f8f9fc;
  border-bottom: 1px solid #ebeef5;
}

.problem-detail-dialog :deep(.el-dialog__body) {
  padding: 0;
  background-color: #f8f9fc;
}

.problem-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #ebeef5;
}

.problem-header-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-host-name, .detail-host-ip {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.problem-status-tag {
  font-weight: 500;
}

.detail-timeline {
  padding: 20px;
}

.timeline-time {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.timeline-content {
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.timeline-priority {
  margin-bottom: 12px;
}

.problem-priority-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
}

.timeline-description {
  font-size: 15px;
  line-height: 1.6;
  color: #303133;
}

/* 子节点问题详情对话框样式 */
.child-node-problem-dialog :deep(.el-dialog__header) {
  padding: 20px;
  background-color: #f8f9fc;
  border-bottom: 1px solid #ebeef5;
}

.child-node-problem-dialog :deep(.el-dialog__body) {
  padding: 0;
  background-color: #f8f9fc;
}

.child-node-detail {
  width: 100%;
}

.child-node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f9fafc;
}

.node-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-type, .node-ip {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #303133;
}

.problem-count {
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 600;
  color: #606266;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.count-number {
  color: #f56c6c;
  margin: 0 4px;
}

.child-node-timeline {
  padding: 20px;
  background-color: #fff;
}

.timeline-problem-card {
  background-color: #f9fafc;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  border: none;
}

.timeline-problem-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.timeline-problem-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.timeline-problem-description {
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
  margin-bottom: 12px;
}

.timeline-problem-host {
  text-align: right;
}
</style>
