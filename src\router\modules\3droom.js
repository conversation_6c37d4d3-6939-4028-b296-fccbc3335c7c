const Layout = () => import("@/layout/index.vue");
const IframeLayout = () => import("@/layout/iframe.vue");
let children = [
  {
    path: "room",
    name: "room",
    component: () => import("@/views/3droom/room.vue"),
    meta: {
      title: "3D机房",
      // link: url,
      cache: true,
      auth: ['admin', "room.browse"],
      copyright: false,
    }
  },
];

export default {
  path: "/3droom",
  redirect: "/3droom/room",
  component: Layout,
  name: "3droom",
  meta: {
    auth: ['admin', "3droom.browse"],
    title: "3D机房",
    icon: "3droom",
  },
  children,
};
