<template>
  <div v-html="parsedMarkdown" class="markdown-content"></div>
</template>

<script setup lang="ts">
import axios from "axios";

const parsedMarkdown = ref("");
onMounted(() => {
  fetchAndParseMarkdown();
});

function fetchAndParseMarkdown() {
  // 使用fetch读取public目录下的md文件，注意路径的处理
  console.log(import.meta.env.DEV);

  const url = import.meta.env.DEV ? "/version.md" : "/job/version.md";
  fetch(url)
    .then((response) => response.text())
    .then((text) => {
      parsedMarkdown.value = text;
    })
    .catch((error) => {
      console.error("读取文件出错：", error);
    });
}
</script>
<style lang="scss">
.markdown-content {
  white-space: pre-wrap; /* 保留空白字符和换行 */
  font-family: monospace; /* 可选：使用等宽字体 */
}
</style>
