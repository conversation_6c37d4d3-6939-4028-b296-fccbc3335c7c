<script setup>
import { updateUser, createUser } from "@/api/modules/user_api_management/users";
import { authList } from "@/constants/auth";

const props = defineProps({
  id: {
    type: [Number, String],
    default: "",
  },
});
const route = useRoute();
const item = route.query;
const newPassword = (rule, value, callback) => {
  var reg1 = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*.])[\da-zA-Z~!@#$%^&*.]{8,}$/; //密码必须是8位以上、必须含有字母、数字、特殊符号
  if (!reg1.test(value)) {
    callback(new Error("密码必须是8位以上、必须含有字母、数字、特殊符号"));
  } else {
    callback();
  }
};

const formRef = ref();

const data = ref({
  loading: false,
  form: {
    id: props.id,
    name: "",
    notes: "",
    full_name: "",
    position: "",
    password: "",
    auth: [],
  },
  rules: {
    name: [{ required: true, message: "请输入账号", trigger: "blur" }],
    notes: [{ required: true, message: "请输入备注", trigger: "blur" }],
    full_name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
    position: [{ required: true, message: "请输入职位", trigger: "blur" }],
    password: [
      { required: true, message: "请输入密码", trigger: "blur" },
      { required: true, trigger: "blur", validator: newPassword },
    ],
  },
});

onMounted(() => {
  if (item.id != undefined) {
    getInfo();
  }
});

function getInfo() {
  data.value.loading = true;
  data.value.form = item;
  data.value.form.password = "";

  data.value.form.auth = JSON.parse(item.auth.replace(/'/g, '"'));
  data.value.loading = false;
}

defineExpose({
  submit(callback) {
    if (data.value.form.id == "") {
      formRef.value.validate((valid) => {
        if (valid) {
          createUser({
            name: data.value.form.name,
            notes: data.value.form.notes,
            full_name: data.value.form.full_name,
            position: data.value.form.position,
            password: data.value.form.password,
            auth: data.value.form.auth,
          }).then(() => {
            callback && callback();
          });
        }
      });
    } else {
      formRef.value.validate((valid) => {
        if (valid) {
          updateUser(data.value.form.id, data.value.form).then((res) => {
            if (res.status_code == 200) {
              callback && callback();
            }
          });
        }
      });
    }
  },
});

function handleCheckedCitiesChange(val) {
  // console.log(val);
  // data.value.form.auth = val;
}
function handleCheckboxChange(item, children) {
  const { auth } = data.value.form;
  const isSelected = auth.includes(item.value);

  // 无子节点时：只处理当前项（取消选中时移除当前值）
  if (!children || children.length === 0) {
    if (!isSelected) {
      const index = auth.indexOf(item.value);
      if (index > -1) auth.splice(index, 1);
    }
    return;
  }

  // 递归收集所有子节点的权限值（包括嵌套子节点）
  const collectChildValues = (nodes, values = new Set()) => {
    nodes.forEach((node) => {
      // 收集当前节点的权限值
      if (node.auth) {
        node.auth.forEach(({ value }) => values.add(value));
      }
      // 递归收集子节点
      if (node.children) {
        collectChildValues(node.children, values);
      }
    });
    return values;
  };

  const childValues = collectChildValues(children);
  const authSet = new Set(auth); // 使用Set提高查找效率

  if (isSelected) {
    // 添加缺失的子权限
    childValues.forEach((value) => {
      if (!authSet.has(value)) {
        auth.push(value);
        authSet.add(value); // 更新临时Set
      }
    });
  } else {
    // 移除所有子权限
    childValues.forEach((value) => {
      if (authSet.has(value)) {
        const index = auth.indexOf(value);
        if (index > -1) auth.splice(index, 1);
      }
    });
  }
  console.log(auth);
}
</script>

<template>
  <div v-loading="data.loading">
    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="120px" label-suffix="：">
      <el-form-item label="账号名称" prop="name">
        <el-input v-model="data.form.name" placeholder="请输入账号名称" />
      </el-form-item>
      <el-form-item label="备注" prop="notes">
        <el-input v-model="data.form.notes" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="姓名" prop="full_name">
        <el-input v-model="data.form.full_name" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="职位" prop="position">
        <el-input v-model="data.form.position" placeholder="请输入职位名称" />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="data.form.password" show-password placeholder="请输入密码" />
      </el-form-item>
      <el-form-item v-if="data.form.auth[0] != 'admin'" label="权限">
        <el-table
          :data="authList()"
          style="width: 100%; margin-bottom: 20px"
          row-key="id"
          border
          :default-expand-all="false"
        >
          <el-table-column prop="menu" label="菜单" />
          <el-table-column label="权限">
            <template #default="scope">
              <el-checkbox-group v-model="data.form.auth" @change="handleCheckedCitiesChange">
                <el-checkbox
                  v-for="(item, index) in scope.row.auth"
                  :key="index"
                  :label="item.value"
                  @change="handleCheckboxChange(item, scope.row.children)"
                >
                  {{ item.lable }}
                </el-checkbox>
              </el-checkbox-group>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
// scss
</style>
