<script lang="ts" setup>
import { onMounted, reactive, ref, onUnmounted, watch } from "vue";
import { usePagination } from "@/utils/composables";
import { cmdbGetResourceList, cmdbDeleteResource, addMonitoring } from "@/api/modules/cmdb/resource";

import { ElMessage, ElMessageBox } from "element-plus";
import ZabbixResult from "@/containers/zabbix_resulte_dialog/index.vue";
import { ArrowDown, Remove, Location, View } from "@element-plus/icons-vue";
import { showNotification } from "@/plugins/element-ui";
import {
  goToPageAssetDetail,
  hideSensitiveInfo,
  pageChangeNum,
  addZabbixForResource,
  showConfirmationDialog,
  deleteResource,
} from "../components/utils";
import { ResourceTypeEnum } from "../cmdb_asset/components/constants";
import MoreOperations from "../components/more_operations.vue";
import CreateInter from "@/views/homepage/cmdb_service_interface/createInterface.vue";

const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();
const router = useRouter();
const resultDialog = ref(false);
const zabbixLoading = ref(false);
const zabbixResultDialog = ref(false);
const zabbixResultData = ref([]);

// 定义localStorage的key
const STORAGE_KEY = 'cmdb_service_interface_search_condition';

const data = reactive({
  search: {
    searchName: "",
    isSearch: false,
  },
  batch: {
    enable: true,
    selectionDataList: [],
  },
  allList: [],
  dataList: [],
  drawer: false,
  router_id: 0,
});

// 监听搜索条件变化，更新localStorage
watch(() => data.search, (newVal) => {
  if (newVal.searchName) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify({
      searchName: newVal.searchName,
      isSearch: newVal.isSearch
    }));
  }
}, { deep: true });

onMounted(() => {
  // 从localStorage获取筛选条件
  const savedSearch = localStorage.getItem(STORAGE_KEY);
  if (savedSearch) {
    try {
      const parsedSearch = JSON.parse(savedSearch);
      data.search.searchName = parsedSearch.searchName || '';
      data.search.isSearch = parsedSearch.isSearch || false;
    } catch (e) {
      console.error('解析保存的筛选条件出错', e);
    }
  }
  
  getData();
});

// 组件卸载时清除localStorage
onUnmounted(() => {
  localStorage.removeItem(STORAGE_KEY);
});

function getData() {
  cmdbGetResourceList("服务接口").then((res) => {
    data.dataList = res.data;
    data.allList = data.dataList;
    
    // 如果有筛选条件，应用筛选
    if (data.search.isSearch && data.search.searchName) {
      queryData();
    } else {
    changePageNum(data.allList);
    }
  });
}

//分页
function changePageNum(lists) {
  let res = pageChangeNum(lists, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
}

// 每页数量切换

function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      queryData();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）

function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      queryData();
    } else {
      changePageNum(data.allList);
    }
  });
}

const drawerTitle = computed(() => {
  return data.router_id === 0 ? "新增服务接口" : "编辑服务接口";
});
function createInterface() {
  data.router_id = 0;
  data.drawer = true;
}
function editInterface(row) {
  data.router_id = row.id;
  data.drawer = true;
}

//筛选数据
function queryData() {
  if (data.search.searchName == "") {
    data.search.isSearch = false;
    getData();
    return;
  }
  let list = [];
  const searchStr = data.search.searchName.trim();
  const ipRegex = /^[\d.]+$/;
  if (ipRegex.test(searchStr)) {
    list = data.allList.filter((item) => {
      return item.ip.trim().indexOf(searchStr) != -1;
    });
  } else {
    list = data.allList.filter((item) => {
      return item.name.trim().toLowerCase().indexOf(searchStr.toLowerCase()) !== -1;
    });
  }
  data.search.isSearch = true;
  changePageNum(list);
}
//批量删除
function batchDel() {
  let list = [];
  data.batch.selectionDataList.forEach((item) => {
    list.push({ relation_id: 0, zabbix_id: item.zabbix_id });
  });
  delFunc(list);
}
//删除公共函数
async function delFunc(list) {
  const result = await deleteResource(ResourceTypeEnum.serviceInterface, list);
  if (!result) {
    return;
  }
  getData();
}
//单个删除
function oneDel(id) {
  console.log(id);
  let list = [{ relation_id: 0, zabbix_id: id }];
  delFunc(list);
}
//安装单个监控
function installZabbix(item) {
  addZabbix([item]);
}
//批量安装
function allInstallZabbix() {
  let list = [];
  list = data.dataList.filter((item) => item.status == "offmonitor");
  if (list.length == 0) {
    ElMessage.error("请检查是否有未安装监控服务的设备");
    return;
  }

  addZabbix(list);
}
//安装agent
async function addZabbix(list) {
  let ip_information_list = [];
  list.forEach((item) => {
    ip_information_list.push({
      ip: item.ip,
      type: "service",
      id: item.zabbix_id,
    });
  });
  let params = {
    resource_type_name: "service",
    ip_information_list,
  };
  const confirm = await showConfirmationDialog();
  if (confirm) {
    zabbixLoading.value = true;
    zabbixResultDialog.value = true;
    zabbixResultData.value = [];
    try {
      zabbixResultData.value = await addZabbixForResource(params);
    } catch (error) {}

    zabbixLoading.value = false;
    getData();
  } else {
    ElMessage.info("已取消监控安装!");
  }
}

// 跳转最新数据
function jumpLatest(row) {
  router.push({
    path: "/monitor_center/latest",
    query: {
      hostip: row.ip,
      zabbix_id: row.zabbix_id,
    },
  });
}
</script>
<template>
  <div>
    <page-main title="服务接口">
      <div class="flex justify-between">
        <el-space wrap>
          <el-button type="primary" @click="createInterface">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            新增
          </el-button>
          <el-button type="primary" @click="allInstallZabbix">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:coordinate" />
              </el-icon>
            </template>
            一键安装所有
          </el-button>

          <el-button type="danger" :disabled="!data.batch.selectionDataList.length" @click="batchDel">
            批量删除
          </el-button>
        </el-space>
        <el-space wrap>
          <div class="w-220px">
            <el-input
              v-model="data.search.searchName"
              placeholder="输入ip或名称,支持模糊查询"
              clearable
              @keyup.enter="queryData"
            />
          </div>
          <el-button type="primary" @click="queryData()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>

      <el-table
        border
        class="list-table"
        stripe
        highlight-current-row
        :data="data.dataList"
        @selection-change="data.batch.selectionDataList = $event"
      >
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column label="服务名称" prop="name" align="center" />
        <el-table-column label="服务器" prop="ip" align="center" />
        <el-table-column label="监控状态" align="center">
          <template #default="scoped">
            <el-tag v-if="scoped.row.status == 'online'">已启用</el-tag>
            <el-tag v-else-if="scoped.row.status == 'offmonitor'" type="info" effect="dark">未监控</el-tag>
            <el-tag v-else type="danger">停用的</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scoped">
            <el-button @click="editInterface(scoped.row)" plain type="primary">编辑</el-button>
            <MoreOperations>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-if="scoped.row.status == 'offmonitor'"
                  :icon="Location"
                  @click="installZabbix(scoped.row)"
                >
                  安装监控
                </el-dropdown-item>
                <el-dropdown-item
                  :icon="View"
                  @click="jumpLatest(scoped.row)"
                  v-if="scoped.row.status == 'online' || scoped.row.status == 'offline'"
                >
                  最新数据
                </el-dropdown-item>
                <el-dropdown-item :icon="Remove" @click="oneDel(scoped.row.zabbix_id)">删除接口</el-dropdown-item>
              </el-dropdown-menu>
            </MoreOperations>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>
    <ZabbixResult v-model="zabbixResultDialog" :stepData="zabbixResultData" :loading="zabbixLoading"></ZabbixResult>

    <el-drawer v-model="data.drawer" size="50%" :destroy-on-close="true" :close-on-click-modal="false">
      <template #header>
        <h4>{{ drawerTitle }}</h4>
      </template>
      <el-divider class="cancel_top" />
      <CreateInter v-model="data.router_id" @closeDialog="data.drawer = false" @getResource="getData()"></CreateInter>
    </el-drawer>
  </div>
</template>
<style scoped>
.cancel_top {
  margin-top: 0px;
}
</style>
