<script setup name="IframeLayout">
import { useRouter, useRoute } from "vue-router";
import { ref, onMounted, watchEffect, onBeforeMount, reactive } from "vue";
const iframe = ref({
  loading: true,
  src: "itoa/app/apm/services?comparisonEnabled=true&environment=ENVIRONMENT_ALL&rangeFrom=now-24h&rangeTo=now&offset=1d",
});
const timer = reactive({
  timeInter: null,
});
const apmIframe = ref();
onMounted(() => {
  timer.timeInter = setInterval(() => {
    cancel_css();
  }, 100);
  // setTimeout(() => {
  //   cancel_css();
  // }, 1000);
  getData();
});
onUnmounted(() => {
  clearInterval(timer.timeInter); //销毁
  timer.timeInter = null;
});
function getData() {
  apmIframe.value.onload = () => {
    iframe.value.loading = false;
  };
}
const cancel_css = () => {
  const iframeElement = document.getElementById("myIframe");
  const iframeContent = iframeElement.contentDocument || iframeElement.contentWindow.document;
  const targetElement = iframeContent.getElementsByClassName("header__bars")[0];
  if (targetElement) {
    targetElement.style.display = "none"; // 设置为display: none
  }
  // 待测试，由于kibana的8.1和8.2在模板方面class有变化，导致这里getElementsByClassName需要兼容，所以从上一级获取kbnPageTemplate，然后再获取div从而达到隐藏目的。目前发现kbnPageTemplate是两套模板里面共同拥有的class名称
  const kbnPageTemplate = iframeContent.getElementsByClassName("kbnPageTemplate")[0];
  if (kbnPageTemplate) {
    const firstDiv = kbnPageTemplate.getElementsByTagName("div")[0];
    console.log(kbnPageTemplate, firstDiv);
    firstDiv.style.display = "none";
  }
  // 8.11.1
  const headNavElement = iframeContent.getElementsByClassName("kbnSolutionNav__sidebar")[0];
  if (headNavElement) {
    headNavElement.style.display = "none"; // 设置为display: none
  }
  // 8.2
  const headNavElementPlus = iframeContent.getElementsByClassName("euiPageSideBar")[0];
  if (headNavElementPlus) {
    headNavElementPlus.style.display = "none"; // 设置为display: none
  }

  const headPadding = iframeContent.getElementsByClassName("euiBody--headerIsFixed")[0];
  if (headPadding) {
    headPadding.style.paddingTop = "0px"; // 设置为display: none
  }
};
</script>

<template>
  <div>
    <iframe ref="apmIframe" id="myIframe" frameborder="no" border="0" :src="iframe.src"></iframe>
  </div>
</template>

<style lang="scss" scoped>
.iframe,
iframe {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
}

.back-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}
</style>
