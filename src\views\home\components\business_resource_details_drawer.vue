<script setup>
// const router = useRouter()
import { onMounted, reactive } from "vue";
// const route = useRoute()
import Warning from "../../business_monitor/components/alarm/index.vue";
const emit = defineEmits(["update:modelValue"]);
let myVisible = computed({
  get: function () {
    return props.modelValue;
  },
  set: function (val) {
    emit("update:modelValue", val);
  },
});
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
  },
});
onMounted(() => {
  getData();
});
function getData() {

}
function closeDraw() {
  myVisible.value = false;
}
</script>

<template>
  <div>
    <el-drawer :title="props.item.name" v-model="props.modelValue" :destroy-on-close="true" size="50%"
      @closed="closeDraw">
      <div>
        <div class="vertical-line"></div>
        <span class="sub-title">资源列表</span>
        <div v-if="props.item.list.length !== 0">
          <el-table :data="props.item.list" style="width: 100%" height="260">
            <el-table-column fixed prop="name" label="名称" />
            <el-table-column label="类型" width="100"><template #default="scope"><el-tag type="primary">{{
              scope.row.type
            }}</el-tag></template></el-table-column>
            <el-table-column label="状态" width="100"><template #default="scope">
                <div style="display: flex;
  align-items: center;">
                  <div
                    :class="scope.row.status === 'success' ? 'status-circle' : scope.row.status === 'warning' ? 'warning-circle' : 'down-circle'">
                  </div>
                  <span class="descripe">{{ scope.row.status === 'success' ? '运行' : scope.row.status ===
                    'warning' ? '故障' :
                    scope.row.status === 'down' ? '停用' : scope.row.status }}</span>
                </div>
              </template></el-table-column>
          </el-table>
        </div>
        <el-empty :image-size="120" description="未添加资源" v-else />
      </div>
      <div>
        <div class="vertical-line"></div>
        <span class="sub-title" >告警列表</span>
        <el-scrollbar v-if="props.item.problemList.length !== 0">
          <div class="warning">
            <Warning :item="props.item.problemList" ></Warning>
          </div>
        </el-scrollbar>
        <el-empty :image-size="120" description="没有告警" v-else />
      </div>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
// scss
.vertical-line {
  border-left: 4px solid #337ecc;
  /* 2px 宽度的加粗直线，黑色 */
  height: 13px;
  /* 设置高度 */
  display: inline-block;
}

.sub-title {
  display: inline-block;
  /* 设置为内联块级元素 */
  margin-left: 5px;
  /* 添加一些左边距以分隔线和文本 */
  font-size: 14px;
  font-weight: bold;
}

:deep(.el-drawer__header) {
  margin-bottom: 0px;
}

.descripe {
  font-size: 14px;
  padding: 5px;
  color: #909399;
}

.scrollbar-demo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  text-align: center;
  border-bottom: 1px solid #C0C4CC;
}

.duration {
  font-size: 16px;
  font-weight: bold
}

.date-tag {
  margin-right: 10px;
}

.status-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #67C23A;
  margin-left: 10px;
  outline: 2px solid #d1edc4;
}

.warning-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #E6A23C;
  margin-left: 10px;
  outline: 2px solid #f3d19e;
}

.danger-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #F56C6C;
  margin-left: 10px;
  outline: 2px solid #fab6b6;
}

.down-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #909399;
  margin-left: 10px;
  outline: 2px solid #c8c9cc;
}

.warning {
  padding-top: 10px;
  overflow: auto;
}
</style>
