<script setup>
import api from "@/plugins/axios";
import G6 from "@antv/g6";
import { reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
const containerId = "graph-container"; // 添加一个具体的 ID
const container = ref(null);
const animateCfg = { duration: 200, easing: "easeCubic" };

const toolbar = new G6.ToolBar({
  position: { x: 0, y: 0 },
  getContent: () => `
  <ul class='g6-component-toolbar'>
    <li  code='zoomOut'>
      <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
        <path d="M658.432 428.736a33.216 33.216 0 0 1-33.152 33.152H525.824v99.456a33.216 33.216 0 0 1-66.304 0V461.888H360.064a33.152 33.152 0 0 1 0-66.304H459.52V296.128a33.152 33.152 0 0 1 66.304 0V395.52H625.28c18.24 0 33.152 14.848 33.152 33.152z m299.776 521.792a43.328 43.328 0 0 1-60.864-6.912l-189.248-220.992a362.368 362.368 0 0 1-215.36 70.848 364.8 364.8 0 1 1 364.8-364.736 363.072 363.072 0 0 1-86.912 235.968l192.384 224.64a43.392 43.392 0 0 1-4.8 61.184z m-465.536-223.36a298.816 298.816 0 0 0 298.432-298.432 298.816 298.816 0 0 0-298.432-298.432A298.816 298.816 0 0 0 194.24 428.8a298.816 298.816 0 0 0 298.432 298.432z"></path>
      </svg>
    </li>
    <li code='zoomIn'>
      <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
        <path d="M639.936 416a32 32 0 0 1-32 32h-256a32 32 0 0 1 0-64h256a32 32 0 0 1 32 32z m289.28 503.552a41.792 41.792 0 0 1-58.752-6.656l-182.656-213.248A349.76 349.76 0 0 1 480 768 352 352 0 1 1 832 416a350.4 350.4 0 0 1-83.84 227.712l185.664 216.768a41.856 41.856 0 0 1-4.608 59.072zM479.936 704c158.784 0 288-129.216 288-288S638.72 128 479.936 128a288.32 288.32 0 0 0-288 288c0 158.784 129.216 288 288 288z" p-id="3853"></path>
      </svg>
    </li>
    <li code='realZoom'>
      <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="24">
        <path d="M384 320v384H320V320h64z m256 0v384H576V320h64zM512 576v64H448V576h64z m0-192v64H448V384h64z m355.968 576H92.032A28.16 28.16 0 0 1 64 931.968V28.032C64 12.608 76.608 0 95.168 0h610.368L896 192v739.968a28.16 28.16 0 0 1-28.032 28.032zM704 64v128h128l-128-128z m128 192h-190.464V64H128v832h704V256z"></path>
      </svg>
    </li>
    <li code='autoZoom'>
      <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="24">
        <path d="M684.288 305.28l0.128-0.64-0.128-0.64V99.712c0-19.84 15.552-35.904 34.496-35.712a35.072 35.072 0 0 1 34.56 35.776v171.008h170.944c19.648 0 35.84 15.488 35.712 34.432a35.072 35.072 0 0 1-35.84 34.496h-204.16l-0.64-0.128a32.768 32.768 0 0 1-20.864-7.552c-1.344-1.024-2.816-1.664-3.968-2.816-0.384-0.32-0.512-0.768-0.832-1.088a33.472 33.472 0 0 1-9.408-22.848zM305.28 64a35.072 35.072 0 0 0-34.56 35.776v171.008H99.776A35.072 35.072 0 0 0 64 305.216c0 18.944 15.872 34.496 35.84 34.496h204.16l0.64-0.128a32.896 32.896 0 0 0 20.864-7.552c1.344-1.024 2.816-1.664 3.904-2.816 0.384-0.32 0.512-0.768 0.768-1.088a33.024 33.024 0 0 0 9.536-22.848l-0.128-0.64 0.128-0.704V99.712A35.008 35.008 0 0 0 305.216 64z m618.944 620.288h-204.16l-0.64 0.128-0.512-0.128c-7.808 0-14.72 3.2-20.48 7.68-1.28 1.024-2.752 1.664-3.84 2.752-0.384 0.32-0.512 0.768-0.832 1.088a33.664 33.664 0 0 0-9.408 22.912l0.128 0.64-0.128 0.704v204.288c0 19.712 15.552 35.904 34.496 35.712a35.072 35.072 0 0 0 34.56-35.776V753.28h170.944c19.648 0 35.84-15.488 35.712-34.432a35.072 35.072 0 0 0-35.84-34.496z m-593.92 11.52c-0.256-0.32-0.384-0.768-0.768-1.088-1.088-1.088-2.56-1.728-3.84-2.688a33.088 33.088 0 0 0-20.48-7.68l-0.512 0.064-0.64-0.128H99.84a35.072 35.072 0 0 0-35.84 34.496 35.072 35.072 0 0 0 35.712 34.432H270.72v171.008c0 19.84 15.552 35.84 34.56 35.776a35.008 35.008 0 0 0 34.432-35.712V720l-0.128-0.64 0.128-0.704a33.344 33.344 0 0 0-9.472-22.848zM512 374.144a137.92 137.92 0 1 0 0.128 275.84A137.92 137.92 0 0 0 512 374.08z"></path>
      </svg>
    </li>
  </ul>`,
  handleClick: (code, graph) => {
    switch (code) {
      case "zoomOut":
        graph.zoom(1.2, undefined, true, animateCfg);
        break;
      case "zoomIn":
        graph.zoom(0.8, undefined, true, animateCfg);
        break;
      case "realZoom":
        graph.zoomTo(1, undefined, true, animateCfg);
        break;
      case "autoZoom":
        graph.fitView(20, undefined, true, animateCfg);
        break;
    }
  },
});
var graph;
G6.registerNode(
  "card-node",
  {
    drawShape: function drawShape(cfg, group) {
      const color = cfg.error ? "#F4664A" : "#30BF78";
      const typeColor = cfg.typeColor == "subscriber" ? "#87CEFA" : cfg.typeColor == "provider" ? "#FFD700" : "#000000";
      const r = 2;
      const shape = group.addShape("rect", {
        attrs: {
          x: 0,
          y: 0,
          width: 220,
          height: 60,
          stroke: color,
          radius: r,
        },
        name: "main-box",
        draggable: true,
      });
      group.addShape("rect", {
        attrs: {
          x: 0,
          y: 0,
          width: 220,
          height: 20,
          fill: color,
          radius: [r, r, 0, 0],
        },
        name: "title-box",
        draggable: true,
      });
      group.addShape("rect", {
        attrs: {
          x: 0,
          y: 20,
          width: 5,
          height: 40,
          fill: typeColor,
          radius: 1.5,
        },
        name: "left-border-shape",
      });
      // title text
      group.addShape("text", {
        attrs: {
          textBaseline: "top",
          y: 4,
          x: 8,
          lineHeight: 20,
          text: cfg.title,
          fill: "#fff",
        },
        name: "title",
      });
      cfg.panels.forEach((item, index) => {
        // name text
        group.addShape("text", {
          attrs: {
            textBaseline: "top",
            y: 25,
            x: 8 + index * 50,
            lineHeight: 20,
            text: item.title,
            fill: "rgba(0,0,0, 0.4)",
          },
          name: `index-title-${index}`,
        });
        group.addShape("text", {
          attrs: {
            textBaseline: "top",
            y: 42,
            x: 8 + index * 50,
            lineHeight: 20,
            text: item.value,
            fill: "#595959",
          },
          name: `index-value-${index}`,
        });
      });
      return shape;
    },
  },
  "single-node"
);
G6.registerEdge(
  "circle-running",
  {
    afterDraw(cfg, group) {
      // get the first shape in the group, it is the edge's path here=
      const shape = group.get("children")[0];
      // the start position of the edge's path
      const startPoint = shape.getPoint(0);

      // add red circle shape
      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint.x,
          y: startPoint.y,
          fill: "#1890ff",
          r: 6,
        },
        // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
        name: "circle-shape",
      });

      // animation for the red circle
      circle.animate(
        (ratio) => {
          // the operations in each frame. Ratio ranges from 0 to 1 indicating the prograss of the animation. Returns the modified configurations
          // get the position on the edge according to the ratio
          const tmpPoint = shape.getPoint(ratio);
          // returns the modified configurations here, x and y here
          return {
            x: tmpPoint.x,
            y: tmpPoint.y,
          };
        },
        {
          repeat: true, // Whether executes the animation repeatly
          duration: 3000, // the duration for executing once
        }
      );
    },
  },
  "cubic" // extend the built-in edge 'cubic'
);
G6.registerEdge(
  "warning-running",
  {
    afterDraw(cfg, group) {
      // get the first shape in the group, it is the edge's path here=
      const shape = group.get("children")[0];
      // the start position of the edge's path
      const startPoint = shape.getPoint(0);

      // add red circle shape
      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint.x,
          y: startPoint.y,
          fill: "#E6A23C",
          r: 6,
        },
        // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
        name: "circle-shape",
      });

      // animation for the red circle
      circle.animate(
        (ratio) => {
          // the operations in each frame. Ratio ranges from 0 to 1 indicating the prograss of the animation. Returns the modified configurations
          // get the position on the edge according to the ratio
          const tmpPoint = shape.getPoint(ratio);
          // returns the modified configurations here, x and y here
          return {
            x: tmpPoint.x,
            y: tmpPoint.y,
          };
        },
        {
          repeat: true, // Whether executes the animation repeatly
          duration: 3000, // the duration for executing once
        }
      );
    },
  },
  "cubic" // extend the built-in edge 'cubic'
);
// 注册自定义节点
// G6.registerNode("donut-node", {
//   draw(cfg, group) {
//     const radius = cfg.size / 1.5; // 圆的半径
//     const centerX = 0; // 圆心X坐标
//     const centerY = 0; // 圆心Y坐标
//     const percentage = cfg.percentage; // 百分比

//     // 计算角度
//     const startAngle = -Math.PI / 2; // 从12点钟方向开始
//         const endAngle = startAngle + (percentage / 100) * (Math.PI * 2);
//     // 创建圆形路径
//     const path = [];
//     const angleStep = Math.PI / 90; // 每次增加的角度步长
//     for (let angle = 0; angle < Math.PI * 2; angle += angleStep) {
//       const x = centerX + Math.cos(angle) * radius;
//       const y = centerY + Math.sin(angle) * radius;
//       if (angle === 0) {
//         path.push(['M', x, y]); // 移动到起始点
//       } else {
//         path.push(['L', x, y]); // 画线到下一个点
//       }
//     }
//     path.push(['Z']); // 封闭路径
//     const path2 = [
//       ['M', centerX, centerY],
//       ['L', centerX + Math.cos(startAngle) * radius, centerY + Math.sin(startAngle) * radius],
//       ['A', radius, radius, 0, percentage > 50 ? 1 : 0, 1, centerX + Math.cos(endAngle) * radius, centerY + Math.sin(endAngle) * radius],
//       ['Z']
//     ];
//     // 创建外部圆
//     group.addShape('path', {
//       attrs: {
//         path,
//         fill: '#d1edc4', // 超过50%为红色，否则为绿色
//         stroke:'#d1edc4',
//         lineWidth: 10, // 可根据需要调整线宽
//       }
//     });

//     group.addShape('path', {
//       attrs: {
//         path: path2,
//         fill: '#E6A23C', // 剩下部分的颜色
//         lineWidth: 1,
//       }
//     });
//      group.addShape("circle", {
//       attrs: {
//         x: 0,
//         y: 0,
//         r: cfg.size / 1.7, // 内部圆的半径，可以根据需要调整
//         fill: "#fff",
//       }
//     });
//      group.addShape("text", {
//       attrs: {
//         x: 0,
//         y: 0,
//         textAlign: "center",
//         textBaseline: "middle",
//         text: cfg.label,
//         fill: "#000",
//         fontSize: 20,
//         fontWeight:'bold',
//       }
//     });
//     // 创建节点文本
//     group.addShape("text", {
//       attrs: {
//         x: 0,
//         y: 20, // 调整Y坐标以控制描述文本的位置
//         textAlign: "center",
//         textBaseline: "middle",
//         text: '服务：'+cfg.service,
//         fill: "#666"
//       }
//     });

//     return group;
//   }
// });
// G6.registerNode("service-node", {
//   draw(cfg, group) {
//     const radius = cfg.size / 1.5; // 圆的半径
//     const centerX = 0; // 圆心X坐标
//     const centerY = 0; // 圆心Y坐标
//     const percentage = cfg.percentage; // 百分比

//     // 计算角度
//     const startAngle = -Math.PI / 2; // 从12点钟方向开始
//         const endAngle = startAngle + (percentage / 100) * (Math.PI * 2);
//     // 创建圆形路径
//     const path = [];
//     const angleStep = Math.PI / 90; // 每次增加的角度步长
//     for (let angle = 0; angle < Math.PI * 2; angle += angleStep) {
//       const x = centerX + Math.cos(angle) * radius;
//       const y = centerY + Math.sin(angle) * radius;
//       if (angle === 0) {
//         path.push(['M', x, y]); // 移动到起始点
//       } else {
//         path.push(['L', x, y]); // 画线到下一个点
//       }
//     }
//     path.push(['Z']); // 封闭路径
//     const path2 = [
//       ['M', centerX, centerY],
//       ['L', centerX + Math.cos(startAngle) * radius, centerY + Math.sin(startAngle) * radius],
//       ['A', radius, radius, 0, percentage > 50 ? 1 : 0, 1, centerX + Math.cos(endAngle) * radius, centerY + Math.sin(endAngle) * radius],
//       ['Z']
//     ];
//     // 创建外部圆
//     group.addShape('path', {
//       attrs: {
//         path,
//         fill: '#337ecc', // 超过50%为红色，否则为绿色
//         stroke:'#c6e2ff',
//         lineWidth: 10, // 可根据需要调整线宽
//       }
//     });

//     group.addShape('path', {
//       attrs: {
//         path: path2,
//         fill: '#E6A23C', // 剩下部分的颜色
//         lineWidth: 1,
//       }
//     });
//      group.addShape("circle", {
//       attrs: {
//         x: 0,
//         y: 0,
//         r: cfg.size / 1.7, // 内部圆的半径，可以根据需要调整
//         fill: "#fff",
//       }
//     });
//      group.addShape("text", {
//       attrs: {
//         x: 0,
//         y: 0,
//         textAlign: "center",
//         textBaseline: "middle",
//         text: cfg.label,
//         fill: "#000",
//         fontSize: 20,
//         fontWeight:'bold',
//       }
//     });
//     // 创建节点文本
//     group.addShape("text", {
//       attrs: {
//         x: 0,
//         y: 20, // 调整Y坐标以控制描述文本的位置
//         textAlign: "center",
//         textBaseline: "middle",
//         text: '服务：'+cfg.service,
//         fill: "#666"
//       }
//     });

//     return group;
//   }
// });

G6.registerNode("circle-node", {
  draw(cfg, group) {
    const radius = cfg.size / 1.5; // 圆的半径
    const centerX = 0; // 圆心X坐标
    const centerY = 0; // 圆心Y坐标
    const percentage = cfg.percentage; // 百分比

    // 计算角度
    const startAngle = -Math.PI / 2; // 从12点钟方向开始
    const endAngle = startAngle + (percentage / 100) * (Math.PI * 2);
    // 创建圆形路径
    const path = [];
    const angleStep = Math.PI / 90; // 每次增加的角度步长
    for (let angle = 0; angle < Math.PI * 2; angle += angleStep) {
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      if (angle === 0) {
        path.push(["M", x, y]); // 移动到起始点
      } else {
        path.push(["L", x, y]); // 画线到下一个点
      }
    }
    path.push(["Z"]); // 封闭路径
    const path2 = [
      ["M", centerX, centerY],
      ["L", centerX + Math.cos(startAngle) * radius, centerY + Math.sin(startAngle) * radius],
      [
        "A",
        radius,
        radius,
        0,
        percentage > 50 ? 1 : 0,
        1,
        centerX + Math.cos(endAngle) * radius,
        centerY + Math.sin(endAngle) * radius,
      ],
      ["Z"],
    ];
    // 创建圆形的两个部分，分别填充不同颜色
    group.addShape("path", {
      attrs: {
        path,
        fill: "#337ecc",
        stroke: "#c6e2ff",
        lineWidth: 10, // 可根据需要调整线宽
      },
    });

    group.addShape("path", {
      attrs: {
        path: path2,
        fill: "#E6A23C", // 剩下部分的颜色
      },
    });

    // 创建内部圆
    group.addShape("circle", {
      attrs: {
        x: 0,
        y: 0,
        r: cfg.size / 1.7, // 内部圆的半径，可以根据需要调整
        fill: "#fff",
      },
    });

    group.addShape("text", {
      attrs: {
        x: 0,
        y: -20,
        textAlign: "center",
        textBaseline: "middle",
        text: cfg.label,
        fontSize: 25,
        fontWeight: "bold",
        fill: "#000",
      },
    });

    // 创建节点文本
    group.addShape("text", {
      attrs: {
        x: 0,
        y: 30, // 调整Y坐标以控制描述文本的位置
        textAlign: "center",
        textBaseline: "middle",
        text: "服务：" + cfg.service,
        fontSize: 12,
        fill: "#666",
      },
    });

    group.addShape("text", {
      attrs: {
        x: 0,
        y: 10, // 调整Y坐标以控制描述文本的位置
        textAlign: "center",
        textBaseline: "middle",
        text: "平均耗时：" + cfg.time,
        fontSize: 12,
        fill: "#666",
      },
    });

    // 创建矩形标记
    group.addShape("rect", {
      attrs: {
        x: -cfg.size / 2 + 45,
        y: cfg.size / 2 - 30, // 根据需要调整位置
        width: cfg.size / 2.5,
        height: 30, // 根据需要调整大小
        fill: "#f3d19e", // 黄色
        stroke: "#000",
        radius: [4, 4],
      },
    });

    // 在矩形内添加内容
    group.addShape("text", {
      attrs: {
        x: 0,
        y: cfg.size / 2 - 15, // 根据需要调整位置
        textAlign: "center",
        textBaseline: "middle",
        text: "告警：" + cfg.problem,
        fontSize: 12,
        fill: "#000",
      },
    });
    return group;
  },
});

const tooltip = new G6.Tooltip({
  offsetX: 10,
  offsetY: 10,
  fixToNode: [1, 0.5],
  // the types of items that allow the tooltip show up
  // 允许出现 tooltip 的 item 类型
  itemTypes: ["node", "edge"],
  // custom the tooltip's content
  // 自定义 tooltip 内容
  getContent: (e) => {
    const outDiv = document.createElement("div");
    outDiv.style.width = "fit-content";
    outDiv.style.height = "fit-content";
    const model = e.item.getModel();
    if (e.item.getType() === "node") {
      let relative = model.relative.join(",");
      outDiv.innerHTML = `服务名：${model.title}<br/>关联服务：${relative}<br/>告警数量：${model.problem_level}<br/>健康度：${model.health_level}<br/>吞吐量：${model.throughput}<br/>服务数：${model.service}`;
    } else {
      outDiv.innerHTML = `耗时：${e.item["_cfg"].model.label}`;
    }
    return outDiv;
  },
});
onMounted(() => {
  // ... 其他代码 ...
  getData();
});
const typeConfigs = {
  type1: {
    type: "circle",
    size: 20,
    style: {
      fill: "#FFD700",
    },
  },
  type2: {
    type: "circle",
    size: 20,
    style: {
      fill: "#87CEFA",
    },
  },
};
const legendData = {
  nodes: [
    {
      id: "type1",
      label: "服务提供者",
      ...typeConfigs["type1"],
    },
    {
      id: "2",
      label: "服务订阅者",
      legendType: "type2",
      ...typeConfigs["type2"],
    },
  ],
};

const legend = new G6.Legend({
  data: legendData,
  align: "center",
  layout: "horizontal", // vertical
  position: "top",
  margin: [30, 0, 0, 0],
  vertiSep: 12,
  horiSep: 24,
  offsetY: -24,
  padding: [10, 16, 8, 16],
  containerStyle: {
    fill: "#fff",
    stroke: null,
  },
});

function getData() {
  api.get("business_monitor/topology", { baseURL: "/mock/" }).then((res) => {
    const nodes = {};
    const edges = [];
    res.data.business_list.forEach((element, index) => {
      element["id"] = (index + 1).toString();
      const isSubscriber = element.type === "subscriber";
      const source = isSubscriber ? "0" : element.id;
      const target = isSubscriber ? element.id : "0";
      //   const type = isSubscriber ? 'service-node' : 'donut-node';
      const type = "card-node";
      //   const node = {
      //     id: element.id,
      //     size: 70,
      //     type: type,
      //     label: element.name,
      //     service: element.service,
      //     percentage: element.problem_level,
      //     relative: element.relative
      //   };
      const node = {
        id: element.id,
        title: element.name,
        error: !element.status,
        type: type,
        typeColor: element.type,
        health_level: element.health_level,
        problem_level: element.problem_level,
        throughput: element.throughput,
        service: element.service,
        panels: [
          { title: "健康度", value: element.health_level },
          { title: "服务数", value: element.service },
          { title: "错误数", value: element.problem_level },
          { title: "吞吐量", value: element.throughput },
        ],
        relative: element.relative,
      };
      const edge = {
        source: source,
        target: target,
        style: { endArrow: true },
        type: element.status ? "circle-running" : "warning-running",
        label: element.time_consuming,
      };

      nodes[element.id] = node;
      edges.push(edge);
    });
    data.nodes = Object.values(nodes);
    data.nodes.unshift({
      id: "0",
      size: 150,
      type: "circle-node",
      label: res.data.center.name,
      service: res.data.center.service,
      problem: res.data.center.problem,
      time: res.data.center.time,
      percentage: res.data.center.problem_level,
      relative: [],
    });
    data.edges = edges;
    container.value = document.getElementById(containerId);
    const el = document.createElement("pre");
    container.value.appendChild(el);
    graph = new G6.Graph({
      container: container.value,
      width: container.value.scrollWidth,
      height: 780,
      plugins: [toolbar, tooltip, legend, menu],
      layout: {
        type: "dagre",
        rankdir: "LR",
        nodesep: 10,
        ranksep: 120,
      },
      defaultNode: {
        labelCfg: {
          position: "center",
          offset: 10,
          style: {
            // fill: '#666',
          },
        },
      },
      defaultEdge: {
        style: {
          endArrow: true,
          stroke: "#00CED1",
          lineWidth: 2,
        },
        labelCfg: {
          refY: 15,
          refX: 20,
        },
      },
      modes: {
        default: ["drag-canvas", "zoom-canvas", "drag-node", "activate-relations", "click-select"],
      },

      // ... 其他配置 ...
    });
    graph.data({
      nodes: data.nodes,
      edges: data.edges.map(function (edge, i) {
        edge.id = "edge" + i;
        return Object.assign({}, edge);
      }),
    });
    graph.render();
    graph.on("node:dblclick", (ev) => {
      const clickedNode = ev.item;
      const clickedNodeId = clickedNode.get("id");
      let source = "";
      let target = "";
      // 隐藏与点击节点有关联的节点
      graph.getEdges().forEach((edge) => {
        const clicksource = edge.getSource().get("id");
        const clicktarget = edge.getTarget().get("id");
        if (clickedNodeId !== "0") {
          if (clicksource === clickedNodeId || clicktarget === clickedNodeId) {
            source = clicksource;
            target = clicktarget;
            edge.show();
          } else {
            edge.hide();
          }
        }
      });

      // 隐藏其他节点
      if (clickedNodeId !== "0") {
        graph.getNodes().forEach((node) => {
          if (node.get("id") !== clickedNodeId) {
            node.hide();
          } else {
            node["_cfg"].model.relative.forEach((element) => {
              graph.getEdges().forEach((edge) => {
                console.log(edge);
                if (edge["_cfg"].targetNode["_cfg"].model.title == element) {
                  edge.show();
                  edge["_cfg"].targetNode.show();
                }
              });
            });
          }
          if (node._cfg.model.id == source) {
            node.show();
          }
          if (node._cfg.model.id == target) {
            node.show();
          }
        });
      }
      if (clickedNodeId !== "0") {
        graph.getNodes().forEach((node) => {
          if (node.get("id") == clickedNodeId) {
            node["_cfg"].model.relative.forEach((element) => {
              graph.getEdges().forEach((edge) => {
                if (edge["_cfg"].sourceNode["_cfg"].model.title == element) {
                  edge.show();
                  edge["_cfg"].source.show();
                  edge["_cfg"].sourceNode.show();
                }
              });
            });
          }
        });
      }
    });
    graph.on("canvas:click", (ev) => {
      const nodes = graph.getNodes();
      const edges = graph.getEdges();
      nodes.forEach((node) => {
        node.show();
      });
      edges.forEach((edge) => {
        edge.show();
      });
    });
  });
}
const menu = new G6.Menu({
  offsetX: 6,
  offsetY: 10, // 将 offsetX 改为 offsetY，用于设置垂直偏移量
  itemTypes: ["node"],
  getContent(e) {
    if (e.item._cfg.id != "0") {
      const outDiv = document.createElement("div");
      outDiv.style.width = "100px";
      const ul = document.createElement("ul");
      ul.style.listStyle = "none";
      ul.style.padding = "0";
      ul.style.margin = "0";

      const li1 = document.createElement("li");
      li1.textContent = "查看详情";
      li1.style.padding = "8px 16px";
      li1.style.cursor = "pointer";
      ul.appendChild(li1);

      // const li2 = document.createElement('li'); // 创建第二个 li 元素
      // li2.textContent = ''; // 设置第二个 li 元素的文本内容
      // li2.style.padding = '8px 16px';
      // li2.style.cursor = 'pointer';
      // ul.appendChild(li2); // 将第二个 li 元素添加到 ul 中

      outDiv.appendChild(ul);
      return outDiv;
      // const outDiv = document.createElement('div');
      // outDiv.style.width = '100px';
      // const ul = document.createElement('ul');
      // ul.style.listStyle = 'none';
      // ul.style.padding = '0';
      // ul.style.margin = '0';
      // const li1 = document.createElement('li');
      // li1.textContent = '查看数据';
      // li1.style.padding = '8px 16px';
      // li1.style.cursor = 'pointer';
      // ul.appendChild(li1);
      // outDiv.appendChild(ul);
      // return outDiv;
    }
    // else{

    // }
  },
  handleMenuClick(target, item) {
    const liText = target.textContent; // 获取点击的 li 元素的文本内容
    if (liText === "查看详情") {
      router.push({
        name: "WarningMonitior",
        query: { item: item["_cfg"].model.title, name: "topology" },
      });
    }
    //  else if (liText === '查看数据') {
    //   router.push({
    //     path: '/business_monitor/CenterPlatform',
    //     query: {  }
    //   });
    // }
  },
});
const data = reactive({
  nodes: [],
  edges: [],
});
const nodes = data.nodes;
onBeforeUnmount(() => {
  graph.destroy();
});
</script>

<template>
  <page-main>
    <div class="iframe">
      <div style="background-color: white" class="w-full" ref="myDiv" id="graph-container"></div>
    </div>
  </page-main>
</template>

<style lang="scss" scoped>
// scss
</style>
