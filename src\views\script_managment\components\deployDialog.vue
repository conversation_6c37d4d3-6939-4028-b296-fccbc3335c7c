<script setup>
import { onMounted, reactive } from "vue";
const emit = defineEmits(["update:modelValue", "submitTask"]);
let item = computed({
  get: function () {
    return props.item;
  }
});
const props = defineProps({
  modelValue: {
    default: false,
    type: Boolean
  },
  item: {
    default: {},
    type: Object,
  },
});
const data = reactive({
  taskDialog: false,
  isDeploy: 1,
  form: {
    deploy_path: "",
    cron_time: "",
  }
});
let Visible = computed({
  get: function () {
    return props.modelValue;
  },
  set: function (val) {
    emit("update:modelValue", val);
  },
});
function submit() {
  if (data.isDeploy === 1) {
    data.taskDialog = true
  } else if (data.isDeploy === 2) {
    let params = {
      deploy_path: data.form.deploy_path,
      cron_time: data.form.cron_time,
      file_name: item.value.file_name,
      deploy_type: 1
    }
    Visible.value = false;
    emit("submitTask", params);
  }
}
function submitTask() {
  let params = {
    deploy_path: data.form.deploy_path,
    cron_time: data.form.cron_time,
    file_name: item.value.file_name,
    deploy_type: 0
  }
  emit("submitTask", params);
  data.taskDialog = false
  Visible.value = false;
  // 重置表单
  data.form = {
    deploy_path: "",
    cron_time: "",
  };
}
onMounted(() => {
  data.form.deploy_path = item.value.deploy_path
  data.form.cron_time = item.value.cron_time
})

</script>

<template>
  <div>
    <el-dialog v-model="Visible" title="部署" width="400px" :destroy-on-close="true" align-center>
      <div style="display: flex; justify-content: center; align-items: center; height: 100px;">
        <el-radio-group v-model="data.isDeploy">
          <el-radio :value="1" size="large">定时任务</el-radio>
          <el-radio :value="2" size="large">非定时任务</el-radio>
        </el-radio-group>
      </div>
      <el-dialog width="500px" v-model="data.taskDialog" title="定时任务" append-to-body :destroy-on-close="true">
        <el-form :label-position="labelPosition" label-width="auto" :model="data.form" style="max-width: 480px">
          <el-form-item label="文件路径" :label-position="itemLabelPosition">
            <el-input v-model="data.form.deploy_path" placeholder="例如：/usr/local" />
          </el-form-item>
          <el-form-item label="定时任务时间" :label-position="itemLabelPosition">
            <el-input v-model="data.form.cron_time" placeholder="例如：0 1 * * 6" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer" style="display: flex; justify-content: center; gap: 10px;">
            <el-button @click="data.taskDialog = false">取消</el-button>
            <el-button type="primary" @click="submitTask">
              确定
            </el-button>
          </div>
        </template>
      </el-dialog>
      <template #footer>
        <div class="dialog-footer" style="display: flex; justify-content: center; gap: 10px;">
          <el-button @click="Visible = false">取消</el-button>
          <el-button type="primary" @click="submit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.el-dialog .el-dialog__body {
  display: flex;
  jusify-content: center;
  align-items: center;
}
</style>
