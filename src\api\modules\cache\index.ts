import api from "@/plugins/axios";

/**
 * 业务监控
 */
export function monitorOverview() {
  return api.get("/cache/get_cached_results?key=get_business_resource_information");
}

/**
 * 主页告警zabbix
 * historical_alarm_information
 * historical_has_been_cleared_alarm_information
 */
export enum alarmInfoEnum {
  unresolved = "historical_alarm_information", //未解决
  resolved = "historical_has_been_cleared_alarm_information", //已解决
}
export function ZabbixWarning(type) {
  //zabbix告警
  return api.get("/cache/get_cached_results?key=" + type);
}

/**
 * 获取zabbix 预警列表  未解决
 * @returns
 */
export function getZabbixHistoricalAlarmInformation() {
  return api.get("/zabbix/historical_alarm_information/");
}

/**
 * 获取zabbix 预警列表  已解决
 * @returns
 */
export function getZabbixHistoricalCloseAlarmInformationn() {
  return api.get("/zabbix/historical_close_alarm_information/");
}
