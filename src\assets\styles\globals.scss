@use "./element-plus/index.scss";
@use "./element-plus/override.scss";

// 页面布局 CSS 变量
:root {
  // 这是一个复合变量
  // 当页宽模式为 adaption-min-width 时，它代表 最小宽度
  // 当页宽模式为 center 时，它代表 固定宽度
  // 当页宽模式为 center-max-width 时，它代表 最大宽度
  --g-app-width: #{$g-app-width};
  // 头部宽度（默认自适应宽度，可固定宽度，固定宽度后为居中显示）
  --g-header-width: #{$g-header-width};
  // 头部高度
  --g-header-height: 80px;
  // 侧边栏宽度
  --g-main-sidebar-width: 90px;
  --g-sub-sidebar-width: 220px;
  --g-sub-sidebar-collapse-width: 64px;
  // 侧边栏 Logo 区域高度
  --g-sidebar-logo-height: 50px;
  // 标签栏高度
  --g-tabbar-height: 40px;
  // 工具栏高度
  --g-toolbar-height: 50px;
}
// 明暗模式 CSS 变量
/* stylelint-disable-next-line no-duplicate-selectors */
:root {
  &::view-transition-old(*) {
    animation: none;
    mix-blend-mode: normal;
  }
  &::view-transition-new(*) {
    mix-blend-mode: normal;
    animation: clip 0.5s ease-in;
  }
  @keyframes clip {
    from {
      clip-path: circle(0% at var(--x) var(--y));
    }
    to {
      clip-path: circle(100% at var(--x) var(--y));
    }
  }

  --g-box-shadow-color: rgb(0 0 0 / 12%);
  &.dark {
    --g-box-shadow-color: rgb(0 0 0 / 72%);
  }
  @include set-themes-css-var();
}
// 全局样式
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}
::-webkit-scrollbar-thumb {
  background-color: rgb(0 0 0 / 40%);
  background-clip: padding-box;
  border: 3px solid transparent;
  border-radius: 6px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgb(0 0 0 / 50%);
}
::-webkit-scrollbar-track {
  background-color: transparent;
}
html,
body {
  height: 100%;
}
body {
  margin: 0;
  background-color: var(--g-app-bg);
  transition: background-color 0.3s;
  box-sizing: border-box;
  font-family: Lato, "PingFang SC", "Microsoft YaHei", sans-serif;
  -webkit-tap-highlight-color: transparent;
  &.overflow-hidden {
    overflow: hidden;
  }
}
* {
  box-sizing: inherit;
}
// 右侧内容区针对fixed元素，有横向铺满的需求，可在fixed元素上设置 [data-fixed-calc-width]
[data-fixed-calc-width] {
  position: fixed;
  left: 50%;
  right: 0;
}
[data-app-width-mode="adaption"],
[data-app-width-mode="adaption-min-width"] {
  [data-fixed-calc-width] {
    width: calc(100% - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
    transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2))
      translateX(calc(var(--g-sub-sidebar-actual-width) / 2));
  }
}
[data-app-width-mode="center"],
[data-app-width-mode="center-max-width"] {
  [data-fixed-calc-width] {
    width: calc(var(--g-app-width) - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
    transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2))
      translateX(calc(var(--g-sub-sidebar-actual-width) / 2));
  }
  @media screen and (max-width: $g-app-width) {
    [data-fixed-calc-width] {
      width: calc(100% - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
      transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2))
        translateX(calc(var(--g-sub-sidebar-actual-width) / 2));
    }
  }
}
[data-mode="mobile"] {
  [data-fixed-calc-width] {
    width: 100% !important;
    transform: translateX(-50%) !important;
  }
}
// textarea 字体跟随系统
textarea {
  font-family: inherit;
}
.paginationTable {
  margin-top: 10px;
}

.el-form-item {
  width: 100%;
}

.el-form-item__content {
  width: 100%;
}
.el-select,
.el-input_inner {
  width: 100%;
}

// 全局样式

// 当屏幕宽度小于1600px时应用的样式
@media screen and (max-width: 1499px) {
  .left-box {
    width: 0 !important;
  }
  .tools .oragnName {
    position: unset !important;
    h3 {
      width: auto !important;
      text-align: left !important;
    }
  }
}

.el-drawer__header {
  margin-bottom: 0;
}
