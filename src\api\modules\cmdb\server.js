import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取所有展示服务器
 * @returns
 */
export function cmdbGetAllServerList() {
  //获取所有展示服务器
  return api.get("/server/list/get_all_server/");
}

/**
 * 根据server_id获取服务器详情信息
 * @returns
 */
export function cmdbGetServerMessageById(id) {
  return api.get("/server/get_server_info/" + "?server_id=" + id);
}

/**
 * 根据server_id和zabbix_id删除服务器信息
 */
export function cmdbBatchDelServerMessage(data) {
  //批量删除服务器
  return api.post("/server/delete_cmdb_server/", data);
}

/**
 * 添加服务器信息
 * @param {*} data
 * @returns
 */
export function cmdbAddServerMessage(data) {
  //添加服务器
  return api.post("/server/server_create/", JSON.stringify(data));
}

/**
 * 修改服务器信息
 * @param {*} data
 * @returns
 */
export function cmdbUpdateServerMessage(data) {
  //修改服务器
  return api.post("/server/update_cmdb_server/", JSON.stringify(data));
}

/**
 * 批量导入服务器模板（excel文件）
 * @param {*} data
 * @returns
 */
export function cmdbBatchImportServerMessage(data) {
  //批量导入服务器
  return api.post("/server/batch_import_server_resource/", JSON.stringify(data));
}

/**
 * 获取所有服务器名称信息
 * @returns
 */
export function getServerNameList() {
  return api.get("/server/server_name_list/");
}

/**
 * 手动更新数据
 * @param {*} data
 * @returns
 */
export function cmdbUpdateServerAsset(data) {
  return api.post("/server/list/hand_update_server_cmdb/", JSON.stringify(data));
}

/**
 * 根据server_id获取服务器详情信息
 * @returns
 */
export function cmdbGetServerAutoCmddbById(data) {
  return api.get("/server/auto_cmdb/query_details_auto_cmdb/" + "?ip=" + data.ip);
}

/**
 * 批量导入更新服务器信息
 * @param {*} data 
 */
export function batchImportServerInfoToUpdate(data){
  return api.post("/server/batch_modify_server_under_business/",JSON.stringfy(data))
}
