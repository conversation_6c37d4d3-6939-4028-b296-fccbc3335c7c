<script setup>
import { AnsiUp } from "ansi_up";

const props = defineProps({
  content: {
    type: Object,
  },
});

const data = ref({
  loading: false,
  logDetails: "",
});

onMounted(() => {});
watch(
  () => props.content,
  (newValue, oldValue) => {
    const ansi_up = new AnsiUp();
    data.value.logDetails = "";
    data.value.logDetails = ansi_up.ansi_to_html(props.content);
  },
  {
    immediate: true,
  }
);
</script>

<template>
  <div v-loading="data.loading">
    <page-main style="margin: 10px 0">
      <li>
        <span>{{ "执行结果" }}</span>
        <br />
        <!-- <Viewer class="break-all whitespace-pre-wrap" :value="'```sh\n' + props.content + '```'" :plugins="plugins" /> -->
        <div class="break-all whitespace-pre-wrap" v-html="data.logDetails"></div>
      </li>
    </page-main>
  </div>
</template>
