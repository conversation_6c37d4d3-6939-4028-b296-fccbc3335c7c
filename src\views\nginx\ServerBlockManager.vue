// @ts-nocheck
<script setup lang="ts">
import { ref, reactive, onMounted, watch, defineProps, defineEmits, defineExpose, computed, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Edit, Refresh, Delete } from '@element-plus/icons-vue'
// 导入AddBusinessDialog组件
import AddBusinessDialog from './components/AddBusinessDialog.vue';
// 定义类型
interface Location {
  id: string;
  path: string;
  proxy_pass?: string;
  root?: string;
  enabled: boolean;
  config: string; // 原始配置（可能包含注释）
  rawConfig?: string; // 无注释的配置（用于展示）
  indentation?: string; // 添加缩进属性
  proxy_connect_timeout?: string; // 连接超时时间
  proxy_read_timeout?: string; // 读取超时时间
  proxy_send_timeout?: string; // 发送超时时间
  [key: string]: any;
}

interface ServerBlock {
  id: string;
  name: string;
  listen: string[];
  server_name: string[];
  locations: Location[];
  enabled: boolean;
  config: string;
  rawConfig?: string;
  file_path?: string; // 添加文件路径字段
  identifier?: string; // 添加唯一标识符
}

const props = defineProps({
  // 反向代理配置数据数组
  serverBlocks: {
    type: Array,
    default: () => []
  },
  // 服务器IP地址
  serverIp: {
    type: String,
    default: ''
  },
  // 添加Nginx配置数据
  nginxConfig: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['config-changed']);

// 本地数据
const data = reactive({
  loading: false,
  serverBlocks: [] as ServerBlock[], // 反向代理列表
  selectedBlockIndex: -1, // 当前选中的反向代理索引
  selectedLocationIndex: -1, // 当前选中的location索引
  filter: '', // 过滤条件
  showEditDialog: false, // 控制编辑对话框显示
  showAddDialog: false, // 控制添加对话框显示
  showAddLocationDialog: false, // 控制添加Location对话框显示
  editingBlock: null as ServerBlock | null, // 当前编辑的服务器块
  editingLocation: null as Location | null, // 当前编辑的location
  editType: 'add', // 编辑类型: add, edit
  isConfigModified: false, // 标记配置是否被修改
  defaultFilePath: '/etc/nginx/conf.d/server_blocks.conf', // 默认文件路径
  filePaths: [] as string[], // 存储所有可用的文件路径
  upstreamName: 'my_upstream', // 业务名称
  upstreamStrategy: '', // 负载均衡策略
  upstreamServers: [{ 
    address: '127.0.0.1:8080', 
    weight: 1,
    max_fails: { value: '3', enabled: false },
    fail_timeout: { value: '30s', enabled: false },
    showAdvanced: false
  }], // 上游服务器列表
  upstreamParams: { // 全局负载均衡参数
    keepalive: { value: '32', enabled: false, description: '保持连接数' },
    max_fails: { value: '3', enabled: false, description: '最大失败次数' },
    fail_timeout: { value: '30s', enabled: false, description: '失败超时时间' }
  },
  locations: [{ 
    path: '/', 
    proxy_pass: 'http://my_upstream',
    basic_headers: false, // 默认不添加基本headers参数
    template: 'basic_proxy' // 添加模板类型
  }], // 路径配置列表
  proxyTimeout: [ // 代理超时参数
    { name: 'proxy_connect_timeout', value: '6s', enabled: false, description: '连接超时时间' },
    { name: 'proxy_read_timeout', value: '60s', enabled: false, description: '读取超时时间' },
    { name: 'proxy_send_timeout', value: '15s', enabled: false, description: '发送超时时间' }
  ],
  advancedParams: { // 高级参数
    backlog: { value: '4096', enabled: false, description: '连接等待队列' }
  }
});

// 配置模板选择
const configTemplate = ref('');
// location类型 (proxy 或 static)
const locationType = ref('proxy');
// 是否添加额外配置
const addExtraConfig = ref(false);
// 选择的额外配置选项
const extraConfigOptions = ref<string[]>([]);

// Server模板选择
const serverTemplateValue = ref('');
// Location模板选择
const locationTemplateValue = ref('');
// 显示预览
const showPreview = ref(false);

// 监听props变化
watch(() => props.serverBlocks, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    // 解析反向代理配置数据
    parseServerBlocks(newVal);
  }
}, { immediate: true, deep: true });

// 监听nginxConfig变化，提取文件路径
watch(() => props.nginxConfig, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    // 提取文件路径
    extractFilePaths(newVal);
  }
}, { immediate: true, deep: true });

// 通过正则表达式匹配 location 块
function extractLocations(config: string): Location[] {
  // 修改正则表达式，使其能够识别带修饰符的location路径，如"location = /50x.html"
  // 原来的正则表达式: /(?:^|\n)(\s*)(?:(#\s*)?location\s+([^\s{]+)\s*{([^}]*)})(?=\n|$)/gs
  // 修改为允许路径中包含等号、波浪号等特殊符号
  const locationRegex = /(?:^|\n)(\s*)(?:(#\s*)?location\s+([^{]+?)\s*{([^}]*)})(?=\n|$)/gs;
  const locationBlocks: Location[] = [];
  let match;
  let index = 0;

  while ((match = locationRegex.exec(config)) !== null) {
    const indentation = match[1] || '';  // 捕获的前导空格
    const isCommented = !!match[2];      // 是否存在注释符号
    const path = match[3].trim();        // 路径(使用trim移除多余空格)
    let content = match[4];              // location块内容
    const locationConfig = match[0];     // 完整的location块，包括前导空格和注释

    // 如果被注释，为每一行移除注释标记，但保留缩进
    let rawConfig = undefined;
    if (isCommented) {
      // 保存格式化良好的无注释版本，用于后续恢复格式
      rawConfig = locationConfig.split('\n').map(line => {
        // 如果这行有注释
        if (line.trim().startsWith('#')) {
          // 找到注释符号的位置
          const hashIndex = line.indexOf('#');
          // 保留前面的空格，移除注释符号和紧随其后的一个空格(如果有)
          const lineBeforeHash = line.substring(0, hashIndex);
          const lineAfterHash = line.substring(hashIndex + 1).replace(/^\s/, '');
          return lineBeforeHash + lineAfterHash;
        }
        return line;
      }).join('\n');
    }

    // 提取 proxy_pass 或 root
    const proxyPassMatch = content.match(/proxy_pass\s+([^;]+);/);
    const rootMatch = content.match(/root\s+([^;]+);/);

    // 提取超时参数 - 新增代码：直接从location块内容中提取超时参数
    const connectTimeoutMatch = content.match(/proxy_connect_timeout\s+([^;]+);/);
    const readTimeoutMatch = content.match(/proxy_read_timeout\s+([^;]+);/);
    const sendTimeoutMatch = content.match(/proxy_send_timeout\s+([^;]+);/);

    const locationBlock: Location = {
      id: `loc_${index++}`,
      path,
      proxy_pass: proxyPassMatch ? proxyPassMatch[1].trim() : undefined,
      root: rootMatch ? rootMatch[1].trim() : undefined,
      enabled: !isCommented,
      config: locationConfig,
      rawConfig: isCommented ? rawConfig : undefined,
      indentation: indentation,  // 保存原始缩进
      // 新增：保存超时参数，便于后续使用
      proxy_connect_timeout: connectTimeoutMatch ? connectTimeoutMatch[1].trim() : undefined,
      proxy_read_timeout: readTimeoutMatch ? readTimeoutMatch[1].trim() : undefined,
      proxy_send_timeout: sendTimeoutMatch ? sendTimeoutMatch[1].trim() : undefined
    };

    locationBlocks.push(locationBlock);
  }

  return locationBlocks;
}

// 解析配置以提取反向代理
function parseServerBlocks(configData) {
  data.loading = true;
  try {

    // 验证输入数据，确保是有效的数组
    if (!Array.isArray(configData)) {
      return;
    }

    // 深度克隆输入数据以防止修改原始数据
    const clonedData = JSON.parse(JSON.stringify(configData));


    // 创建一个集合来跟踪已使用的ID
    const usedIds = new Set();

    // 根据传入的配置数据解析反向代理
    const parsedBlocks = clonedData.map((block, index) => {


      // 如果block为null或undefined，跳过
      if (!block) {
        return null;
      }
      
      // 为每个块创建独立的变量，不共享
      const blockCopy = { ...block };
      
      // 创建新的serverName和listen变量，确保不复用之前的值
      let serverName: string[] | null = null;
      let listen: string[] | null = null;
      
      // 获取配置内容 - 可能存储在content或config属性中
      const configContent = blockCopy.content || blockCopy.config || '';
      
      // 检查是否被注释 - 判断是否所有非空行都被注释
      let isFullyCommented = false;
      if (configContent) {
        const lines = configContent.split('\n');
        // 检查是否所有非空行都被注释
        const nonEmptyLines = lines.filter(line => line.trim().length > 0);
        const commentedLines = nonEmptyLines.filter(line => line.trim().startsWith('#'));
        isFullyCommented = nonEmptyLines.length > 0 && commentedLines.length === nonEmptyLines.length;
      }
      
      // 如果被注释，移除注释标记以用于显示，但保留格式
      let formattedConfig = configContent;
      // 用于解析的无注释内容
      let parseableContent = configContent;
      
      if (isFullyCommented) {

        
        // 1. 创建一个用于解析的无注释内容，将所有注释行的#移除
        parseableContent = configContent.split('\n').map(line => {
          // 如果这行有注释
          if (line.trim().startsWith('#')) {
            // 找到注释符号的位置
            const hashIndex = line.indexOf('#');
            // 保留前面的空格，移除注释符号和紧随其后的一个空格(如果有)
            const lineBeforeHash = line.substring(0, hashIndex);
            const lineAfterHash = line.substring(hashIndex + 1).replace(/^\s/, '');
            return lineBeforeHash + lineAfterHash;
          }
          return line;
        }).join('\n');
        
        // 2. 同样创建一个用于显示的格式化内容
        formattedConfig = parseableContent;
      }
      
      // 检查是否有有效的配置内容
      if (configContent && typeof configContent === 'string') {
        try {

          // 1. 提取server_name - 使用parseableContent以便能处理注释中的指令
          const serverNameRegex = /(?:^|\n)\s*server_name\s+([^;]+);/m;
          const serverNameMatch = parseableContent.match(serverNameRegex);
          if (serverNameMatch && serverNameMatch[1]) {
            // 提取完整的server_name值
            const serverNameValue = serverNameMatch[1].trim();


            // 根据空格分割多个域名
            serverName = serverNameValue.split(/\s+/);

          } else {

            
            // 备用方法：按行扫描 - 同样使用parseableContent
            const lines = parseableContent.split('\n');
            for (const line of lines) {
              const trimmedLine = line.trim();
              if (trimmedLine.startsWith('server_name')) {
                const parts = trimmedLine.split(/\s+/);
                // 跳过'server_name'部分，提取剩余部分并移除分号
                if (parts.length > 1) {
                  const values = parts.slice(1).join(' ').replace(/;$/, '').trim();
                  if (values) {
                    serverName = values.split(/\s+/);
                    break;
                  }
                }
              }
            }
          }

          // 2. 提取listen - 使用parseableContent以便能处理注释中的指令
          const listenRegex = /(?:^|\n)\s*listen\s+([^;]+);/m;
          const listenMatch = parseableContent.match(listenRegex);
          if (listenMatch && listenMatch[1]) {
            // 提取完整的listen值
            const listenValue = listenMatch[1].trim();


            // 保留完整的listen配置，包括SSL和HTTP/2参数
            listen = [listenValue];

          } else {

            
            // 备用方法：按行扫描 - 同样使用parseableContent
            const lines = parseableContent.split('\n');
            for (const line of lines) {
              const trimmedLine = line.trim();
              if (trimmedLine.startsWith('listen')) {
                const parts = trimmedLine.split(/\s+/);
                // 跳过'listen'部分
                if (parts.length > 1) {
                  // 提取剩余部分并移除分号
                  const values = parts.slice(1).join(' ').replace(/;$/, '').trim();
                  if (values) {
                    listen = [values];

                    break;
                  }
                }
              }
            }
          }
        } catch (err) {
          console.error(`服务器块[${index}] 提取server_name或listen时出错:`, err);
        }
      } else {
        console.warn(`服务器块[${index}] 配置内容无效或为空`);
      }

      // 解析location块 - 使用parseableContent而不是configContent用于解析
      const locations = extractLocations(isFullyCommented ? parseableContent : configContent);

      // 生成唯一ID
      let uniqueId;
      if (blockCopy.id && !usedIds.has(blockCopy.id) && blockCopy.id.startsWith('server_')) {
        // 如果原始ID有效且未被使用，保留它
        uniqueId = blockCopy.id;
      } else {
        // 否则生成新ID - 使用时间戳+随机数，确保唯一性
        uniqueId = `server_${Date.now()}${Math.floor(Math.random() * 10000000)}`;
        // 确保ID确实是唯一的
        while (usedIds.has(uniqueId)) {
          uniqueId = `server_${Date.now()}${Math.floor(Math.random() * 10000000)}`;
        }
      }
      usedIds.add(uniqueId);
      


      // 优先使用block中已有的server_name和listen值，其次使用解析出的值
      const finalServerName = blockCopy.server_name || serverName || []; // 修改这里: 不再默认使用 'localhost'
      const finalListen = blockCopy.listen || listen || ['80'];

      // 为每个块创建一个全新的配置对象，确保不共享引用
      const serverBlock = {
        id: uniqueId, // 使用已有ID或生成新的
        name: blockCopy.name || '', // 稍后会重新设置名称
        listen: Array.isArray(finalListen) ? finalListen : [String(finalListen)], // 确保是数组格式
        server_name: Array.isArray(finalServerName) ? finalServerName : (finalServerName ? [String(finalServerName)] : []), // 修改这里: 确保是数组格式，没有时为空数组
        locations: locations.map(loc => ({ ...loc })), // 深度复制locations数组
        enabled: blockCopy.enabled !== undefined ? blockCopy.enabled : !isFullyCommented,
        config: configContent, // 使用原始configContent
        rawConfig: blockCopy.rawConfig || formattedConfig, // 无注释但保留格式的配置，用于启用时恢复
        file_path: blockCopy.file_path || data.defaultFilePath, // 添加文件路径
      };


      // 立即设置显示名称（如果不存在）
      serverBlock.name = blockCopy.name || getBlockDisplayName(serverBlock);



      return serverBlock;
    });

    // 过滤掉null或未定义的服务器块
    const validBlocks = parsedBlocks.filter(block => block !== null && block !== undefined);

    // 检查listen值的正确性
    validBlocks.forEach((block, index) => {
      if (!block.listen || (Array.isArray(block.listen) && block.listen.length === 0)) {
      
        block.listen = ['80'];
      }

      // 确保listen值是数组
      if (!Array.isArray(block.listen)) {
         block.listen = [String(block.listen)];
      }

      // 确保server_name值是数组
      if (!Array.isArray(block.server_name)) {
        
        // 修改这里: 不再默认使用 'localhost'，如果server_name为空，使用空数组
        block.server_name = block.server_name ? [String(block.server_name)] : [];
      }

      // 再次更新显示名称
      block.name = getBlockDisplayName(block);

    });

    data.serverBlocks = validBlocks;
  } catch (error) {
    console.error('解析错误:', error);
    ElMessage.error('解析反向代理配置失败');
  }
  data.loading = false;
}

// 构建服务器块名称显示
function getBlockDisplayName(block: ServerBlock) {

  // 检查是否有关联的upstream
  if (block.locations && Array.isArray(block.locations)) {
    for (const location of block.locations) {
      if (location.proxy_pass) {
        // 从proxy_pass中提取upstream名称，例如从http://zlemr提取zlemr
        const upstreamMatch = location.proxy_pass.match(/http:\/\/([^\/\s:]+)/);
        if (upstreamMatch && upstreamMatch[1]) {
          const upstreamName = upstreamMatch[1];
          // 检查这个upstream名称是否存在于Nginx配置中
          // 通过查找具有相同名称的upstream块来验证它是有效的upstream
          if (findUpstreamByProxyPass(location.proxy_pass)) {
            // 如果有有效的upstream，只使用upstream名称，不带端口号

            return upstreamName;
          }
        }
      }
    }
  }

  // 如果没有找到有效的upstream，使用原来的逻辑
  // 确保server_name总是以一致的方式显示
  let serverName = '未命名';
  if (block.server_name) {
    if (Array.isArray(block.server_name) && block.server_name.length > 0) {
      serverName = block.server_name[0];
    } else {
      serverName = String(block.server_name);
    }
  }

  let listen = '80';
  if (block.listen) {
    if (Array.isArray(block.listen) && block.listen.length > 0) {
      // 提取listen值，可能包含ssl、http2等参数
      const listenValue = block.listen[0];
      // 提取只包含端口部分的值
      if (typeof listenValue === 'string') {
        // 优先使用正则表达式提取数字部分作为端口号
        const portMatch = listenValue.match(/^(\d+)/);
        if (portMatch && portMatch[1]) {
          listen = portMatch[1];
        } else if (listenValue.includes(' ')) {
          // 如果正则不匹配但包含空格，尝试提取第一个部分
          listen = listenValue.split(' ')[0];
        } else {
          // 否则使用整个值
          listen = listenValue;
        }
      } else {
        listen = String(listenValue);
      }
    } else if (block.listen) {
      // 处理非数组的情况
      const listenStr = String(block.listen);
      const portMatch = listenStr.match(/^(\d+)/);
      if (portMatch && portMatch[1]) {
        listen = portMatch[1];
      } else if (listenStr.includes(' ')) {
        listen = listenStr.split(' ')[0];
      } else {
        listen = listenStr;
      }
    }
  }


  return `${serverName} (${listen})`;
}

// 选择反向代理
function selectServerBlock(index) {
  // 修改：根据过滤结果获取正确的原始数据索引
  let originalIndex = index;

  if (data.filter) {
    // 如果有过滤条件，需要将过滤结果的索引映射到原始数组的索引
    const filteredBlocks = filteredServerBlocks();
    if (index >= 0 && index < filteredBlocks.length) {
      // 获取过滤后结果对应的ID
      const targetId = filteredBlocks[index].id;
      // 在原始数组中查找匹配的索引
      originalIndex = data.serverBlocks.findIndex(block => block.id === targetId);
    }
  }

  data.selectedBlockIndex = originalIndex;
  data.selectedLocationIndex = -1; // 重置选中的location
}



// 启用/停用整个server block
function toggleServerBlock(index) {
  if (index < 0 || index >= data.serverBlocks.length) return;

  const serverBlock = data.serverBlocks[index];

  // 切换启用状态
  serverBlock.enabled = !serverBlock.enabled;

  // 修改配置 - 添加或删除注释（简化处理方式）
  if (!serverBlock.enabled) {
    // 停用：在每行前添加一个#，保留原始缩进
    const lines = serverBlock.config.split('\n');
    const commentedLines = lines.map(line => {
      const indent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
      const contentAfterIndent = line.substring(indent.length);
      
      // 如果行不为空，在缩进后添加一个#
      if (contentAfterIndent.trim()) {
        return `${indent}# ${contentAfterIndent}`;
      }
      // 空行保持不变
      return line;
    });
    serverBlock.config = commentedLines.join('\n');
  } else {
    // 启用：移除每行的第一个#，保留原始缩进
    const lines = serverBlock.config.split('\n');
    const uncommentedLines = lines.map(line => {
      const indent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
      const contentAfterIndent = line.substring(indent.length);
      
      // 如果内容以# 开头，移除一个#和后面的一个空格
      if (contentAfterIndent.startsWith('# ')) {
        return `${indent}${contentAfterIndent.substring(2)}`;
      } 
      // 如果内容以#开头但没有跟空格，只移除#
      else if (contentAfterIndent.startsWith('#')) {
        return `${indent}${contentAfterIndent.substring(1)}`;
      }
      // 其他情况保持不变
      return line;
    });
    serverBlock.config = uncommentedLines.join('\n');
  }

  // 查找相关的upstream
  let relatedUpstreams = collectRelatedUpstreams(serverBlock);
  // 更新配置文件
  const updatedConfig = props.nginxConfig.map(file => {
    // @ts-ignore - 忽略TypeScript类型错误，file可能是任何类型
    if (file.file_path === serverBlock.file_path) {

      // 找到目标文件
      // @ts-ignore - 忽略TypeScript类型错误，file.content可能为未知类型
      let content = String(file.content || '');
      // 根据当前操作使用合适的匹配参数
      const matchParams = serverBlock.enabled
        ? {
            // 启用操作：使用注释版本的配置作为检索参数，因为文件中的是注释版本
            listen: serverBlock.listen,
            server_name: serverBlock.server_name,
            // 标记这是注释匹配模式
            isCommentedMode: true
          }
        : serverBlock;
        
      const exactBlock = findExactServerBlock(content, matchParams);

      if (exactBlock) {
        // 使用精确定位的结果替换服务器块
        content = replaceAtPosition(content, exactBlock, serverBlock.config);
      } else {
        
        // 构建更灵活的正则表达式
        const listenPattern = Array.isArray(serverBlock.listen) 
          ? serverBlock.listen[0].replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')
          : serverBlock.listen.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
        
        // 获取server_name模式，如果存在
        let serverNamePattern = '';
        if (serverBlock.server_name && (Array.isArray(serverBlock.server_name) ? serverBlock.server_name.length > 0 : serverBlock.server_name)) {
          serverNamePattern = Array.isArray(serverBlock.server_name)
            ? serverBlock.server_name[0].replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')
            : serverBlock.server_name.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
        }
        
        // 创建更强大的正则表达式，更好地匹配注释块
        // 改进：匹配已注释的server区块
        const commentPrefixPattern = serverBlock.enabled ? '(?:#\\s*)*' : '';
        let serverBlockRegex;
        
        if (serverNamePattern) {
          // 如果有server_name，创建可以匹配两种顺序(listen在前或server_name在前)的正则表达式
          serverBlockRegex = new RegExp(
            `(^|\\n)${commentPrefixPattern}server\\s+\\{[\\s\\S]*?${commentPrefixPattern}listen\\s+(?:${listenPattern})\\b[\\s\\S]*?${commentPrefixPattern}server_name\\s+(?:${serverNamePattern})\\b[\\s\\S]*?\\n${commentPrefixPattern}|(^|\\n)${commentPrefixPattern}server\\s+\\{[\\s\\S]*?${commentPrefixPattern}server_name\\s+(?:${serverNamePattern})\\b[\\s\\S]*?${commentPrefixPattern}listen\\s+(?:${listenPattern})\\b[\\s\\S]*?\\n${commentPrefixPattern}\\}`,
              'gm'
            );
        } else {
          // 如果没有server_name，只匹配listen
          serverBlockRegex = new RegExp(
            `(^|\\n)${commentPrefixPattern}server\\s+\\{[\\s\\S]*?${commentPrefixPattern}listen\\s+(?:${listenPattern})\\b[\\s\\S]*?\\n${commentPrefixPattern}\\}`,
            'gm'
          );
        }
        
        // 替换匹配的服务器块
        let matchCount = 0;
        content = content.replace(serverBlockRegex, (match, prefix) => {
          matchCount++;
          // 替换为新配置
          return prefix + serverBlock.config;
        });
        
        // 如果上面的匹配失败，尝试更宽松的匹配
        if (matchCount === 0) {
          // 尝试匹配更极端的注释情况
          const extremeCommentPattern = serverBlock.enabled ? '#\\s*' : '';
          const looseRegex = new RegExp(
            `(^|\\n)(?:${extremeCommentPattern})+server\\s+\\{[\\s\\S]*?(?:${extremeCommentPattern})+listen\\s+(?:${listenPattern})\\b[\\s\\S]*?\\n(?:${extremeCommentPattern})+\\}`,
              'gm'
            );

          content = content.replace(looseRegex, (match, prefix) => {
            matchCount++;
            return prefix + serverBlock.config;
          });
          
          // 如果仍然失败，尝试最宽松的匹配方式
          if (matchCount === 0) {
            // 最后的尝试：匹配任何包含listen值的server块
            const lastResortRegex = new RegExp(
              `(^|\\n)#?\\s*server\\s+\\{[\\s\\S]*?#?\\s*listen\\s+[^;]*${listenPattern}[^;]*;[\\s\\S]*?\\n#?\\s*\\}`,
                'gm'
              );

            content = content.replace(lastResortRegex, (match, prefix) => {
              matchCount++;
              return prefix + serverBlock.config;
            });
          }
        }
      }

      // 同时处理相关的upstream配置
      if (relatedUpstreams.length > 0) {
        for (const upstream of relatedUpstreams) {
          // @ts-ignore
          if (upstream.name && content.includes(`upstream ${upstream.name}`)) {

            
            // 根据服务器块状态决定upstream的注释状态
            if (!serverBlock.enabled) {
              // 注释upstream块

              const upstreamRegex = new RegExp(`(^|\\n)(\\s*)upstream\\s+${upstream.name}\\s*\\{([\\s\\S]*?)\\n\\2\\}`, 'gm');
              content = content.replace(upstreamRegex, (match, prefix, indent, upstreamContent) => {

                // 添加注释到整个upstream块
                const commentedContent = match.substring(prefix.length).split('\n').map(line => {
                  const lineIndent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
                  const contentAfterIndent = line.substring(lineIndent.length);
                  // 简化处理：对所有非空行添加一个#前缀
                  if (contentAfterIndent.trim()) {
                    return `${lineIndent}# ${contentAfterIndent}`;
                  }
                  return line;
                }).join('\n');
                return prefix + commentedContent;
              });
            } else {
              // 取消注释upstream块

              const commentedUpstreamRegex = new RegExp(`(^|\\n)(\\s*)#\\s*upstream\\s+${upstream.name}\\s*\\{([\\s\\S]*?)\\n\\2#\\s*\\}`, 'gm');
              content = content.replace(commentedUpstreamRegex, (match, prefix, indent, upstreamContent) => {
                // 移除注释
                const uncommentedContent = match.substring(prefix.length).split('\n').map(line => {
                  const lineIndent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
                  const contentAfterIndent = line.substring(lineIndent.length);
                  // 简化处理：只移除每行的第一个#符号
                  if (contentAfterIndent.startsWith('# ')) {
                    return `${lineIndent}${contentAfterIndent.substring(2)}`;
                  } else if (contentAfterIndent.startsWith('#')) {
                    return `${lineIndent}${contentAfterIndent.substring(1)}`;
                  }
                  return line;
                }).join('\n');
                return prefix + uncommentedContent;
              });
            }
          }
        }
      }

      // 检查文件内容是否真的有变化
      const contentChanged = content !== file.content;

      // 确保文件内容被实际更新
      const result = {
        // @ts-ignore
        file_path: file.file_path,
        content: normalizeEmptyLines(content) // 最后再次规范化整个文件的空行
      };
      return result;
    }
    return file;
  });

  // 通知父组件配置已更改
  emit('config-changed', {
    type: 'nginx',
    config: updatedConfig,
    serverBlocks: data.serverBlocks,
    forceRefresh: true
  });
}


// 判断服务器块是否位于stream块中
function isServerInStreamBlock(block) {
  if (!block) return false;

  // 检查是否有典型的stream特征
  const isStreamPort = Array.isArray(block.listen)
    ? block.listen.some(port => port === '1935' || port === '5432')
    : (block.listen === '1935' || block.listen === '5432');

  // stream块通常有proxy_pass但没有location
  const hasProxyPass = block.config && block.config.includes('proxy_pass');
  const hasNoLocations = !block.locations || block.locations.length === 0;

  // 重要修改：不把server块中存在代理超时参数作为stream块特征
  // 这些参数也可能正常出现在http块的server配置中

  return isStreamPort || (hasProxyPass && hasNoLocations);
}

// 添加注释到配置
function addCommentToConfig(config) {
  // 分割每一行
  const lines = config.split('\n');
  const commentedLines = [];
  
  // 处理上游服务器标记，避免重复加注释
  let inUpstreamBlock = false;
  
  // 遍历每一行添加注释
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    
    // 提取行的缩进
    const indent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
    
    // 检测是否进入或退出upstream块
    if (trimmedLine.startsWith('upstream ')) {
      inUpstreamBlock = true;
    } else if (trimmedLine === '}' && inUpstreamBlock) {
      inUpstreamBlock = false;
    }
    
    // 如果行不为空，添加注释
    if (trimmedLine) {
      // 检查是否已经有注释
      if (trimmedLine.startsWith('#')) {
        // 已经有注释，直接保留
        commentedLines.push(line);
      } else {
        // 没有注释，添加"# "前缀，保留原始缩进
        commentedLines.push(`${indent}# ${trimmedLine}`);
      }
    } else {
      // 对于空行，保持不变
      commentedLines.push(line);
    }
  }
  
  // 合并处理后的内容
  const result = commentedLines.join('\n');
  return result;
}

// 从配置中移除注释
function removeCommentFromConfig(config) {
  // 分割每一行
  const lines = config.split('\n');
  const uncommentedLines = [];
  
  // 遍历每一行移除注释
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    
    // 提取行的缩进
    const indent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
    
    // 如果行以注释开始，移除注释
    if (trimmedLine.startsWith('# ')) {
      // 标准的"# "格式注释
      const uncommentedContent = trimmedLine.substring(2);
      uncommentedLines.push(`${indent}${uncommentedContent}`);
    } else if (trimmedLine.startsWith('#')) {
      // 处理没有空格的注释
      const uncommentedContent = trimmedLine.substring(1);
      uncommentedLines.push(`${indent}${uncommentedContent}`);
    } else {
      // 非注释行保持不变
      uncommentedLines.push(line);
    }
  }
  
  // 合并处理后的内容
  const result = uncommentedLines.join('\n');
  return result;
}



// 构建更新后的配置
function buildUpdatedConfig() {
  // 将反向代理按文件路径分组
  const fileGroups = {};
  const completeFiles = {};

  // 首先获取当前完整的配置文件内容
  if (props.nginxConfig && Array.isArray(props.nginxConfig)) {
    props.nginxConfig.forEach(file => {
      if (file && typeof file === 'object' && 'file_path' in file && 'content' in file) {
        const filePath = String(file.file_path);
        const fileContent = String(file.content);
        completeFiles[filePath] = fileContent;
      }
    });
  }

  // 首先确保每个服务器块都有正确的注释状态
  data.serverBlocks.forEach(block => {
    // 确保block.config存在
    if (!block.config) {
      return; // 跳过没有配置内容的块
    }

    // 确保每个块的注释状态与启用状态一致
    const isCurrentlyCommented = block.config.trim().startsWith('#');
    const shouldBeCommented = !block.enabled;

    // 如果注释状态与启用状态不一致，修正它
    if (isCurrentlyCommented !== shouldBeCommented) {
      if (shouldBeCommented) {
        // 需要加注释但没有
        block.config = addCommentToConfig(block.config);
      } else {
        // 有注释但需要启用
        block.config = removeCommentFromConfig(block.config);
      }
    }
  });

  // 特别注意: 在这里不要修改文件中的 upstream 块的位置
  // 预处理：找出哪些文件包含server块，需要特殊处理
  const serverBlockFiles = {};
  const streamBlocks = []; // 专门收集stream块的server
  const httpBlocks = []; // 收集http块的server

  data.serverBlocks.forEach(block => {
    if (block.file_path) {
      // 检查是否是stream块中的server
      const isInNginxConf = block.file_path === '/etc/nginx/nginx.conf';
      const isInStreamBlock = isInNginxConf && isServerInStreamBlock(block);

      if (isInStreamBlock) {
        // stream块中的server特殊处理
        streamBlocks.push(block);
      } else {
        // 常规http块server
        httpBlocks.push(block);

        // 按文件分组
        if (!serverBlockFiles[block.file_path]) {
          serverBlockFiles[block.file_path] = [];
        }
        serverBlockFiles[block.file_path].push(block);
      }
    }
  });

  // 重要修改: 保持文件中的 upstream 位置不变，只修改 server 块
  // 处理包含http server块的文件（不含nginx.conf的stream部分）
  Object.keys(serverBlockFiles).forEach(filePath => {
    if (filePath !== '/etc/nginx/nginx.conf') {
      // 获取原始文件内容
      const originalContent = completeFiles[filePath] || '';

      // 提取所有的 upstream 块，保留其原始位置
      const upstreamRegex = /(^|\n)\s*(#\s*)?upstream\s+([^\s{]+)\s*\{[\s\S]*?\n\s*\}/gm;
      // @ts-ignore - 忽略TypeScript类型错误，允许存储自定义对象结构
      const upstreams = [];
      let upstreamMatch;

      while ((upstreamMatch = upstreamRegex.exec(originalContent)) !== null) {
        // @ts-ignore - 忽略TypeScript类型错误，允许向upstreams添加自定义对象
        upstreams.push({
          content: upstreamMatch[0],
          index: upstreamMatch.index
        });
      }

      // 构建该文件的所有server块
      const blocks = serverBlockFiles[filePath];
      let serverContent = '';

      blocks.forEach(block => {
        serverContent += block.config + '\n\n';
      });

      // 如果有upstream块，则保持它们的位置不变
      if (upstreams.length > 0) {
        // 提取所有非upstream和server的内容
        const otherContentRegex = /(^|\n)(?!.*?upstream|.*?server)(.+?)(?=\n|$)/g;
        const otherContent = [];
        let otherMatch;

        while ((otherMatch = otherContentRegex.exec(originalContent)) !== null) {
          otherContent.push({
            content: otherMatch[0],
            index: otherMatch.index
          });
        }

        // 按照原始位置重建内容
        const allParts = [...upstreams, ...otherContent].sort((a, b) => a.index - b.index);
        let rebuiltContent = '';

        // 添加非server部分的内容
        allParts.forEach(part => {
          rebuiltContent += part.content;
        });

        // 添加更新后的server块
        rebuiltContent += '\n\n' + serverContent;

        fileGroups[filePath] = rebuiltContent;
      } else {
        // 如果没有upstream块，直接将server内容添加到文件末尾
        fileGroups[filePath] = serverContent;
      }
    }
  });

  // 特殊处理nginx.conf文件
  if (serverBlockFiles['/etc/nginx/nginx.conf'] || streamBlocks.length > 0) {
    const nginxFilePath = '/etc/nginx/nginx.conf';
    // 获取原始配置
    const originalContent = completeFiles[nginxFilePath] || '';

    // 构建HTTP块server内容
    let httpServerContent = '';
    if (serverBlockFiles[nginxFilePath]) {
      serverBlockFiles[nginxFilePath].forEach(block => {
        httpServerContent += block.config + '\n\n';
      });
    }

    // 构建stream块内容
    let streamServerContent = '';
    streamBlocks.forEach(block => {
      // 获取或设置基本缩进
      let blockContent = block.config;

      // 检查内容是否缩进，如果不是，则添加标准缩进（4个空格）
      if (blockContent && !blockContent.startsWith('    ')) {
        // 为每一行添加标准缩进
        blockContent = blockContent.split('\n').map(line => {
          // 如果行不为空，且不是已有缩进，则添加标准缩进
          if (line.trim() && !line.startsWith('    ')) {
            return '    ' + line;
          }
          return line;
        }).join('\n');
      }

      // 保留原始格式，包括缩进和注释状态
      streamServerContent += blockContent;

      // 确保每个块之间有一个换行
      if (!blockContent.endsWith('\n')) {
        streamServerContent += '\n';
      }

      // 只在块之间添加一个额外的空行
      streamServerContent += '\n';
    });

    // 清理连续的空行但保留原始格式
    streamServerContent = streamServerContent.replace(/\n{3,}/g, '\n\n');

    // 找出原始stream块中的上游配置
    const streamRegex = /(^|\n)(#\s*)?数据流配置\s*stream\s*\{([\s\S]*?)\n\}/gm;
    let existingStreamContent = '';
    let streamMatch = streamRegex.exec(originalContent);

    if (streamMatch) {
      // 提取原始 stream 块内容（去除 stream {} 外层）
      existingStreamContent = streamMatch[3] || '';

      // 提取 upstream 块
      const upstreamRegex = /(^|\n)\s*(#\s*)?upstream\s+([^\s{]+)\s*\{[\s\S]*?\n\s*\}/gm;
      let upstreamBlocks = '';
      let upstreamMatch;

      // 收集所有 upstream 块，保留原始格式
      while ((upstreamMatch = upstreamRegex.exec(existingStreamContent)) !== null) {
        // 保留原始格式，不添加额外换行符
        upstreamBlocks += upstreamMatch[0];
      }

      // 保留 upstream 块内容添加到新的 stream 块内容中
      if (upstreamBlocks.trim()) {
        if (streamServerContent.trim()) {
          // 优化：确保 upstream 与 server 块之间有适当的空行
          const needsNewline = !upstreamBlocks.endsWith('\n\n');
          streamServerContent = upstreamBlocks + (needsNewline ? '\n\n' : '') + streamServerContent.trimStart();
        } else {
          streamServerContent = upstreamBlocks;
        }
      }
    }

    // 删除旧的 stream 块
    let cleanedContent = originalContent.replace(streamRegex, '');

    // 如果有stream块内容，重建stream块
    if (streamServerContent.trim()) {
      // 保证文件末尾有换行
      if (!cleanedContent.endsWith('\n')) {
        cleanedContent += '\n';
      }
      // 添加一个空行作为分隔
      if (!cleanedContent.endsWith('\n\n')) {
        cleanedContent += '\n';
      }

      // 对 streamServerContent 进行处理，确保格式一致
      // 1. 移除开头的空行
      streamServerContent = streamServerContent.replace(/^\s*\n/, '');
      // 2. 确保每个块之间只有一个空行
      streamServerContent = streamServerContent.replace(/\n{3,}/g, '\n\n');
      // 3. 确保结尾没有多余的空行
      streamServerContent = streamServerContent.replace(/\n+$/, '');

      cleanedContent += `# 数据流配置\nstream {\n${streamServerContent}\n}\n`;
    }

    // 如果有HTTP块内容，放在适当位置
    if (httpServerContent.trim()) {
      const httpMatch = cleanedContent.match(/http\s*\{[^}]*\}/s);
      if (httpMatch && httpMatch.index !== undefined) {
        const httpEnd = httpMatch.index + httpMatch[0].length;
        cleanedContent = cleanedContent.substring(0, httpEnd) + '\n\n' + httpServerContent + cleanedContent.substring(httpEnd);
      }
    }

    // 保存新内容
    fileGroups[nginxFilePath] = cleanedContent;
  }

  // 处理其他配置文件（除了已处理的文件）
  Object.keys(completeFiles).forEach(filePath => {
    if (!fileGroups[filePath]) {
      fileGroups[filePath] = completeFiles[filePath];
    }
  });

  // 转换为期望的格式
  const result = Object.keys(fileGroups).map(filePath => ({
    file_path: filePath,
    content: fileGroups[filePath]
  }));

  return result;
}

// 过滤反向代理
function filteredServerBlocks() {
  // 保持原始顺序，只过滤不排序
  const blocks = data.serverBlocks;

  if (!data.filter) return blocks;

  const filterLower = data.filter.toLowerCase();
  return blocks.filter(block =>
    block.name.toLowerCase().includes(filterLower) ||
    // 修复：server_name 是数组，需要检查数组中的每个元素
    (Array.isArray(block.server_name) ?
      block.server_name.some(name => name.toLowerCase().includes(filterLower)) :
      String(block.server_name).toLowerCase().includes(filterLower)) ||
    block.locations.some(loc => loc.path.toLowerCase().includes(filterLower))
  );
}

// 添加新的反向代理
function addServerBlock() {
  data.editingBlock = {
    id: `server_${Date.now()}`,
    name: '',
    listen: ['80'],
    server_name: ['example.com'],
    locations: [],
    enabled: true,
    config: '',
    file_path: data.defaultFilePath,
  };
  data.editType = 'add';
  data.showAddDialog = true;

  // 初始化upstream相关数据
  data.upstreamName = 'my_upstream';
  data.upstreamStrategy = '';
  data.upstreamServers = [
    { 
      address: '127.0.0.1:8080', 
      weight: 1,
      max_fails: { value: '3', enabled: false },
      fail_timeout: { value: '30s', enabled: false },
      showAdvanced: false
    }
  ];
  
  // 初始化全局负载均衡参数
  data.upstreamParams = {
    keepalive: { value: '32', enabled: false, description: '保持连接数' },
    max_fails: { value: '3', enabled: false, description: '最大失败次数' },
    fail_timeout: { value: '30s', enabled: false, description: '失败超时时间' }
  };
  
  // 初始化路径配置
  data.locations = [
    { path: '/', proxy_pass: 'http://my_upstream', basic_headers: false, template: 'basic_proxy' }
  ];
  
  // 初始化代理超时参数
  data.proxyTimeout = [
    { name: 'proxy_connect_timeout', value: '6s', enabled: false, description: '连接超时时间' },
    { name: 'proxy_read_timeout', value: '60s', enabled: false, description: '读取超时时间' },
    { name: 'proxy_send_timeout', value: '15s', enabled: false, description: '发送超时时间' }
  ];
  
  // 初始化高级参数
  data.advancedParams = {
    backlog: { value: '4096', enabled: false, description: '连接等待队列' }
  };

  // 重置模板选择
  serverTemplateValue.value = '';
  locationTemplateValue.value = '';
  showPreview.value = false;
}

// 编辑反向代理
function editServerBlock(index) {
  // 修改：根据过滤结果获取正确的原始数据索引
  let originalIndex = index;

  if (data.filter) {
    // 如果有过滤条件，需要将过滤结果的索引映射到原始数组的索引
    const filteredBlocks = filteredServerBlocks();
    if (index >= 0 && index < filteredBlocks.length) {
      // 获取过滤后结果对应的ID
      const targetId = filteredBlocks[index].id;
      // 在原始数组中查找匹配的索引
      originalIndex = data.serverBlocks.findIndex(block => block.id === targetId);
    }
  }

  if (originalIndex >= 0 && originalIndex < data.serverBlocks.length) {
    // 深拷贝以防止直接修改原对象
    const serverBlock = JSON.parse(JSON.stringify(data.serverBlocks[originalIndex]));
    
    // 关键修复：确保使用rawConfig而不是config，彻底解决自动添加超时参数的问题
    // 直接用rawConfig替换掉config，因为config可能包含了自动添加的超时参数
    if (serverBlock.rawConfig) {
      serverBlock.config = serverBlock.rawConfig;
    }
    
    // 设置编辑块并确保设置编辑类型为edit
    data.editingBlock = serverBlock;
    data.editType = 'edit';
  
    // 新增：重置所有upstream相关参数，防止状态污染
    data.upstreamName = '';
    data.upstreamStrategy = ''; // 确保重置负载均衡策略
    data.upstreamServers = [];
    // 重置upstreamParams为默认值，确保不会保留上一个业务配置的参数状态
    data.upstreamParams = {
      keepalive: { value: '32', enabled: false, description: '保持连接数' },
      max_fails: { value: '3', enabled: false, description: '最大失败次数' },
      fail_timeout: { value: '30s', enabled: false, description: '失败超时时间' }
    };
  
    if (serverBlock.locations && serverBlock.locations.length > 0) {
      // 尝试提取上游服务器名称
      const firstLocation = serverBlock.locations[0];
      if (firstLocation.proxy_pass) {
        const upstreamMatch = firstLocation.proxy_pass.match(/http:\/\/([^\/\s:]+)/);
        if (upstreamMatch && upstreamMatch[1]) {
          data.upstreamName = upstreamMatch[1];
          // 同时设置到editingBlock对象中，以便在对话框显示
          data.editingBlock.upstreamName = upstreamMatch[1];
          
          // 尝试获取upstream内容
          const upstreams = collectRelatedUpstreams(serverBlock);
          if (upstreams && upstreams.length > 0) {
            const firstUpstream = upstreams[0];
            
            // 关键修复：保持原始格式，不做任何修改，直接将内容设置到firstUpstreamContent
            data.editingBlock.firstUpstreamContent = firstUpstream.content;
            
            // 确保firstUpstream.content被正确设置
            if (firstUpstream.content) {
              
              // 直接解析服务器列表并设置到data.upstreamServers
              const serverRegex = /server\s+([^;]+);/g;
              const serverList = [];
              let serverMatch;
              // 修改：将content改名为serverContent，避免与后面定义的content变量冲突
              const serverContent = firstUpstream.content;
              
              // 尝试直接通过正则表达式匹配服务器
              while ((serverMatch = serverRegex.exec(serverContent)) !== null) {
                const serverStr = serverMatch[1];
                
                // 解析服务器地址和参数
                const addressMatch = serverStr.match(/^([^\s]+)/);
                const weightMatch = serverStr.match(/weight=(\d+)/);
                const maxFailsMatch = serverStr.match(/max_fails=(\d+)/);
                const failTimeoutMatch = serverStr.match(/fail_timeout=([^,\s]+)/);
                const backupMatch = serverStr.includes('backup');
                const downMatch = serverStr.includes('down');
                
                const hasWeight = !!weightMatch;
                
                serverList.push({
                  address: addressMatch ? addressMatch[1] : '',
                  weight: weightMatch ? {
                    value: parseInt(weightMatch[1]), 
                    enabled: true  // 如果在原始配置中找到weight参数，则启用它
                  } : {
                    value: 1,
                    enabled: false
                  },
                  max_fails: { 
                    value: maxFailsMatch ? maxFailsMatch[1] : '3', 
                    enabled: !!maxFailsMatch 
                  },
                  fail_timeout: { 
                    value: failTimeoutMatch ? failTimeoutMatch[1] : '30s', 
                    enabled: !!failTimeoutMatch 
                  },
                  backup: { enabled: backupMatch },
                  down: { enabled: downMatch },
                  showAdvanced: hasWeight || !!maxFailsMatch || !!failTimeoutMatch || backupMatch || downMatch
                });
              }
              
              // 如果成功解析到服务器，直接更新upstreamServers
              if (serverList.length > 0) {
                data.upstreamServers = serverList;
              }
            }
          }
        }
      }
      
      // 复制locations数据 - 重要修复：确保location不包含自动添加的超时参数
      // 使用来自rawConfig的location，而不是可能包含额外参数的config
      data.locations = serverBlock.locations.map(loc => {
        // 优先使用原始配置
        const originalConfig = loc.rawConfig || loc.config;
        
        // 分析location配置内容，判断是否是WebSocket
        const isWebSocket = originalConfig && (
          originalConfig.includes('proxy_set_header Upgrade $http_upgrade') || 
          originalConfig.includes('proxy_set_header Connection "upgrade"') ||
          originalConfig.includes('proxy_http_version 1.1')
        );
        
        // 检查原始配置中是否包含特定的超时参数
        const hasConnectTimeout = originalConfig && originalConfig.includes('proxy_connect_timeout');
        const hasReadTimeout = originalConfig && originalConfig.includes('proxy_read_timeout');
        const hasSendTimeout = originalConfig && originalConfig.includes('proxy_send_timeout');
        
        return {
          path: loc.path,
          proxy_pass: loc.proxy_pass || `http://${data.upstreamName}`,
          // 根据配置内容判断模板类型，而不是路径名称
          template: isWebSocket ? 'websocket' : 'basic_proxy',
          // 代理头部设置
          proxy_host_header: { 
            enabled: true, 
            value: isWebSocket ? '$host:$server_port' : '$host' 
          },
          proxy_real_ip_header: { enabled: true, value: '$remote_addr' },
          proxy_remote_host_header: { enabled: true, value: '$remote_addr' },
          proxy_forwarded_for_header: { enabled: true, value: '$proxy_add_x_forwarded_for' },
          // 超时设置 - 从现有配置中提取值，只有在原始配置中存在时才启用
          proxy_connect_timeout: { 
            enabled: hasConnectTimeout, 
            value: extractConfigValue(originalConfig, 'proxy_connect_timeout', '6s') 
          },
          proxy_read_timeout: { 
            enabled: hasReadTimeout, 
            value: extractConfigValue(originalConfig, 'proxy_read_timeout', isWebSocket ? '6000s' : '60s') 
          },
          proxy_send_timeout: { 
            enabled: hasSendTimeout, 
            value: extractConfigValue(originalConfig, 'proxy_send_timeout', isWebSocket ? '6000s' : '15s') 
          },
          // WebSocket设置 - 根据实际配置启用
          websocket_upgrade: { 
            enabled: isWebSocket, 
            value: '$http_upgrade' 
          },
          websocket_connection: { 
            enabled: isWebSocket, 
            value: 'upgrade' 
          },
          websocket_keepalive: { 
            enabled: isWebSocket, 
            value: 'keep-alive' 
          },
          // 保存原始配置，用于后续处理
          config: originalConfig,
          rawConfig: loc.rawConfig
        };
      });
    }
    
    // 尝试从Nginx配置中查找对应的upstream并提取配置参数
    const upstreams = collectRelatedUpstreams(serverBlock);
    if (upstreams && upstreams.length > 0) {
      // 解析第一个上游服务器的配置
      const firstUpstream = upstreams[0];
      
      // 使用上游服务器的实际名称
      data.upstreamName = firstUpstream.name || data.upstreamName;
      // 修复：确保upstreamName设置到editingBlock中
      data.editingBlock.upstreamName = data.upstreamName;
      
      // 修复：确保firstUpstreamContent设置到editingBlock中，保持原始格式，不进行任何处理
      data.editingBlock.firstUpstreamContent = firstUpstream.content;
      
      // 如果上面的服务器解析失败，这里再尝试一次
      if (!data.upstreamServers || data.upstreamServers.length === 0) {
      // 解析upstream中的服务器列表
      const serverRegex = /server\s+([^;]+);/g;
      const servers = [];
      
      let serverMatch;
      const content = firstUpstream.isCommented 
        ? firstUpstream.content.replace(/^\s*#\s*/gm, '') 
        : firstUpstream.content;
      

      
      // 尝试标准正则表达式匹配
      while ((serverMatch = serverRegex.exec(content)) !== null) {
        const serverStr = serverMatch[1];
        
        // 解析服务器地址和参数
        const addressMatch = serverStr.match(/^([^\s]+)/);
        const weightMatch = serverStr.match(/weight=(\d+)/);
        const maxFailsMatch = serverStr.match(/max_fails=(\d+)/);
        const failTimeoutMatch = serverStr.match(/fail_timeout=([^,\s]+)/);
        const backupMatch = serverStr.includes('backup');
        const downMatch = serverStr.includes('down');
        
        servers.push({
          address: addressMatch ? addressMatch[1] : '',
            weight: weightMatch ? {
              value: parseInt(weightMatch[1]), 
              enabled: true  // 如果在原始配置中找到weight参数，则启用它
            } : {
              value: 1,
              enabled: false
            },
          max_fails: { 
            value: maxFailsMatch ? maxFailsMatch[1] : '3', 
            enabled: !!maxFailsMatch 
          },
          fail_timeout: { 
            value: failTimeoutMatch ? failTimeoutMatch[1] : '30s', 
            enabled: !!failTimeoutMatch 
          },
          backup: { enabled: backupMatch },
          down: { enabled: downMatch },
            showAdvanced: !!weightMatch || !!maxFailsMatch || !!failTimeoutMatch || backupMatch || downMatch
        });
      }
      
      // 如果标准正则没有匹配到服务器，尝试更宽松的匹配方式
      if (servers.length === 0) {
 
        // 使用简单的行分割和查找方式
        const lines = content.split('\n');
        for (const line of lines) {
          const trimmedLine = line.trim();
          // 匹配任何以server开头的非注释行
          if (trimmedLine.startsWith('server ') && !trimmedLine.startsWith('#')) {
            // 提取服务器地址 - 移除"server "前缀和结尾的分号及前面的空格
            const serverConfig = trimmedLine.substring(7).replace(/;$/, '').trim();
            const addressMatch = serverConfig.match(/^([^\s]+)/);
            
            servers.push({
              address: addressMatch ? addressMatch[1] : serverConfig,
                weight: {
                  value: 1,
                  enabled: false
                },
              max_fails: { value: '3', enabled: false },
              fail_timeout: { value: '30s', enabled: false },
              backup: { enabled: false },
              down: { enabled: false },
              showAdvanced: false
            });
          }
        }
      }
      
      // 只有在有服务器地址的情况下才更新
      if (servers.length > 0) {
        data.upstreamServers = servers;
      } 
      }
      
      // 解析策略 - 修复：改进负载均衡策略的识别逻辑
      let strategy = '';
      // 确保从firstUpstream中获取内容，解决content未定义问题
      const upstreamContent = firstUpstream.isCommented 
        ? firstUpstream.content.replace(/^\s*#\s*/gm, '') 
        : firstUpstream.content;
      
      // 重要修复：使用更精确的正则表达式匹配负载均衡策略，避免误匹配和状态污染
      // 增加行首匹配条件，确保只匹配行开头的策略声明
      if (upstreamContent.match(/^\s*ip_hash\s*;/m)) {
        strategy = 'ip_hash';
      } else if (upstreamContent.match(/^\s*least_conn\s*;/m)) {
        strategy = 'least_conn';
      } else if (upstreamContent.match(/^\s*hash\s+[^;]+;/m)) {
        // 提取完整的hash配置，包括参数
        const hashMatch = upstreamContent.match(/^\s*hash\s+([^;]+);/m);
        if (hashMatch) {
          strategy = `hash ${hashMatch[1]}`;
        } else {
          strategy = 'hash';
        }
      } else if (upstreamContent.match(/^\s*random\s*;/m)) {
        strategy = 'random';
      }
      // 如果没有匹配到任何策略，保持strategy为空字符串
      
      // 更新数据
      data.upstreamStrategy = strategy;
      
      // 关键修复：从当前upstream配置中解析参数，而不是继承先前配置的参数
      // 只启用原始配置中明确存在的参数
      const keepaliveMatch = upstreamContent.match(/keepalive\s+(\d+);/);
      const zoneMatch = upstreamContent.match(/zone\s+([^;]+);/);
      const keepaliveTimeoutMatch = upstreamContent.match(/keepalive_timeout\s+([^;]+);/);
      
      // 重置所有参数为禁用状态
      data.upstreamParams = {
        keepalive: {
          enabled: !!keepaliveMatch,
          value: keepaliveMatch ? keepaliveMatch[1] : '32',
          description: '保持连接数'
        },
        max_fails: { 
          value: '3', 
          enabled: false, 
          description: '最大失败次数' 
        },
        fail_timeout: { 
          value: '30s', 
          enabled: false, 
          description: '失败超时时间' 
        }
      };
      
      // 扩展data.upstreamParams对象以包含zone和keepalive_timeout参数
      if (zoneMatch) {
        data.upstreamParams.zone = {
          enabled: true,
          value: zoneMatch[1],
          description: '共享内存区域'
        };
      }
      
      if (keepaliveTimeoutMatch) {
        data.upstreamParams.keepalive_timeout = {
          enabled: true,
          value: keepaliveTimeoutMatch[1],
          description: '保持连接超时'
        };
      }
    } else {
      // 如果没有找到对应的upstream，使用默认值但保留upstreamName
      data.upstreamStrategy = '';
      data.upstreamServers = [
        { 
          address: '127.0.0.1:8080', 
          weight: { value: 1, enabled: false },
          max_fails: { value: '3', enabled: false },
          fail_timeout: { value: '30s', enabled: false },
          backup: { enabled: false },
          down: { enabled: false },
          showAdvanced: false
        }
      ];
      
      // 使用默认值，确保所有参数都显式设置为禁用状态
      data.upstreamParams = {
        keepalive: { value: '32', enabled: false, description: '保持连接数' },
        max_fails: { value: '3', enabled: false, description: '最大失败次数' },
        fail_timeout: { value: '30s', enabled: false, description: '失败超时时间' }
      };
    }
    
    // 解析高级服务器参数 - 增强识别能力
    // 优先使用rawConfig来解析参数
    const config = serverBlock.rawConfig || serverBlock.config || '';
    const backlogMatch = config.match(/listen\s+[^;]*backlog=(\d+)/);
    const clientMaxBodySizeMatch = config.match(/client_max_body_size\s+([^;]+);/);
    
    data.serverParams = {
      backlog: {
        enabled: !!backlogMatch,
        value: backlogMatch ? backlogMatch[1] : '4096'
      },
      client_max_body_size: {
        enabled: !!clientMaxBodySizeMatch,
        value: clientMaxBodySizeMatch ? clientMaxBodySizeMatch[1] : '60m'
      }
    };
    
    // 使用AddBusinessDialog而不是编辑对话框
    data.showAddDialog = true;
    data.showEditDialog = false;
  }
}



// 取消编辑
function cancelEdit() {
  data.editingBlock = null;
  data.editingLocation = null;
  data.showEditDialog = false;
  data.showAddDialog = false;
  data.showAddLocationDialog = false;

  // 重置表单状态
  configTemplate.value = '';
  locationType.value = 'proxy';
  addExtraConfig.value = false;
  extraConfigOptions.value = [];
}



// 从Nginx配置中提取可用的文件路径
function extractFilePaths(configFiles) {
  if (!configFiles || !Array.isArray(configFiles)) return;

  // 清空现有路径
  data.filePaths = [];

  // 提取所有文件路径
  configFiles.forEach(item => {
    if (item && item.file_path && !data.filePaths.includes(item.file_path)) {
      data.filePaths.push(item.file_path);
    }
  });

  // 如果没有找到任何路径，添加默认路径
  if (data.filePaths.length === 0) {
    data.filePaths.push(data.defaultFilePath);
  }

  // 默认文件路径设置
  if (data.filePaths.length > 0) {
    // 优先选择conf.d目录下的文件
    const confDFile = data.filePaths.find(path => path.includes('/conf.d/'));
    if (confDFile) {
      data.defaultFilePath = confDFile;
    } else {
      // 否则使用第一个路径
      data.defaultFilePath = data.filePaths[0];
    }
  }
}



// 导出组件方法
defineExpose({
  // 返回配置是否被修改
  isConfigModified: () => data.isConfigModified,

  // 应用所有未保存的更改
  applyChanges: () => {
    if (!data.isConfigModified) return;

    // 构建更新后的配置
    const updatedConfig = buildUpdatedConfig();

    // 发送配置更新事件
    emit('config-changed', {
      type: 'server-blocks',
      blocks: data.serverBlocks,
      config: updatedConfig,
      pendingChanges: false
    });

    // 重置修改标记
    data.isConfigModified = false;

    return true;
  }
});


// 快速将HTTP服务器块转换为HTTPS
function convertToHttps(index) {
  if (index < 0 || index >= data.serverBlocks.length) return;

  const block = data.serverBlocks[index];

  // 检查是否是stream块中的server - 防止对stream中的server应用HTTP/HTTPS转换
  if (isServerInStreamBlock(block)) {
    ElMessage.warning('无法将stream块中的服务器转换为HTTPS');
    return;
  }

  // 检查是否已经是HTTPS
  const httpsStatus = checkHttpsStatus(block);
  if (httpsStatus.isHttps) {
    ElMessage.warning('该服务器块已经是HTTPS');
    return;
  }

  // 如果有已注释的SSL配置，取消注释这些配置
  if (httpsStatus.hasCommentedSsl) {
    ElMessageBox.confirm(
      `检测到该服务器块有被注释的SSL配置，确定要取消注释这些配置转换为HTTPS吗？`,
      'HTTPS转换确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    ).then(() => {
      try {


        // 保存原始配置
        const originalConfig = block.config;
        
        // 获取服务器块的文件路径，准备找到正确的文件
        const filePath = block.file_path;
        
        // 使用正则表达式取消注释SSL相关配置
        const lines = originalConfig.split('\n');
        const updatedLines = lines.map(line => {
          const trimmedLine = line.trim();
          // 如果是被注释的SSL行，取消注释
          if (trimmedLine.startsWith('#')) {
            // 改进：处理注释后有多个空格的情况
            const uncommentedLine = trimmedLine.replace(/^#\s+/, '').replace(/^#/, '');
            if (uncommentedLine.startsWith('ssl_') || 
                uncommentedLine.startsWith('ssl ') ||
                (uncommentedLine.startsWith('listen') && /\s+ssl(\s+|;|$)/.test(uncommentedLine))) {
              // 取消注释SSL配置行，保持原缩进
              const indent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
              return `${indent}${uncommentedLine}`;
            }
          }
          return line;
        });
        
        // 更新配置，保持整体结构不变
        block.config = updatedLines.join('\n');
        
        // 更新配置文件
        const updatedConfig = props.nginxConfig.map(file => {
          // @ts-ignore - 忽略TypeScript类型错误
          if (file.file_path === filePath) {
            // 找到目标文件
            // @ts-ignore - 忽略TypeScript类型错误
            let content = String(file.content || '');

            // 尝试精确定位服务器块
            const exactBlock = findExactServerBlock(content, block);
            
            if (exactBlock) {
              // 使用精确定位的结果替换服务器块
              content = replaceAtPosition(content, exactBlock, block.config);
            } else {
              // 如果精确定位失败，使用正则表达式替换
              
              // 构建匹配服务器块的正则表达式
              const serverNamePattern = Array.isArray(block.server_name)
                ? block.server_name[0].replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')
                : block.server_name.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
                
              const listenPattern = Array.isArray(block.listen)
                ? block.listen[0].replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')
                : block.listen.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
              
              // 创建匹配服务器块的正则表达式
              const serverBlockRegex = new RegExp(
                `(^|\\n)(?:(?:#\\s*)+)?server\\s+\\{[\\s\\S]*?(?:(?:#\\s*)+)?listen\\s+(?:${listenPattern})\\b[\\s\\S]*?(?:(?:#\\s*)+)?server_name\\s+(?:${serverNamePattern})\\b[\\s\\S]*?\\n\\}`,
                'gm'
              );
              
              // 替换找到的服务器块
              let matchCount = 0;
              content = content.replace(serverBlockRegex, (match, prefix) => {
                matchCount++;
                // 替换为新配置
                return prefix + block.config;
              });
            }
            
            // 返回更新后的文件内容
            return {
              file_path: file.file_path,
              content: normalizeEmptyLines(content)
            };
          }
          return file;
        });
        
        // 通知父组件配置已更改
        emit('config-changed', {
          type: 'server-blocks',
          action: 'convert-to-https',
          config: updatedConfig,
          blocks: data.serverBlocks,
          convertedBlock: {
            index,
            original: originalConfig,
            updated: block.config
          },
          syntaxErrors: [],
          pendingChanges: false
        });
        
        ElMessage.success(`成功将反向代理 "${block.name}" 转换为HTTPS`);
      } catch (error) {
        ElMessage.error(`转换失败: ${error.message || '未知错误'}`);
      }
    }).catch(() => {
      // 用户取消操作
    });
  } else {
    // 没有已注释的SSL配置，需要添加新的SSL配置
    ElMessageBox.prompt(
      '请输入SSL证书文件路径 (例如: ssl/dlyyserverCA.cer)',
      '证书配置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: 'ssl/dlyyserverCA.cer',
      }
    ).then(({ value: certPath }) => {
      // 获取证书密钥文件路径
      ElMessageBox.prompt(
        '请输入SSL证书密钥文件路径 (例如: ssl/dlyyserverCA.pvk)',
        '证书配置',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: 'ssl/dlyyserverCA.pvk',
        }
      ).then(({ value: keyPath }) => {
        try {
          // 使用用户提供的证书路径
          const sslCertPath = certPath && certPath.trim() ? certPath : 'ssl/dlyyserverCA.cer';
          const sslKeyPath = keyPath && keyPath.trim() ? keyPath : 'ssl/dlyyserverCA.pvk';
          
          // 保存原始配置
          const originalConfig = block.config;
          
          // 获取服务器块的文件路径
          const filePath = block.file_path;
          
          // 获取listen行
          const listenLine = Array.isArray(block.listen) ? block.listen[0] : block.listen;
          
          // 构建新的服务器块配置，保留原结构
      const lines = block.config.split('\n');
          let newConfig = '';
      let listenLineFound = false;
      
      // 为每一行处理
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();
        
        if (trimmedLine.startsWith('listen') && !listenLineFound) {
          // 获取缩进
          const indent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
              
              // 创建标准的SSL配置，保持原有缩进
              newConfig += `${indent}listen ${listenLine} ssl http2;\n`;
              newConfig += `${indent}ssl_protocols TLSv1.2 TLSv1.3;\n`;
              newConfig += `${indent}ssl_ciphers EECDH+CHACHA20:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;\n`;
              newConfig += `${indent}ssl_prefer_server_ciphers on;\n`;
              newConfig += `${indent}ssl_session_cache shared:SSL:10m;\n`;
              newConfig += `${indent}ssl_session_timeout 10m;\n`;
              newConfig += `${indent}ssl_certificate ${sslCertPath};\n`;
              newConfig += `${indent}ssl_certificate_key ${sslKeyPath};\n`;
              
          listenLineFound = true;
        } else if (!trimmedLine.startsWith('listen')) {
          // 保留非listen行
          newConfig += line + '\n';
        }
        // 跳过其他listen行，不添加到新配置中
      }
      
      // 更新配置
      block.config = newConfig.trim();
      
      // 更新listen属性
      block.listen = [`${listenLine} ssl http2`];
      
      // 更新服务器块名称
      block.name = getBlockDisplayName(block);
      
            // 更新配置文件，保持文件结构
            const updatedConfig = props.nginxConfig.map(file => {
              // @ts-ignore - 忽略TypeScript类型错误
              if (file.file_path === filePath) {
                
                // 找到目标文件
                // @ts-ignore - 忽略TypeScript类型错误
                let content = String(file.content || '');
                
                // 尝试精确定位服务器块
                const exactBlock = findExactServerBlock(content, {
                  ...block,
                  listen: [listenLine], // 使用原始的listen值查找
                  config: originalConfig
                });
                
                if (exactBlock) {
                  // 使用精确定位的结果替换服务器块
                  content = replaceAtPosition(content, exactBlock, block.config);
                } else {
                  
                  // 构建匹配服务器块的正则表达式
                  const serverNamePattern = Array.isArray(block.server_name)
                    ? block.server_name[0].replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')
                    : block.server_name.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
                    
                  // 创建匹配服务器块的正则表达式，更精确匹配
                  const serverBlockRegex = new RegExp(
                    `(server\\s+\\{[\\s\\S]*?listen\\s+${listenLine.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}\\b[\\s\\S]*?server_name\\s+(?:${serverNamePattern})\\b[\\s\\S]*?\\n\\})`,
                    'gm'
                  );
                  
                  // 替换找到的服务器块
                  let matchCount = 0;
                  content = content.replace(serverBlockRegex, () => {
                    matchCount++;

                    // 替换为新配置
                    return block.config;
                  });
                  
                  // 如果没有找到匹配，可能是因为正则表达式不够精确，尝试另一种方法
                  if (matchCount === 0) {
                    const simpleServerBlockRegex = new RegExp(
                      `(server\\s+\\{[\\s\\S]*?listen\\s+${listenLine.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}[\\s\\S]*?\\n\\})`,
                      'gm'
                    );
                    
                    content = content.replace(simpleServerBlockRegex, () => {
                      matchCount++;
                      return block.config;
                    });
                  }
                }
                
                // 返回更新后的文件内容
                return {
                  file_path: file.file_path,
                  content: normalizeEmptyLines(content)
                };
              }
              return file;
            });
      
      // 通知父组件配置已更改
      emit('config-changed', {
        type: 'server-blocks',
        action: 'convert-to-https',
        config: updatedConfig,
        blocks: data.serverBlocks,
        convertedBlock: {
          index,
                original: originalConfig,
          updated: block.config
        },
        syntaxErrors: [],
        pendingChanges: false
      });
      
      ElMessage.success(`成功将反向代理 "${block.name}" 转换为HTTPS`);
        } catch (error) {
          ElMessage.error(`转换失败: ${error.message || '未知错误'}`);
        }
      }).catch(() => {
        // 用户取消操作
      });
    }).catch(() => {
      // 用户取消操作
    });
  }
}

// 快速将HTTPS服务器块转换为HTTP
function convertToHttp(index) {
  if (index < 0 || index >= data.serverBlocks.length) return;

  const block = data.serverBlocks[index];

  // 检查是否已经是HTTP
  if (!isHttps(block)) {
    ElMessage.warning('该服务器块已经是HTTP');
    return;
  }

  ElMessageBox.confirm(
    `确定要将反向代理 "${block.name}" 转换为HTTP吗？`,
    'HTTP转换确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    try {
      
      // 保存原始配置
      const originalConfig = block.config;
      
      // 获取服务器块的文件路径
      const filePath = block.file_path;
      
      // 使用正则表达式注释SSL相关配置
      const lines = originalConfig.split('\n');
      const updatedLines = [];
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();
        
        // 获取缩进
          const indent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
        
        if (!trimmedLine.startsWith('#')) {
          // 处理非注释行
          if (trimmedLine.startsWith('listen') && /\s+ssl(\s+|;|$)/.test(trimmedLine)) {
            // 提取listen的基本部分（不包含ssl和http2）
            const listenBasePart = trimmedLine.replace(/\s+ssl(\s+http2)?(\s+|;|$)/, '$2');
            
            // 检查是否已经存在相同的非SSL listen行
            const hasExistingNonSslListen = lines.some(l => {
              const otherTrimmed = l.trim();
              return otherTrimmed.startsWith('listen') && 
                     !otherTrimmed.includes('ssl') && 
                     otherTrimmed.replace(/listen\s+|\s*;$/g, '') === listenBasePart.replace(/listen\s+|\s*;$/g, '');
            });
            
            // 注释原始SSL listen行
            updatedLines.push(`${indent}# ${trimmedLine}`);
            
            // 只有当不存在相同的非SSL listen行时才添加新行
            if (!hasExistingNonSslListen) {
              const newListenLine = trimmedLine.replace(/\s+ssl(\s+http2)?(\s+|;|$)/, '$2');
              updatedLines.push(`${indent}${newListenLine}`);
            }
          } else if (trimmedLine.startsWith('ssl_') || trimmedLine.startsWith('ssl ')) {
            // 注释其他SSL配置行
            updatedLines.push(`${indent}# ${trimmedLine}`);
          } else {
            // 保留其他行
            updatedLines.push(line);
          }
        } else {
          // 保留已注释行
          updatedLines.push(line);
        }
      }
      
      // 更新配置，保持整体结构不变
      block.config = updatedLines.join('\n');
      
      // 更新listen属性，去除ssl关键字
      if (Array.isArray(block.listen)) {
        block.listen = block.listen.map(listen => {
          const listenStr = String(listen);
          return listenStr.replace(/\s+ssl(\s+|;|$)/, '$1');
        });
      }
      
      // 更新服务器块名称
      block.name = getBlockDisplayName(block);
      
      // 更新配置文件，保持文件结构
      const updatedConfig = props.nginxConfig.map(file => {
        // @ts-ignore - 忽略TypeScript类型错误
        if (file.file_path === filePath) {

          // 找到目标文件
          // @ts-ignore - 忽略TypeScript类型错误
          let content = String(file.content || '');
          
          // 尝试精确定位服务器块
          const exactBlock = findExactServerBlock(content, {
            ...block,
            config: originalConfig // 使用原始配置进行查找
          });
          
          if (exactBlock) {

            // 使用精确定位的结果替换服务器块
            content = replaceAtPosition(content, exactBlock, block.config);
          } else {
            
            // 构建匹配服务器块的正则表达式
            const serverNamePattern = Array.isArray(block.server_name)
              ? block.server_name[0].replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')
              : block.server_name.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
              
            // 创建匹配服务器块的正则表达式，更精确匹配
            const serverBlockRegex = new RegExp(
              `(server\\s+\\{[\\s\\S]*?listen\\s+[^\\n]*?ssl[\\s\\S]*?server_name\\s+(?:${serverNamePattern})\\b[\\s\\S]*?\\n\\})`,
              'gm'
            );
            
            // 替换找到的服务器块
            let matchCount = 0;
            content = content.replace(serverBlockRegex, () => {
              matchCount++;
              // 替换为新配置
              return block.config;
            });
            
            // 如果没有找到匹配，可能是因为正则表达式不够精确，尝试另一种方法
            if (matchCount === 0) {

              const simpleServerBlockRegex = new RegExp(
                `(server\\s+\\{[\\s\\S]*?listen\\s+[^\\n]*?ssl[\\s\\S]*?\\n\\})`,
                'gm'
              );
              
              content = content.replace(simpleServerBlockRegex, () => {
                matchCount++;

                return block.config;
              });
            }
          }
          
          // 返回更新后的文件内容
          return {
            file_path: file.file_path,
            content: normalizeEmptyLines(content)
          };
        }
        return file;
      });
      
      // 通知父组件配置已更改
      emit('config-changed', {
        type: 'server-blocks',
        action: 'convert-to-http',
        config: updatedConfig,
        blocks: data.serverBlocks,
        convertedBlock: {
          index,
          original: originalConfig,
          updated: block.config
        },
        syntaxErrors: [],
        pendingChanges: false
      });
      
      ElMessage.success(`成功将反向代理 "${block.name}" 转换为HTTP (已注释SSL配置)`);
    } catch (error) {
      ElMessage.error(`转换失败: ${error.message || '未知错误'}`);
    }
  }).catch(() => {
    // 用户取消操作
  });
}


// 判断是否为HTTPS并返回原因
function checkHttpsStatus(block) {
  if (!block || !block.config) return { isHttps: false, reasons: [] as string[], hasCommentedSsl: false };

  let hasCommentedSsl = false; // 新增：是否有被注释的SSL指令

  // 检查是否有非注释的SSL指令
  const hasSSL = block.config.split('\n')
    .some(line => {
      const trimmedLine = line.trim();
      // 只考虑非注释行
      if (trimmedLine.startsWith('#')) {
        // 新增：检查这一行是否包含SSL指令，允许注释后有多个空格
        const uncommentedLine = trimmedLine.replace(/^#\s+/, '').replace(/^#/, '');
        if (uncommentedLine.startsWith('ssl_') || 
            uncommentedLine.startsWith('ssl ') ||
            (uncommentedLine.startsWith('listen') && /\s+ssl(\s+|;|$)/.test(uncommentedLine))) {
          hasCommentedSsl = true; // 标记找到了被注释的SSL配置
          return false; // 不计入hasSSL判断
        }
        return false;
      }

      // 检查是否包含SSL指令（以ssl_开头的指令或listen指令包含ssl）
      return (trimmedLine.startsWith('ssl_') ||
        trimmedLine.startsWith('ssl ') ||
        (trimmedLine.startsWith('listen') &&
          /\s+ssl(\s+|;|$)/.test(trimmedLine)));
    });

  // 检查是否使用标准HTTPS端口443
  const hasPort443 = Array.isArray(block.listen)
    ? block.listen.some(port => port.includes('443'))
    : String(block.listen).includes('443');

  // 检查listen指令本身是否包含ssl (listen数组中不会包含注释行，因为parseServerBlocks函数的工作方式)
  const listenHasSSL = Array.isArray(block.listen)
    ? block.listen.some(listen => /\bssl\b/.test(String(listen)))
    : /\bssl\b/.test(String(block.listen));

  // 生成分析结果
  const result = {
    isHttps: hasSSL || hasPort443 || listenHasSSL,
    reasons: [] as string[],
    hasCommentedSsl // 新增：标记是否有被注释的SSL配置
  };

  if (hasSSL) result.reasons.push('配置中包含非注释的SSL指令');
  if (hasPort443) result.reasons.push('使用HTTPS标准端口443');
  if (listenHasSSL) result.reasons.push('listen指令中包含ssl关键字');
  if (hasCommentedSsl) result.reasons.push('配置中包含被注释的SSL指令'); // 新增

  return result;
}

// 判断是否为HTTPS
function isHttps(block) {
  const result = checkHttpsStatus(block);
  return result.isHttps;
}

// 获取HTTPS转换按钮的工具提示
function getHttpsTooltip(block) {
  const result = checkHttpsStatus(block);
  if (result.isHttps) {
    return `当前状态: HTTPS\n识别原因: ${Array.isArray(result.reasons) ? result.reasons.join(', ') : result.reasons}\n点击转换为HTTP`;
  } else if (result.hasCommentedSsl) {
    return `当前状态: HTTP (含被注释的SSL配置)\n点击转换为HTTPS`;
  } else {
    return `当前状态: HTTP\n点击转换为HTTPS`;
  }
}

// 在正则表达式相关函数前添加一个函数，用于精确定位服务器块


// 查找并精确匹配服务器块，改进匹配注释块的能力
function findExactServerBlock(content, block) {
  // 检查参数
  if (!content || !block || !block.listen) {

    return null;
  }

  // 使用精确值来匹配
  const listenValues = Array.isArray(block.listen) ? block.listen : [block.listen];
  const serverNameValues = Array.isArray(block.server_name) ? block.server_name : (block.server_name ? [block.server_name] : []);


  // 正则表达式查找所有server块，包括注释的块
  // 改进：更好地支持嵌套注释的块，用 '*?' 进行非贪婪匹配
  const serverBlockRegex = /(^|\n)(?:#\s*)*server\s+\{[\s\S]*?(?:^|\n)(?:#\s*)*\}/gm;
  const serverBlocks = [];
  
  let match;
  while ((match = serverBlockRegex.exec(content)) !== null) {
    const blockContent = match[0];
    const startIndex = match.index;
    const endIndex = startIndex + blockContent.length;
    // 检查是否包含匹配的listen行（支持注释行）
    let hasMatchingListen = false;
    for (const listenValue of listenValues) {
      // 支持带或不带注释的listen行匹配
      // 改进：更灵活地匹配各种注释格式
      const listenPattern = new RegExp(`(?:^|\\n)\\s*(?:#\\s*)*listen\\s+${listenValue.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}\\s*;`, 'i');
      if (listenPattern.test(blockContent)) {
        hasMatchingListen = true;
        break;
      }
    }

    // 如果没有server_name值或者找到匹配的server_name
    let hasMatchingServerName = serverNameValues.length === 0; // 如果没有提供server_name值，视为匹配成功
    
    for (const serverNameValue of serverNameValues) {
      if (serverNameValue) {
        // 支持带或不带注释的server_name行匹配
        // 改进：更灵活地匹配各种注释格式
        const serverNamePattern = new RegExp(`(?:^|\\n)\\s*(?:#\\s*)*server_name\\s+${serverNameValue.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}\\s*;`, 'i');
        if (serverNamePattern.test(blockContent)) {
        hasMatchingServerName = true;
        break;
        }
      }
    }
    
    // 如果同时匹配listen和server_name，或者只匹配listen但没有提供server_name
    if (hasMatchingListen && (hasMatchingServerName || serverNameValues.length === 0)) {
      serverBlocks.push({
        content: blockContent,
        startIndex,
        endIndex
      });
    }
  }

  // 如果有多个匹配，返回最佳匹配（这里简单地返回第一个）
  if (serverBlocks.length > 0) {
    return serverBlocks[0];
  }

  return null;
}


// 替换指定位置的内容，规范化空行
function replaceAtPosition(content, position, newContent) {
  if (!position) return content;

  // 获取替换前的内容
  const beforeContent = content.substring(0, position.startIndex);
  const afterContent = content.substring(position.endIndex);

  // 规范化空行处理 - 确保服务器块之间最多只有一个换行符
  let result = beforeContent;

  // 确保newContent是干净的，没有前导和尾随空行
  const cleanedNewContent = newContent.trim();

  // 如果beforeContent结尾已有换行符，确保不重复
  if (result.endsWith("\n")) {
    // 去除所有尾随换行符，然后添加一个
    while (result.endsWith("\n")) {
      result = result.substring(0, result.length - 1);
    }
    // 添加单个换行符和替换内容
    result += "\n" + cleanedNewContent;
  } else {
    // 如果beforeContent没有换行符，添加一个
    result += "\n" + cleanedNewContent;
  }

  // 规范化afterContent的开头空行
  let normalizedAfterContent = afterContent;

  // 修剪afterContent开头的所有换行符
  normalizedAfterContent = normalizedAfterContent.replace(/^\n+/, "");

  // 确保在替换内容和afterContent之间只有一个换行符
  if (!normalizedAfterContent.startsWith("\n") && normalizedAfterContent.length > 0) {
    // 添加一个换行符
    result += "\n";
  }

  // 添加处理后的后续内容
  result += normalizedAfterContent;

  return result;
}



// 添加新函数：根据proxy_pass找到对应的upstream配置
function findUpstreamByProxyPass(proxyPass) {
  if (!proxyPass) return null;
  
  // 从proxy_pass中提取upstream名称，例如从http://zlemr提取zlemr
  const upstreamMatch = proxyPass.match(/http:\/\/([^\/\s:]+)/);
  if (!upstreamMatch || !upstreamMatch[1]) return null;
  
  const upstreamName = upstreamMatch[1];
  
  // 在props.nginxConfig中查找包含这个upstream定义的文件和内容
  let matchedUpstream = null;
  
  // 检查所有配置文件
  if (props.nginxConfig && Array.isArray(props.nginxConfig)) {
    for (const file of props.nginxConfig) {
      if (!file || !file.content) continue;
      
      // 修改正则表达式，更健壮地匹配upstream块，不再要求结束大括号和开始缩进匹配
      // 使用非贪婪匹配确保只匹配到对应的upstream块
      const upstreamRegex = new RegExp(`(^|\\n)(\\s*)((?:#\\s*)?upstream\\s+${upstreamName}\\s*\\{[\\s\\S]*?\\n[^\\{]*\\})`, 'gm');
      const matches = [...String(file.content).matchAll(upstreamRegex)];
      
      if (matches && matches.length > 0) {
        const match = matches[0];
        const prefix = match[1] || '';  // 前置内容（换行符等）
        const indent = match[2] || '';  // 缩进
        const fullBlock = match[3];     // 完整的upstream块内容
        
        // 检查块是否已被注释
        const isCommented = fullBlock.trim().startsWith('#');
        
        // 创建upstream对象
        matchedUpstream = {
          name: upstreamName,
          file_path: file.file_path,
          content: fullBlock,  // 保留原始内容（包括注释）
          isCommented: isCommented
        };
        
        // 如果找到匹配的upstream，但没有发现server指令，尝试用更简单的方式再次匹配
        if (!matchedUpstream.content.includes('server ')) {
          // 尝试更直接的匹配方式，查找包含server的完整upstream块
          const simpleUpstreamRegex = new RegExp(`upstream\\s+${upstreamName}\\s*\\{[\\s\\S]*?server\\s+[^;]+;[\\s\\S]*?\\}`, 'gm');
          const simpleMatches = simpleUpstreamRegex.exec(String(file.content));
          
          if (simpleMatches && simpleMatches[0]) {
            matchedUpstream.content = simpleMatches[0];
          }
        }
        
        break;
      } else {
        // 如果上面的正则没匹配到，尝试使用更宽松的正则表达式
        const looseUpstreamRegex = new RegExp(`upstream\\s+${upstreamName}\\s*\\{[\\s\\S]*?\\}`, 'gm');
        const looseMatches = looseUpstreamRegex.exec(String(file.content));
        
        if (looseMatches && looseMatches[0]) {
          const fullBlock = looseMatches[0];
          const isCommented = fullBlock.trim().startsWith('#');
          
          matchedUpstream = {
            name: upstreamName,
            file_path: file.file_path,
            content: fullBlock,
            isCommented: isCommented
          };
          break;
        }
      }
    }
  }
  
  return matchedUpstream;
}

// 添加新函数：收集服务器块中所有location使用的upstream配置
function collectRelatedUpstreams(serverBlock) {
  if (!serverBlock || !serverBlock.locations || !Array.isArray(serverBlock.locations)) {
    return [];
  }
  
  // 用于存储唯一的upstream配置
  const upstreamsMap = new Map();
  
  // 遍历所有location
  for (const location of serverBlock.locations) {
    if (location.proxy_pass) {
      const upstream = findUpstreamByProxyPass(location.proxy_pass);
      if (upstream && !upstreamsMap.has(upstream.name)) {
        upstreamsMap.set(upstream.name, upstream);
      }
    }
  }
  
  // 获取服务器块状态
  const serverEnabled = serverBlock.enabled;
  
  // 处理上游配置以匹配服务器块状态
  const upstreams = Array.from(upstreamsMap.values()).map(upstream => {
    // 如果服务器块已停用，但上游配置未注释，添加注释
    if (!serverEnabled && !upstream.isCommented) {
      // 对上游配置添加注释，保留原始缩进
      const commentedContent = upstream.content.split('\n').map(line => {
        const indent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
          return `${indent}# ${trimmedLine}`;
        }
        return line;
      }).join('\n');
      
      return {
        ...upstream,
        content: commentedContent,
        isCommented: true
      };
    }
    
    // 如果服务器块已启用，但上游配置已注释，移除注释
    if (serverEnabled && upstream.isCommented) {
      // 移除上游配置的注释，保留原始缩进
      const uncommentedContent = upstream.content.split('\n').map(line => {
        const indent = line.match(/^(\s*)/) ? line.match(/^(\s*)/)[0] : '';
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('# ')) {
          return `${indent}${trimmedLine.substring(2)}`;
        } else if (trimmedLine.startsWith('#')) {
          return `${indent}${trimmedLine.substring(1)}`;
        }
        return line;
      }).join('\n');
      
      return {
        ...upstream,
        content: uncommentedContent,
        isCommented: false
      };
    }
    
    // 保持原样
    return upstream;
  });
  
  return upstreams;
}


// 构建upstream配置
function buildUpstreamConfig() {
  let config = `upstream ${data.upstreamName} {\n`;
  
  // 添加负载均衡策略
  if (data.upstreamStrategy) {
    // 改进对hash策略的处理
    if (data.upstreamStrategy.startsWith('hash')) {
      // 检查是否包含完整参数
      if (data.upstreamStrategy.includes('$')) {
        // 直接使用原始的完整hash策略
        config += `    ${data.upstreamStrategy};\n`;
      } else {
        // 默认使用remote_addr和consistent选项
        config += `    hash $remote_addr consistent;\n`;
      }
    } else {
      // 其他策略（ip_hash、least_conn等）直接添加
      config += `    ${data.upstreamStrategy};\n`;
    }
  }
  
  // 检查是否有服务器
  let hasServers = false;
  
  // 添加服务器列表
  if (data.upstreamServers && data.upstreamServers.length > 0) {
    data.upstreamServers.forEach(server => {
      if (server.address) {
        hasServers = true;
        let serverConfig = `    server ${server.address}`;
        
        // 修复：对weight参数的处理，使用对象结构
        if (typeof server.weight === 'object' && server.weight.enabled) {
          // 如果weight是对象并且enabled为true，使用value属性
          serverConfig += ` weight=${server.weight.value}`;
        } else if (typeof server.weight === 'number' && server.weight > 1) {
          // 兼容旧格式：如果weight是数字且大于1，直接使用
          serverConfig += ` weight=${server.weight}`;
        }
        
        // 添加每个服务器的高级参数，确保属性存在
        if (server.max_fails && server.max_fails.enabled) {
          serverConfig += ` max_fails=${server.max_fails.value}`;
        }
        if (server.fail_timeout && server.fail_timeout.enabled) {
          serverConfig += ` fail_timeout=${server.fail_timeout.value}`;
        }
        // 添加backup选项
        if (server.backup && server.backup.enabled) {
          serverConfig += ` backup`;
        }
        // 添加down选项
        if (server.down && server.down.enabled) {
          serverConfig += ` down`;
        }
        
        serverConfig += ';';
        config += `${serverConfig}\n`;
      }
    });
  }
  
  // 如果没有找到服务器，并且当前正在编辑一个服务器块
  if (!hasServers && data.editingBlock && data.editingBlock.firstUpstreamContent) {
    // 尝试从firstUpstreamContent中解析服务器
    const serverRegex = /\s*server\s+([^;]+);/g;
    let serverMatch;
    
    while ((serverMatch = serverRegex.exec(data.editingBlock.firstUpstreamContent)) !== null) {
      hasServers = true;
      const serverLine = serverMatch[0].trim();
      config += `    ${serverLine}\n`;
    }
  }
  
  // 添加全局参数
  if (data.upstreamParams && data.upstreamParams.keepalive && data.upstreamParams.keepalive.enabled) {
    config += `    keepalive ${data.upstreamParams.keepalive.value};\n`;
  }
  
  // 添加zone参数
  if (data.upstreamParams && data.upstreamParams.zone && data.upstreamParams.zone.enabled) {
    config += `    zone ${data.upstreamParams.zone.value};\n`;
  }
  
  // 添加keepalive_timeout参数
  if (data.upstreamParams && data.upstreamParams.keepalive_timeout && data.upstreamParams.keepalive_timeout.enabled) {
    config += `    keepalive_timeout ${data.upstreamParams.keepalive_timeout.value};\n`;
  }
  
  config += '}';
  return config;
}


// 新增函数：根据upstream名称移除服务器块
function removeServerBlocksByUpstreamName(content, upstreamName) {
  if (!upstreamName || typeof upstreamName !== 'string') {
    console.error("缺少有效的upstream名称");
    return content;
  }
  
  // 匹配所有server块的正则表达式
  const serverBlockRegex = /(^|\n)server\s*\{[\s\S]*?\n\}/g;
  
  // 用于存储新内容
  let newContent = content;
  let matchCount = 0;
  
  // 从内容中查找所有服务器块并检查它们是否包含目标upstream
  let match;
  const blocksToRemove = [];
  
  while ((match = serverBlockRegex.exec(content)) !== null) {
    const blockContent = match[0];
    const startIndex = match.index;
    const endIndex = startIndex + blockContent.length;
    
    // 检查此服务器块是否包含对目标upstream的引用
    // 查找如 proxy_pass http://upstreamName; 的模式
    const proxyPassRegex = new RegExp(`proxy_pass\\s+http://${upstreamName}[/;\\s]`, 'i');
    
    if (proxyPassRegex.test(blockContent)) {
      blocksToRemove.push({
        content: blockContent,
        startIndex,
        endIndex
      });
      matchCount++;
    }
  }
  
  // 从后往前移除找到的块，以避免索引位置变化问题
  blocksToRemove.reverse().forEach(block => {
    newContent = newContent.substring(0, block.startIndex) + newContent.substring(block.endIndex);
  });
  
  // 规范化空行
  return normalizeEmptyLines(newContent);
}

// 新增：直接修改现有的upstream配置块
function modifyExistingUpstream(content, upstreamName, newUpstreamConfig) {
  // 创建正则表达式匹配整个upstream块
  const upstreamRegex = new RegExp(`(^|\\n)(upstream\\s+${upstreamName}\\s*\\{[\\s\\S]*?\\n\\})`, 'g');
  
  // 如果找到匹配的upstream块，替换它
  let modified = false;
  const newContent = content.replace(upstreamRegex, (match, prefix, originalBlock) => {
    modified = true;
    return prefix + newUpstreamConfig;
  });
  
  // 如果没有找到匹配的upstream块，返回原始内容
  return { content: newContent, modified };
}

// 将upstream和server block保存到一起
function saveServerBlockWithUpstream() {
  if (!validateServerBlockWithUpstream()) return;

  try {
    // 获取原始配置，以便保留原有的特殊参数
    const originalConfig = data.editingBlock.config || '';
    const originalUpstreamContent = data.editingBlock.firstUpstreamContent || '';
    
    // 解析原始server块中的特殊配置
    const proxyNextUpstreamMatch = originalConfig.match(/proxy_next_upstream\s+([^;]+);/);
    const proxyNextUpstream = proxyNextUpstreamMatch ? proxyNextUpstreamMatch[1] : '';
    
    // 构建upstream配置，保留原始weight等参数
    let upstreamConfig = '';
    
    // 如果有原始upstream内容，优先使用它进行解析
    if (originalUpstreamContent && data.editType === 'edit') {
      // 完全保留原始upstream格式，只替换名称
      // 首先检查是否匹配upstream名称模式
      const upstreamNameRegex = /upstream\s+([^\s{]+)(\s*){/;
      const upstreamNameMatch = originalUpstreamContent.match(upstreamNameRegex);
      
      if (upstreamNameMatch) {
        // 提取原始upstream名称和大括号前的空格或无空格
        const originalName = upstreamNameMatch[1];
        const spaceBeforeBrace = upstreamNameMatch[2] || ''; // 保留原始格式，空格或无空格
        
        // 使用原始格式替换，确保保留原始的空格或无空格格式
        upstreamConfig = originalUpstreamContent.replace(
          new RegExp(`upstream\\s+${originalName.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}(\\s*){`), 
          `upstream ${data.upstreamName}$1{`
        );
      } else {
        // 保持原始格式，只替换名称
      upstreamConfig = originalUpstreamContent.replace(/upstream\s+([^{]+)\s*{/, `upstream ${data.upstreamName} {`);
      }
      
      // 确保upstream中包含所有的服务器，可能会有新增的服务器
      // 修改：不在这里替换服务器，而是在模板生成时包含所有服务器和参数
      upstreamConfig = buildUpstreamConfig();
    } else {
      // 否则重新构建upstream配置
      upstreamConfig = buildUpstreamConfig();
    }
    
    // 更新location配置，确保使用正确的业务名称
    data.locations.forEach(location => {
      if (location.proxy_pass.includes('//my_upstream') || !location.proxy_pass.includes(`//${data.upstreamName}`)) {
        location.proxy_pass = `http://${data.upstreamName}`;
      }
    });
    
    // 构建服务器块配置，保持原始格式
    let serverBlockConfig = '';
    
    // 如果在编辑模式下且有原始配置，尽量保持原格式
    if (originalConfig && data.editType === 'edit') {
      // 首先尝试使用与原始配置相同的格式来构建新配置
      // 提取原始的server块结构，包括缩进和换行格式
      serverBlockConfig = originalConfig;
      
      // 替换listen行，保持原始的缩进
      const listenRegex = /(^\s*|\n\s*)listen\s+[^;]+;/g;
      const listen = Array.isArray(data.editingBlock.listen) ? data.editingBlock.listen.join(' ') : data.editingBlock.listen;
      let listenConfig = `${listen}`;
      if (data.advancedParams.backlog && data.advancedParams.backlog.enabled && !listenConfig.includes('backlog=')) {
      listenConfig += ` backlog=${data.advancedParams.backlog.value}`;
    }
      serverBlockConfig = serverBlockConfig.replace(listenRegex, (match, prefix) => {
        return `${prefix}listen ${listenConfig};`;
      });
    
      // 替换server_name行，保持原始的缩进
      if (data.editingBlock.server_name && data.editingBlock.server_name.length > 0) {
    const serverName = Array.isArray(data.editingBlock.server_name) ? data.editingBlock.server_name.join(' ') : data.editingBlock.server_name;
        const serverNameRegex = /(^\s*|\n\s*)server_name\s+[^;]+;/g;
        if (serverNameRegex.test(serverBlockConfig)) {
          serverBlockConfig = serverBlockConfig.replace(serverNameRegex, (match, prefix) => {
            return `${prefix}server_name ${serverName};`;
          });
        } else if (serverName && serverName.trim() !== '') {
          // 如果没有server_name行，添加一个，保持与listen行相同的缩进
          const listenLineMatch = serverBlockConfig.match(/(^\s*|\n\s*)listen\s+[^;]+;/);
          if (listenLineMatch) {
            const indentation = listenLineMatch[1];
            serverBlockConfig = serverBlockConfig.replace(listenLineMatch[0], 
                                               `${listenLineMatch[0]}\n${indentation}server_name ${serverName};`);
          }
        }
      }
      
      // 处理location块
      data.locations.forEach(location => {
        // 查找原始location块
        const locationRegex = new RegExp(`(\\s*)location\\s+${location.path.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*{([\\s\\S]*?)\\n\\s*}`, 'g');
        const locationMatch = locationRegex.exec(serverBlockConfig);
        
        if (locationMatch) {
          // 找到匹配的location块，更新其中的proxy_pass
          let indentation = locationMatch[1];
          let locationContent = locationMatch[2];
          
          // 更新proxy_pass，保持原有缩进
          const proxyPassRegex = /(^\s*|\n\s*)proxy_pass\s+[^;]+;/g;
          if (proxyPassRegex.test(locationContent)) {
            locationContent = locationContent.replace(proxyPassRegex, (match, prefix) => {
              return `${prefix}proxy_pass ${location.proxy_pass};`;
            });
          } else {
            // 如果没有proxy_pass，根据原内容格式添加一个
            // 分析原始内容的缩进风格
            const contentLines = locationContent.split('\n');
            let contentIndent = '';
            for (const line of contentLines) {
              if (line.trim() && line.match(/^\s+/)) {
                contentIndent = line.match(/^\s+/)[0];
                break;
              }
            }
            contentIndent = contentIndent || (indentation + '    ');
            locationContent += `\n${contentIndent}proxy_pass ${location.proxy_pass};`;
          }
          
         
          
          // 重要修复：根据location参数的启用状态处理超时参数，确保保存正确的超时配置
          ['proxy_connect_timeout', 'proxy_read_timeout', 'proxy_send_timeout'].forEach(paramName => {
            const paramKey = paramName.replace(/^proxy_/, '');
            const param = location[paramKey];
            
         
            
            if (param && param.enabled) {
              // 如果参数被启用
              const paramRegex = new RegExp(`(\\s*)${paramName}\\s+[^;]+;`, 'g');
              if (paramRegex.test(locationContent)) {
                // 找到现有参数，更新它
                locationContent = locationContent.replace(paramRegex, (match, indent) => {
               
                  return `${indent}${paramName} ${param.value};`;
                });
              } else {
                // 没找到参数，添加一个，保持与其他行相同的缩进
                const contentLines = locationContent.split('\n');
                let contentIndent = '';
                for (const line of contentLines) {
                  if (line.trim()) {
                    const match = line.match(/^\s+/);
                    if (match) {
                      contentIndent = match[0];
                    break;
                    }
                  }
                }
                contentIndent = contentIndent || (indentation + '    ');
          
                locationContent += `\n${contentIndent}${paramName} ${param.value};`;
              }
            } else if (param) {
              // 如果超时参数被禁用，移除它
              const paramRegex = new RegExp(`\\s*${paramName}\\s+[^;]+;\\s*\\n?`, 'g');
              const originalContent = locationContent;
              locationContent = locationContent.replace(paramRegex, '');
             
            }
          });
          
          // 更新整个location块，保持原始缩进和格式
          let newLocationBlock = `${indentation}location ${location.path} {${locationContent}\n${indentation}}`;
          serverBlockConfig = serverBlockConfig.replace(locationMatch[0], newLocationBlock);
        } else {
          // 没找到匹配的location块，添加一个新的
          // 分析原始server块的缩进风格
          const serverLines = serverBlockConfig.split('\n');
          let serverIndent = '';
          for (const line of serverLines) {
            if (line.trim() && line.match(/^\s+/) && !line.includes('location')) {
              serverIndent = line.match(/^\s+/)[0];
              break;
            }
          }
          serverIndent = serverIndent || '    ';
          const locationIndent = serverIndent + '    ';
          
          // 构建新的location块
          let newLocationBlock = `\n${serverIndent}location ${location.path} {
${locationIndent}proxy_pass ${location.proxy_pass};`;
          
          // 重要修复：添加超时参数（如果启用）
          if (location.proxy_connect_timeout && location.proxy_connect_timeout.enabled) {
            newLocationBlock += `\n${locationIndent}proxy_connect_timeout ${location.proxy_connect_timeout.value};`;
          }
          
          if (location.proxy_read_timeout && location.proxy_read_timeout.enabled) {
            newLocationBlock += `\n${locationIndent}proxy_read_timeout ${location.proxy_read_timeout.value};`;
          }
          
          if (location.proxy_send_timeout && location.proxy_send_timeout.enabled) {
            newLocationBlock += `\n${locationIndent}proxy_send_timeout ${location.proxy_send_timeout.value};`;
          }
          
          newLocationBlock += `\n${serverIndent}}`;
          
          // 定位添加位置 - 在server块最后一个大括号之前
          const lastBracket = serverBlockConfig.lastIndexOf('}');
          if (lastBracket > 0) {
            serverBlockConfig = serverBlockConfig.substring(0, lastBracket) + newLocationBlock + '\n' + serverBlockConfig.substring(lastBracket);
          } else {
            // 如果找不到大括号（不应该发生），添加到末尾
            serverBlockConfig += newLocationBlock;
          }
        }
      });
    } else {
      // 新增模式：创建一个标准格式的server块
      const listen = Array.isArray(data.editingBlock.listen) ? data.editingBlock.listen.join(' ') : data.editingBlock.listen;
      const serverName = Array.isArray(data.editingBlock.server_name) ? data.editingBlock.server_name.join(' ') : data.editingBlock.server_name;
      
      // 添加server块开始
      serverBlockConfig = `server {\n`;
      
      // 添加listen
      let listenConfig = `    listen ${listen}`;
      if (data.advancedParams.backlog && data.advancedParams.backlog.enabled && !listen.includes('backlog=')) {
        listenConfig += ` backlog=${data.advancedParams.backlog.value}`;
      }
      serverBlockConfig += `${listenConfig};\n`;
      
      // 添加server_name（如果有）
    if (serverName && serverName.trim() !== '') {
      serverBlockConfig += `    server_name ${serverName};\n`;
    }
    
      // 添加proxy_next_upstream（如果有）
    if (proxyNextUpstream) {
      serverBlockConfig += `    proxy_next_upstream ${proxyNextUpstream};\n`;
    }
    
      // 添加client_max_body_size参数（如果有）
    if (data.advancedParams && data.advancedParams.client_max_body_size && data.advancedParams.client_max_body_size.enabled) {
      serverBlockConfig += `    client_max_body_size ${data.advancedParams.client_max_body_size.value};\n`;
    }
    
    // 添加locations配置
    data.locations.forEach(location => {
        serverBlockConfig += `\n    location ${location.path} {
        proxy_pass ${location.proxy_pass};`;
        
        // 检查location是否应该添加基本header参数
        // 只有当显式指定basic_headers为true时才添加
        if (location.basic_headers === true) {
            serverBlockConfig += `
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;`;
        }
        
        // 确保添加启用的超时参数
        if (location.proxy_connect_timeout && location.proxy_connect_timeout.enabled) {
            serverBlockConfig += `
        proxy_connect_timeout ${location.proxy_connect_timeout.value};`;
        }
        
        if (location.proxy_read_timeout && location.proxy_read_timeout.enabled) {
            serverBlockConfig += `
        proxy_read_timeout ${location.proxy_read_timeout.value};`;
        }
        
        if (location.proxy_send_timeout && location.proxy_send_timeout.enabled) {
            serverBlockConfig += `
        proxy_send_timeout ${location.proxy_send_timeout.value};`;
        }
        
        serverBlockConfig += `
    }`;
    });
    
      // 添加server块结束
      serverBlockConfig += `\n}`;
    }
    
    // 更新服务器块配置
    data.editingBlock.config = serverBlockConfig;
    
    // 在data.serverBlocks中查找是否已存在相同ID的服务器块
    let existingIndex = -1;
    if (data.editType === 'edit' && data.editingBlock.id) {
      existingIndex = data.serverBlocks.findIndex(block => block.id === data.editingBlock.id);
    }
    
    // 根据查找结果更新或添加服务器块
    if (data.editType === 'edit') {
      if (existingIndex !== -1) {
        // 如果找到了原有对象，替换它
        data.serverBlocks[existingIndex] = data.editingBlock;
      } else {
        // 如果没找到（可能ID变了），添加新的
        data.serverBlocks.push(data.editingBlock);
      }
    } else {
      // 新增模式：直接添加到服务器块列表
      data.serverBlocks.push(data.editingBlock);
    }
    
    // 准备要更新的文件
    const targetFilePath = data.editingBlock.file_path;
    const filesToUpdate = [];
    
    // 查找目标文件是否存在
    const targetFile = props.nginxConfig.find(file => {
      // @ts-ignore - 忽略TypeScript类型错误
      return file.file_path === targetFilePath;
    });
    
    if (targetFile) {
      // 目标文件存在，添加到更新列表
      // @ts-ignore - 忽略TypeScript类型错误
      let content = String(targetFile.content || '');
      
      // 修改：不再移除现有upstream块，而是尝试修改它
      let upstreamModified = false;
      
      // 新增：先尝试修改现有的upstream块
      const modifyResult = modifyExistingUpstream(content, data.upstreamName, upstreamConfig);
      content = modifyResult.content;
      upstreamModified = modifyResult.modified;
      
      // 新增：判断是编辑还是新增模式
      const isEditMode = data.editType === 'edit';
      
      // 如果是编辑模式，尝试使用精确定位找到并替换原有server块
      if (isEditMode) {
        // 首先尝试使用findExactServerBlock精确定位原有server块
        const originalServerBlock = findExactServerBlock(content, {
          listen: data.editingBlock.listen,
          server_name: data.editingBlock.server_name
        });
        
        if (originalServerBlock) {
      
          // 找到了精确匹配的server块，使用replaceAtPosition替换它
          content = replaceAtPosition(content, originalServerBlock, serverBlockConfig);
        } else {
          
          // 如果精确定位失败，尝试使用upstream名称查找关联的server块
          const contentBackup = content; // 备份原内容
          content = removeServerBlocksByUpstreamName(content, data.upstreamName);
          
          // 如果内容未发生变化，说明没有找到关联的server块
          if (content === contentBackup) {
          
            // 在文件末尾添加服务器块配置
            if (content && !content.endsWith('\n\n')) {
              // 确保有足够的空行
              content += content.endsWith('\n') ? '\n' : '\n\n';
            }
            content += serverBlockConfig;
          } else {
           
            // 在文件末尾添加新的server块配置
            if (content && !content.endsWith('\n\n')) {
              // 确保有足够的空行
              content += content.endsWith('\n') ? '\n' : '\n\n';
            }
            content += serverBlockConfig;
          }
        }
      } else {
        // 新增模式：在文件末尾添加服务器块配置
        if (content && !content.endsWith('\n\n')) {
          // 确保有足够的空行
          content += content.endsWith('\n') ? '\n' : '\n\n';
        }
        content += serverBlockConfig;
      }
      
      // 如果upstream块没有被修改（可能不存在），则添加到文件开头
      if (!upstreamModified) {
        // 在文件开头添加upstream配置(先添加新的空行再添加upstream配置)
        let newContent = upstreamConfig;
        if (content && !content.startsWith('\n')) {
          newContent += '\n';
        }
        
        // 将处理过的原始内容添加到新内容后面
        newContent += content;
        content = newContent;
      }
      
      // 规范化连续空行
      content = content.replace(/\n{3,}/g, '\n\n');
      
      filesToUpdate.push({
        file_path: targetFilePath,
        content: normalizeEmptyLines(content)
      });
    } else {
      // 如果是新文件，直接添加
      let newContent = upstreamConfig + '\n' + serverBlockConfig;
      filesToUpdate.push({
        file_path: targetFilePath,
        content: normalizeEmptyLines(newContent)
      });
    }
    
    // 通知父组件配置已更改
    emit('config-changed', {
      type: 'server-blocks',
      config: filesToUpdate,
      blocks: data.serverBlocks.map(block => ({
        file_path: block.file_path,
        content: block.config
      })),
      syntaxErrors: [],
      pendingChanges: false
    });
    
    // 关闭对话框
    data.showAddDialog = false;
    data.editingBlock = null;
    
    // 重置表单状态
    configTemplate.value = '';
    
    ElMessage.success('业务配置添加成功');
  } catch (error) {

    ElMessage.error('保存业务配置失败：' + error.message);
  }
}

// 验证服务器块和upstream配置
function validateServerBlockWithUpstream() {
  if (!data.editingBlock) return false;
  
  if (!data.editingBlock.listen) {
    ElMessage.warning('请填写监听端口');
    return false;
  }
  
  if (!data.editingBlock.file_path) {
    ElMessage.warning('请填写配置文件路径');
    return false;
  }
  
  if (!data.upstreamName) {
    ElMessage.warning('请填写上游服务名称');
    return false;
  }
  
  // 确保至少有一个上游服务器，且地址不为空
  if (data.upstreamServers.length === 0) {
    ElMessage.warning('请添加至少一个上游服务器');
    return false;
  }
  
  const invalidServer = data.upstreamServers.find(server => !server.address);
  if (invalidServer) {
    ElMessage.warning('请填写所有上游服务器的地址');
    return false;
  }
  
  // 确保至少有一个路径配置，且路径不为空，代理地址不为空
  if (data.locations.length === 0) {
    ElMessage.warning('请添加至少一个路径配置');
    return false;
  }
  
  const invalidLocation = data.locations.find(loc => !loc.path || !loc.proxy_pass);
  if (invalidLocation) {
    ElMessage.warning('请填写所有路径的路径和代理地址');
    return false;
  }
  
  return true;
}

// 保存服务器块
function handleSaveServerBlock(formData) {
  try {
    // 更新data.editingBlock
    data.editingBlock = formData;

    // 同步从对话框传回的upstream名称
    if (formData.upstreamName) {
      data.upstreamName = formData.upstreamName;
    }

    // 同步上游服务器和其他upstream相关配置
    if (formData.upstreamServers) {
      data.upstreamServers = formData.upstreamServers;
    }

    if (formData.upstreamStrategy) {
      data.upstreamStrategy = formData.upstreamStrategy;
    }

    if (formData.upstreamParams) {
      data.upstreamParams = formData.upstreamParams;
    }

    if (formData.locations) {
      data.locations = formData.locations;
    }
    
    // 同步serverParams到advancedParams - 修复此处以确保数据一致性
    if (formData.serverParams) {
      // 如果data.advancedParams不存在，初始化它
      if (!data.advancedParams) {
        data.advancedParams = {};
      }
      
      // 映射backlog参数
      if (formData.serverParams.backlog) {
        data.advancedParams.backlog = formData.serverParams.backlog;
      }
      
      // 映射client_max_body_size参数
      if (formData.serverParams.client_max_body_size) {
        data.advancedParams.client_max_body_size = formData.serverParams.client_max_body_size;
      }
    }
    
    // 添加：更新预览
    updateConfigPreview();
    
    // 调用原有的保存方法
    saveServerBlockWithUpstream();
  } catch (error) {
    ElMessage.error('保存业务配置失败：' + error.message);
  }
}

// 辅助函数：从配置中提取特定参数的值
function extractConfigValue(config, paramName, defaultValue) {
  if (!config) return defaultValue;
  
  const regex = new RegExp(`${paramName}\\s+([^;]+);`);
  const match = config.match(regex);
  
  return match && match[1] ? match[1] : defaultValue;
}

// 判断服务器块是否有关联的upstream
function hasRelatedUpstream(block) {
  // 没有block或没有locations，直接返回false
  if (!block || !block.locations || !Array.isArray(block.locations)) {
    return false;
  }
  
  // 遍历所有location，查找是否有proxy_pass指向upstream
  for (const location of block.locations) {
    if (location.proxy_pass) {
      // 从proxy_pass中提取upstream名称，例如从http://zlemr提取zlemr
      const upstreamMatch = location.proxy_pass.match(/http:\/\/([^\/\s:]+)/);
      if (upstreamMatch && upstreamMatch[1]) {
        const upstreamName = upstreamMatch[1];
        // 检查这个upstream名称是否存在于Nginx配置中
        if (findUpstreamByProxyPass(location.proxy_pass)) {
          return true;
        }
      }
    }
  }
  
  return false;
}

// 新增：添加watch以监听表单变化并实时更新预览
watch(() => [
  data.upstreamName, 
  data.upstreamServers, 
  data.upstreamStrategy, 
  data.upstreamParams,
  data.locations,
  data.advancedParams,
  data.proxyTimeout
], () => {
  // 只在编辑模式下动态更新预览
  if (data.editingBlock && data.editType === 'edit' && data.showAddDialog) {
    updateConfigPreview();
  }
}, { deep: true });

// 新增：实时更新配置预览
function updateConfigPreview() {
  // 如果不在编辑模式或没有选中的块，直接返回
  if (!data.editingBlock || data.editType !== 'edit') return;
  
  try {
    // 获取原始配置
    const originalConfig = data.editingBlock.config || '';
    const originalUpstreamContent = data.editingBlock.firstUpstreamContent || '';
    
    // 解析原始server块中的特殊配置
    const proxyNextUpstreamMatch = originalConfig.match(/proxy_next_upstream\s+([^;]+);/);
    const proxyNextUpstream = proxyNextUpstreamMatch ? proxyNextUpstreamMatch[1] : '';
    
    // 构建upstream配置
    let upstreamConfig = '';
    
    // 如果有原始upstream内容，基于它进行更新，保持原始格式
    if (originalUpstreamContent) {
      // 完全保留原始upstream格式，只替换名称
      const upstreamNameRegex = /upstream\s+([^\s{]+)(\s*){/;
      const upstreamNameMatch = originalUpstreamContent.match(upstreamNameRegex);
      if (upstreamNameMatch) {
        // 提取原始upstream名称和大括号前的空格或无空格
        const originalName = upstreamNameMatch[1];
        const spaceBeforeBrace = upstreamNameMatch[2] || ''; // 保留原始格式，空格或无空格
        
        // 使用原始格式替换，确保保留原始的空格或无空格格式
        upstreamConfig = originalUpstreamContent.replace(
          new RegExp(`upstream\\s+${originalName.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}(\\s*){`), 
          `upstream ${data.upstreamName} {`
        );
      } else {
        // 保持原始格式，只替换名称
        upstreamConfig = originalUpstreamContent.replace(
          /upstream\s+([^{]+)\s*{/, 
          `upstream ${data.upstreamName} {`
        );
      }
      
      // 更新服务器列表 - 保持原有的缩进格式
      if (data.upstreamServers && data.upstreamServers.length > 0) {
        // 分析原始upstream内容中的server行格式
        const serverLineRegex = /(\s+)server\s+[^;]+;/;
        const serverLineMatch = originalUpstreamContent.match(serverLineRegex);
        const serverIndent = serverLineMatch ? serverLineMatch[1] : '        ';
        
        // 创建新的服务器列表字符串，保持原有缩进
        let newServers = '';
        data.upstreamServers.forEach(server => {
          if (!server.address) return;
          
          let serverStr = `${serverIndent}server ${server.address}`;
          
          // 修复：对weight参数的处理，使用对象结构
          if (typeof server.weight === 'object' && server.weight.enabled) {
            // 如果weight是对象并且enabled为true，使用value属性
            serverStr += ` weight=${server.weight.value}`;
          } else if (typeof server.weight === 'number' && server.weight > 1) {
            // 兼容旧格式：如果weight是数字且大于1，直接使用
            serverStr += ` weight=${server.weight}`;
          }
          
          if (server.max_fails && server.max_fails.enabled) {
            serverStr += ` max_fails=${server.max_fails.value}`;
          }
          
          if (server.fail_timeout && server.fail_timeout.enabled) {
            serverStr += ` fail_timeout=${server.fail_timeout.value}`;
          }
          
          if (server.backup && server.backup.enabled) {
            serverStr += ` backup`;
          }
          
          if (server.down && server.down.enabled) {
            serverStr += ` down`;
          }
          
          serverStr += ';';
          newServers += serverStr + '\n';
        });
        
        // 替换服务器行，保持原有的格式
        const serversRegex = /(\s+server\s+[^;]+;(\s*\n)?)+/;
        if (serversRegex.test(upstreamConfig)) {
        } else {
          // 没有找到原有的server行，尝试在括号后添加
          const braceIndex = upstreamConfig.indexOf('{');
          if (braceIndex > 0) {
            upstreamConfig = 
              upstreamConfig.substring(0, braceIndex + 1) + 
              '\n' + newServers + 
              (upstreamConfig.substring(braceIndex + 1).trim() ? upstreamConfig.substring(braceIndex + 1) : '}');
          }
        }
      }
    } else {
      // 如果没有原始内容，使用buildUpstreamConfig
      upstreamConfig = buildUpstreamConfig();
    }
    
    // 构建服务器块配置 - 保持原始格式
    let serverBlockConfig = '';
    
    // 优先使用原始配置，保留格式，但移除upstream部分
    if (originalConfig) {
      // 移除可能存在的upstream配置
      serverBlockConfig = originalConfig.replace(/upstream\s+[^\s{]+\s*{[\s\S]*?}\s*\n?/g, '');
      
      // 更新listen行，保持原有缩进
      const listenRegex = /(^\s*|\n\s*)listen\s+[^;]+;/g;
    const listen = Array.isArray(data.editingBlock.listen) ? data.editingBlock.listen.join(' ') : data.editingBlock.listen;
    
      let listenConfig = listen;
    if (data.advancedParams.backlog && data.advancedParams.backlog.enabled && !listenConfig.includes('backlog=')) {
      listenConfig += ` backlog=${data.advancedParams.backlog.value}`;
    }
      
      if (listenRegex.test(serverBlockConfig)) {
        serverBlockConfig = serverBlockConfig.replace(listenRegex, (match, prefix) => {
          return `${prefix}listen ${listenConfig};`;
        });
      }
      
      // 更新server_name行，保持原有缩进
    const serverName = Array.isArray(data.editingBlock.server_name) ? data.editingBlock.server_name.join(' ') : data.editingBlock.server_name;
    
    if (serverName && serverName.trim() !== '') {
        const serverNameRegex = /(^\s*|\n\s*)server_name\s+[^;]+;/g;
        if (serverNameRegex.test(serverBlockConfig)) {
          serverBlockConfig = serverBlockConfig.replace(serverNameRegex, (match, prefix) => {
            return `${prefix}server_name ${serverName};`;
          });
        }
      }
      
      // 更新location块，保持原有格式
      data.locations.forEach(location => {
        const locationRegex = new RegExp(`(\\s*)location\\s+${location.path.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*{([\\s\\S]*?)\\n\\s*}`, 'g');
        const locationMatch = locationRegex.exec(serverBlockConfig);
        
        if (locationMatch) {
          // 找到匹配的location块，更新其中的proxy_pass
          const indentation = locationMatch[1];
          let locationContent = locationMatch[2];
          
          // 更新proxy_pass行，保持原有缩进
          const proxyPassRegex = /(^\s*|\n\s*)proxy_pass\s+[^;]+;/g;
          if (proxyPassRegex.test(locationContent)) {
            locationContent = locationContent.replace(proxyPassRegex, (match, prefix) => {
              return `${prefix}proxy_pass ${location.proxy_pass};`;
            });
          } else {
            // 如果没有proxy_pass，根据原内容格式添加一个
            // 分析原始内容的缩进风格
            const contentLines = locationContent.split('\n');
            let contentIndent = '';
            for (const line of contentLines) {
              if (line.trim() && line.match(/^\s+/)) {
                contentIndent = line.match(/^\s+/)[0];
                break;
              }
            }
            contentIndent = contentIndent || (indentation + '    ');
            locationContent += `\n${contentIndent}proxy_pass ${location.proxy_pass};`;
          }
          
         
          
          // 重要修复：根据location参数的启用状态处理超时参数，确保保存正确的超时配置
          ['proxy_connect_timeout', 'proxy_read_timeout', 'proxy_send_timeout'].forEach(paramName => {
            const paramKey = paramName.replace(/^proxy_/, '');
            const param = location[paramKey];
            
         
            
            if (param && param.enabled) {
              // 如果参数被启用
              const paramRegex = new RegExp(`(\\s*)${paramName}\\s+[^;]+;`, 'g');
              if (paramRegex.test(locationContent)) {
                // 找到现有参数，更新它
                locationContent = locationContent.replace(paramRegex, (match, indent) => {
           
                  return `${indent}${paramName} ${param.value};`;
                });
            } else {
                // 没找到参数，添加一个，保持与其他行相同的缩进
                const contentLines = locationContent.split('\n');
                let contentIndent = '';
                for (const line of contentLines) {
                  if (line.trim()) {
                    const match = line.match(/^\s+/);
                    if (match) {
                      contentIndent = match[0];
                    break;
                    }
                  }
                }
                contentIndent = contentIndent || (indentation + '    ');
            
                locationContent += `\n${contentIndent}${paramName} ${param.value};`;
              }
            } else if (param) {
              // 如果超时参数被禁用，移除它
              const paramRegex = new RegExp(`\\s*${paramName}\\s+[^;]+;\\s*\\n?`, 'g');
              const originalContent = locationContent;
              locationContent = locationContent.replace(paramRegex, '');
             
            }
          });
          
          // 更新整个location块，保持原始缩进和格式
          let newLocationBlock = `${indentation}location ${location.path} {${locationContent}\n${indentation}}`;
          serverBlockConfig = serverBlockConfig.replace(locationMatch[0], newLocationBlock);
        }
      });
      } else {
      // 如果没有原始配置，创建一个新的标准格式
      // 确保listen和server_name数据正确
      const listen = Array.isArray(data.editingBlock.listen) ? data.editingBlock.listen.join(' ') : data.editingBlock.listen;
      const serverName = Array.isArray(data.editingBlock.server_name) ? data.editingBlock.server_name.join(' ') : data.editingBlock.server_name;
      
      serverBlockConfig = `server {\n`;
      serverBlockConfig += `    listen ${listen}`;
      
      if (data.advancedParams.backlog && data.advancedParams.backlog.enabled && !listen.includes('backlog=')) {
        serverBlockConfig += ` backlog=${data.advancedParams.backlog.value}`;
      }
      serverBlockConfig += `;\n`;
      
      if (serverName && serverName.trim() !== '') {
        serverBlockConfig += `    server_name ${serverName};\n`;
      }
      
      if (proxyNextUpstream) {
        serverBlockConfig += `    proxy_next_upstream ${proxyNextUpstream};\n`;
      }
      
      if (data.advancedParams && data.advancedParams.client_max_body_size && data.advancedParams.client_max_body_size.enabled) {
        serverBlockConfig += `    client_max_body_size ${data.advancedParams.client_max_body_size.value};\n`;
      }
      
      // 添加location块，使用标准缩进
      data.locations.forEach(location => {
        serverBlockConfig += `\n    location ${location.path} {
        proxy_pass ${location.proxy_pass};`;
        
        // 检查location是否应该添加基本header参数
        // 只有当显式指定basic_headers为true时才添加
        if (location.basic_headers === true) {
            serverBlockConfig += `
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;`;
        }
        
        // 确保添加启用的超时参数
        if (location.proxy_connect_timeout && location.proxy_connect_timeout.enabled) {
            serverBlockConfig += `
        proxy_connect_timeout ${location.proxy_connect_timeout.value};`;
        }
        
        if (location.proxy_read_timeout && location.proxy_read_timeout.enabled) {
            serverBlockConfig += `
        proxy_read_timeout ${location.proxy_read_timeout.value};`;
        }
        
        if (location.proxy_send_timeout && location.proxy_send_timeout.enabled) {
            serverBlockConfig += `
        proxy_send_timeout ${location.proxy_send_timeout.value};`;
        }
        
        serverBlockConfig += `
    }`;
      });
      
      // 添加server块结束
      serverBlockConfig += `\n}`;
    }
    
    // 移除多余的空行，保留基本格式
    serverBlockConfig = serverBlockConfig.replace(/\n{3,}/g, '\n\n');
    
    // 更新服务器块配置 - 这里不保存到data.serverBlocks中，只更新预览
    data.editingBlock.config = serverBlockConfig;
    data.editingBlock.firstUpstreamContent = upstreamConfig;
  } catch (error) {
    console.error('更新预览时发生错误:', error);
  }
}

// 规范化内容中的空行 - 增强版，保留更多原始格式
function normalizeEmptyLines(content) {
  if (!content) return content;
  
  // 处理upstream和server块之间的空行，以及其内部的空行
  let normalized = content;
  
  // 标准化upstream格式 - 确保大括号前有空格
  normalized = normalized.replace(/upstream\s+([^\s{]+)\s*{/g, 'upstream $1 {');
  
  // 处理upstream块内部的多余空行
  normalized = normalized.replace(/(upstream\s+[^\s{]+\s*{)([^}]*)\}/gs, (match, start, body) => {
    // 移除内部多余空行，确保server行之间没有空行
    const cleanBody = body
      .replace(/\n\s*\n/g, '\n')  // 先移除所有多余空行
      .replace(/(\s*server\s+[^;]+;)\n+(\s*server)/g, '$1\n$2');  // 确保server行之间没有多余空行
    return `${start}${cleanBody}}`;
  });
  
  // 处理server块内部的多余空行
  normalized = normalized.replace(/(server\s*{)([^}]*)\}/gs, (match, start, body) => {
    // 移除内部多余空行
    const cleanBody = body.replace(/\n\s*\n\s*\n/g, '\n\n');
    return `${start}${cleanBody}}`;
  });
  
  // 处理location块内部的多余空行
  normalized = normalized.replace(/(location\s+[^{]*{)([^}]*)\}/gs, (match, start, body) => {
    // 移除内部多余空行
    const cleanBody = body.replace(/\n\s*\n/g, '\n');
    return `${start}${cleanBody}}`;
  });
  
  // 确保upstream块和server块之间只有一个换行符
  normalized = normalized.replace(/}\s*\n+(\s*server\s*{)/g, '}\n$1');
  
  // 移除server块末尾的多余空行
  normalized = normalized.replace(/\}\s*\n\s*$/g, '}');
  
  return normalized;
}

// 添加格式化服务器配置预览的函数
function formatConfigPreview(serverBlock) {
  if (!serverBlock) return '';
  
  let preview = '';
  
  // 获取所有相关的upstream
  const upstreams = collectRelatedUpstreams(serverBlock);
  
  // 添加upstream预览
  if (upstreams && upstreams.length > 0) {
    upstreams.forEach(upstream => {
      preview += upstream.content + '\n';
    });
  }
  
  // 添加server块预览
  if (serverBlock.config) {
    preview += serverBlock.config;
  }
  
  // 规范化预览内容的格式
  return normalizeEmptyLines(preview);
}
</script>

<template>
  <div class="server-block-manager">
    <div class="manager-header">
      <h2 class="title">
        <el-icon><svg-icon name="server" /></el-icon>
        负载均衡
      </h2>
      <div class="header-controls">
        <el-input v-model="data.filter" placeholder="搜索反向代理" clearable size="small" class="search-input" />
        <el-button type="primary" size="small" @click="addServerBlock">
          <el-icon><svg-icon name="ep:plus" /></el-icon>
          添加业务
        </el-button>
      </div>
    </div>

    <el-divider />

    <div class="main-content">
      <el-skeleton :rows="5" animated v-if="data.loading" />

      <el-empty v-else-if="data.serverBlocks.length === 0" description="暂无配置" />

      <div v-else class="server-blocks-container">
        <div class="blocks-list">
          <div v-for="(block, index) in filteredServerBlocks()" :key="block.id" class="block-item" :class="{
            'active': index === data.selectedBlockIndex,
            'disabled': !block.enabled,
            'server-block-disabled': !block.enabled
          }" @click="selectServerBlock(index)">
            <div class="block-info">
              <el-tooltip placement="right" :popper-class="'custom-tooltip'">
                <template #content>
                  <div class="block-info-tooltip">
                    <div class="tooltip-title">{{ block.name }}</div>
                    <div class="tooltip-item">
                      <span class="label">状态:</span>
                      <span>{{ block.enabled ? '已启用' : '已停用' }}</span>
                    </div>
                    <div class="tooltip-item">
                      <span class="label">文件路径:</span>
                      <span>{{ block.file_path }}</span>
                    </div>
                    <div class="tooltip-item">
                      <span class="label">路径数量:</span>
                      <span>{{ block.locations.length }}个</span>
                    </div>
                  </div>
                </template>
                <div class="block-name">
                  <el-icon>
                    <svg-icon :name="block.enabled ? 'server' : 'server-off'" />
                  </el-icon>
                  <span>{{ block.name }}</span>
                  <el-tag size="small" :type="block.enabled ? 'success' : 'danger'" class="status-tag">
                    {{ block.enabled ? '已启用' : '已停用' }}
                  </el-tag>
                </div>
              </el-tooltip>
              <div class="block-details">
                <span class="detail-item">
                  <el-icon><svg-icon name="ep:folder" /></el-icon>
                  {{ block.file_path }}
                </span>
                <span class="detail-item">
                  <el-icon><svg-icon name="folder" /></el-icon>
                  路径: {{ block.locations.length }}个
                </span>
              </div>
            </div>
            <div class="block-actions">
              <el-switch :model-value="block.enabled" :active-color="'var(--el-color-success)'"
                :inactive-color="'var(--el-color-danger)'" @click.stop="toggleServerBlock(index)" inline-prompt
                :active-text="'开'" :inactive-text="'关'" />

              <!-- HTTP/HTTPS 转换按钮 -->
              <el-tooltip :content="getHttpsTooltip(block)" placement="top">
                <el-button :type="isHttps(block) ? 'success' : 'warning'" size="small"
                  @click.stop="isHttps(block) ? convertToHttp(index) : convertToHttps(index)" circle>
                  <el-icon>
                    <svg-icon :name="isHttps(block) ? 'ep:refresh' : 'ep:refresh'" />
                  </el-icon>
                </el-button>
              </el-tooltip>

              <!-- 只有当服务器块有关联的upstream时才显示编辑按钮 -->
              <el-button type="primary" size="small" v-if="hasRelatedUpstream(block)" @click.stop="editServerBlock(index)" :icon="Edit" circle />
            </div>
          </div>
        </div>

        <div class="block-details-panel" v-if="data.selectedBlockIndex !== -1">
          <div class="panel-header">
            <h3>
              {{ data.serverBlocks[data.selectedBlockIndex].name }} 详情
            </h3>
            
          </div>

          <div class="panel-content">
            <div class="config-section">
              <h4>服务器配置预览</h4>
              
              <!-- 修改关联的upstream配置预览和服务器块配置预览成统一格式 -->
              <pre class="config-preview">{{ formatConfigPreview(data.serverBlocks[data.selectedBlockIndex]) }}</pre>
            </div>
          </div>
        </div>

        <div class="no-selection" v-else>
          <div class="no-selection-content">
            <svg-icon name="select" class="select-icon" />
            <p>请从左侧选择一个业务查看详情</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加反向代理对话框 -->
    <AddBusinessDialog 
      v-model:visible="data.showAddDialog"
      :editing-data="data.editingBlock"
      :is-edit="!!data.editingBlock?.id && data.serverBlocks.some(b => b.id === data.editingBlock?.id)"
      :file-paths="data.filePaths"
      @save="handleSaveServerBlock"
      @cancel="cancelEdit"
      @create-file-path="path => data.filePaths.push(path)"
    />

  </div>
</template>

<style lang="scss" scoped>
.server-block-manager {
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        font-size: 20px;
        color: var(--el-color-primary);
      }
    }

    .header-controls {
      display: flex;
      gap: 12px;

      .search-input {
        width: 240px;
      }
    }
  }

  .main-content {
    position: relative;
    min-height: 400px;
  }

  .server-blocks-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;

    .blocks-list {
      width: 300px;
      flex: 0 0 auto;
      height: 500px;
      overflow-y: auto;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 10px;

      .block-item {
        padding: 12px;
        border-bottom: 1px solid var(--el-border-color-light);
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        flex-direction: column;

        &:hover {
          background-color: var(--el-fill-color-light);
        }

        &.active {
          background-color: var(--el-color-primary-light-9);
          border-left: 3px solid var(--el-color-primary);
        }

        &.disabled {
          opacity: 0.7;
          background-color: var(--el-fill-color-lighter);

          &:hover {
            background-color: var(--el-fill-color-light);
          }
        }

        .block-info {
          flex: 1;
          margin-bottom: 8px;

          .block-name {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            margin-bottom: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            .status-tag {
              margin-left: 8px;
              flex-shrink: 0;
            }
          }

          .block-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 12px;
            color: var(--el-text-color-secondary);

            .detail-item {
              display: flex;
              align-items: center;
              gap: 4px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;

              .el-icon {
                flex-shrink: 0;
              }
            }
          }
        }

        .block-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px dashed var(--el-border-color-lighter);
          padding-top: 8px;
          width: 100%;
          gap: 8px;
          /* 添加间距 */

          .el-button {
            padding: 4px;
          }
        }

        .block-info-tooltip {
          font-size: 12px;
          max-width: 220px;

          .tooltip-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--el-color-primary);
          }

          .tooltip-item {
            margin-bottom: 3px;

            .label {
              font-weight: 500;
              margin-right: 5px;
            }
          }
        }
      }
    }

    .block-details-panel {
      flex: 1;
      min-width: 300px;
      height: 500px;
      overflow: hidden;
      border: 1px solid var(--el-border-color-light);
      border-radius: 6px;
      background-color: var(--el-bg-color);
      display: flex;
      flex-direction: column;

      .panel-header {
        padding: 12px 16px;
        border-bottom: 1px solid var(--el-border-color-light);
        background-color: var(--el-fill-color-light);
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
        }

        .panel-actions {
          display: flex;
          gap: 8px;
        }
      }

      .panel-content {
        flex: 1;
        padding: 16px;
        overflow: auto;
        height: calc(100% - 52px);

        .locations-list {
          margin-bottom: 20px;

          h4 {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 14px;
            color: var(--el-text-color-regular);
          }
        }

        .location-detail {
          margin-bottom: 20px;

          h4 {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 14px;
            color: var(--el-text-color-regular);
          }
        }

        .config-section {
          margin-top: 24px;

          h4,
          h5 {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 14px;
            color: var(--el-color-primary);
          }
          
          .config-preview {
            white-space: pre-wrap;
            padding: 10px;
            background-color: var(--el-fill-color-light);
            border-radius: 4px;
            margin-bottom: 10px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 12px;
            line-height: 1.5;
            overflow-x: auto;
          }
        }
      }
    }

    .no-selection {
      flex: 1;
      border: 1px solid var(--el-border-color-light);
      border-radius: 6px;
      background-color: var(--el-bg-color);
      display: flex;
      align-items: center;
      justify-content: center;

      .no-selection-content {
        text-align: center;

        .select-icon {
          width: 64px;
          height: 64px;
          color: var(--el-color-info-light-5);
          margin-bottom: 16px;
        }

        p {
          color: var(--el-text-color-secondary);
          font-size: 14px;
        }
      }
    }
  }

  .text-danger {
    color: var(--el-color-danger);
  }

  .text-success {
    color: var(--el-color-success);
  }

  // 新增样式
  .el-form-item-hint {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }

  .checkbox-item {
    margin-bottom: 8px;

    .checkbox-desc {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-left: 24px;
      margin-top: 2px;
    }
  }

  // 模板信息样式
  .template-info {
    margin-top: 16px;
    padding: 12px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;

    .template-title {
      font-weight: 600;
      margin-bottom: 8px;
      color: var(--el-color-primary);
    }

    .template-content {
      .template-description {
        font-size: 13px;
        line-height: 1.5;
        color: var(--el-text-color-regular);
        margin-bottom: 8px;
      }

      .config-preview {
        font-family: monospace;
        white-space: pre-wrap;
        background-color: var(--el-bg-color);
        padding: 12px;
        border-radius: 4px;
        color: var(--el-text-color-primary);
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
      }
    }
  }

  // 添加禁用状态的视觉提示
  :deep(.el-switch.is-disabled) {
    opacity: 0.6;
    cursor: not-allowed;
  }

  // 如果location所在的server block被禁用，增加视觉提示
  .server-block-disabled {
    .location-item {
      opacity: 0.8;
      background-color: var(--el-fill-color-lighter);
    }
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .server-manager {
    .header-controls {
      width: 100%;
      flex-direction: column;

      .search-input {
        width: 100%;
      }
    }
  }

  .server-blocks-container {
    flex-direction: column;

    .blocks-list {
      width: 100%;
      max-height: 300px;
      height: 300px;
    }

    .selected-block {
      width: 100%;
      height: 300px;
    }

    .block-details-panel {
      width: 100%;
      height: 300px;
    }
  }
}

.upstream-info {
  margin-top: 4px;
}

.upstream-detail {
  margin-bottom: 16px;
  padding: 10px;
  border-radius: 4px;
  background-color: var(--el-fill-color-lighter);
  
  h4 {
    margin-top: 0 !important;
    margin-bottom: 8px !important;
    color: var(--el-color-primary) !important;
  }
}

.upstream-server-item {
  margin-bottom: 12px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  padding: 12px;
  
  .server-basic-info {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .server-address {
      flex: 2;
    }
    
    .server-weight {
      width: 120px;
    }
  }
  
  .server-advanced-params {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px dashed var(--el-border-color-lighter);
  }
}

.location-item {
  margin-bottom: 12px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  padding: 12px;
  
  .location-row {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .location-path {
      width: 100px;
    }
    
    .location-proxy {
      flex: 2;
    }
    
    .location-template {
      width: 140px;
    }
  }
}

.advanced-param-item,
.timeout-param-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
  
  .param-name {
    min-width: 160px;
    font-family: monospace;
  }
  
  .param-value {
    width: 100px;
  }
  
  .param-description {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }
}

.timeout-params-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.advanced-params-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.advanced-param-group {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  padding: 12px;
  
  .param-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: var(--el-text-color-primary);
  }
}

.advanced-param-item,
.timeout-param-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
  
  .param-name {
    min-width: 160px;
    font-family: monospace;
  }
  
  .param-value {
    width: 100px;
  }
  
  .param-description {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }
}

.timeout-params-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-preview-container {
  margin-top: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
  
  .preview-header {
    background-color: var(--el-fill-color-light);
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
  }
  
  .config-preview {
    background-color: var(--el-fill-color-darker);
    color: var(--el-text-color-primary);
    padding: 12px;
    margin: 0;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 12px;
    max-height: 400px;
    overflow: auto;
  }
}
</style>