const Layout = () => import("@/layout/index.vue");
let children = [
  {
    path: "analysis_platform",
    name: "analysis_platform",
    component: () => import("@/views/visualization_platform/index.vue"),
    meta: {
      title: "智联面板",
      cache: true,
      sidebar: false,
      auth: ["admin", "visual_analysis.browse"],
      activeMenu: "/visual_analysis",
    },
    children: [
      {
        path: "analysis_iframe",
        name: "analysis_iframe",
        component: () => import("@/views/visualization_platform/dashbord.vue"),
        meta: {
          title: "智联面板",
          copyright: false,
          cache: true,
          sidebar: false,
          activeMenu: "/visual_analysis",
        },
      },
    ],
  },
];
export default {
  path: "/visual_analysis",
  component: Layout,
  redirect: "/visual_analysis/analysis_platform",
  name: "visual_analysis",
  meta: {
    auth: ["admin", "visual_analysis.browse"],
    title: "运营分析",
    icon: "analysis",
  },
  children,
};
