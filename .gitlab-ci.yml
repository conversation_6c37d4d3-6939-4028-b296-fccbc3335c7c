image: node:20

stages:
  - install
  - test
  - deploy
  - package

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - .pnpm-store/
    - node_modules/

before_script:
  - npm install -g pnpm
  - export NODE_OPTIONS="--max-old-space-size=16384"

install_dependencies:
  stage: install
  script:
    - pnpm install
  tags:
    - zlsoft-platform-development-vue3

test_project:
  stage: test
  script:
    - pnpm run build:test
  tags:
    - zlsoft-platform-development-vue3

deploy_project:
  stage: deploy
  script:
    - npm run build
    - rm -rf /usr/local/publish_products/zlsoft-platform-development-vue3/*
    - cp -r dist/* /usr/local/publish_products/zlsoft-platform-development-vue3/
  only:
    - master
  tags:
    - zlsoft-platform-development-vue3

package_project:
  stage: package
  script:
    - pnpm run build
    - mv dist /usr/local/dist/${CI_COMMIT_TAG}
  artifacts:
    paths:
      - /usr/local/dist/${CI_COMMIT_TAG}
  only:
    - tags
