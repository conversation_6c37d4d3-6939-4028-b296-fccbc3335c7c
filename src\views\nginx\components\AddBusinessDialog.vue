<template>
  <el-drawer v-model="dialogVisible" :title="isEdit ? '编辑业务' : '添加业务'" size="70%" direction="rtl" :close-on-click-modal="false">
    <el-form label-width="120px" v-if="formData" class="drawer-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务器名称">
            <el-input v-model="formData.server_name" placeholder="example.com" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监听端口">
            <el-input v-model="formData.listen" placeholder="80" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 添加Upstream配置部分 -->
      <el-divider content-position="left">
        <el-icon><svg-icon name="ep:connection" /></el-icon>
        上游服务器配置
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="业务名称">
            <el-input v-model="upstreamName" placeholder="upstream" @input="syncUpstreamName" :disabled="isEdit" />
            <div class="el-form-item-hint">{{ isEdit ? '编辑模式下不可修改业务名称' : '负载均衡组的名称，用于在代理配置中引用' }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负载策略">
            <el-select v-model="upstreamStrategy" placeholder="请选择负载均衡策略" style="width: 100%">
              <el-option label="轮询(默认)" value="" />
              <el-option label="IP哈希" value="ip_hash" />
              <el-option label="最少连接" value="least_conn" />
              <el-option label="哈希" value="hash $request_uri" />
              <el-option label="随机" value="random" />
            </el-select>
            <div class="el-form-item-hint">选择请求分发到各服务器的策略</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="服务器列表">
        <div v-for="(server, index) in upstreamServers" :key="index" class="upstream-server-item">
          <div class="server-basic-info">
            <el-input v-model="server.address" placeholder="127.0.0.1:8080" class="server-address" />
            <el-button type="primary" size="small" @click="server.showAdvanced = !server.showAdvanced">
              {{ server.showAdvanced ? '隐藏高级设置' : '高级设置' }}
            </el-button>
            <el-button type="danger" :icon="Delete" @click="removeUpstreamServer(index)" circle plain size="small" />
          </div>
          
          <!-- 每个服务器的高级参数设置 -->
          <div v-if="server.showAdvanced" class="server-advanced-params">
            <div class="advanced-param-grid">
              <div class="advanced-param-item">
                <el-switch v-model="server.weight.enabled" />
                <div class="param-name">weight</div>
                <el-input-number v-model="server.weight.value" :disabled="!server.weight.enabled" 
                  :min="1" :max="100" placeholder="1" class="param-value" />
                <span class="param-description">服务器权重</span>
              </div>
              <div class="advanced-param-item">
                <el-switch v-model="server.max_fails.enabled" />
                <div class="param-name">max_fails</div>
                <el-input v-model="server.max_fails.value" :disabled="!server.max_fails.enabled" 
                  placeholder="3" class="param-value" />
                <span class="param-description">最大失败次数</span>
              </div>
              <div class="advanced-param-item">
                <el-switch v-model="server.fail_timeout.enabled" />
                <div class="param-name">fail_timeout</div>
                <el-input v-model="server.fail_timeout.value" :disabled="!server.fail_timeout.enabled" 
                  placeholder="30s" class="param-value" />
                <span class="param-description">失败超时时间</span>
              </div>
              <div class="advanced-param-item">
                <el-switch v-model="server.backup.enabled" />
                <div class="param-name">backup</div>
                <span class="param-description">备用服务器</span>
              </div>
              <div class="advanced-param-item">
                <el-switch v-model="server.down.enabled" />
                <div class="param-name">down</div>
                <span class="param-description">标记为离线状态</span>
              </div>
            </div>
          </div>
        </div>
        <el-button type="primary" @click="addUpstreamServer" size="small" plain>
          <el-icon><svg-icon name="ep:plus" /></el-icon>
          添加服务器
        </el-button>
      </el-form-item>
      
      <!-- 全局负载均衡参数 -->
      <el-form-item label="全局连接参数">
        <div class="upstream-global-params">
          <div class="advanced-param-item">
            <el-switch v-model="upstreamParams.keepalive.enabled" />
            <div class="param-name">keepalive</div>
            <el-input v-model="upstreamParams.keepalive.value" :disabled="!upstreamParams.keepalive.enabled" 
              placeholder="32" class="param-value" />
            <span class="param-description">保持连接数</span>
          </div>
          <div class="advanced-param-item">
            <el-switch v-model="upstreamParams.zone.enabled" />
            <div class="param-name">zone</div>
            <el-input v-model="upstreamParams.zone.value" :disabled="!upstreamParams.zone.enabled" 
              placeholder="upstream_zone 10m" class="param-value" />
            <span class="param-description">共享内存区域</span>
          </div>
          <div class="advanced-param-item">
            <el-switch v-model="upstreamParams.keepalive_timeout.enabled" />
            <div class="param-name">keepalive_timeout</div>
            <el-input v-model="upstreamParams.keepalive_timeout.value" :disabled="!upstreamParams.keepalive_timeout.enabled" 
              placeholder="60s" class="param-value" />
            <span class="param-description">保持连接超时</span>
          </div>
        </div>
      </el-form-item>
      
      <el-divider content-position="left">
        <el-icon><svg-icon name="server" /></el-icon>
        服务器配置
      </el-divider>
      
      <el-form-item label="配置文件路径">
        <el-select v-model="formData.file_path" placeholder="选择文件路径" filterable allow-create
          default-first-option @create="createFilePath" style="width: 100%">
          <el-option v-for="path in filePaths" :key="path" :label="path" :value="path" />
        </el-select>
        <div class="el-form-item-hint">选择或创建保存此业务配置的文件路径</div>
      </el-form-item>

      <!-- 高级服务器参数 -->
      <el-form-item label="高级服务器参数">
        <div class="server-advanced-settings">
          <div class="advanced-param-item">
            <el-switch v-model="serverParams.backlog.enabled" />
            <div class="param-name">backlog</div>
            <el-input v-model="serverParams.backlog.value" :disabled="!serverParams.backlog.enabled" 
              placeholder="4096" class="param-value" />
            <span class="param-description">监听队列大小</span>
          </div>
          <div class="advanced-param-item">
            <el-switch v-model="serverParams.client_max_body_size.enabled" />
            <div class="param-name">client_max_body_size</div>
            <el-input v-model="serverParams.client_max_body_size.value" :disabled="!serverParams.client_max_body_size.enabled" 
              placeholder="60m" class="param-value" />
            <span class="param-description">客户端请求体最大大小</span>
          </div>
        </div>
      </el-form-item>

      <!-- 路径配置 -->
      <el-form-item label="路径配置">
        <div class="location-container">
          <div v-for="(location, index) in locations" :key="index" class="location-item">
            <div class="location-row">
              <el-input v-model="location.path" placeholder="/" class="location-path"  />
              <el-input v-model="location.proxy_pass" placeholder="http://upstream" class="location-proxy" >
                <template #prepend>代理到</template>
              </el-input>
              <el-select v-model="location.template" @change="val => applyLocationItemTemplate(index, val)" class="location-template">
                <el-option label="基本代理" value="basic_proxy" />
                <el-option label="WebSocket" value="websocket" />
              </el-select>
              <el-button type="danger" :icon="Delete" @click="removeLocation(index)" circle plain size="small" />
            </div>
            
            <!-- 每个路径的代理设置 - 修改为对所有模板生效 -->
            <div class="location-proxy-settings">
              <!-- 代理头部设置 -->
              <el-divider content-position="left">代理头部设置</el-divider>
              <div class="proxy-params-grid">
                <div class="advanced-param-item">
                  <el-switch v-model="location.proxy_host_header.enabled"  />
                  <div class="param-name">Host</div>
                  <el-input v-model="location.proxy_host_header.value" :disabled="!location.proxy_host_header.enabled" 
                    :placeholder="location.template === 'websocket' ? '$host:$server_port' : '$host'" class="param-value"  />
                </div>
                <div class="advanced-param-item">
                  <el-switch v-model="location.proxy_real_ip_header.enabled"  />
                  <div class="param-name">X-Real-IP</div>
                  <el-input v-model="location.proxy_real_ip_header.value" :disabled="!location.proxy_real_ip_header.enabled" 
                    placeholder="$remote_addr" class="param-value"  />
                </div>
                <div class="advanced-param-item">
                  <el-switch v-model="location.proxy_remote_host_header.enabled"  />
                  <div class="param-name">REMOTE-HOST</div>
                  <el-input v-model="location.proxy_remote_host_header.value" :disabled="!location.proxy_remote_host_header.enabled" 
                    placeholder="$remote_addr" class="param-value"  />
                </div>
                <div class="advanced-param-item">
                  <el-switch v-model="location.proxy_forwarded_for_header.enabled"  />
                  <div class="param-name">X-Forwarded-For</div>
                  <el-input v-model="location.proxy_forwarded_for_header.value" :disabled="!location.proxy_forwarded_for_header.enabled" 
                    placeholder="$proxy_add_x_forwarded_for" class="param-value"  />
                </div>
              </div>
              
              <!-- WebSocket特有的设置 -->
              <div v-if="location.template === 'websocket'">
                <el-divider content-position="left">WebSocket设置</el-divider>
                <div class="proxy-params-grid">
                  <div class="advanced-param-item">
                    <el-switch v-model="location.proxy_http_version.enabled"  />
                    <div class="param-name">proxy_http_version</div>
                    <el-input v-model="location.proxy_http_version.value" :disabled="!location.proxy_http_version.enabled" 
                      placeholder="1.1" class="param-value"  />
                  </div>
                  <div class="advanced-param-item">
                    <el-switch v-model="location.websocket_upgrade.enabled"  />
                    <div class="param-name">Upgrade</div>
                    <el-input v-model="location.websocket_upgrade.value" :disabled="!location.websocket_upgrade.enabled" 
                      placeholder="$http_upgrade" class="param-value"  />
                  </div>
                  <div class="advanced-param-item">
                    <el-switch v-model="location.websocket_connection.enabled"  />
                    <div class="param-name">Connection</div>
                    <el-input v-model="location.websocket_connection.value" :disabled="!location.websocket_connection.enabled" 
                      placeholder="upgrade" class="param-value"  />
                  </div>
                  <div class="advanced-param-item">
                    <el-switch v-model="location.websocket_keepalive.enabled"  />
                    <div class="param-name">Connection Keep-Alive</div>
                    <el-input v-model="location.websocket_keepalive.value" :disabled="!location.websocket_keepalive.enabled" 
                      placeholder="keep-alive" class="param-value"  />
                  </div>
                </div>
              </div>
              
              <!-- 代理超时参数 -->
              <el-divider content-position="left">代理超时参数</el-divider>
              <div class="proxy-params-grid">
                <div class="advanced-param-item">
                  <el-switch v-model="location.proxy_connect_timeout.enabled" />
                  <div class="param-name">proxy_connect_timeout</div>
                  <el-input v-model="location.proxy_connect_timeout.value" :disabled="!location.proxy_connect_timeout.enabled" 
                    placeholder="6s" class="param-value" />
                </div>
                <div class="advanced-param-item">
                  <el-switch v-model="location.proxy_read_timeout.enabled" />
                  <div class="param-name">proxy_read_timeout</div>
                  <el-input v-model="location.proxy_read_timeout.value" :disabled="!location.proxy_read_timeout.enabled" 
                    placeholder="60s" class="param-value" />
                </div>
                <div class="advanced-param-item">
                  <el-switch v-model="location.proxy_send_timeout.enabled" />
                  <div class="param-name">proxy_send_timeout</div>
                  <el-input v-model="location.proxy_send_timeout.value" :disabled="!location.proxy_send_timeout.enabled" 
                    placeholder="15s" class="param-value" />
                </div>
              </div>
            </div>
          </div>
          <el-button type="primary" @click="addLocationItem" size="small" plain>
            <el-icon><svg-icon name="ep:plus" /></el-icon>
            添加路径
          </el-button>
        </div>
      </el-form-item>
      
      <!-- 配置预览 -->
      <el-form-item label="配置预览">
        <pre class="config-preview">{{ generateConfigPreview() }}</pre>
      </el-form-item>

      <div class="drawer-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </div>
    </el-form>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch, defineProps, defineEmits, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Delete} from '@element-plus/icons-vue'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editingData: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  filePaths: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'save', 'cancel', 'create-file-path']);

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 表单数据
const formData = ref(null);
const upstreamName = ref('upstream');
const upstreamStrategy = ref('');


// 服务器参数
const serverParams = ref({
  backlog: { 
    enabled: false, 
    value: '4096' 
  },
  client_max_body_size: { 
    enabled: false, 
    value: '60m' 
  }
});

// 上游服务器列表
const upstreamServers = ref([]);

// 上游服务器全局参数
const upstreamParams = ref({
  keepalive: {
    enabled: false,
    value: '32',
    description: '保持连接数'
  },
  zone: {
    enabled: false,
    value: 'upstream_zone 10m',
    description: '共享内存区域'
  },
  keepalive_timeout: {
    enabled: false,
    value: '60s',
    description: '保持连接超时'
  }
});

// 位置配置
const locations = ref([]);

// 监听对话框可见性变化，初始化表单数据
watch(() => props.visible, (newValue) => {
  if (newValue) {
    // 重要修复：在初始化表单前重置 upstreamParams，防止不同业务间的状态污染
    upstreamParams.value = {
      keepalive: {
        enabled: false,
        value: '32',
        description: '保持连接数'
      },
      zone: {
        enabled: false,
        value: 'upstream_zone 10m',
        description: '共享内存区域'
      },
      keepalive_timeout: {
        enabled: false,
        value: '60s',
        description: '保持连接超时'
      }
    };
    
    // 重要修复：确保负载均衡策略也被重置，避免不同业务配置间的状态污染
    upstreamStrategy.value = '';
    
    // 调用初始化表单函数
    initFormData();
  }
});

// 初始化表单数据
function initFormData() {
  // 重要修复：重置 upstreamParams 以防止不同业务间状态污染
  upstreamParams.value = {
    keepalive: {
      enabled: false,
      value: '32',
      description: '保持连接数'
    },
    zone: {
      enabled: false,
      value: 'upstream_zone 10m',
      description: '共享内存区域'
    },
    keepalive_timeout: {
      enabled: false,
      value: '60s',
      description: '保持连接超时'
    }
  };
  
  if (props.isEdit && props.editingData) {
    // 编辑模式，使用传入的数据
    formData.value = JSON.parse(JSON.stringify(props.editingData));
    
    // 确保formData有正确的结构和类型
    if (formData.value) {
      // 确保主要属性存在
      if (!formData.value.serverExtraParams) {
        formData.value.serverExtraParams = {};
      }
      
    // 保存原始配置，以便后续使用
    if (props.editingData.rawConfig) {
      formData.value.originalRawConfig = props.editingData.rawConfig;
    }
    
    // 检查传入的数据是否有问题：比较rawConfig和config
    if (props.editingData.rawConfig !== props.editingData.config) {
      // 如果不一致，优先使用rawConfig
      if (props.editingData.rawConfig) {
        formData.value.config = props.editingData.rawConfig;
      }
    }
      
    // 处理 listen 参数 - 保留完整的原始参数
    if (props.editingData.rawConfig) {
      const listenRegex = /listen\s+([^;]+);/;
      const listenMatch = props.editingData.rawConfig.match(listenRegex);
      if (listenMatch && listenMatch[1]) {
          // 保存完整的listen参数，包括ssl http2等
        formData.value.listen = listenMatch[1];
          
          // 检查是否有backlog参数，如果有则设置serverParams.backlog
          const backlogMatch = listenMatch[1].match(/backlog=(\d+)/);
          if (backlogMatch) {
            serverParams.value.backlog.enabled = true;
            serverParams.value.backlog.value = backlogMatch[1];
          }
        }
      }
      
      // 添加：提取服务器额外参数并赋值给formData
      formData.value.serverExtraParams = extractServerExtraParams();
    }
    
    if (props.editingData.upstreamName) {
      upstreamName.value = props.editingData.upstreamName;
    } else if (props.editingData.rawConfig && props.editingData.rawConfig.includes('upstream ')) {
      // 从配置中提取upstream名称
      const match = props.editingData.rawConfig.match(/upstream\s+([^\s{]+)\s*{/);
      if (match && match[1]) {
        upstreamName.value = match[1];
      }
    }

    // 处理负载均衡策略 - 优化检测策略
    if (props.editingData.upstreamStrategy) {
      upstreamStrategy.value = props.editingData.upstreamStrategy;
    } else if (props.editingData.firstUpstreamContent) {
      // 从firstUpstreamContent中提取负载均衡策略，确保检测注释行
      const contentLines = props.editingData.firstUpstreamContent.split('\n');
      const nonCommentedLines = contentLines.filter(line => {
        const trimmed = line.trim();
        // 过滤掉完全是注释的行
        return trimmed && !trimmed.startsWith('#');
      });
      
      // 重新组合非注释行来处理
      const cleanContent = nonCommentedLines.join('\n');
      
      // 使用更精确的正则表达式匹配负载均衡策略，避免误匹配
      // 检查ip_hash - 使用行首匹配，确保是配置开头的策略
      if (cleanContent.match(/^\s*ip_hash\s*;/m)) {
        upstreamStrategy.value = 'ip_hash';
      } 
      // 检查least_conn
      else if (cleanContent.match(/^\s*least_conn\s*;/m)) {
        upstreamStrategy.value = 'least_conn';
      }
      // 检查hash
      else if (cleanContent.match(/^\s*hash\s+[^;]+;/m)) {
        const hashMatch = cleanContent.match(/^\s*hash\s+([^;]+);/m);
        if (hashMatch) {
          upstreamStrategy.value = `hash ${hashMatch[1]}`;
        } else {
          upstreamStrategy.value = 'hash';
        }
      }
      // 检查random
      else if (cleanContent.match(/^\s*random\s*;/m)) {
        upstreamStrategy.value = 'random';
      }
    }

    if (props.editingData.upstreamServers) {
      upstreamServers.value = JSON.parse(JSON.stringify(props.editingData.upstreamServers));
      // 确保每个服务器的嵌套属性存在
      upstreamServers.value.forEach(server => {
        // 转换旧的weight格式为新格式
        if (typeof server.weight === 'number') {
          server.weight = { value: server.weight, enabled: true };
        } else if (!server.weight || typeof server.weight !== 'object') {
          server.weight = { value: 1, enabled: false };
        }
        if (!server.max_fails) server.max_fails = { value: '3', enabled: false };
        if (!server.fail_timeout) server.fail_timeout = { value: '30s', enabled: false };
        if (!server.backup) server.backup = { enabled: false };
        if (!server.down) server.down = { enabled: false };
        if (!server.showAdvanced) server.showAdvanced = false;
      });
    } else if (props.editingData.upstreamName || props.editingData.firstUpstreamContent) {
      // 如果父组件提供了upstreamName或firstUpstreamContent
      
      if (props.editingData.upstreamName) {
      upstreamName.value = props.editingData.upstreamName;
      }
      
      // 从 firstUpstream.content 中解析服务器
      if (props.editingData.firstUpstreamContent) {
        // 先将内容按行分割，以便处理注释
        const contentLines = props.editingData.firstUpstreamContent.split('\n');
        const nonCommentedLines = contentLines.filter(line => {
          const trimmed = line.trim();
          // 过滤掉完全是注释的行
          return trimmed && !trimmed.startsWith('#');
        });
        
        // 重新组合非注释行来处理
        const cleanContent = nonCommentedLines.join('\n');
        
        const serverRegex = /server\s+([^;]+);/g;
        let serverMatch;
        const serverList = [];
        
        while ((serverMatch = serverRegex.exec(cleanContent)) !== null) {
          const serverStr = serverMatch[1];
          
          // 解析服务器地址和参数
          const addressMatch = serverStr.match(/^([^\s]+)/);
          const weightMatch = serverStr.match(/weight=(\d+)/);
          const maxFailsMatch = serverStr.match(/max_fails=(\d+)/);
          const failTimeoutMatch = serverStr.match(/fail_timeout=([^,\s]+)/);
          const backupMatch = serverStr.includes('backup');
          const downMatch = serverStr.includes('down');
          
          // 修复：确保weight参数被正确启用
          const hasWeight = !!weightMatch;
          
          serverList.push({
            address: addressMatch ? addressMatch[1] : '',
            weight: { 
              value: weightMatch ? parseInt(weightMatch[1]) : 1, 
              enabled: hasWeight // 明确根据原始配置设置weight状态
            },
            max_fails: { 
              value: maxFailsMatch ? maxFailsMatch[1] : '3', 
              enabled: !!maxFailsMatch 
            },
            fail_timeout: { 
              value: failTimeoutMatch ? failTimeoutMatch[1] : '30s', 
              enabled: !!failTimeoutMatch 
            },
            backup: { enabled: backupMatch },
            down: { enabled: downMatch },
            showAdvanced: hasWeight || !!maxFailsMatch || !!failTimeoutMatch || backupMatch || downMatch // 如果有任何高级参数，默认显示高级设置
          });
        }
        
        if (serverList.length > 0) {
          upstreamServers.value = serverList;
        }
      }
    }
    
    // 处理upstreamParams - 从firstUpstreamContent中提取全局参数
    if (props.editingData.upstreamParams) {
      // 修复：不要简单复制对象，而是逐个检查和设置参数，避免状态污染
      if (props.editingData.upstreamParams.keepalive && props.editingData.upstreamParams.keepalive.enabled) {
        upstreamParams.value.keepalive.enabled = true;
        upstreamParams.value.keepalive.value = props.editingData.upstreamParams.keepalive.value || '32';
      }
      
      // 关键修复：只有当原始配置中明确启用了zone时才启用它
      if (props.editingData.upstreamParams.zone && props.editingData.upstreamParams.zone.enabled) {
        upstreamParams.value.zone.enabled = true;
        upstreamParams.value.zone.value = props.editingData.upstreamParams.zone.value || 'upstream_zone 10m';
      }
      
      // 关键修复：只有当原始配置中明确启用了keepalive_timeout时才启用它
      if (props.editingData.upstreamParams.keepalive_timeout && props.editingData.upstreamParams.keepalive_timeout.enabled) {
        upstreamParams.value.keepalive_timeout.enabled = true;
        upstreamParams.value.keepalive_timeout.value = props.editingData.upstreamParams.keepalive_timeout.value || '60s';
      }
    } 
    else if (props.editingData.firstUpstreamContent) {
      // 从firstUpstreamContent中提取全局参数
      const keepaliveMatch = props.editingData.firstUpstreamContent.match(/^\s*keepalive\s+(\d+)\s*;/m);
      if (keepaliveMatch) {
        upstreamParams.value.keepalive.enabled = true;
        upstreamParams.value.keepalive.value = keepaliveMatch[1];
      }
      
      const zoneMatch = props.editingData.firstUpstreamContent.match(/^\s*zone\s+([^;]+)\s*;/m);
      if (zoneMatch) {
        upstreamParams.value.zone.enabled = true;
        upstreamParams.value.zone.value = zoneMatch[1];
      }
      
      const keepaliveTimeoutMatch = props.editingData.firstUpstreamContent.match(/^\s*keepalive_timeout\s+([^;]+)\s*;/m);
      if (keepaliveTimeoutMatch) {
        upstreamParams.value.keepalive_timeout.enabled = true;
        upstreamParams.value.keepalive_timeout.value = keepaliveTimeoutMatch[1];
      }
    }

    if (props.editingData.locations) {
      locations.value = JSON.parse(JSON.stringify(props.editingData.locations));
      // 确保每个location的嵌套属性存在，但根据原始配置设置enabled状态
      locations.value.forEach(location => {
        // 获取该location的原始配置内容（如果存在）
        let locationConfig = '';
        if (props.editingData.rawConfig) {
          // 修复：使用更精确的正则表达式来匹配location块，支持嵌套大括号
          const path = location.path.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1"); // 转义路径中的特殊字符
          
          // 使用更复杂的方法来提取完整的location块
          try {
            const rawConfig = props.editingData.rawConfig;
            const locationPattern = new RegExp(`\\s*location\\s+${path}\\s*\\{`);
            const match = locationPattern.exec(rawConfig);
            
            if (match) {
              // 找到location开始位置
              const startPos = match.index;
              // 计算嵌套大括号以找到正确的结束位置
              let braceCount = 0;
              let inLocation = false;
              let endPos = startPos;
              
              // 扫描配置找到完整的location块
              for (let i = startPos; i < rawConfig.length; i++) {
                const char = rawConfig[i];
                if (char === '{') {
                  braceCount++;
                  inLocation = true;
                } else if (char === '}') {
                  braceCount--;
                }
                
                // 当大括号计数归零且我们已经在location块内，找到了结束位置
                if (inLocation && braceCount === 0) {
                  endPos = i + 1; // 包含结束大括号
                  break;
                }
              }
              
              // 提取完整的location块
              if (endPos > startPos) {
                const fullLocationBlock = rawConfig.substring(startPos, endPos);
                // 提取大括号内的内容
                const contentMatch = fullLocationBlock.match(/\{([\s\S]*)\}/);
                if (contentMatch && contentMatch[1]) {
                  locationConfig = contentMatch[1];
                 
                }
              }
            }
          } catch (error) {
            console.error(`提取location ${location.path} 块时出错:`, error);
          }
        }
        
        // 检查原始配置中是否存在各参数，据此设置enabled状态
        // 代理头部设置
        location.proxy_host_header = { 
          enabled: containsNonCommented(locationConfig, 'proxy_set_header') && 
                   containsNonCommented(locationConfig, 'Host'),
          value: '$host' 
        };
        location.proxy_real_ip_header = { 
          enabled: containsNonCommented(locationConfig, 'proxy_set_header') && 
                   containsNonCommented(locationConfig, 'X-Real-IP'),
          value: '$remote_addr' 
        };
        location.proxy_remote_host_header = { 
          enabled: containsNonCommented(locationConfig, 'proxy_set_header') && 
                   containsNonCommented(locationConfig, 'REMOTE-HOST'),
          value: '$remote_addr' 
        };
        location.proxy_forwarded_for_header = { 
          enabled: containsNonCommented(locationConfig, 'proxy_set_header') && 
                   containsNonCommented(locationConfig, 'X-Forwarded-For'),
          value: '$proxy_add_x_forwarded_for' 
        };

        // 超时设置 - 重要修复：明确检查原始配置中是否存在这些参数
        location.proxy_connect_timeout = { 
          enabled: containsNonCommented(locationConfig, 'proxy_connect_timeout'),
          value: '6s' 
        };
        // 从原始配置提取超时值
        const connectTimeoutMatch = locationConfig.match(/proxy_connect_timeout\s+([^;]+);/);
        if (connectTimeoutMatch) {
          location.proxy_connect_timeout.value = connectTimeoutMatch[1];
        }

        location.proxy_read_timeout = { 
          enabled: containsNonCommented(locationConfig, 'proxy_read_timeout'),
          value: '60s' 
        };
        // 从原始配置提取读取超时值
        const readTimeoutMatch = locationConfig.match(/proxy_read_timeout\s+([^;]+);/);
        if (readTimeoutMatch) {
          location.proxy_read_timeout.value = readTimeoutMatch[1];
        }

        location.proxy_send_timeout = { 
          enabled: containsNonCommented(locationConfig, 'proxy_send_timeout'),
          value: '15s' 
        };
        // 从原始配置提取发送超时值
        const sendTimeoutMatch = locationConfig.match(/proxy_send_timeout\s+([^;]+);/);
        if (sendTimeoutMatch) {
          location.proxy_send_timeout.value = sendTimeoutMatch[1];
        }

        // WebSocket设置
        location.websocket_upgrade = { 
          enabled: containsNonCommented(locationConfig, 'Upgrade') && 
                   containsNonCommented(locationConfig, '$http_upgrade'),
          value: '$http_upgrade' 
        };
        location.websocket_connection = { 
          enabled: containsNonCommented(locationConfig, 'Connection') && 
                   (containsNonCommented(locationConfig, '"upgrade"') || 
                    containsNonCommented(locationConfig, "'upgrade'")),
          value: 'upgrade' 
        };
        location.websocket_keepalive = { 
          enabled: containsNonCommented(locationConfig, 'Connection') && 
                   containsNonCommented(locationConfig, 'keep-alive'),
          value: 'keep-alive' 
        };
        // 添加proxy_http_version设置
        location.proxy_http_version = {
          enabled: containsNonCommented(locationConfig, 'proxy_http_version'),
          value: '1.1'
        };
        // 从原始配置提取HTTP版本值
        const httpVersionMatch = locationConfig.match(/proxy_http_version\s+([^;]+);/);
        if (httpVersionMatch) {
          location.proxy_http_version.value = httpVersionMatch[1];
        }

        // 缓存设置
        location.proxy_cache = {
          enabled: containsNonCommented(locationConfig, 'proxy_cache'),
          value: 'off'
        };
        // 从原始配置提取缓存值
        const cacheMatch = locationConfig.match(/proxy_cache\s+([^;]+);/);
        if (cacheMatch) {
          location.proxy_cache.value = cacheMatch[1];
        }

        location.proxy_buffering = {
          enabled: containsNonCommented(locationConfig, 'proxy_buffering'),
          value: 'off'
        };
        // 从原始配置提取缓冲值
        const bufferingMatch = locationConfig.match(/proxy_buffering\s+([^;]+);/);
        if (bufferingMatch) {
          location.proxy_buffering.value = bufferingMatch[1];
        }
        
        // 更新：根据实际配置内容判断是否为WebSocket
        const isWebSocket = (
          // 检查配置内容
          (location.config && (
            location.config.includes('proxy_set_header Upgrade $http_upgrade') || 
            location.config.includes('proxy_set_header Connection "upgrade"') ||
            location.config.includes('proxy_http_version 1.1')
          )) ||
          // 检查WebSocket相关设置是否已启用
          (locationConfig.includes('Upgrade') && locationConfig.includes('$http_upgrade')) ||
          (locationConfig.includes('Connection') && (locationConfig.includes('"upgrade"') || locationConfig.includes("'upgrade'"))) ||
          // 检查proxy_pass中是否明确指向WebSocket
          (location.proxy_pass && (
            location.proxy_pass.includes('ws://') || 
            location.proxy_pass.includes('wss://')
          ))
        );
        
        if (isWebSocket) {
          location.template = 'websocket';
        } else {
          // 不是WebSocket则设置为basic_proxy
          location.template = 'basic_proxy';
        }

        // 添加：从配置中提取location块额外参数
        extractLocationExtraParams(location, locationConfig);
      });
    }
    
    // 如果编辑模式，尝试从配置中提取upstream名称
    const config = props.editingData.rawConfig || '';
    const upstreamRegex = /upstream\s+(\w+)\s*\{/;
    const upstreamMatch = config.match(upstreamRegex);
    
    if (upstreamMatch && upstreamMatch[1]) {
      upstreamName.value = upstreamMatch[1];
    } else if (props.editingData.locations && props.editingData.locations.length > 0) {
      // 尝试从location的proxy_pass提取upstream名称
      const firstLocation = props.editingData.locations[0];
      if (firstLocation.proxy_pass) {
        const proxyPassMatch = firstLocation.proxy_pass.match(/http:\/\/([^\/\s:]+)/);
        if (proxyPassMatch && proxyPassMatch[1]) {
          upstreamName.value = proxyPassMatch[1];
        }
      }
    }
    
    // 处理高级服务器参数
    if (props.editingData.serverParams) {
      // 如果传入了serverParams，直接使用它
      serverParams.value = JSON.parse(JSON.stringify(props.editingData.serverParams));
    } else {
      // 如果没有传入serverParams，尝试从配置中解析
      const config = props.editingData.rawConfig || '';
      
      // 解析listen参数
      const listenMatch = config.match(/listen\s+([^;]+);/);
      if (listenMatch && listenMatch[1]) {
        const listenValue = listenMatch[1];
        // 分离端口和参数
        const portMatch = listenValue.match(/(\d+)(?:\s+(.*))?/);
        if (portMatch) {
          formData.value.listen = portMatch[1];
          
          // 检查是否有backlog参数
          const backlogMatch = listenValue.match(/backlog=(\d+)/);
          if (backlogMatch && backlogMatch[1]) {
            serverParams.value.backlog.enabled = true;
            serverParams.value.backlog.value = backlogMatch[1];
          } else {
            serverParams.value.backlog.enabled = false;
          }
        } else {
          formData.value.listen = listenValue;
        }
      }
      
      // 解析client_max_body_size参数
      const clientMaxBodySizeMatch = config.match(/client_max_body_size\s+([^;]+);/);
      if (clientMaxBodySizeMatch && clientMaxBodySizeMatch[1]) {
        serverParams.value.client_max_body_size.enabled = true;
        serverParams.value.client_max_body_size.value = clientMaxBodySizeMatch[1];
      } else {
        serverParams.value.client_max_body_size.enabled = false;
      }
    }
    
    // 如果formData.server_name是空字符串、空数组或未定义，设置为空字符串
    if (!formData.value.server_name || 
        (Array.isArray(formData.value.server_name) && formData.value.server_name.length === 0)) {
      formData.value.server_name = '';
    } else if (Array.isArray(formData.value.server_name) && formData.value.server_name.length > 0) {
      // 如果是数组，取第一个值
      formData.value.server_name = formData.value.server_name[0];
    }
    
    // 确保其他关键字段存在
    if (!formData.value.name) {
      updateName(); // 更新名称
    }
    
    // 初始化完成后，应用正确的模板到现有位置
    applyTemplateToExistingLocations();
  } else {
    // 新增模式，创建默认值
    formData.value = {
      name: '',
      listen: '80',
      server_name: '', // 空字符串，不提供默认的 server_name
      locations: [],
      enabled: true,
      file_path: props.filePaths.length > 0 ? props.filePaths[0] : '',
      config: '',
      upstream: null,
      identifier: Date.now(),
      serverExtraParams: {} // 确保serverExtraParams属性存在
    };
    
    // 创建默认的upstream名称
    upstreamName.value = 'my_upstream';
    
    // 创建默认的上游服务器
    upstreamServers.value = [
      { 
        address: '127.0.0.1:8080', 
        weight: { value: 1, enabled: false },
        max_fails: { value: '3', enabled: false },
        fail_timeout: { value: '30s', enabled: false },
        backup: { enabled: false },
        down: { enabled: false },
        showAdvanced: false
      }
    ];
    
    // 创建默认的路径配置 - 仅在新增模式下默认启用超时参数
    locations.value = [{
      path: '/',
      proxy_pass: `http://${upstreamName.value}`,
      template: 'basic_proxy',
      basic_headers: false, // 添加basic_headers标志，默认为false，避免自动添加header参数
      // 所有参数默认禁用
      proxy_connect_timeout: { enabled: false, value: '6s' },
      proxy_read_timeout: { enabled: false, value: '60s' },
      proxy_send_timeout: { enabled: false, value: '15s' },
      // 头部设置默认禁用
      proxy_host_header: { enabled: false, value: '$host' },
      proxy_real_ip_header: { enabled: false, value: '$remote_addr' },
      proxy_remote_host_header: { enabled: false, value: '$remote_addr' },
      proxy_forwarded_for_header: { enabled: false, value: '$proxy_add_x_forwarded_for' },
      // WebSocket相关参数
      websocket_upgrade: { enabled: false, value: '$http_upgrade' },
      websocket_connection: { enabled: false, value: 'upgrade' },
      websocket_keepalive: { enabled: false, value: 'keep-alive' },
      proxy_http_version: { enabled: false, value: '1.1' },
      // 缓存相关参数
      proxy_cache: { enabled: false, value: 'off' },
      proxy_buffering: { enabled: false, value: 'off' }
    }];
  }
  
  // 确保配置预览初次加载时是最新的
  if (formData.value) {
    // 延迟更新预览，确保其他属性已初始化完成
    setTimeout(() => {
      try {
    formData.value.config = generateConfigPreview();
      } catch (error) {
        console.error('更新配置预览时出错:', error);
      }
    }, 0);
  }
}

// 添加新的函数：更新配置预览
function updateConfigPreview() {
  if (formData.value) {
    formData.value.config = generateConfigPreview();
  }
}

// 添加上游服务器
function addUpstreamServer() {
  upstreamServers.value.push({
    address: '',
    weight: { value: 1, enabled: false },
    max_fails: { value: '3', enabled: false },
    fail_timeout: { value: '30s', enabled: false },
    backup: { enabled: false },
    down: { enabled: false },
    showAdvanced: false
  });
}

// 删除上游服务器
function removeUpstreamServer(index) {
  // 如果只剩一个服务器，不允许删除
  if (upstreamServers.value.length <= 1) {
    ElMessage.warning('至少需要保留一个上游服务器');
    return;
  }
  upstreamServers.value.splice(index, 1);
}

// 添加路径配置
function addLocationItem() {
  locations.value.push({
    path: '/',
    proxy_pass: `http://${upstreamName.value}`,
    template: 'basic_proxy',
    basic_headers: false, // 添加basic_headers标志，默认为false，避免自动添加header参数
    // 所有参数默认禁用
    proxy_host_header: { enabled: false, value: '$host' },
    proxy_real_ip_header: { enabled: false, value: '$remote_addr' },
    proxy_remote_host_header: { enabled: false, value: '$remote_addr' },
    proxy_forwarded_for_header: { enabled: false, value: '$proxy_add_x_forwarded_for' },
    // 超时设置默认禁用
    proxy_connect_timeout: { enabled: false, value: '6s' },
    proxy_read_timeout: { enabled: false, value: '60s' },
    proxy_send_timeout: { enabled: false, value: '15s' },
    // WebSocket设置默认禁用
    websocket_upgrade: { enabled: false, value: '$http_upgrade' },
    websocket_connection: { enabled: false, value: 'upgrade' },
    websocket_keepalive: { enabled: false, value: 'keep-alive' },
    // WebSocket必需的HTTP版本设置，默认为禁用
    proxy_http_version: { enabled: false, value: '1.1' },
    // 缓存相关参数
    proxy_cache: { enabled: false, value: 'off' },
    proxy_buffering: { enabled: false, value: 'off' }
  });
}

// 删除路径配置
function removeLocation(index) {
  locations.value.splice(index, 1);
  // 保证至少有一个路径
  if (locations.value.length === 0) {
    addLocationItem();
  }
}

// 创建新的文件路径
function createFilePath(path) {
  if (path && !props.filePaths.includes(path)) {
    emit('create-file-path', path);
  }
}

// 同步上游服务器名称到代理路径
function syncUpstreamName() {
  locations.value.forEach(location => {
    if (location.proxy_pass && location.proxy_pass.includes('http://')) {
      // 仅更新使用上游服务器的代理配置
      const parts = location.proxy_pass.split('http://');
      if (parts.length === 2) {
        // 检查是否是一个简单的上游名称（不包含路径或查询参数）
        if (!parts[1].includes('/') || parts[1].indexOf('/') === parts[1].length - 1) {
          location.proxy_pass = `http://${upstreamName.value}${parts[1].includes('/') ? parts[1].substring(parts[1].indexOf('/')) : ''}`;
        }
      }
    }
  });
  
  // 强制更新配置预览
  if (formData.value) {
    formData.value.config = generateConfigPreview();
  }
}

// 应用位置模板
function applyLocationItemTemplate(index, template) {
  const location = locations.value[index];
  if (!location) return;

  // 保存当前参数的启用状态
  const currentState = {
    proxy_connect_timeout: location.proxy_connect_timeout?.enabled,
    proxy_read_timeout: location.proxy_read_timeout?.enabled,
    proxy_send_timeout: location.proxy_send_timeout?.enabled,
    proxy_host_header: location.proxy_host_header?.enabled,
    proxy_real_ip_header: location.proxy_real_ip_header?.enabled,
    proxy_remote_host_header: location.proxy_remote_host_header?.enabled,
    proxy_forwarded_for_header: location.proxy_forwarded_for_header?.enabled,
    websocket_upgrade: location.websocket_upgrade?.enabled,
    websocket_connection: location.websocket_connection?.enabled,
    websocket_keepalive: location.websocket_keepalive?.enabled,
    proxy_http_version: location.proxy_http_version?.enabled,
    proxy_cache: location.proxy_cache?.enabled,
    proxy_buffering: location.proxy_buffering?.enabled
  };

  // 确保基本参数存在
  if (!location.proxy_connect_timeout) {
    location.proxy_connect_timeout = { enabled: currentState.proxy_connect_timeout || false, value: '6s' };
  }
  if (!location.proxy_read_timeout) {
    location.proxy_read_timeout = { enabled: currentState.proxy_read_timeout || false, value: '60s' };
  }
  if (!location.proxy_send_timeout) {
    location.proxy_send_timeout = { enabled: currentState.proxy_send_timeout || false, value: '15s' };
  }
  if (!location.proxy_host_header) {
    location.proxy_host_header = { enabled: currentState.proxy_host_header || false, value: '$host' };
  }
  if (!location.proxy_real_ip_header) {
    location.proxy_real_ip_header = { enabled: currentState.proxy_real_ip_header || false, value: '$remote_addr' };
  }
  if (!location.proxy_remote_host_header) {
    location.proxy_remote_host_header = { enabled: currentState.proxy_remote_host_header || false, value: '$remote_addr' };
  }
  if (!location.proxy_forwarded_for_header) {
    location.proxy_forwarded_for_header = { enabled: currentState.proxy_forwarded_for_header || false, value: '$proxy_add_x_forwarded_for' };
  }
  if (!location.websocket_upgrade) {
    location.websocket_upgrade = { enabled: currentState.websocket_upgrade || false, value: '$http_upgrade' };
  }
  if (!location.websocket_connection) {
    location.websocket_connection = { enabled: currentState.websocket_connection || false, value: 'upgrade' };
  }
  if (!location.websocket_keepalive) {
    location.websocket_keepalive = { enabled: currentState.websocket_keepalive || false, value: 'keep-alive' };
  }
  if (!location.proxy_http_version) {
    location.proxy_http_version = { enabled: currentState.proxy_http_version || false, value: '1.1' };
  }
  if (!location.proxy_cache) {
    location.proxy_cache = { enabled: currentState.proxy_cache || false, value: 'off' };
  }
  if (!location.proxy_buffering) {
    location.proxy_buffering = { enabled: currentState.proxy_buffering || false, value: 'off' };
  }

  switch (template) {
    case 'basic_proxy':
    case 'api_proxy': // 合并基本代理和API代理的实现，它们使用相同配置
      // 基本代理设置，只更新值而不修改启用状态
      location.proxy_host_header.value = '$host';
      // 保持超时参数的值，但不更改其启用状态
      location.proxy_connect_timeout.value = '6s';
      location.proxy_read_timeout.value = '60s';
      location.proxy_send_timeout.value = '15s';
      location.template = 'basic_proxy';
      break;
    case 'websocket':
      // WebSocket代理配置，更新值并根据需要启用WebSocket特定的配置
      // 更新超时参数值，但不改变启用状态
      location.proxy_connect_timeout.value = '6s';
      location.proxy_read_timeout.value = '6000s';
      location.proxy_send_timeout.value = '6000s';
      
      location.proxy_host_header.value = '$host:$server_port';
      location.websocket_upgrade.value = '$http_upgrade';
      location.websocket_connection.value = 'upgrade';
      location.websocket_keepalive.value = 'keep-alive';
      location.proxy_http_version.value = '1.1';

      // WebSocket模板下自动启用关键参数
      if (!currentState.websocket_upgrade) {
        location.websocket_upgrade.enabled = true;
      }
      if (!currentState.websocket_connection) {
        location.websocket_connection.enabled = true;
      }
      if (!currentState.proxy_http_version) {
        location.proxy_http_version.enabled = true;
      }

      // 显式设置模板为websocket
      location.template = 'websocket';
      break;
    default:
      // 默认设置为basic_proxy
      location.template = 'basic_proxy';
      break;
  }
  
  // 立即强制更新配置预览
  if (formData.value) {
    nextTick(() => {
      formData.value.config = generateConfigPreview();
    });
  }
}

// 生成配置预览
function generateConfigPreview() {
  try {

    
    // 生成upstream配置
    let config = generateUpstreamConfig() + '\n';
    
    // 生成server块配置
    config += 'server {\n';
    
    // 添加listen指令 - 确保保留所有原始参数
    if (props.isEdit && props.editingData && props.editingData.rawConfig) {
      // 从原始配置中提取listen行
      const listenRegex = /listen\s+([^;]+);/;
      const listenMatch = props.editingData.rawConfig.match(listenRegex);
      if (listenMatch && listenMatch[1]) {
        // 使用原始的listen配置（包括ssl http2等参数）
        let listenValue = listenMatch[1];
        
        // 如果formData.value.listen是从原始配置中提取的完整参数，直接使用它
        if (formData.value.listen && formData.value.listen.includes(listenMatch[1])) {
          listenValue = formData.value.listen;
        }
        
        // 处理backlog参数
        if (serverParams.value.backlog.enabled) {
          // 如果启用了backlog但listen值中没有这个参数，添加它
          if (!listenValue.includes('backlog=')) {
            listenValue += ` backlog=${serverParams.value.backlog.value}`;
          } else {
            // 如果已经存在backlog参数，更新它的值
            listenValue = listenValue.replace(/backlog=\d+/, `backlog=${serverParams.value.backlog.value}`);
          }
        } else {
          // 如果禁用了backlog参数，从listen值中移除它
          listenValue = listenValue.replace(/\s*backlog=\d+/, '');
        }
        
        // 关键修复：将更新后的listenValue保存回formData.value.listen
        formData.value.listen = listenValue;
        
        config += `    listen ${listenValue};\n`;
      } else {
        // 回退到表单数据
        let listenValue = formData.value.listen || '80';
        // 处理backlog参数
        if (serverParams.value.backlog.enabled && !listenValue.includes('backlog=')) {
          listenValue += ` backlog=${serverParams.value.backlog.value}`;
        } else if (!serverParams.value.backlog.enabled) {
          // 如果禁用了backlog参数，从listen值中移除它
          listenValue = listenValue.replace(/\s*backlog=\d+/, '');
        }
        
        // 关键修复：将更新后的listenValue保存回formData.value.listen
        formData.value.listen = listenValue;
        
        config += `    listen ${listenValue};\n`;
      }
    } else {
      // 非编辑模式，使用表单数据
      let listenValue = formData.value.listen || '80';
      // 处理backlog参数
      if (serverParams.value.backlog.enabled && !listenValue.includes('backlog=')) {
        listenValue += ` backlog=${serverParams.value.backlog.value}`;
      } else if (!serverParams.value.backlog.enabled) {
        // 如果禁用了backlog参数，从listen值中移除它
        listenValue = listenValue.replace(/\s*backlog=\d+/, '');
      }
      
      // 关键修复：将更新后的listenValue保存回formData.value.listen
      formData.value.listen = listenValue;
      
      config += `    listen ${listenValue};\n`;
    }
    
    // 添加server_name指令
    if (formData.value.server_name && formData.value.server_name.trim() !== '') {
      config += `    server_name ${formData.value.server_name};\n`;
    }
    
    // 添加服务器级别的额外参数 - 修复：确保使用formData.value.serverExtraParams
    if (formData.value && formData.value.serverExtraParams) {
      // 严格规范超时参数，只有在明确是服务器级别时才添加
      const serverProxyTimeoutParams = [
        'proxy_connect_timeout', 
        'proxy_read_timeout', 
        'proxy_send_timeout'
      ];
      
      // 检查每个location的超时参数使用情况
      const timeoutParamsInLocations = {
        proxy_connect_timeout: false,
        proxy_read_timeout: false,
        proxy_send_timeout: false
      };
      
      // 记录所有location块中使用的超时参数
      locations.value.forEach(location => {
        if (location.proxy_connect_timeout && location.proxy_connect_timeout.enabled) {
          timeoutParamsInLocations.proxy_connect_timeout = true;
        }
        if (location.proxy_read_timeout && location.proxy_read_timeout.enabled) {
          timeoutParamsInLocations.proxy_read_timeout = true;
        }
        if (location.proxy_send_timeout && location.proxy_send_timeout.enabled) {
          timeoutParamsInLocations.proxy_send_timeout = true;
        }
      });

      
      // 先添加非超时参数
      for (const [paramName, param] of Object.entries(formData.value.serverExtraParams)) {
        // 跳过超时参数，稍后处理
        if (serverProxyTimeoutParams.includes(paramName)) {
          continue;
        }
        
        // 确保参数被启用才添加到配置中
        if (param && param.enabled) {
          // 不处理proxy_pass和location特定参数
          if (paramName !== 'proxy_pass' && 
              !paramName.startsWith('add_header_') && 
              paramName !== 'return') {
            config += `    ${paramName} ${param.value};\n`;
          }
        }
      }
      
      // 然后处理超时参数 - 修改：在Nginx中，server块的超时参数与location块的超时参数可以共存
      // 它们是独立的，即使同一个参数同时存在于server块和location块中也是合法的
      for (const timeoutParam of serverProxyTimeoutParams) {
        const param = formData.value.serverExtraParams[timeoutParam];
        
        if (param && param.enabled) {
          // 允许超时参数同时存在于server块和location块中
          // 在Nginx中，这些参数可以在不同级别设置，它们的作用域是独立的
  
          config += `    ${timeoutParam} ${param.value};\n`;
        }
      }
      
      // 然后添加所有add_header和return参数(这些需要特殊处理，位于SSL参数之后)
      for (const [paramName, param] of Object.entries(formData.value.serverExtraParams)) {
        if (param && param.enabled) {
          if (paramName.startsWith('add_header_')) {
            // 从paramName中提取出原始的add_header指令
            config += `    add_header ${param.value};\n`;
          } else if (paramName === 'return') {
            config += `    return ${param.value};\n`;
          }
        }
      }
    }
    
    // 从原始配置中提取全局指令（编辑模式）
    let globalDirectives = '';
    let hasClientMaxBodySize = false;
    
    if (props.isEdit && props.editingData && props.editingData.rawConfig) {
      try {
        // 提取server块内容
        const serverBlockRegex = /server\s*\{([\s\S]*?)\n\s*\}/;
        const serverBlockMatch = props.editingData.rawConfig.match(serverBlockRegex);
        
        if (serverBlockMatch && serverBlockMatch[1]) {
          const serverBlock = serverBlockMatch[1];
          
          // 检查client_max_body_size
          if (serverBlock.includes('client_max_body_size')) {
            hasClientMaxBodySize = true;
          }
          
          // 首先找出所有location块的起始位置和结束位置
          const locationBlocks = [];
          let locationStartPattern = /\s*location\s+([^\s{]+)\s*\{/g;
          let locationMatch;
          
          while ((locationMatch = locationStartPattern.exec(serverBlock)) !== null) {
            // 找到location块的起始位置
            const startPath = locationMatch[1];
            const startIndex = locationMatch.index;
            
            // 从起始位置查找匹配的结束大括号
            let count = 1; // 已经找到一个左大括号
            let endIndex = startIndex + locationMatch[0].length;
            
            while (count > 0 && endIndex < serverBlock.length) {
              const char = serverBlock[endIndex];
              if (char === '{') count++;
              if (char === '}') count--;
              endIndex++;
            }
            
            if (count === 0) {
              // 找到匹配的结束大括号
              locationBlocks.push({
                path: startPath,
                start: startIndex,
                end: endIndex
              });
            }
          }
          
          // 现在我们知道了所有location块的位置，可以提取全局指令（不在任何location块内的指令）
          let currentPos = 0;
          let globalContent = '';
          
          // 按照位置排序location块
          locationBlocks.sort((a, b) => a.start - b.start);
          
          // 提取全局部分
          for (const block of locationBlocks) {
            // 添加当前位置到location块开始位置之间的内容（全局指令）
            if (block.start > currentPos) {
              globalContent += serverBlock.substring(currentPos, block.start);
            }
            currentPos = block.end;
          }
          
          // 添加最后一个location块后面的全局指令
          if (currentPos < serverBlock.length) {
            globalContent += serverBlock.substring(currentPos);
          }
          
          // 处理全局内容，提取有效的全局指令
          const lines = globalContent.split('\n');
          
          // 超时参数的特殊处理
          const serverProxyTimeoutParams = [
            'proxy_connect_timeout',
            'proxy_read_timeout',
            'proxy_send_timeout'
          ];
          
          // 确定哪些代理超时参数存在于服务器块中
          const serverTimeoutParams = {
            proxy_connect_timeout: false,
            proxy_read_timeout: false,
            proxy_send_timeout: false
          };
          
          // 第一次扫描，识别服务器块中存在哪些超时参数
          for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine) continue;
            
            for (const timeoutParam of serverProxyTimeoutParams) {
              if (trimmedLine.startsWith(timeoutParam)) {
                serverTimeoutParams[timeoutParam] = true;
               
              }
            }
          }
          
          // 第二次扫描，处理每一行
          for (const line of lines) {
            const trimmedLine = line.trim();
            
            // 跳过空行、listen行和server_name行（这些会单独处理）
            if (!trimmedLine || 
                trimmedLine.startsWith('listen') || 
                trimmedLine.startsWith('server_name') ||
                trimmedLine.startsWith('proxy_pass') ||  // 显式排除proxy_pass
                trimmedLine.startsWith('add_header') ||  // 现在排除add_header，它们会通过serverExtraParams处理
                trimmedLine.startsWith('return')) {      // 现在排除return，它会通过serverExtraParams处理
                continue;
            }
            
            // 检查是否是超时参数
            let isTimeoutParam = false;
            for (const timeoutParam of serverProxyTimeoutParams) {
              if (trimmedLine.startsWith(timeoutParam)) {
                isTimeoutParam = true;
                break;
              }
            }
            
            // 超时参数特殊处理 - 只有当参数明确在server级别并且不在location块中使用时才保留
            if (isTimeoutParam) {
              // 跳过超时参数，这些会通过serverExtraParams处理
              continue;
            }
            
            // 跳过已经作为serverExtraParams处理的参数
            let isExtraParam = false;
            if (formData.value && formData.value.serverExtraParams) {
              for (const paramName of Object.keys(formData.value.serverExtraParams)) {
                if (trimmedLine.startsWith(paramName) || 
                    (paramName.startsWith('add_header_') && trimmedLine.startsWith('add_header'))) {
                  isExtraParam = true;
                  break;
                }
              }
            }
            if (isExtraParam) continue;
            
            // 保留注释行和其他指令，但排除location特有的参数
            if (trimmedLine && (trimmedLine.includes(';') || trimmedLine.startsWith('#'))) {
              // 排除通常仅在location块中使用的参数
              const locationSpecificParams = ['proxy_pass', 'proxy_set_header', 'proxy_http_version', 
                                             'proxy_cache', 'proxy_buffering'];
              let isLocationParam = false;
              
              for (const param of locationSpecificParams) {
                if (trimmedLine.startsWith(param)) {
                  isLocationParam = true;
                  break;
                }
              }
              
              if (!isLocationParam) {
                globalDirectives += `    ${trimmedLine}\n`;
              }
            }
          }
        }
      } catch (error) {
        console.error('提取全局指令时出错:', error);
      }
    }
    
    // 添加全局指令
    if (globalDirectives) {
      config += globalDirectives;
    }
    
    // 添加client_max_body_size - 修复：确保此参数显示在预览中
    if (serverParams.value.client_max_body_size.enabled && !hasClientMaxBodySize) {
      config += `    client_max_body_size ${serverParams.value.client_max_body_size.value};\n`;
    }
    
    // 添加locations配置 - 直接从当前的locations数组生成，而不使用原始配置
    for (let i = 0; i < locations.value.length; i++) {
      const location = locations.value[i];
      // 始终从头生成location块，确保反映当前表单中的所有更改
      const locationConfig = generateLocationConfigFromScratch(location);
      
      config += '\n' + locationConfig;
    }
    
    config += '\n}';
    
    // 规范化配置格式
    const result = normalizeServerConfig(config);
 
    return result;
  } catch (error) {
    console.error("[调试] 生成配置预览时出错:", error);
    return "配置生成错误，请检查控制台输出";
  }
}

// 添加规范化服务器配置的函数，确保和ServerBlockManager.vue中的normalizeEmptyLines逻辑一致
function normalizeServerConfig(content) {
  if (!content) return content;
  
  let normalized = content;
  
  // 标准化upstream格式 - 确保大括号前有空格
  normalized = normalized.replace(/upstream\s+([^\s{]+)\s*{/g, 'upstream $1 {');
  
  // 处理upstream块内部的多余空行
  normalized = normalized.replace(/(upstream\s+[^\s{]+\s*{)([^}]*)\}/gs, (match, start, body) => {
    // 移除内部多余空行，确保server行之间没有空行
    const cleanBody = body
      .replace(/\n\s*\n/g, '\n')  // 先移除所有多余空行
      .replace(/(\s*server\s+[^;]+;)\n+(\s*server)/g, '$1\n$2');  // 确保server行之间没有多余空行
    return `${start}${cleanBody}}`;
  });
  
  // 处理server块内部的多余空行
  normalized = normalized.replace(/(server\s*{)([^}]*)\}/gs, (match, start, body) => {
    // 移除内部多余空行
    const cleanBody = body.replace(/\n\s*\n\s*\n/g, '\n\n');
    return `${start}${cleanBody}}`;
  });
  
  // 处理location块内部的多余空行
  normalized = normalized.replace(/(location\s+[^{]*{)([^}]*)\}/gs, (match, start, body) => {
    // 移除内部多余空行
    const cleanBody = body.replace(/\n\s*\n/g, '\n');
    return `${start}${cleanBody}}`;
  });
  
  // 确保upstream块和server块之间只有一个换行符
  normalized = normalized.replace(/}\s*\n+(\s*server\s*{)/g, '}\n$1');
  
  // 移除server块末尾的多余空行
  normalized = normalized.replace(/\}\s*\n\s*$/g, '}');
  
  return normalized;
}


// 更新服务器块名称
function updateName() {
  if (!formData.value) return;
  
  const serverName = formData.value.server_name;
  let displayServerName = "default";
  
  if (typeof serverName === 'string' && serverName.trim() !== '') {
    displayServerName = serverName.trim();
  } else if (Array.isArray(serverName) && serverName.length > 0) {
    displayServerName = serverName[0];
  }
  
  const listen = formData.value.listen || '';
    
  // 如果没有服务器名称，使用"default"作为显示名称
  formData.value.name = `${displayServerName} (${listen})`;
}

// 验证表单
function validate() {
  if (!formData.value) return false;

  if (!formData.value.listen) {
    ElMessage.error('请输入监听端口');
    return false;
  }

  if (!formData.value.file_path) {
    ElMessage.error('请选择配置文件路径');
    return false;
  }

  if (upstreamServers.value.some(server => !server.address)) {
    ElMessage.error('请为所有上游服务器指定地址');
    return false;
  }

  if (locations.value.some(loc => !loc.path)) {
    ElMessage.error('请为所有路径配置指定路径');
    return false;
  }

  // 服务器名称不再作为必填项

  return true;
}

// 保存配置
function save() {
  if (!validate()) return;
  
  // 显式更新配置预览以确保最新数据
  const updatedConfig = generateConfigPreview();
  formData.value.config = updatedConfig;
  
  // 构建完整的表单数据
  const completeData = {
    ...formData.value,
    upstreamName: upstreamName.value,
    upstreamStrategy: upstreamStrategy.value,
    upstreamServers: upstreamServers.value,
    upstreamParams: upstreamParams.value,
    locations: locations.value,
    serverParams: serverParams.value
  };

  // 触发保存事件
  emit('save', completeData);
  // 关闭对话框
  emit('update:visible', false);
}

// 取消
function cancel() {
  // 直接关闭对话框，不做任何校验
  emit('cancel');
  dialogVisible.value = false;
}

// 为现有的位置配置应用适当的模板
function applyTemplateToExistingLocations() {
  if (!locations.value) return;
  
  locations.value.forEach(location => {
    if (!location) return;
    
    // 分析位置配置，判断是否为WebSocket
    const isWebSocket = (
      // 检查WebSocket特定的配置项是否已启用
      (location.websocket_upgrade && location.websocket_upgrade.enabled) ||
      (location.websocket_connection && location.websocket_connection.enabled) ||
      // 检查代理地址是否明确指向WebSocket
      (location.proxy_pass && (
        location.proxy_pass.includes('ws://') ||
        location.proxy_pass.includes('wss://')
      ))
    );
    
    // 根据分析结果设置模板
    if (isWebSocket) {
      // 如果是WebSocket，设置WebSocket模板
      if (location.template !== 'websocket') {
        // 显式调用模板应用函数
        applyLocationItemTemplate(locations.value.indexOf(location), 'websocket');
      }
    } else {
      // 如果不是WebSocket，默认设置为基本代理
      if (!location.template || location.template === 'api_proxy') {
        // 显式调用模板应用函数
        applyLocationItemTemplate(locations.value.indexOf(location), 'basic_proxy');
      }
    }
  });
}


// 添加函数，用于检测字符串是否包含非注释内容
function containsNonCommented(text, pattern) {
  if (!text) return false;
  
  // 按行分割以便检查每行
  const lines = text.split('\n');
  for (const line of lines) {
    const trimmed = line.trim();
    // 跳过注释行
    if (!trimmed || trimmed.startsWith('#')) continue;
    
    // 检查非注释行是否包含指定模式
    if (trimmed.includes(pattern)) {
      return true;
    }
  }
  return false;
}

// 额外监听upstreamName变更，确保能实时更新预览
watch(() => upstreamName.value, (newValue) => {
  // 仅当表单初始化完成后才触发更新
  if (formData.value) {
    // 同步名称到所有使用此upstream的路径
    syncUpstreamName();
    
    // 强制更新配置预览
    updateConfigPreview();
  }
});

// 监听更多关键参数的变化，实时更新配置预览
// 监听upstreamServers变化
watch(() => JSON.stringify(upstreamServers.value), () => {
  updateConfigPreview();
}, { deep: true });

// 监听upstreamStrategy变化
watch(() => upstreamStrategy.value, () => {
  updateConfigPreview();
});

// 监听locations变化（包括所有路径参数的开关状态）
watch(() => JSON.stringify(locations.value), () => {
  // 确保每次locations变化时都强制更新配置预览
  if (formData.value) {
    formData.value.config = generateConfigPreview();
  }
}, { deep: true });

// 监听服务器参数变化
watch(() => JSON.stringify(serverParams.value), () => {
  updateConfigPreview();
}, { deep: true });

// 监听上游参数变化
watch(() => JSON.stringify(upstreamParams.value), () => {
  updateConfigPreview();
}, { deep: true });

// 生成upstream块配置
function generateUpstreamConfig() {
  let config = `upstream ${upstreamName.value} {\n`;
  
  // 添加负载均衡策略 - 修复：确保负载策略正确添加到预览
  if (upstreamStrategy.value) {
    config += `    ${upstreamStrategy.value};\n`;
  }
  
  // 添加服务器列表
  upstreamServers.value.forEach(server => {
    if (!server.address) return;
    
    let serverLine = '    server ' + server.address;
    
    // 添加启用的参数
    if (server.weight?.enabled) {
      serverLine += ` weight=${server.weight.value}`;
    }
    if (server.max_fails?.enabled) {
      serverLine += ` max_fails=${server.max_fails.value}`;
    }
    if (server.fail_timeout?.enabled) {
      serverLine += ` fail_timeout=${server.fail_timeout.value}`;
    }
    if (server.backup?.enabled) {
      serverLine += ' backup';
    }
    if (server.down?.enabled) {
      serverLine += ' down';
    }
    
    serverLine += ';';
    config += serverLine + '\n';
  });
  
  // 添加全局upstream参数 - 修复：确保全局参数显示在预览中
  if (upstreamParams.value.keepalive.enabled) {
    config += `    keepalive ${upstreamParams.value.keepalive.value};\n`;
  }
  if (upstreamParams.value.zone.enabled) {
    config += `    zone ${upstreamParams.value.zone.value};\n`;
  }
  if (upstreamParams.value.keepalive_timeout.enabled) {
    config += `    keepalive_timeout ${upstreamParams.value.keepalive_timeout.value};\n`;
  }
  
  config += '}';
  return config;
}


// 辅助函数：从头生成location配置
function generateLocationConfigFromScratch(location) {
  

  let config = `    location ${location.path} {\n`;
  
  // 根据模板类型调整配置顺序
  if (location.template === 'websocket') {
    // WebSocket配置 - 首先添加WebSocket特有设置
    // proxy_http_version 对于WebSocket是必需的
    if (location.proxy_http_version?.enabled) {
      config += `        proxy_http_version ${location.proxy_http_version.value};\n`;
    }
    
    // 添加Upgrade头，仅当用户启用
    if (location.websocket_upgrade && location.websocket_upgrade.enabled) {
      config += `        proxy_set_header Upgrade ${location.websocket_upgrade.value};\n`;
    }
    
    // 添加Connection头，仅当用户启用
    if (location.websocket_connection && location.websocket_connection.enabled) {
      config += `        proxy_set_header Connection "${location.websocket_connection.value}";\n`;
    }
    
    // 添加keep-alive，仅当用户启用
    if (location.websocket_keepalive && location.websocket_keepalive.enabled) {
      config += `        proxy_set_header Connection "keep-alive";\n`;
    }
    
    // 添加proxy_pass
    if (location.proxy_pass) {
      config += `        proxy_pass ${location.proxy_pass};\n`;
    }
  } else {
    // 非WebSocket配置
    
    // 先添加if块（确保在proxy_pass之前添加，以便它们按原始顺序显示）
    if (location.extraParams) {
      // 首先添加if块
      const ifBlocks = Object.entries(location.extraParams)
        .filter(([key]) => key.startsWith('if_block_'))
        .map(([_, param]) => param);
      
      if (ifBlocks.length > 0) {
        for (const ifBlock of ifBlocks) {
          if (ifBlock.enabled) {
            // 处理if块的缩进，确保格式正确
            const ifLines = ifBlock.value.split('\n');
            let currentIndent = 8; // 基本缩进
            
            // 处理每一行的缩进
            const indentedLines = ifLines.map((line, idx) => {
              const trimmedLine = line.trim();
              
              // 增加缩进级别，如果是开始括号
              if (trimmedLine.endsWith('{')) {
                const indented = ' '.repeat(currentIndent) + trimmedLine;
                currentIndent += 4; // 增加一级缩进
                return indented;
              }
              // 减少缩进级别，如果是结束括号
              else if (trimmedLine === '}') {
                currentIndent = Math.max(8, currentIndent - 4); // 减少一级缩进
                return ' '.repeat(currentIndent) + trimmedLine;
              }
              // 第一行if语句特殊处理
              else if (idx === 0 && trimmedLine.startsWith('if')) {
                return ' '.repeat(currentIndent) + trimmedLine;
              }
              // 其他行使用当前缩进级别
              else {
                return ' '.repeat(currentIndent) + trimmedLine;
              }
            });
            
            // 确保if块正确闭合 - 检查最后一行是否有右括号
            let ifBlockText = indentedLines.join('\n');
            
            // 如果最后一行不是右括号，添加一个闭合的右括号
            if (!ifBlockText.trim().endsWith('}')) {
              ifBlockText += '\n        }';
            }
            
            config += ifBlockText + '\n';
          }
        }
      }
    }
    
    // 然后添加proxy_pass
    if (location.proxy_pass) {
      config += `        proxy_pass ${location.proxy_pass};\n`;
    }
  }
  
  // 添加代理头部设置，仅当用户启用
  if (location.proxy_host_header && location.proxy_host_header.enabled) {
    config += `        proxy_set_header            Host ${location.proxy_host_header.value};\n`;
  }
  if (location.proxy_real_ip_header && location.proxy_real_ip_header.enabled) {
    config += `        proxy_set_header            X-Real-IP ${location.proxy_real_ip_header.value};\n`;
  }
  if (location.proxy_remote_host_header && location.proxy_remote_host_header.enabled) {
    config += `        proxy_set_header            REMOTE-HOST ${location.proxy_remote_host_header.value};\n`;
  }
  if (location.proxy_forwarded_for_header && location.proxy_forwarded_for_header.enabled) {
    config += `        proxy_set_header            X-Forwarded-For ${location.proxy_forwarded_for_header.value};\n`;
  }
  
  // 添加代理超时参数，仅当用户启用
  // 这些参数只应该出现在location块中，而不是server块
  if (location.proxy_connect_timeout && location.proxy_connect_timeout.enabled) {
   
    config += `        proxy_connect_timeout       ${location.proxy_connect_timeout.value};\n`;
  }
  if (location.proxy_read_timeout && location.proxy_read_timeout.enabled) {
   
    config += `        proxy_read_timeout          ${location.proxy_read_timeout.value};\n`;
  }
  if (location.proxy_send_timeout && location.proxy_send_timeout.enabled) {
   
    config += `        proxy_send_timeout          ${location.proxy_send_timeout.value};\n`;
  }
  
  // 缓存和缓冲设置，仅当用户启用
  if (location.proxy_cache && location.proxy_cache.enabled) {
    config += `        proxy_cache ${location.proxy_cache.value};\n`;
  }
  if (location.proxy_buffering && location.proxy_buffering.enabled) {
    config += `        proxy_buffering ${location.proxy_buffering.value};\n`;
  }
  
  // 添加：处理额外参数
  if (location.extraParams) {
    for (const [paramName, param] of Object.entries(location.extraParams)) {
      // 跳过已经处理过的if块
      if (paramName.startsWith('if_block_')) {
        continue; // 这些已经在前面处理过了
      }
      
      if (param.enabled) {
        // 常规参数
        config += `        ${paramName} ${param.value};\n`;
      }
    }
  }
  
  config += '    }';

  return config;
}




// 添加：从配置中提取location块额外参数
function extractLocationExtraParams(location, locationConfig) {
  if (!locationConfig) return;
  

  
  // 扩展已知参数列表以确保不会将常规参数视为额外参数
  const knownParams = [
    'proxy_pass', 'proxy_set_header', 'proxy_connect_timeout', 
    'proxy_read_timeout', 'proxy_send_timeout', 'proxy_http_version',
    'proxy_cache', 'proxy_buffering', 'proxy_cache_valid', 'proxy_cache_key',
    'proxy_ignore_headers', 'proxy_intercept_errors'
  ];
  
  try {
    const lines = locationConfig.split('\n');
    location.extraParams = {};
    
    // 修复：改进if块提取逻辑，确保可以提取多个if块
    let i = 0;
    let ifBlockCounter = 0;
    
    while (i < lines.length) {
      const line = lines[i].trim();
      
      // 检测if语句的开始
      if (line.startsWith('if ')) {
        // 保存if块的起始行号
        const startLineIndex = i;
        let ifBlock = [line];
        let braceCount = line.includes('{') ? 1 : 0;
        let foundOpeningBrace = line.includes('{');
        
        // 继续读取直到找到匹配的结束括号
        i++;
        while (i < lines.length && (braceCount > 0 || !foundOpeningBrace)) {
          const currentLine = lines[i].trim();
          ifBlock.push(lines[i]);
          
          if (currentLine.includes('{')) {
            braceCount++;
            foundOpeningBrace = true;
          }
          if (currentLine.includes('}')) {
            braceCount--;
          }
          
          if ((braceCount === 0 && foundOpeningBrace) || i === lines.length - 1) {
            break;
          }
          i++;
        }
        
        // 构建完整的if块
        const ifBlockKey = `if_block_${ifBlockCounter++}`;
        const completeIfBlock = ifBlock.join('\n');
        
    
        
        // 保存if块作为额外参数
        location.extraParams[ifBlockKey] = {
          enabled: true,
          value: completeIfBlock
        };
        
        // 关键修复：不再标记这些行为已处理，这样可以保留原始内容继续解析
        // 仅递增i继续处理下一行
        i++;
        continue;
      }
      
      // 检查是否是proxy_pass行
      if (line.startsWith('proxy_pass ')) {
        const proxyPassMatch = line.match(/proxy_pass\s+([^;]+);/);
        if (proxyPassMatch && proxyPassMatch[1]) {
          // 确保location对象中有proxy_pass属性
          location.proxy_pass = proxyPassMatch[1];
      
        }
      }
      
      // 匹配普通参数
      const paramMatch = line.match(/^([^\s;]+)\s+([^;]+);/);
      if (paramMatch) {
        const paramName = paramMatch[1];
        const paramValue = paramMatch[2];
        
        // 排除已知参数
        let isKnownParam = false;
        for (const knownParam of knownParams) {
          if (paramName === knownParam || paramName.startsWith(`${knownParam}_`)) {
            isKnownParam = true;
            break;
          }
        }
        
        // 排除proxy_set_header特定参数
        if (paramName === 'proxy_set_header') {
          const headerValue = paramValue.trim();
          if (headerValue.startsWith('Host') || 
              headerValue.startsWith('X-Real-IP') ||
              headerValue.startsWith('REMOTE-HOST') ||
              headerValue.startsWith('X-Forwarded-For') ||
              headerValue.startsWith('Upgrade') ||
              headerValue.startsWith('Connection')) {
            isKnownParam = true;
          }
        }
        
        if (!isKnownParam) {
      
          // 添加到额外参数中
          location.extraParams[paramName] = {
            enabled: true,
            value: paramValue
          };
        }
      }
      
      i++;
    }
    
 
  } catch (error) {

  }
}

// 添加：从配置中提取服务器额外参数
function extractServerExtraParams() {
  if (!props.isEdit || !props.editingData || !props.editingData.rawConfig) return {};

  
  // 扩展已知常规参数列表，这些不会被视为额外参数
  const knownServerParams = [
    'listen', 'server_name', 'client_max_body_size', 'location',
    'ssl_certificate', 'ssl_certificate_key', 'ssl_protocols',
    'ssl_ciphers', 'ssl_prefer_server_ciphers', 'ssl_session_cache',
    'ssl_session_timeout'
  ];
  
  // 特别需要包含的server块级别代理参数
  const serverProxyParams = [
    'proxy_next_upstream', 'proxy_redirect'
  ];

  // 代理超时参数，仅当server块内实际存在时才包含
  const serverProxyTimeoutParams = [
    'proxy_connect_timeout', 'proxy_read_timeout', 'proxy_send_timeout'
  ];

  // 应该排除的location块级别指令
  const locationSpecificParams = [
    'add_header', 'return', 'proxy_pass', 'proxy_set_header', 'proxy_http_version',
    'proxy_cache', 'proxy_buffering'
  ];
  
  const config = props.editingData.rawConfig;
  const serverExtraParams = {};
  
  try {
    // 尝试使用改进的括号匹配算法提取server块内容
    let serverContent = '';
    let serverBlockExtracted = false;
    
    // 方法1：使用正则表达式提取server块的所有内容 - 改进为支持嵌套块
    try {
      const serverStartRegex = /server\s*{/i;
      const startMatch = serverStartRegex.exec(config);
      
      if (startMatch) {
        // 找到server块的起始位置
        const startIndex = startMatch.index;
        let braceCount = 1; // 找到了开始的左大括号
        let endIndex = startIndex + 7; // 'server {'的长度
        
        // 找到匹配的结束大括号
        while (braceCount > 0 && endIndex < config.length) {
          const char = config[endIndex];
          if (char === '{') braceCount++;
          if (char === '}') braceCount--;
          endIndex++;
        }
        
        if (braceCount === 0) {
          // 成功找到匹配的结束大括号
          const serverBlock = config.substring(startIndex, endIndex);
          serverContent = serverBlock.substring(serverBlock.indexOf('{') + 1, serverBlock.lastIndexOf('}'));
          serverBlockExtracted = true;
        }
      }
    } catch (e) {
    
    }
    
    // 方法2：如果方法1失败，回退到更简单的行扫描方式
    if (!serverBlockExtracted) {
      
      const lines = config.split('\n');
      let inServerBlock = false;
      let serverLines = [];
      let openBraces = 0;
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        
        // 检测server块的开始
        if (!inServerBlock && trimmedLine.match(/^server\s*{/)) {
          inServerBlock = true;
          openBraces = 1;
          
          // 提取server行后面的内容（去除server {前缀）
          const content = trimmedLine.substring(trimmedLine.indexOf('{') + 1).trim();
          if (content) serverLines.push(content);
          continue;
        }
        
        // 如果我们在server块内，收集行
        if (inServerBlock) {
          // 计算当前行中的大括号数量
          for (const char of trimmedLine) {
            if (char === '{') openBraces++;
            if (char === '}') openBraces--;
          }
          
          // 如果我们到达了匹配的结束大括号，退出server块
          if (openBraces === 0 && trimmedLine.includes('}')) {
            // 只保留到大括号之前的内容
            const contentBeforeBrace = trimmedLine.substring(0, trimmedLine.lastIndexOf('}')).trim();
            if (contentBeforeBrace) serverLines.push(contentBeforeBrace);
            break;
          }
          
          // 否则添加整行
          serverLines.push(trimmedLine);
        }
      }
      
      // 如果我们收集了行，将它们合并
      if (serverLines.length > 0) {
        serverContent = serverLines.join('\n');
        serverBlockExtracted = true;
      }
    }
    
    // 方法3：如果服务器块仍然无法提取，则尝试直接使用整个配置作为备用
    if (!serverBlockExtracted) {
      // 尝试识别server块的位置，并保留其内容
      if (config.includes('server {') && config.includes('location {')) {
        serverContent = config;
        serverBlockExtracted = true;
      } else {
        // 如果仍无法识别，返回空对象
        return {};
      }
    }
    
    
    // 无论使用哪种方法提取到server块内容，现在开始分析它
    
    // 提取所有的location块
    const locationBlocks = [];
    const locationRegex = /\s*location\s+([^{]+)\s*{/g;
    let locationMatch;
    
    // 重置正则表达式的lastIndex
    locationRegex.lastIndex = 0;
    
    while ((locationMatch = locationRegex.exec(serverContent)) !== null) {
      const path = locationMatch[1].trim();
      const startPos = locationMatch.index;
      
      // 尝试找到匹配的结束大括号
      let openBraces = 1;
      let closePos = startPos + locationMatch[0].length;
      let locationEndFound = false;
      
      // 计算location块终止位置（寻找匹配的大括号）
      for (let i = closePos; i < serverContent.length; i++) {
        if (serverContent[i] === '{') openBraces++;
        if (serverContent[i] === '}') openBraces--;
        
        if (openBraces === 0) {
          // 找到匹配的结束大括号
          locationBlocks.push({
            path: path,
            content: serverContent.substring(startPos, i + 1),
            start: startPos,
            end: i + 1
          });
          locationEndFound = true;
          break;
        }
      }
      
      // 如果没有找到匹配的结束大括号，尝试查找下一个location的开始作为结束位置
      if (!locationEndFound) {
        const nextLocationMatch = locationRegex.exec(serverContent);
        if (nextLocationMatch) {
          locationRegex.lastIndex = locationMatch.index + 1; // 重置索引
          const nextLocationStart = nextLocationMatch.index;
          locationBlocks.push({
            path: path,
            content: serverContent.substring(startPos, nextLocationStart),
            start: startPos,
            end: nextLocationStart
          });
        } else {
          // 如果没有下一个location，使用到结尾的内容
          locationBlocks.push({
            path: path,
            content: serverContent.substring(startPos),
            start: startPos,
            end: serverContent.length
          });
        }
      }
    }
    
    
    // 创建标记哪些位置属于location块的掩码
    let isInLocationMask = new Array(serverContent.length).fill(false);
    
    // 标记所有location块中的内容
    for (const block of locationBlocks) {
      for (let i = block.start; i < block.end && i < isInLocationMask.length; i++) {
        isInLocationMask[i] = true;
      }
    }
    
    // 提取非location块内容，即纯server块配置
    let pureServerContent = '';
    for (let i = 0; i < serverContent.length; i++) {
      if (!isInLocationMask[i]) {
        pureServerContent += serverContent[i];
      }
    }
    
    
    // 按行分割并清理纯server块内容
    const serverLines = pureServerContent.split('\n')
                                        .map(line => line.trim())
                                        .filter(line => line && !line.startsWith('#'));
    
    // 检查server块中存在的超时参数
    const serverTimeoutParams = {
      proxy_connect_timeout: false,
      proxy_read_timeout: false,
      proxy_send_timeout: false
    };
    
    // 扫描server行查找超时参数和其他参数
    for (const line of serverLines) {
      // 检查超时参数
      for (const timeoutParam of serverProxyTimeoutParams) {
        if (line.startsWith(timeoutParam)) {
          serverTimeoutParams[timeoutParam] = true;
        }
      }
    }
    
    // 检查location块中的超时参数使用情况
    const locationTimeoutParams = {
      proxy_connect_timeout: false,
      proxy_read_timeout: false,
      proxy_send_timeout: false
    };
    
    for (const block of locationBlocks) {
      for (const timeoutParam of serverProxyTimeoutParams) {
        if (block.content.includes(timeoutParam) && 
            // 确保不是注释掉的参数
            !block.content.includes(`#${timeoutParam}`) &&
            !block.content.replace(/\s+/g, ' ').includes(`# ${timeoutParam}`)) {
          locationTimeoutParams[timeoutParam] = true;
        }
      }
    }
    
    // 现在处理服务器参数
    for (const line of serverLines) {
      const paramMatch = line.match(/^([^\s;]+)\s+([^;]+);/);
      if (!paramMatch) continue;
      
      const paramName = paramMatch[1];
      const paramValue = paramMatch[2];
      
      // 跳过注释行和已知常规服务器参数
      if (line.startsWith('#') || knownServerParams.includes(paramName)) {
        continue;
      }
      
      // 特殊处理：检查是否是服务器级别的代理参数
      if (serverProxyParams.includes(paramName)) {
        serverExtraParams[paramName] = {
          enabled: true,
          value: paramValue
        };
        continue;
      }
      
      // 处理超时参数 - 仅在server块中存在且不在任何location块中使用时作为server级别参数
      if (serverProxyTimeoutParams.includes(paramName) && serverTimeoutParams[paramName]) {
        // 即使在location块中也存在同名参数，仍然保留server块中的参数
        // 这是因为在Nginx中，server块和location块可以同时有相同的参数，它们独立作用
        serverExtraParams[paramName] = {
          enabled: true,
          value: paramValue
        };
        continue;
      }
      
      // 排除通常属于location块的参数
      if (locationSpecificParams.includes(paramName)) {
        continue;
      }
      
      // 处理其他未知参数
      serverExtraParams[paramName] = {
        enabled: true,
        value: paramValue
      };

    }
    
    return serverExtraParams;
  } catch (error) {
    console.error('[调试] 提取服务器额外参数时出错:', error);
    
    // 即使发生错误，也尝试从原始配置中提取关键参数
    try {
      const lines = config.split('\n');
      
      // 提取服务器级别的关键参数
      for (const line of lines) {
        const trimmedLine = line.trim();
        
        // 跳过注释行和location块
        if (trimmedLine.startsWith('#') || 
            trimmedLine.startsWith('location ') || 
            !trimmedLine.includes(';')) {
          continue;
        }
        
        // 尝试匹配参数
        const paramMatch = trimmedLine.match(/^([^\s;]+)\s+([^;]+);/);
        if (paramMatch) {
          const paramName = paramMatch[1];
          const paramValue = paramMatch[2];
          
          // 只提取服务器级别的代理参数和超时参数
          if (serverProxyParams.includes(paramName) || 
              serverProxyTimeoutParams.includes(paramName)) {
            
            if (!locationSpecificParams.includes(paramName)) {
              serverExtraParams[paramName] = {
                enabled: true,
                value: paramValue
              };
            }
          }
        }
      }
    } catch (backupError) {
      console.error('[调试] 备用参数提取也失败:', backupError);
    }
    
    return serverExtraParams;
  }
}



</script>

<style scoped>
.upstream-server-item {
  margin-bottom: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}

.server-basic-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.server-address {
  flex: 3;
}

.server-weight {
  flex: 1;
  min-width: 110px;
}

.server-advanced-params {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-top: 5px;
}

.advanced-param-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.advanced-param-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 10px;
}

.param-name {
  width: 120px;
  font-weight: bold;
}

.param-value {
  width: 150px;
}

.param-description {
  color: #909399;
  font-size: 0.9em;
}

.location-container {
  margin-bottom: 10px;
}

.location-item {
  margin-bottom: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}

.location-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.location-path {
  flex: 1;
}

.location-proxy {
  flex: 2;
}

.location-template {
  width: 140px;
}

.location-proxy-settings {
  margin-top: 10px;
  border-top: 1px dashed #ebeef5;
  padding-top: 10px;
}

.proxy-params-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 10px;
}

.upstream-global-params {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 10px;
}

.server-advanced-settings {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 10px;
}

.config-preview {
  background-color: #f5f7fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  font-family: monospace;
  white-space: pre-wrap;
  font-size: 12px;
  line-height: 1.5;
  overflow: auto;
  max-height: 400px;
}

.el-form-item-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  position: relative;
  bottom: 0;
  padding: 15px 0;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
  z-index: 10;
  margin-top: 20px;
}

/* 抽屉式组件的特殊样式 */
.drawer-form {
  padding: 0 20px 60px 20px;
  height: 100%;
  overflow-y: auto;
}

/* 覆盖element-plus的抽屉样式 */
:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-drawer__body) {
  padding: 0;
  overflow: hidden;
}
</style> 