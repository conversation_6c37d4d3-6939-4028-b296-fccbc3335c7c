import api from "@/plugins/axios/index";
import { data } from "autoprefixer";
import axios from "axios";

/**
 * 获取具有nginx配置相关的业务情况
 * @returns
 */
export function nginxList() {
  return api.get("/nginx_management/list");
}

/**
 *获取业务下的nginx配置
 * @param {*} data
 * @returns
 */
export function nginxConfig(data) {
  return api.post("/nginx_management", JSON.stringify(data));
}

/**
 * 更新nginx配置
 * @param {*} data
 * @returns
 */
export function updateNginx(data) {
  return api.post("/nginx_management/update_nginx_configuration/", JSON.stringify(data));
}

/**
 * 获取业务下nginx备份
 * @param {*} data
 * @returns
 */
export function getAllNginxBackup(data) {
  return api.post("/nginx_management/backup", JSON.stringify(data));
}

/**
 * 备份nginx配置文件
 * @param {*} data
 * @returns
 */
export function backupNginx(data) {
  return api.post("/nginx_management/backup_nginx_configuration_file/", JSON.stringify(data));
}

/**
 * 还原nginx备份文件
 * @param {*} data
 * @returns
 */
export function restoreNginx(data) {
  return api.patch("/nginx_management/backup", JSON.stringify(data));
}

/**
 * 获取服务器下nginx配置文件
 * @param {*} data
 * @returns
 */
export function getNginxFile(data) {
  return api.patch("/nginx_management", JSON.stringify(data));
}
