<script setup name="ColorfulCard">
defineProps({
    problem:{
        type: Number,
        default: 0
    },
    colorFrom: {
        type: String,
        default: '#843cf6'
    },
    colorTo: {
        type: String,
        default: '#759bff'
    },
    header: {
        type: String,
        default: ''
    },
    num: {
        type: String,
        default: ''
    },
    tip: {
        type: String,
        default: ''
    },
    status:{
          type: String,
        default: ''
    },
    isline:{
          type: Boolean,
        default: false
    },
    failed:{
        type: String,
        default: ''
    },
    icon: {
        type: String,
        default: ''
    },
    success:{
        type: String,
        default: ''
    },
    value:{
       type: Boolean,
        default: false
    },
    system:{
         type: String,
        default: ''
    },
    category:{
        type: String,
        default:''
    }
})
</script>

<template>
 <el-badge v-if="problem==0" hidden class="item">
    <el-card
        shadow="hover" class="mini-card" :style="{
            'background': `linear-gradient(50deg, ${colorFrom}, ${colorTo})`
        }"
    >
    <!-- <template #header> <el-button type="primary" circle @click="$emit('edit')">
              <svg-icon name="i-ep:edit" />
        </el-button>
         <el-button type="danger" circle @click="$emit('del')">
              <svg-icon name="i-ep:delete" />
        </el-button>
        </template> -->
        <template #header>{{ header }}</template>
        <div class="num">{{ num }}</div>
        <div class="tip" v-if="system!=''">系统：{{ system }}</div>
        <div class="tip">监控状态：<el-tag v-if="status=='offmonitor'" type="info"  effect="dark">未安装监控</el-tag><el-tag v-else-if="status=='offline'" type="danger"  effect="dark">停用的</el-tag><el-tag v-else-if="status=='online'" type="success"  effect="dark">已启用</el-tag></div>
         <div v-if="category!='network'" class="tip">连接状态：<el-tag v-if="isline==false" type="danger"  effect="dark">离线</el-tag><el-tag v-else-if="isline==true" type="success"  effect="dark">在线</el-tag></div>
         <!-- <div class="tip">安装监控：<el-button size="small" @click="$emit('install')">安装</el-button></div> -->
        <!-- <div class="tip1"><el-switch
    v-model="value"
    class="ml-2"
    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
  /></div> -->
        <!-- <div class="tip"><el-tag type="success"  effect="dark">{{failed}}</el-tag></div> -->
        <div class="edit">
            <el-tooltip
        class="box-item"
        effect="dark"
        content="安装监控"
        placement="top-start"
      >
        <el-button v-if="status=='offmonitor'" color="#C0C4CC" :dark="true" circle  @click="$emit('install') ">
                <svg-icon name="ep:coordinate" />
        </el-button>
 </el-tooltip>
            <el-tooltip
        class="box-item"
        effect="dark"
        content="最新数据"
        placement="top-start"
      >
             <el-button v-if="status=='online'"  color="#626aef" :dark="true" circle  @click="$emit('view') ">
                <svg-icon name="ep:view" />
        </el-button>
            </el-tooltip>
            <el-tooltip
        class="box-item"
        effect="dark"
        content="编辑"
        placement="top-start"
      >
      <el-button type="primary" circle @click="$emit('edit')">
              <svg-icon name="ep:edit" />
        </el-button>
            </el-tooltip>
         <el-tooltip
        class="box-item"
        effect="dark"
        content="ssh连接"
        placement="top-start"
      >
        <el-button v-if="isline==true&&system=='linux'" type="success" circle  @click="$emit('sshLink') ">
              <svg-icon name="ep:link" />
        </el-button></el-tooltip>
         <el-tooltip
        class="box-item"
        effect="dark"
        content="执行任务"
        placement="top-start"
      >
        <el-button v-if="category!='network'" color="#be22dd" :dark="true" circle  @click="$emit('execute') ">
              <svg-icon name="ep:edit-pen" />
        </el-button></el-tooltip>
        <el-popover placement="top" :width="250">
    <div style="text-align: center;padding-bottom:5px;">
  <span style="font-size: 16px; font-weight: bold;">请选择你想要删除的内容：</span>
</div>
    <div style="text-align: center; margin: 0">
        <!-- <el-button v-if="(category=='linux'||category=='windows')&&status=='online'" size="small" type="danger" @click="$emit('delall')"
        >全部删除</el-button> -->
      <el-button size="small" type="danger" @click="$emit('delres')"
        >删除资源</el-button>
        <!-- <el-button v-if="status=='online'" size="small" type="danger"  @click="$emit('delzabbix')"
        >删除zabbix资源</el-button>
        <el-button v-if="(category=='linux'||category=='windows')&&status=='online'" size="small" type="danger"  @click="$emit('delagent')"
        >删除agent</el-button> -->
    </div>
    <template #reference>
       <el-button type="danger" circle>
              <svg-icon name="ep:delete" />
        </el-button>
    </template>
  </el-popover>
        </div>
        <el-icon v-if="icon">
            <svg-icon :name="icon"  />
        </el-icon>
    </el-card>
 </el-badge>
 <el-badge v-else :value="problem" class="item" size="medium">
    <el-card
        shadow="hover" class="mini-card" :style="{
            'background': `linear-gradient(50deg, ${colorFrom}, ${colorTo})`
        }"
    >
    <!-- <template #header> <el-button type="primary" circle @click="$emit('edit')">
              <svg-icon name="i-ep:edit" />
        </el-button>
         <el-button type="danger" circle @click="$emit('del')">
              <svg-icon name="i-ep:delete" />
        </el-button>
        </template> -->
        <template #header>{{ header }}</template>
        <div class="num">{{ num }}</div>
        <div class="tip" v-if="system!=''">系统：{{ system }}</div>
        <div class="tip">监控状态：<el-tag v-if="status=='offmonitor'" type="info"  effect="dark">未安装监控</el-tag><el-tag v-else-if="status=='offline'" type="danger"  effect="dark">停用的</el-tag><el-tag v-else-if="status=='online'" type="success"  effect="dark">已启用</el-tag></div>
         <div v-if="category!='network'" class="tip">连接状态：<el-tag v-if="isline==false" type="danger"  effect="dark">离线</el-tag><el-tag v-else-if="isline==true" type="success"  effect="dark">在线</el-tag></div>
        <!-- <div class="tip">安装监控：<el-button size="small" @click="$emit('install')">安装</el-button></div> -->
        <!-- <div class="tip1"><el-switch
    v-model="value"
    class="ml-2"
    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
  /></div> -->
        <!-- <div class="tip"><el-tag type="success"  effect="dark">{{failed}}</el-tag></div> -->
        <div class="edit">
            <el-tooltip
        class="box-item"
        effect="dark"
        content="安装监控"
        placement="top-start"
      >
        <el-button v-if="status=='offmonitor'" color="#C0C4CC" :dark="true" circle  @click="$emit('install') ">
                <svg-icon name="ep:coordinate" />
        </el-button>
 </el-tooltip>
             <el-tooltip
        class="box-item"
        effect="dark"
        content="最新数据"
        placement="top-start"
      >
             <el-button v-if="status=='online'"  color="#626aef" :dark="true" circle  @click="$emit('view') ">
                <svg-icon name="ep:view" />
        </el-button>
             </el-tooltip>
         <el-tooltip
        class="box-item"
        effect="dark"
        content="告警"
        placement="top-start"
      >
        <el-button v-if="problem!=0" color="#eebe77" :dark="true" circle  @click="$emit('warning') ">
                <svg-icon name="ep:warning" />
        </el-button></el-tooltip>
        <el-tooltip
        class="box-item"
        effect="dark"
        content="编辑"
        placement="top-start"
      >
      <el-button type="primary" circle @click="$emit('edit')">
              <svg-icon name="ep:edit" />
        </el-button>
            </el-tooltip>
         <el-tooltip
        class="box-item"
        effect="dark"
        content="ssh连接"
        placement="top-start"
      >
        <el-button v-if="isline==true&&system=='linux'" type="success" circle  @click="$emit('sshLink') ">
              <svg-icon name="ep:link" />
        </el-button></el-tooltip>
         <el-tooltip
        class="box-item"
        effect="dark"
        content="执行任务"
        placement="top-start"
      >
        <el-button v-if="category!='network'" color="#be22dd" :dark="true" circle  @click="$emit('execute') ">
              <svg-icon name="ep:edit-pen" />
        </el-button></el-tooltip>

      <el-popover placement="top" :width="250">
    <div style="text-align: center;padding-bottom:5px;">
  <span style="font-size: 16px; font-weight: bold;">请选择你想要删除的内容：</span>
</div>
    <div style="text-align: center; margin: 0">
      <!-- <el-button v-if="(category=='linux'||category=='windows')&&status=='online'" size="small" type="danger" @click="$emit('delall')"
        >全部删除</el-button> -->
      <el-button size="small" type="danger" @click="$emit('delres')"
        >删除资源</el-button>
        <!-- <el-button v-if="status=='online'" size="small" type="danger"  @click="$emit('delzabbix')"
        >删除zabbix资源</el-button>
        <el-button v-if="(category=='linux'||category=='windows')&&status=='online'" size="small" type="danger"  @click="$emit('delagent')"
        >删除agent</el-button> -->
    </div>
    <template #reference>
       <el-button type="danger" circle>
              <svg-icon name="ep:delete" />
        </el-button>
    </template>
  </el-popover>

        </div>
        <el-icon v-if="icon">
            <svg-icon :name="icon"  />
        </el-icon>
    </el-card>
 </el-badge>
</template>

<style lang="scss" scoped>
.mini-card {
    position: relative;
    color: #fff;
    text-shadow: 0 0 2px #000;
    cursor: pointer;
    :hover {
        .el-icon {
            right: 0;
            top: 0;
        }
    }
    :deep(.el-card__header) {
        position: relative;
        z-index: 1;
        border-bottom: 0;
        font-size: 18px;
    }
    :deep(.el-card__body) {
        padding-top: 0;
    }
    .num {
        position: relative;
        z-index: 1;
        font-size: 20px;
             overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 使用省略号表示超出部分 */
    white-space: nowrap; /* 防止内容换行 */
    }
    .tip {
        //  display: inline-block; /* 将div1设置为行内块元素 */
        margin-top: 10px;
        font-size: 14px;
        color: #eee;
    }
    .tip1 {
         display: inline-block; /* 将div1设置为行内块元素 */
        margin-top: 10px;
        font-size: 14px;
        color: #eee;
        padding-left: 10px;
    }
    .el-icon {
        transition: 0.3s;
        font-size: 120px;
        position: absolute;
        right: -30px;
        top: -10px;
    }
    .edit {
        text-align: left;
        margin-top: 25px;
    }
}
.item {
//   margin-top: 10px;
  width: 100%;
}
</style>
