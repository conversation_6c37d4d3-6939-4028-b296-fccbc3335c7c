<script setup lang="ts">
import { ElMessageBox } from "element-plus";
import type Node from "element-plus/es/components/tree/src/model/node";
import dictionaryDialog from "./components/dictionaryDialog/index.vue";
import dictionaryItemDialog from "./components/dictionaryItemDialog/index.vue";
import { apiDictionarydelete, apiDictionarylist, apiDictionaryeAddUpdate } from "@/api/modules/dictionary/index";

interface Dict {
  id: string | number;
  dictionary_name: string;
  dictionary_code: string;
  dictionary_value: [];
}
const dictionaryRef = ref();
const dictionary = ref({
  search: "",
  tree: [] as Dict[],
  currentNode: undefined as Node | undefined,
  currentData: undefined as Dict | undefined,
  dialog: {
    visible: false,

    id: "" as Dict["id"],
  },
});

const dictionaryItemRef = ref();
const dictionaryItem = ref({
  loading: false,
  // 搜索
  search: {
    dictionaryId: "" as Dict["id"],
    title: "",
  },
  // 列表数据
  dataList: [],
  selectionDataList: [],
  dialog: {
    visible: false,
    id: "" as string | number,
  },
});

function getDictionaryList() {
  dictionaryItem.value.search.dictionaryId = "";
  apiDictionarylist().then((res) => {
    dictionary.value.tree = res.data;
  });
}
onMounted(() => {
  getDictionaryList();
});
watch(
  () => dictionary.value.search,
  (val) => {
    dictionaryRef.value!.filter(val);
  }
);
function dictionaryFilter(value: string, data: Dict) {
  if (!value) {
    return true;
  }
  return data.dictionary_name.includes(value);
}
// 新增
function dictionaryAdd(data?: Dict) {
  dictionary.value.currentData = data;

  dictionary.value.dialog.id = "";
  dictionary.value.dialog.visible = true;
}
// 编辑
function dictionaryEdit(node: Node, data: Dict) {
  dictionary.value.currentNode = node;
  dictionary.value.currentData = data;

  dictionary.value.dialog.id = data.id;
  dictionary.value.dialog.visible = true;
}
// 删除
function dictionaryDelete(node: Node, data: Dict) {
  ElMessageBox.confirm(`确认删除「${data.dictionary_name}」吗？`, "确认信息").then(() => {
    apiDictionarydelete(data.id).then(() => {
      const parent = node.parent;
      const children: Dict[] = parent.data.children || parent.data;
      const index = children.findIndex((d) => d.id === data.id);
      children.splice(index, 1);
      dictionary.value.tree = [...dictionary.value.tree];
    });
  });
}
function dictionaryClick(data: Dict) {
  dictionaryItem.value.search.dictionaryId = data.id;
}

watch(
  () => dictionaryItem.value.search.dictionaryId,
  () => {
    getDictionaryItemList();
  }
);

function getDictionaryItemList() {
  const params = {
    dictionary_id: dictionaryItem.value.search.dictionaryId,
    ...(dictionaryItem.value.search.title && { title: dictionaryItem.value.search.title }),
  };
  dictionaryItem.value.loading = true;
  apiDictionarylist()
    .then((res) => {
      const { data } = res;
      dictionary.value.tree = data;
      data.forEach((item) => {
        if (item.id == params.dictionary_id) {
          let itemTemp = item.dictionary_value.filter((item) => {
            return params.title ? item.key.includes(params.title) : true;
          });
          dictionaryItem.value.dataList = itemTemp || [];
        }
      });
    })
    .finally(() => {
      dictionaryItem.value.loading = false;
    });
}

function onCreate() {
  dictionaryItem.value.dialog.id = "";
  dictionaryItem.value.dialog.visible = true;
}

function onEdit(row: any) {
  dictionaryItem.value.dialog.id = row.id;
  dictionaryItem.value.dialog.visible = true;
}
// item删除
function onDelete(row: any) {
  const currentDic = dictionary.value.tree.filter((item) => {
    return dictionaryItem.value.search.dictionaryId == item.id;
  })[0];
  let itemsParams = currentDic.dictionary_value.filter((item: any) => {
    return item.id != row.id;
  });
  const params = {
    ...currentDic,
    dictionary_value: itemsParams,
  };

  ElMessageBox.confirm(`确认删除「${row.key}」吗？`, "确认信息")
    .then(() => {
      dictionaryItem.value.loading = true;
      apiDictionaryeAddUpdate(params)
        .then(() => {
          getDictionaryItemList();
        })
        .finally(() => {
          dictionaryItem.value.loading = false;
        });
    })
    .catch(() => {});
}

// item批量删除
function onDeleteMulti(rows: any[]) {
  const idArr = rows.map((item) => item.id);

  const currentDic = dictionary.value.tree.filter((item) => {
    return dictionaryItem.value.search.dictionaryId == item.id;
  })[0];
  const itemParams = currentDic.dictionary_value.filter((item: any) => {
    return !idArr.includes(item.id);
  });
  const params = {
    ...currentDic,
    dictionary_value: itemParams,
  };
  ElMessageBox.confirm(`确认删除选中的 ${rows.length} 条数据吗？`, "确认信息")
    .then(() => {
      apiDictionaryeAddUpdate(params).then(() => {
        getDictionaryItemList();
      });
    })
    .catch(() => {});
}
</script>

<template>
  <div class="absolute-container">
    <div class="page-main">
      <LayoutContainer :hide-left-side-toggle="false" :left-side-width="300">
        <template #leftSide>
          <el-button-group class="btns">
            <el-button type="primary" class="add" @click="dictionaryAdd()">新增字典</el-button>
            <el-button @click="getDictionaryList">
              <template #icon>
                <el-icon>
                  <svg-icon name="i-ep:refresh" />
                </el-icon>
              </template>
            </el-button>
          </el-button-group>
          <el-input v-model="dictionary.search" placeholder="请输入关键词筛选字典" clearable class="search" />
          <el-scrollbar class="tree">
            <ElTree
              ref="dictionaryRef"
              :data="dictionary.tree"
              :filter-node-method="dictionaryFilter"
              default-expand-all
              @node-click="dictionaryClick"
              node-key="id"
            >
              <template #default="{ node, data }">
                <div class="custom-tree-node">
                  <div class="label" :title="data.dictionary_name">
                    {{ data.dictionary_name }}
                  </div>
                  <div class="code">
                    {{ data.dictionary_code }}
                  </div>
                  <div class="actions">
                    <el-button-group>
                      <el-button type="info" plain size="default" @click.stop="dictionaryEdit(node, data)">
                        <template #icon>
                          <el-icon>
                            <svg-icon name="i-ep:edit" />
                          </el-icon>
                        </template>
                      </el-button>
                      <el-button type="danger" plain size="default" @click.stop="dictionaryDelete(node, data)">
                        <template #icon>
                          <el-icon>
                            <svg-icon name="i-ep:delete" />
                          </el-icon>
                        </template>
                      </el-button>
                    </el-button-group>
                  </div>
                </div>
              </template>
            </ElTree>
          </el-scrollbar>
        </template>
        <div v-show="dictionaryItem.search.dictionaryId" class="container">
          <el-space wrap>
            <el-button type="primary" @click="onCreate">
              <template #icon>
                <el-icon>
                  <svg-icon name="i-ep:plus" />
                </el-icon>
              </template>
            </el-button>
            <el-button
              type="danger"
              :disabled="!dictionaryItem.selectionDataList.length"
              @click="onDeleteMulti(dictionaryItem.selectionDataList)"
            >
              <template #icon>
                <el-icon>
                  <svg-icon name="i-ep:delete" />
                </el-icon>
              </template>
            </el-button>
            <el-input
              v-model="dictionaryItem.search.title"
              placeholder="请输入关键词筛选字典项"
              clearable
              style="width: 200px"
              @keyup.enter="getDictionaryItemList"
            />
            <el-button @click="getDictionaryItemList">
              <template #icon>
                <el-icon>
                  <svg-icon name="i-ep:search" />
                </el-icon>
              </template>
            </el-button>
          </el-space>
          <el-table
            ref="dictionaryItemRef"
            v-loading="dictionaryItem.loading"
            :data="dictionaryItem.dataList"
            border
            stripe
            highlight-current-row
            height="100%"
            @selection-change="dictionaryItem.selectionDataList = $event"
          >
            <el-table-column type="selection" align="center" fixed />
            <el-table-column prop="key" label="名称" />
            <el-table-column label="键值" align="center" width="150">
              <template #default="scope">
                <el-tag type="info">
                  {{ scope.row.value }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" align="center">
              <template #default="scope">
                <el-button type="primary" size="small" plain @click="onEdit(scope.row)">编辑</el-button>
                <el-button type="danger" size="small" plain @click="onDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-show="!dictionaryItem.search.dictionaryId" class="container">
          <div class="empty">请在左侧新增或选择一个字典</div>
        </div>
      </LayoutContainer>
      <dictionaryDialog
        v-if="dictionary.dialog.visible"
        :id="dictionary.dialog.id"
        v-model="dictionary.dialog.visible"
        :tree="dictionary.tree"
        @success="getDictionaryList"
      />
      <dictionaryItemDialog
        v-if="dictionaryItem.dialog.visible"
        :id="dictionaryItem.dialog.id"
        v-model="dictionaryItem.dialog.visible"
        :dictionary-id="dictionaryItem.search.dictionaryId"
        :tree="dictionary.tree"
        @success="getDictionaryItemList"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.absolute-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 0;
  }

  .page-main {
    // 让 page-main 的高度自适应
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;

    .flex-container {
      position: static;
    }
  }
}

.flex-container {
  :deep(.left-side) {
    display: flex;
    flex-direction: column;
    height: 100%;

    .btns {
      display: inline-flex;
      width: 100%;

      .add {
        width: 100%;
      }
    }

    .search {
      margin: 15px 0;
    }

    .tree {
      flex: 1;
      overflow-y: auto;

      .el-tree {
        .el-tree-node__content {
          height: 60px;
        }

        .is-current > .el-tree-node__content {
          background-color: var(--el-color-primary-light-9);
        }

        .custom-tree-node {
          flex: 1;
          position: relative;
          width: 0;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .label {
            width: calc(100% - 10px);
            color: var(--el-text-color-primary);

            @include text-overflow;
          }

          .code {
            width: calc(100% - 10px);
            color: var(--el-text-color-placeholder);

            @include text-overflow;
          }

          &:hover {
            .actions {
              display: block;
            }
          }

          .actions {
            display: none;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);

            .el-button {
              padding: 5px 8px;
            }
          }
        }
      }
    }
  }

  :deep(.main) {
    display: flex;
    justify-content: center;
  }

  .container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 100%;

    .empty {
      text-align: center;
      font-size: 32px;
      color: var(--el-text-color-placeholder);
    }

    .el-table {
      margin: 15px 0;
    }
  }
}
</style>
