{"prefix": "line-md", "info": {"name": "Material Line Icons", "total": 346, "version": "0.2.4", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyberalien/line-md"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/cyberalien/line-md/blob/master/license.txt"}, "samples": ["loading-twotone-loop", "beer-alt-twotone-loop", "image-twotone"], "height": 24, "category": "Animated Icons", "palette": false}, "lastModified": **********, "icons": {"account": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"28\" stroke-dashoffset=\"28\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M4 21V20C4 16.6863 6.68629 14 10 14H14C17.3137 14 20 16.6863 20 20V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"28;0\"/></path><path d=\"M12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7C16 9.20914 14.2091 11 12 11Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"28;0\"/></path></g>"}, "account-add": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 21V20C3 17.7909 4.79086 16 7 16H11C13.2091 16 15 17.7909 15 20V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M9 13C7.34315 13 6 11.6569 6 10C6 8.34315 7.34315 7 9 7C10.6569 7 12 8.34315 12 10C12 11.6569 10.6569 13 9 13Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M15 6H21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M18 3V9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "account-alert": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M5 21V20C5 17.7909 6.79086 16 9 16H13C15.2091 16 17 17.7909 17 20V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M11 13C9.34315 13 8 11.6569 8 10C8 8.34315 9.34315 7 11 7C12.6569 7 14 8.34315 14 10C14 11.6569 12.6569 13 11 13Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M20 3V7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"6;0\"/></path></g><circle cx=\"20\" cy=\"11\" r=\"1\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.4s\" values=\"0;1\"/></circle>"}, "account-delete": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 21V20C3 17.7909 4.79086 16 7 16H11C13.2091 16 15 17.7909 15 20V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M9 13C7.34315 13 6 11.6569 6 10C6 8.34315 7.34315 7 9 7C10.6569 7 12 8.34315 12 10C12 11.6569 10.6569 13 9 13Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M15 3L21 9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M21 3L15 9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"10;0\"/></path></g>"}, "account-remove": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 21V20C3 17.7909 4.79086 16 7 16H11C13.2091 16 15 17.7909 15 20V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M9 13C7.34315 13 6 11.6569 6 10C6 8.34315 7.34315 7 9 7C10.6569 7 12 8.34315 12 10C12 11.6569 10.6569 13 9 13Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M15 6H21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "account-small": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M6 19V18C6 15.7909 7.79086 14 10 14H14C16.2091 14 18 15.7909 18 18V19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"20;0\"/></path><path d=\"M12 11C10.3431 11 9 9.65685 9 8C9 6.34315 10.3431 5 12 5C13.6569 5 15 6.34315 15 8C15 9.65685 13.6569 11 12 11Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"20;0\"/></path></g>"}, "alert": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3L21 20H3L12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 10V14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path></g><circle cx=\"12\" cy=\"17\" r=\"1\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.4s\" values=\"0;1\"/></circle>"}, "alert-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 7V13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"8;0\"/></path></g><circle cx=\"12\" cy=\"17\" r=\"1\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.4s\" values=\"0;1\"/></circle>"}, "alert-circle-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 7V13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"8;0\"/></path></g><circle cx=\"12\" cy=\"17\" r=\"1\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.4s\" values=\"0;1\"/></circle>"}, "alert-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3L21 20H3L12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 10V14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path></g><circle cx=\"12\" cy=\"17\" r=\"1\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.4s\" values=\"0;1\"/></circle>"}, "align-center": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M12 5H19M12 5H5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"9;0\"/></path><path stroke-dasharray=\"7\" stroke-dashoffset=\"7\" d=\"M12 10H17M12 10H7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"7;0\"/></path><path stroke-dasharray=\"11\" stroke-dashoffset=\"11\" d=\"M12 15H21M12 15H3\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"11;0\"/></path><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M12 20H19M12 20H5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"9;0\"/></path></g>"}, "align-justify": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M12 5H18M12 5H6\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"8;0\"/></path><path d=\"M12 10H18M12 10H6\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"8;0\"/></path><path d=\"M12 15H18M12 15H6\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"8;0\"/></path><path d=\"M12 20H18M12 20H6\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "align-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M4 5H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M4 10H14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M4 15H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"18;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M4 20H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"15;0\"/></path></g>"}, "arrow-align-center": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M12 3V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"20;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M22 12H15.5M2 12H8.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M15 12L18 15M9 12L6 15M15 12L18 9M9 12L6 9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "arrow-align-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 3V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"20;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M21 12H7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M7 12L11 16M7 12L11 8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "arrow-close-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 3V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"20;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M21 12H7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M7 12L14 19M7 12L14 5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "arrow-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M21 12H3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"20;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M3 12L10 19M3 12L10 5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "arrow-left-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"12\" r=\"9\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></circle><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M17 12H7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M7 12L11 16M7 12L11 8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "arrow-left-circle-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"12\" r=\"9\" fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.15s\" values=\"0;0.3\"/></circle><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M17 12H7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M7 12L11 16M7 12L11 8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "arrow-long-diagonal": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12 12L3.5 20.5M12 12L20.5 3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"14;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M21 3H13M3 21V13M21 3V11M3 21H11\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "arrow-open-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M21 3V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"20;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M17 12H3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M3 12L10 19M3 12L10 5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "arrow-small-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M19 12H5.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"14;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M5 12L10 17M5 12L10 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "arrows-diagonal": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M10 14L3.5 20.5M14 10L20.5 3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"10;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M21 3H15M3 21V15M21 3V9M3 21H9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "arrows-horizontal": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M15 7H3.5M9 17H20.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"12;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M3 7L7 11M3 7L7 3M21 17L17 21M21 17L17 13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "arrows-horizontal-alt": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M21 7H10.5M3 17H13.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"12;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M10 7L14 11M10 7L14 3M14 17L10 21M14 17L10 13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "beer": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M18 3L16 21H7L5 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"60;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 7.67C6.6 7.3 7.22 7 8 7C10 7 11 9 13 9C14.64 9 15.6 7.66 17 7.17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "beer-alt-filled": {"body": "<mask id=\"svgIDa\"><g transform=\"translate(0 14)\"><path fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 7.67C6.6 7.3 7.22 7 8 7C10 7 11 9 13 9C14.64 9 15.6 7.66 17 7.17\"/><path fill=\"#fff\" d=\"M17 8L16 21H7L6 8z\"/><animateMotion fill=\"freeze\" begin=\"0.6s\" calcMode=\"linear\" dur=\"0.6s\" path=\"M0 0v-14\"/></g></mask><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 3L16 21H7L5 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"60;0\"/></path><rect width=\"11\" height=\"14\" x=\"6\" y=\"6\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "beer-alt-filled-loop": {"body": "<mask id=\"svgIDa\"><g transform=\"translate(0 14)\"><path stroke=\"#fff\" stroke-width=\"2\" d=\"M18 7C16 7 15 9 13 9C11 9 10 7 8 7C6 7 5 9 3 9C1 9 0 7 -2 7C-4 7 -5 9 -7 9\"><animateMotion calcMode=\"linear\" dur=\"3s\" path=\"M0 0h10\" repeatCount=\"indefinite\"/></path><path fill=\"#fff\" d=\"M17 8L16 21H7L6 8z\"/><animateMotion fill=\"freeze\" begin=\"0.6s\" calcMode=\"linear\" dur=\"0.6s\" path=\"M0 0v-14\"/></g></mask><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 3L16 21H7L5 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"60;0\"/></path><rect width=\"11\" height=\"14\" x=\"6\" y=\"6\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "beer-alt-twotone": {"body": "<mask id=\"svgIDa\"><g transform=\"translate(0 14)\"><path fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 7.67C6.6 7.3 7.22 7 8 7C10 7 11 9 13 9C14.64 9 15.6 7.66 17 7.17\"/><path fill=\"#fff\" fill-opacity=\".3\" d=\"M17 8L16 21H7L6 8z\"/><animateMotion fill=\"freeze\" begin=\"0.6s\" calcMode=\"linear\" dur=\"0.6s\" path=\"M0 0v-14\"/></g></mask><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 3L16 21H7L5 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"60;0\"/></path><rect width=\"11\" height=\"14\" x=\"6\" y=\"6\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "beer-alt-twotone-loop": {"body": "<mask id=\"svgIDa\"><g transform=\"translate(0 14)\"><path stroke=\"#fff\" stroke-width=\"2\" d=\"M18 7C16 7 15 9 13 9C11 9 10 7 8 7C6 7 5 9 3 9C1 9 0 7 -2 7C-4 7 -5 9 -7 9\"><animateMotion calcMode=\"linear\" dur=\"3s\" path=\"M0 0h10\" repeatCount=\"indefinite\"/></path><path fill=\"#fff\" fill-opacity=\".3\" d=\"M17 8L16 21H7L6 8z\"/><animateMotion fill=\"freeze\" begin=\"0.6s\" calcMode=\"linear\" dur=\"0.6s\" path=\"M0 0v-14\"/></g></mask><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 3L16 21H7L5 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"60;0\"/></path><rect width=\"11\" height=\"14\" x=\"6\" y=\"6\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "beer-filled": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M18 3L16 21H7L5 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"60;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 7.67C6.6 7.3 7.22 7 8 7C10 7 11 9 13 9C14.64 9 15.6 7.66 17 7.17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path></g><path fill=\"currentColor\" fill-opacity=\"0\" d=\"M17 8L16 21H7L6 8z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.5s\" values=\"0;1\"/></path>"}, "beer-loop": {"body": "<mask id=\"svgIDa\"><path stroke=\"#fff\" stroke-width=\"2\" d=\"M18 7C16 7 15 9 13 9C11 9 10 7 8 7C6 7 5 9 3 9C1 9 0 7 -2 7C-4 7 -5 9 -7 9\" opacity=\"0\"><animateMotion calcMode=\"linear\" dur=\"3s\" path=\"M0 0h10\" repeatCount=\"indefinite\"/><animate fill=\"freeze\" attributeName=\"opacity\" begin=\"0.6s\" dur=\"0.5s\" values=\"0;1\"/></path></mask><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 3L16 21H7L5 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"60;0\"/></path><path fill=\"currentColor\" d=\"M18 3L16 21H7L5 3z\" mask=\"url(#svgIDa)\"/>"}, "beer-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M18 3L16 21H7L5 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"60;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 7.67C6.6 7.3 7.22 7 8 7C10 7 11 9 13 9C14.64 9 15.6 7.66 17 7.17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path></g><path fill=\"currentColor\" fill-opacity=\"0\" d=\"M17 8L16 21H7L6 8z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></path>"}, "beer-twotone-loop": {"body": "<mask id=\"svgIDa\"><path stroke=\"#fff\" stroke-width=\"2\" d=\"M18 7C16 7 15 9 13 9C11 9 10 7 8 7C6 7 5 9 3 9C1 9 0 7 -2 7C-4 7 -5 9 -7 9\" opacity=\"0\"><animateMotion calcMode=\"linear\" dur=\"3s\" path=\"M0 0h10\" repeatCount=\"indefinite\"/><animate fill=\"freeze\" attributeName=\"opacity\" begin=\"0.6s\" dur=\"0.5s\" values=\"0;1\"/></path></mask><path fill=\"currentColor\" fill-opacity=\"0\" d=\"M17 8L16 21H7L6 8z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.1s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 3L16 21H7L5 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"60;0\"/></path><path fill=\"currentColor\" d=\"M18 3L16 21H7L5 3z\" mask=\"url(#svgIDa)\"/>"}, "bell": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"4\" stroke-dashoffset=\"4\" d=\"M12 3V5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"4;0\"/></path><path stroke-dasharray=\"28\" stroke-dashoffset=\"28\" d=\"M12 5C8.68629 5 6 7.68629 6 11L6 17C5 17 4 18 4 19H12M12 5C15.3137 5 18 7.68629 18 11L18 17C19 17 20 18 20 19H12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.4s\" values=\"28;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M10 20C10 21.1046 10.8954 22 12 22C13.1046 22 14 21.1046 14 20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "bell-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"4\" stroke-dashoffset=\"4\" d=\"M12 3V5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"4;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"28\" stroke-dashoffset=\"28\" d=\"M12 5C8.68629 5 6 7.68629 6 11L6 17C5 17 4 18 4 19H12M12 5C15.3137 5 18 7.68629 18 11L18 17C19 17 20 18 20 19H12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.4s\" values=\"28;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M10 20C10 21.1046 10.8954 22 12 22C13.1046 22 14 21.1046 14 20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "buy-me-a-coffee": {"body": "<mask id=\"svgIDa\"><path fill=\"#fff\" d=\"M5 6C5 4 7 6 11.5 6C16 6 19 4 19 6L19 7C19 8.5 17 9 12.5 9C8 9 5 9 5 7L5 6Z\"/></mask><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"32\" stroke-dashoffset=\"32\" d=\"M7.5 10.5C7.5 10.5 8.33 17.43 8.5 19C8.67 20.57 10 21 11 21L13 21C14.5 21 15.875 19.86 16 19C16.125 18.14 17 7 17 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"32;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M16.5 6C16.5 3.5 14 3 12 3C10 3 9.1 3.43 8 4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"12;24\"/></path></g><rect width=\"16\" height=\"5\" x=\"20\" y=\"4\" fill=\"currentColor\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"x\" begin=\"0.4s\" dur=\"0.4s\" values=\"20;4\"/></rect>"}, "buy-me-a-coffee-filled": {"body": "<mask id=\"svgIDa\"><path fill=\"#fff\" d=\"M5 6C5 4 7 6 11.5 6C16 6 19 4 19 6L19 7C19 8.5 17 9 12.5 9C8 9 5 9 5 7L5 6Z\"/></mask><mask id=\"svgIDb\"><path fill=\"#fff\" d=\"M10.125 18.15C10.04 17.29 9.4 11.98 9.4 11.98C9.4 11.98 11.34 12.31 12.5 11.73C13.66 11.16 14.98 11 14.98 11C14.98 11 14.4 17.96 14.35 18.46C14.3 18.96 13.45 19.3 12.95 19.3L11.23 19.3C10.73 19.3 10.21 19 10.125 18.15Z\"/></mask><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"32\" stroke-dashoffset=\"32\" d=\"M7.5 10.5C7.5 10.5 8.33 17.43 8.5 19C8.67 20.57 10 21 11 21L13 21C14.5 21 15.875 19.86 16 19C16.125 18.14 17 7 17 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"32;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M16.5 6C16.5 3.5 14 3 12 3C10 3 9.1 3.43 8 4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"12;24\"/></path></g><rect width=\"16\" height=\"5\" x=\"20\" y=\"4\" fill=\"currentColor\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"x\" begin=\"0.4s\" dur=\"0.4s\" values=\"20;4\"/></rect><rect width=\"8\" height=\"10\" x=\"8\" y=\"20\" fill=\"currentColor\" mask=\"url(#svgIDb)\"><animate fill=\"freeze\" attributeName=\"y\" begin=\"1s\" dur=\"0.4s\" values=\"20;10\"/></rect>"}, "buy-me-a-coffee-twotone": {"body": "<mask id=\"svgIDa\"><path fill=\"#fff\" d=\"M5 6C5 4 7 6 11.5 6C16 6 19 4 19 6L19 7C19 8.5 17 9 12.5 9C8 9 5 9 5 7L5 6Z\"/></mask><mask id=\"svgIDb\"><path fill=\"#fff\" d=\"M10.125 18.15C10.04 17.29 9.4 11.98 9.4 11.98C9.4 11.98 11.34 12.31 12.5 11.73C13.66 11.16 14.98 11 14.98 11C14.98 11 14.4 17.96 14.35 18.46C14.3 18.96 13.45 19.3 12.95 19.3L11.23 19.3C10.73 19.3 10.21 19 10.125 18.15Z\"/></mask><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"32\" stroke-dashoffset=\"32\" d=\"M7.5 10.5C7.5 10.5 8.33 17.43 8.5 19C8.67 20.57 10 21 11 21L13 21C14.5 21 15.875 19.86 16 19C16.125 18.14 17 7 17 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"32;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M16.5 6C16.5 3.5 14 3 12 3C10 3 9.1 3.43 8 4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"12;24\"/></path></g><rect width=\"16\" height=\"5\" x=\"20\" y=\"4\" fill=\"currentColor\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"x\" begin=\"0.4s\" dur=\"0.4s\" values=\"20;4\"/></rect><rect width=\"8\" height=\"10\" x=\"8\" y=\"20\" fill=\"currentColor\" fill-opacity=\".3\" mask=\"url(#svgIDb)\"><animate fill=\"freeze\" attributeName=\"y\" begin=\"1s\" dur=\"0.4s\" values=\"20;10\"/></rect>"}, "calendar": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><rect width=\"16\" height=\"16\" x=\"4\" y=\"4\" stroke-dasharray=\"64\" stroke-dashoffset=\"64\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"64;0\"/></rect><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M7 4V2M17 4V2\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M7 11H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M7 15H14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"9;0\"/></path></g><rect width=\"14\" height=\"0\" x=\"5\" y=\"5\" fill=\"currentColor\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;3\"/></rect>"}, "calendar-out": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><rect width=\"16\" height=\"16\" x=\"4\" y=\"4\" stroke-dasharray=\"64\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.5s\" values=\"0;64\"/></rect><path stroke-dasharray=\"6\" d=\"M7 4V2M17 4V2\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;6\"/></path><path stroke-dasharray=\"12\" d=\"M7 11H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"0;12\"/></path><path stroke-dasharray=\"9\" d=\"M7 15H14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"0;9\"/></path></g><rect width=\"14\" height=\"3\" x=\"5\" y=\"5\" fill=\"currentColor\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"3;0\"/></rect>"}, "cancel": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M5.63604 5.63603C9.15076 2.12131 14.8492 2.12131 18.364 5.63603C21.8787 9.15075 21.8787 14.8492 18.364 18.364C14.8492 21.8787 9.15076 21.8787 5.63604 18.364C2.12132 14.8492 2.12132 9.15075 5.63604 5.63603Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M6 6L18 18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"18;0\"/></path></g>"}, "cancel-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M5.63604 5.63603C9.15076 2.12131 14.8492 2.12131 18.364 5.63603C21.8787 9.15075 21.8787 14.8492 18.364 18.364C14.8492 21.8787 9.15076 21.8787 5.63604 18.364C2.12132 14.8492 2.12132 9.15075 5.63604 5.63603Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M6 6L18 18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"18;0\"/></path></g>"}, "check-list-3": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-dasharray=\"10\" stroke-dashoffset=\"10\" stroke-width=\"2\"><path d=\"M3 5L5 7L9 3\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M3 12L5 14L9 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M3 19L5 21L9 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path></g><g stroke-dasharray=\"22\" stroke-dashoffset=\"22\"><rect width=\"9\" height=\"3\" x=\"11.5\" y=\"3.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.5s\" values=\"22;0\"/></rect><rect width=\"9\" height=\"3\" x=\"11.5\" y=\"10.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.5s\" values=\"22;0\"/></rect><rect width=\"9\" height=\"3\" x=\"11.5\" y=\"17.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.5s\" values=\"22;0\"/></rect></g></g>"}, "check-list-3-filled": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g fill=\"none\" stroke-dasharray=\"10\" stroke-dashoffset=\"10\" stroke-width=\"2\"><path d=\"M3 5L5 7L9 3\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M3 12L5 14L9 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M3 19L5 21L9 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"22\" stroke-dashoffset=\"22\"><rect width=\"9\" height=\"3\" x=\"11.5\" y=\"3.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.5s\" values=\"22;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.7s\" dur=\"0.5s\" values=\"0;1\"/></rect><rect width=\"9\" height=\"3\" x=\"11.5\" y=\"10.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.5s\" values=\"22;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.9s\" dur=\"0.5s\" values=\"0;1\"/></rect><rect width=\"9\" height=\"3\" x=\"11.5\" y=\"17.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.5s\" values=\"22;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.1s\" dur=\"0.5s\" values=\"0;1\"/></rect></g></g>"}, "check-list-3-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g fill=\"none\" stroke-dasharray=\"10\" stroke-dashoffset=\"10\" stroke-width=\"2\"><path d=\"M3 5L5 7L9 3\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M3 12L5 14L9 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M3 19L5 21L9 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"22\" stroke-dashoffset=\"22\"><rect width=\"9\" height=\"3\" x=\"11.5\" y=\"3.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.5s\" values=\"22;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.7s\" dur=\"0.15s\" values=\"0;0.3\"/></rect><rect width=\"9\" height=\"3\" x=\"11.5\" y=\"10.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.5s\" values=\"22;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.9s\" dur=\"0.15s\" values=\"0;0.3\"/></rect><rect width=\"9\" height=\"3\" x=\"11.5\" y=\"17.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.5s\" values=\"22;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.1s\" dur=\"0.15s\" values=\"0;0.3\"/></rect></g></g>"}, "chevron-double-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"10\" stroke-dashoffset=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M5 12L12 5M5 12L12 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"10;0\"/></path><path d=\"M11 12L18 5M11 12L18 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.3s\" values=\"10;0\"/></path></g>"}, "chevron-left": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"10\" stroke-dashoffset=\"10\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8 12L15 5M8 12L15 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"10;0\"/></path>"}, "chevron-left-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M10 12L13 9M10 12L13 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "chevron-left-circle-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M10 12L13 9M10 12L13 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "chevron-small-double-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M12 12L17 7M12 12L17 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"8;0\"/></path><path d=\"M6 12L11 7M6 12L11 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.3s\" values=\"8;0\"/></path></g>"}, "chevron-small-left": {"body": "<path stroke=\"currentColor\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9 12L14 7M9 12L14 17\" fill=\"currentColor\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"8;0\"/></path>"}, "chevron-small-triple-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M14 12L19 7M14 12L19 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"8;0\"/></path><path d=\"M9 12L14 7M9 12L14 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.3s\" values=\"8;0\"/></path><path d=\"M4 12L9 7M4 12L9 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.3s\" values=\"8;0\"/></path></g>"}, "chevron-triple-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"10\" stroke-dashoffset=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M2 12L9 5M2 12L9 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"10;0\"/></path><path d=\"M8 12L15 5M8 12L15 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.3s\" values=\"10;0\"/></path><path d=\"M14 12L21 5M14 12L21 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.3s\" values=\"10;0\"/></path></g>"}, "circle": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path>"}, "circle-to-confirm-circle-transition": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"12\" r=\"9\"/><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 12L11 15L16 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "circle-to-confirm-circle-twotone-transition": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"12\" r=\"9\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.2s\" dur=\"0.15s\" values=\"0;0.3\"/></circle><path fill=\"none\" stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 12L11 15L16 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "circle-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.15s\" values=\"0;0.3\"/></path>"}, "circle-twotone-to-confirm-circle-twotone-transition": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"12\" r=\"9\" fill=\"currentColor\" fill-opacity=\".3\"/><path fill=\"none\" stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 12L11 15L16 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "clipboard": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" stroke-width=\"2\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-arrow": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M12 3H19V11\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"16;0\"/></path><path stroke-dasharray=\"44\" stroke-dashoffset=\"44\" d=\"M19 17V21H5V3H12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.4s\" values=\"44;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M21 14H12.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 14L15 17M12 14L15 11\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"6;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-arrow-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 4H10V6H14V4H18V10H20V18H18V20H6V4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.4s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M12 3H19V11\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"16;0\"/></path><path stroke-dasharray=\"44\" stroke-dashoffset=\"44\" d=\"M19 17V21H5V3H12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.4s\" values=\"44;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M21 14H12.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 14L15 17M12 14L15 11\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"6;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-check": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M9 13L11 15L15 11\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-check-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 4H10V6H14V4H18V20H6V4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M9 13L11 15L15 11\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-list": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"5\" stroke-dashoffset=\"5\" d=\"M9 10H12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"5;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M9 13H14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-dasharray=\"7\" stroke-dashoffset=\"7\" d=\"M9 16H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.4s\" dur=\"0.2s\" values=\"7;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-list-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 4H10V6H14V4H18V20H6V4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.6s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"5\" stroke-dashoffset=\"5\" d=\"M9 10H12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"5;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M9 13H14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-dasharray=\"7\" stroke-dashoffset=\"7\" d=\"M9 16H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.4s\" dur=\"0.2s\" values=\"7;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-minus": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M9 13H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-minus-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 4H10V6H14V4H18V20H6V4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M9 13H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-plus": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M9 13H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 10V16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"8;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-plus-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 4H10V6H14V4H18V20H6V4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.4s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M9 13H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 10V16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"8;0\"/></path></g><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "clipboard-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 4H10V6H14V4H18V20H6V4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" stroke-width=\"2\" d=\"M12 3H19V21H5V3H12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14.5 3.5V6.5H9.5V3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "close": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"12\" stroke-dashoffset=\"12\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 12L19 19M12 12L5 5M12 12L5 19M12 12L19 5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"12;0\"/></path>"}, "close-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 12L16 16M12 12L8 8M12 12L8 16M12 12L16 8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "close-circle-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 12L16 16M12 12L8 8M12 12L8 16M12 12L16 8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "cloud": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M12 19C12 19 9.5 19 7 19C4.5 19 3 17 3 15C3 13 4.5 11 7 11C8 11 8.5 11.5 8.5 11.5M12 19H17C19.5 19 21 17 21 15C21 13 19.5 11 17 11C16 11 15.5 11.5 15.5 11.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M17 11C17 11 17 10.5 17 10C17 7.5 15 5 12 5M7 11V10C7 7.5 9 5 12 5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.3s\" values=\"9;0\"/></path></g>"}, "cloud-braces-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"10\" r=\"6\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;13;12\"/></circle><rect width=\"9\" height=\"7\" x=\"8\" y=\"13\"/><rect width=\"15\" height=\"12\" x=\"1\" y=\"8\" rx=\"6\"><animate attributeName=\"x\" dur=\"15s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"13\" height=\"10\" x=\"10\" y=\"10\" rx=\"5\"><animate attributeName=\"x\" dur=\"15s\" repeatCount=\"indefinite\" values=\"10;11;10;9;10\"/></rect></g><path d=\"M18.5 12H18a1 1 0 0 1-1-1v-1a2 2 0 0 0-2-2h-1.5v2H15v1a2 2 0 0 0 2 2 2 2 0 0 0-2 2v1h-1.5v2H15a2 2 0 0 0 2-2v-1a1 1 0 0 1 1-1h.5v-2Z\"><animateMotion calcMode=\"linear\" dur=\"6s\" keyPoints=\"0;0.25;0.5;0.75;1\" keyTimes=\"0;0.1;0.5;0.8;1\" path=\"M0 0h-1h2z\" repeatCount=\"indefinite\"/></path><path d=\"M5.5 12v2H6a1 1 0 0 1 1 1v1a2 2 0 0 0 2 2h1.5v-2H9v-1a2 2 0 0 0-2-2 2 2 0 0 0 2-2v-1h1.5V8H9a2 2 0 0 0-2 2v1a1 1 0 0 1-1 1h-.5Z\"><animateMotion calcMode=\"linear\" dur=\"6s\" keyPoints=\"0;0.25;0.5;0.75;1\" keyTimes=\"0;0.1;0.5;0.8;1\" path=\"M0 0h1h-2z\" repeatCount=\"indefinite\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-down": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M8 18H7C4.5 18 3 16 3 14C3 12 4.5 10 7 10C8 10 8.5 10.5 8.5 10.5M16 18H17C19.5 18 21 16 21 14C21 12 19.5 10 17 10C16 10 15.5 10.5 15.5 10.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"16;0\"/></path><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M17 10C17 10 17 9.5 17 9C17 6.5 15 4 12 4M7 10V9C7 6.5 9 4 12 4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.3s\" values=\"9;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 15V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"3\" stroke-dashoffset=\"3\" d=\"M12 22L14 20M12 22L10 20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"3;0\"/></path></g>"}, "cloud-down-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M9 6L12 4L15 6L21 14L17 19H7L3 14L9 6Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.3s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M8 18H7C4.5 18 3 16 3 14C3 12 4.5 10 7 10C8 10 8.5 10.5 8.5 10.5M16 18H17C19.5 18 21 16 21 14C21 12 19.5 10 17 10C16 10 15.5 10.5 15.5 10.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"16;0\"/></path><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M17 10C17 10 17 9.5 17 9C17 6.5 15 4 12 4M7 10V9C7 6.5 9 4 12 4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.3s\" values=\"9;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 15V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"3\" stroke-dashoffset=\"3\" d=\"M12 22L14 20M12 22L10 20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"3;0\"/></path></g>"}, "cloud-download-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"10\" r=\"6\"/><rect width=\"9\" height=\"7\" x=\"8\" y=\"13\"/><rect width=\"15\" height=\"12\" x=\"1\" y=\"8\" rx=\"6\"><animate attributeName=\"x\" dur=\"15s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"13\" height=\"10\" x=\"10\" y=\"10\" rx=\"5\"><animate attributeName=\"x\" dur=\"19s\" repeatCount=\"indefinite\" values=\"10;9;10;11;10\"/></rect></g><rect width=\"4\" height=\"5\" x=\"10\" y=\"9\"/><path d=\"M12 18L17 13H7L12 18Z\"><animateMotion calcMode=\"linear\" dur=\"1.5s\" keyPoints=\"0;0.25;0.5;0.75;1\" keyTimes=\"0;0.1;0.5;0.8;1\" path=\"M0 0v1v-2z\" repeatCount=\"indefinite\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-download-outline-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"10\" r=\"6\"/><rect width=\"9\" height=\"8\" x=\"8\" y=\"12\"/><rect width=\"17\" height=\"12\" x=\"1\" y=\"8\" rx=\"6\"><animate attributeName=\"x\" dur=\"19s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"17\" height=\"10\" x=\"6\" y=\"10\" rx=\"5\"><animate attributeName=\"x\" dur=\"23s\" repeatCount=\"indefinite\" values=\"6;5;6;7;6\"/></rect></g><circle cx=\"12\" cy=\"10\" r=\"4\"/><rect width=\"8\" height=\"8\" x=\"8\" y=\"10\"/><rect width=\"11\" height=\"8\" x=\"3\" y=\"10\" rx=\"4\"><animate attributeName=\"x\" dur=\"19s\" repeatCount=\"indefinite\" values=\"3;2;3;4;3\"/></rect><rect width=\"13\" height=\"6\" x=\"8\" y=\"12\" rx=\"3\"><animate attributeName=\"x\" dur=\"23s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect><g fill=\"#fff\"><rect width=\"3\" height=\"4\" x=\"10.5\" y=\"10\"/><path d=\"M12 17L16 13H8L12 17Z\"><animateMotion calcMode=\"linear\" dur=\"1.5s\" keyPoints=\"0;0.25;0.5;0.75;1\" keyTimes=\"0;0.1;0.5;0.8;1\" path=\"M0 0v1v-2z\" repeatCount=\"indefinite\"/></path></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-filled": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M9 7L12 5L15 7L21 15L18 19H6L3 15L9 7Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.5s\" values=\"0;1\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M12 19C12 19 9.5 19 7 19C4.5 19 3 17 3 15C3 13 4.5 11 7 11C8 11 8.5 11.5 8.5 11.5M12 19H17C19.5 19 21 17 21 15C21 13 19.5 11 17 11C16 11 15.5 11.5 15.5 11.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M17 11C17 11 17 10.5 17 10C17 7.5 15 5 12 5M7 11V10C7 7.5 9 5 12 5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.3s\" values=\"9;0\"/></path></g>"}, "cloud-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"10\" r=\"6\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;11;12;13;12\"/></circle><rect width=\"9\" height=\"7\" x=\"8\" y=\"13\"/><rect width=\"15\" height=\"12\" x=\"1\" y=\"8\" rx=\"6\"><animate attributeName=\"x\" dur=\"19s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"13\" height=\"10\" x=\"10\" y=\"10\" rx=\"5\"><animate attributeName=\"x\" dur=\"23s\" repeatCount=\"indefinite\" values=\"10;9;10;11;10\"/></rect></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-off-outline-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"10\" r=\"6\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;11;12;13;12\"/></circle><rect width=\"9\" height=\"8\" x=\"8\" y=\"12\"/><rect width=\"17\" height=\"12\" x=\"1\" y=\"8\" rx=\"6\"><animate attributeName=\"x\" dur=\"15s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"17\" height=\"10\" x=\"6\" y=\"10\" rx=\"5\"><animate attributeName=\"x\" dur=\"21s\" repeatCount=\"indefinite\" values=\"6;5;6;7;6\"/></rect></g><circle cx=\"12\" cy=\"10\" r=\"4\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;11;12;13;12\"/></circle><rect width=\"8\" height=\"8\" x=\"8\" y=\"10\"><animate attributeName=\"x\" dur=\"30s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect><rect width=\"11\" height=\"8\" x=\"3\" y=\"10\" rx=\"4\"><animate attributeName=\"x\" dur=\"15s\" repeatCount=\"indefinite\" values=\"3;2;3;4;3\"/></rect><rect width=\"13\" height=\"6\" x=\"8\" y=\"12\" rx=\"3\"><animate attributeName=\"x\" dur=\"21s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect><g fill=\"none\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke=\"#000\" d=\"M1 11h24\" transform=\"rotate(45 13 12)\"/><path stroke=\"#fff\" d=\"M1 13h22\" transform=\"rotate(45 13 12)\"><animate attributeName=\"d\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M1 13h22;M3 13h22;M1 13h22\"/></path></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-outline-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"10\" r=\"6\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;11;12;13;12\"/></circle><rect width=\"9\" height=\"8\" x=\"8\" y=\"12\"/><rect width=\"17\" height=\"12\" x=\"1\" y=\"8\" rx=\"6\"><animate attributeName=\"x\" dur=\"21s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"17\" height=\"10\" x=\"6\" y=\"10\" rx=\"5\"><animate attributeName=\"x\" dur=\"17s\" repeatCount=\"indefinite\" values=\"6;5;6;7;6\"/></rect></g><circle cx=\"12\" cy=\"10\" r=\"4\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;11;12;13;12\"/></circle><rect width=\"8\" height=\"8\" x=\"8\" y=\"10\"><animate attributeName=\"x\" dur=\"30s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect><rect width=\"11\" height=\"8\" x=\"3\" y=\"10\" rx=\"4\"><animate attributeName=\"x\" dur=\"21s\" repeatCount=\"indefinite\" values=\"3;2;3;4;3\"/></rect><rect width=\"13\" height=\"6\" x=\"8\" y=\"12\" rx=\"3\"><animate attributeName=\"x\" dur=\"17s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-print-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"8\" r=\"6\"/><rect width=\"15\" height=\"12\" x=\"1\" y=\"6\" rx=\"6\"><animate attributeName=\"x\" dur=\"24s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"13\" height=\"10\" x=\"10\" y=\"8\" rx=\"5\"><animate attributeName=\"x\" dur=\"17s\" repeatCount=\"indefinite\" values=\"10;9;10;11;10\"/></rect></g><rect width=\"12\" height=\"11\" x=\"6\" y=\"11\" fill=\"#fff\"/><rect width=\"8\" height=\"7\" x=\"8\" y=\"13\"/><path fill=\"#fff\" d=\"M9 12h6v1H9zM9 14h6v1H9zM9 16h6v1H9zM9 18h6v1H9z\"><animateMotion dur=\"1.5s\" path=\"M0 0v2\" repeatCount=\"indefinite\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-print-outline-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"8\" r=\"6\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;11;12;13;12\"/></circle><rect width=\"17\" height=\"12\" x=\"1\" y=\"6\" rx=\"6\"><animate attributeName=\"x\" dur=\"19s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"17\" height=\"10\" x=\"6\" y=\"8\" rx=\"5\"><animate attributeName=\"x\" dur=\"23s\" repeatCount=\"indefinite\" values=\"6;5;6;7;6\"/></rect></g><circle cx=\"12\" cy=\"8\" r=\"4\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;11;12;13;12\"/></circle><rect width=\"8\" height=\"8\" x=\"8\" y=\"8\"><animate attributeName=\"x\" dur=\"30s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect><rect width=\"11\" height=\"8\" x=\"3\" y=\"8\" rx=\"4\"><animate attributeName=\"x\" dur=\"19s\" repeatCount=\"indefinite\" values=\"3;2;3;4;3\"/></rect><rect width=\"13\" height=\"6\" x=\"8\" y=\"10\" rx=\"3\"><animate attributeName=\"x\" dur=\"23s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect><rect width=\"12\" height=\"11\" x=\"6\" y=\"11\" fill=\"#fff\"/><rect width=\"8\" height=\"7\" x=\"8\" y=\"13\"/><path fill=\"#fff\" d=\"M9 12h6v1H9zM9 14h6v1H9zM9 16h6v1H9zM9 18h6v1H9z\"><animateMotion dur=\"1.5s\" path=\"M0 0v2\" repeatCount=\"indefinite\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-tags-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"10\" r=\"6\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;13;12\"/></circle><rect width=\"9\" height=\"7\" x=\"8\" y=\"13\"/><rect width=\"15\" height=\"12\" x=\"1\" y=\"8\" rx=\"6\"><animate attributeName=\"x\" dur=\"15s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"13\" height=\"10\" x=\"10\" y=\"10\" rx=\"5\"><animate attributeName=\"x\" dur=\"15s\" repeatCount=\"indefinite\" values=\"10;11;10;9;10\"/></rect></g><path d=\"M6 10H8V14H12V16H6V10Z\" transform=\"rotate(45 9 13)\"><animateMotion calcMode=\"linear\" dur=\"6s\" keyPoints=\"0;0.25;0.5;0.75;1\" keyTimes=\"0;0.1;0.5;0.8;1\" path=\"M0 0h-1h2z\" repeatCount=\"indefinite\"/></path><path d=\"M12 10H14V14H18V16H12V10Z\" transform=\"rotate(225 15 13)\"><animateMotion calcMode=\"linear\" dur=\"6s\" keyPoints=\"0;0.25;0.5;0.75;1\" keyTimes=\"0;0.1;0.5;0.8;1\" path=\"M0 0h1h-2z\" repeatCount=\"indefinite\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M9 7L12 5L15 7L21 15L18 19H6L3 15L9 7Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M12 19C12 19 9.5 19 7 19C4.5 19 3 17 3 15C3 13 4.5 11 7 11C8 11 8.5 11.5 8.5 11.5M12 19H17C19.5 19 21 17 21 15C21 13 19.5 11 17 11C16 11 15.5 11.5 15.5 11.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"20;0\"/></path><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M17 11C17 11 17 10.5 17 10C17 7.5 15 5 12 5M7 11V10C7 7.5 9 5 12 5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.3s\" values=\"9;0\"/></path></g>"}, "cloud-up": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M8 18H7C4.5 18 3 16 3 14C3 12 4.5 10 7 10C8 10 8.5 10.5 8.5 10.5M16 18H17C19.5 18 21 16 21 14C21 12 19.5 10 17 10C16 10 15.5 10.5 15.5 10.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"16;0\"/></path><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M17 10C17 10 17 9.5 17 9C17 6.5 15 4 12 4M7 10V9C7 6.5 9 4 12 4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.3s\" values=\"9;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 20V14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"4\" stroke-dashoffset=\"4\" d=\"M12 13L14 15M12 13L10 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"4;0\"/></path></g>"}, "cloud-up-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M9 6L12 4L15 6L21 14L17 19H7L3 14L9 6Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.3s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M8 18H7C4.5 18 3 16 3 14C3 12 4.5 10 7 10C8 10 8.5 10.5 8.5 10.5M16 18H17C19.5 18 21 16 21 14C21 12 19.5 10 17 10C16 10 15.5 10.5 15.5 10.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"16;0\"/></path><path stroke-dasharray=\"9\" stroke-dashoffset=\"9\" d=\"M17 10C17 10 17 9.5 17 9C17 6.5 15 4 12 4M7 10V9C7 6.5 9 4 12 4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.3s\" values=\"9;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M12 20V14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"4\" stroke-dashoffset=\"4\" d=\"M12 13L14 15M12 13L10 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"4;0\"/></path></g>"}, "cloud-upload-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"10\" r=\"6\"/><rect width=\"9\" height=\"7\" x=\"8\" y=\"13\"/><rect width=\"15\" height=\"12\" x=\"1\" y=\"8\" rx=\"6\"><animate attributeName=\"x\" dur=\"21s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"13\" height=\"10\" x=\"10\" y=\"10\" rx=\"5\"><animate attributeName=\"x\" dur=\"19s\" repeatCount=\"indefinite\" values=\"10;9;10;11;10\"/></rect></g><rect width=\"4\" height=\"5\" x=\"10\" y=\"12\"/><path d=\"M12 8L17 13H7L12 8Z\"><animateMotion calcMode=\"linear\" dur=\"1.5s\" keyPoints=\"0;0.25;0.5;0.75;1\" keyTimes=\"0;0.1;0.5;0.8;1\" path=\"M0 0v-1v2z\" repeatCount=\"indefinite\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "cloud-upload-outline-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"10\" r=\"6\"/><rect width=\"9\" height=\"8\" x=\"8\" y=\"12\"/><rect width=\"17\" height=\"12\" x=\"1\" y=\"8\" rx=\"6\"><animate attributeName=\"x\" dur=\"24s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"17\" height=\"10\" x=\"6\" y=\"10\" rx=\"5\"><animate attributeName=\"x\" dur=\"15s\" repeatCount=\"indefinite\" values=\"6;5;6;7;6\"/></rect></g><circle cx=\"12\" cy=\"10\" r=\"4\"/><rect width=\"8\" height=\"8\" x=\"8\" y=\"10\"/><rect width=\"11\" height=\"8\" x=\"3\" y=\"10\" rx=\"4\"><animate attributeName=\"x\" dur=\"24s\" repeatCount=\"indefinite\" values=\"3;2;3;4;3\"/></rect><rect width=\"13\" height=\"6\" x=\"8\" y=\"12\" rx=\"3\"><animate attributeName=\"x\" dur=\"15s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect><g fill=\"#fff\"><rect width=\"3\" height=\"4\" x=\"10.5\" y=\"12\"/><path d=\"M12 9L16 13H8L12 9Z\"><animateMotion calcMode=\"linear\" dur=\"1.5s\" keyPoints=\"0;0.25;0.5;0.75;1\" keyTimes=\"0;0.1;0.5;0.8;1\" path=\"M0 0v-1v2z\" repeatCount=\"indefinite\"/></path></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "coffee": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"48\" stroke-dashoffset=\"48\" d=\"M17 4v9a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V4z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.6s\" values=\"48;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M17 9H20C20.55 9 21 8.55 21 8V5C21 4.45 20.55 4 20 4H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;28\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M11 20h8M11 20h-8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"10;0\"/></path></g>"}, "coffee-arrow": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"40\" stroke-dashoffset=\"40\" d=\"M14 4V11C14 11.7956 13.6839 12.5587 13.1213 13.1213C12.5587 13.6839 11.7956 14 11 14H7C6.20435 14 5.44129 13.6839 4.87868 13.1213C4.31607 12.5587 4 11.7956 4 11V4z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"40;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M14 9H17C17.55 9 18 8.55 18 8V5C18 4.45 17.55 4 17 4H14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"14;28\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M4 18H19.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.3s\" values=\"18;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M19.5 18l-3 -3M19.5 18l-3 3\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "coffee-arrow-filled": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"40\" stroke-dashoffset=\"40\" d=\"M14 4V11C14 11.7956 13.6839 12.5587 13.1213 13.1213C12.5587 13.6839 11.7956 14 11 14H7C6.20435 14 5.44129 13.6839 4.87868 13.1213C4.31607 12.5587 4 11.7956 4 11V4z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"40;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.4s\" dur=\"0.5s\" values=\"0;1\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M14 9H17C17.55 9 18 8.55 18 8V5C18 4.45 17.55 4 17 4H14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"14;28\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M4 18H19.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.3s\" values=\"18;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M19.5 18l-3 -3M19.5 18l-3 3\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "coffee-arrow-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"40\" stroke-dashoffset=\"40\" d=\"M14 4V11C14 11.7956 13.6839 12.5587 13.1213 13.1213C12.5587 13.6839 11.7956 14 11 14H7C6.20435 14 5.44129 13.6839 4.87868 13.1213C4.31607 12.5587 4 11.7956 4 11V4z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"40;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.4s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M14 9H17C17.55 9 18 8.55 18 8V5C18 4.45 17.55 4 17 4H14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"14;28\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M4 18H19.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.3s\" values=\"18;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M19.5 18l-3 -3M19.5 18l-3 3\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "coffee-filled": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"48\" stroke-dashoffset=\"48\" d=\"M17 4v9a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V4z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.6s\" values=\"48;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.5s\" values=\"0;1\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M17 9H20C20.55 9 21 8.55 21 8V5C21 4.45 20.55 4 20 4H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;28\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M11 20h8M11 20h-8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"10;0\"/></path></g>"}, "coffee-half-empty-twotone-loop": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M17 14V17a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V14z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"48\" stroke-dashoffset=\"48\" d=\"M17 9v9a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V9z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"48;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M17 14H20C20.55 14 21 13.55 21 13V10C21 9.45 20.55 9 20 9H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"14;28\"/></path></g><mask id=\"svgIDa\"><path fill=\"none\" stroke=\"#fff\" stroke-width=\"2\" d=\"M8 0c0 2-2 2-2 4s2 2 2 4-2 2-2 4 2 2 2 4M12 0c0 2-2 2-2 4s2 2 2 4-2 2-2 4 2 2 2 4M16 0c0 2-2 2-2 4s2 2 2 4-2 2-2 4 2 2 2 4\"><animateMotion calcMode=\"linear\" dur=\"3s\" path=\"M0 0v-8\" repeatCount=\"indefinite\"/></path></mask><rect width=\"24\" height=\"0\" y=\"7\" fill=\"currentColor\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"y\" begin=\"0.8s\" dur=\"0.6s\" values=\"7;2\"/><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.8s\" dur=\"0.6s\" values=\"0;5\"/></rect>"}, "coffee-loop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"48\" stroke-dashoffset=\"48\" d=\"M17 9v9a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V9z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"48;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M17 14H20C20.55 14 21 13.55 21 13V10C21 9.45 20.55 9 20 9H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"14;28\"/></path></g><mask id=\"svgIDa\"><path fill=\"none\" stroke=\"#fff\" stroke-width=\"2\" d=\"M8 0c0 2-2 2-2 4s2 2 2 4-2 2-2 4 2 2 2 4M12 0c0 2-2 2-2 4s2 2 2 4-2 2-2 4 2 2 2 4M16 0c0 2-2 2-2 4s2 2 2 4-2 2-2 4 2 2 2 4\"><animateMotion calcMode=\"linear\" dur=\"3s\" path=\"M0 0v-8\" repeatCount=\"indefinite\"/></path></mask><rect width=\"24\" height=\"0\" y=\"7\" fill=\"currentColor\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"y\" begin=\"0.8s\" dur=\"0.6s\" values=\"7;2\"/><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.8s\" dur=\"0.6s\" values=\"0;5\"/></rect>"}, "coffee-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"48\" stroke-dashoffset=\"48\" d=\"M17 4v9a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V4z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.6s\" values=\"48;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M17 9H20C20.55 9 21 8.55 21 8V5C21 4.45 20.55 4 20 4H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;28\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M11 20h8M11 20h-8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"10;0\"/></path></g>"}, "coffee-twotone-loop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"48\" stroke-dashoffset=\"48\" d=\"M17 9v9a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V9z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"48;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M17 14H20C20.55 14 21 13.55 21 13V10C21 9.45 20.55 9 20 9H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"14;28\"/></path></g><mask id=\"svgIDa\"><path fill=\"none\" stroke=\"#fff\" stroke-width=\"2\" d=\"M8 0c0 2-2 2-2 4s2 2 2 4-2 2-2 4 2 2 2 4M12 0c0 2-2 2-2 4s2 2 2 4-2 2-2 4 2 2 2 4M16 0c0 2-2 2-2 4s2 2 2 4-2 2-2 4 2 2 2 4\"><animateMotion calcMode=\"linear\" dur=\"3s\" path=\"M0 0v-8\" repeatCount=\"indefinite\"/></path></mask><rect width=\"24\" height=\"0\" y=\"7\" fill=\"currentColor\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"y\" begin=\"0.8s\" dur=\"0.6s\" values=\"7;2\"/><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.8s\" dur=\"0.6s\" values=\"0;5\"/></rect>"}, "computer": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 21H17M12 21H7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"6;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 21V17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" d=\"M12 17H3V5H21V17Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.6s\" values=\"64;0\"/></path></g>"}, "computer-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 21H17M12 21H7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"6;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 21V17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"6;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"64\" stroke-dashoffset=\"64\" d=\"M12 17H3V5H21V17Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.6s\" values=\"64;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g>"}, "confirm": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"24\" stroke-dashoffset=\"24\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 11L11 17L21 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"24;0\"/></path>"}, "confirm-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 12L11 15L16 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "confirm-circle-to-circle-transition": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"12\" r=\"9\"/><path stroke-dasharray=\"14\" d=\"M8 12L11 15L16 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"28;14\"/></path></g>"}, "confirm-circle-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 12L11 15L16 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "confirm-circle-twotone-to-circle-transition": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"12\" r=\"9\" fill=\"currentColor\" fill-opacity=\".3\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.2s\" dur=\"0.15s\" values=\"0.3;0\"/></circle><path fill=\"none\" stroke-dasharray=\"14\" d=\"M8 12L11 15L16 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"28;14\"/></path></g>"}, "confirm-circle-twotone-to-circle-twotone-transition": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"12\" r=\"9\" fill=\"currentColor\" fill-opacity=\".3\"/><path fill=\"none\" stroke-dasharray=\"14\" d=\"M8 12L11 15L16 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"28;14\"/></path></g>"}, "construction": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"44\" stroke-dashoffset=\"44\" d=\"M21 21H3V19C3 18 4 17 5 17H19C20 17 21 18 21 19V21Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"44;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M6 17L12 2M18 17L12 2\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.3s\" values=\"18;0\"/></path></g><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M8 12L12.5 9.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M6 16L13.5 12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M9.5 17L14.5 14.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.3s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "construction-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M21 21H3V19C3 18 4 17 5 17H19C20 17 21 18 21 19V21Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.5s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"44\" stroke-dashoffset=\"44\" d=\"M21 21H3V19C3 18 4 17 5 17H19C20 17 21 18 21 19V21Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"44;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M6 17L12 2M18 17L12 2\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.3s\" values=\"18;0\"/></path></g><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M8 12L12.5 9.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"8;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M6 16L13.5 12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M9.5 17L14.5 14.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.3s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "discord": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\"><circle cx=\"9\" cy=\"12\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.4s\" values=\"0;1\"/></circle><circle cx=\"15\" cy=\"12\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.4s\" dur=\"0.4s\" values=\"0;1\"/></circle></g><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M15.5 17.5L16.5 19.5C16.5 19.5 20.671 18.172 22 16C22 15 22.53 7.853 19 5.5C17.5 4.5 15 4 15 4L14 6H12M8.52799 17.5L7.52799 19.5C7.52799 19.5 3.35699 18.172 2.02799 16C2.02799 15 1.49799 7.853 5.02799 5.5C6.52799 4.5 9.02799 4 9.02799 4L10.028 6H12.028\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"30;60\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M5.5 16C10.5 18.5 13.5 18.5 18.5 16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.4s\" values=\"16;0\"/></path></g>"}, "discord-twotone": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\"><path d=\"M5 5L12 5.2L19 5L22 15L19 18.4H5L1.5 15L5 5Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.6s\" dur=\"0.15s\" values=\"0;0.3\"/></path><circle cx=\"9\" cy=\"12\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.4s\" values=\"0;1\"/></circle><circle cx=\"15\" cy=\"12\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.4s\" dur=\"0.4s\" values=\"0;1\"/></circle></g><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M15.5 17.5L16.5 19.5C16.5 19.5 20.671 18.172 22 16C22 15 22.53 7.853 19 5.5C17.5 4.5 15 4 15 4L14 6H12M8.52799 17.5L7.52799 19.5C7.52799 19.5 3.35699 18.172 2.02799 16C2.02799 15 1.49799 7.853 5.02799 5.5C6.52799 4.5 9.02799 4 9.02799 4L10.028 6H12.028\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"30;60\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M5.5 16C10.5 18.5 13.5 18.5 18.5 16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.4s\" values=\"16;0\"/></path></g>"}, "document": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "document-add": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path><g stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-width=\"2\"><path d=\"M9 14H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path><path d=\"M12 11V17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"8;0\"/></path></g></g>"}, "document-add-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M5 3H12.5V8.5H19V21H5V3Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.4s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path><g stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-width=\"2\"><path d=\"M9 14H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path><path d=\"M12 11V17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"8;0\"/></path></g></g>"}, "document-code": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path><g stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-width=\"2\"><path d=\"M10 13L8 15L10 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path><path d=\"M14 13L16 15L14 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"8;0\"/></path></g></g>"}, "document-code-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M5 3H12.5V8.5H19V21H5V3Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.4s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path><g stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-width=\"2\"><path d=\"M10 13L8 15L10 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path><path d=\"M14 13L16 15L14 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"8;0\"/></path></g></g>"}, "document-list": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M9 13H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M9 16H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"8;0\"/></path></g><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "document-list-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M5 3H12.5V8.5H19V21H5V3Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.4s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g stroke-width=\"2\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M9 13H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M9 16H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"8;0\"/></path></g><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "document-remove": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-width=\"2\" d=\"M9 14H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "document-remove-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M5 3H12.5V8.5H19V21H5V3Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-width=\"2\" d=\"M9 14H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "document-report": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path><g stroke-width=\"2\"><path stroke-dasharray=\"5\" stroke-dashoffset=\"5\" d=\"M9 17V14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"5;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 17V13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-dasharray=\"7\" stroke-dashoffset=\"7\" d=\"M15 17V12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.4s\" dur=\"0.2s\" values=\"7;0\"/></path></g></g>"}, "document-report-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M5 3H12.5V8.5H19V21H5V3Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.6s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path><g stroke-width=\"2\"><path stroke-dasharray=\"5\" stroke-dashoffset=\"5\" d=\"M9 17V14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"5;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 17V13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-dasharray=\"7\" stroke-dashoffset=\"7\" d=\"M15 17V12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.4s\" dur=\"0.2s\" values=\"7;0\"/></path></g></g>"}, "document-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M5 3H12.5V8.5H19V21H5V3Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" stroke-width=\"2\" d=\"M13 3L19 9V21H5V3H13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12.5 3V8.5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path></g>"}, "double-arrow-horizontal": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M12 12H3.5M12 12H20.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"10;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M3 12L7 16M21 12L17 16M3 12L7 8M21 12L17 8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.3s\" values=\"6;0\"/></path></g>"}, "download-loop": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"none\" stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"14;0\"/></path><path fill=\"currentColor\" d=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"><animate attributeName=\"d\" calcMode=\"linear\" dur=\"1.5s\" keyTimes=\"0;0.7;1\" repeatCount=\"indefinite\" values=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5;M12 4 h2 v3 h2.5 L12 11.5M12 4 h-2 v3 h-2.5 L12 11.5;M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"/></path></g>"}, "download-off-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"14;0\"/></path><path fill=\"#fff\" d=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"><animate attributeName=\"d\" calcMode=\"linear\" dur=\"1.5s\" keyTimes=\"0;0.7;1\" repeatCount=\"indefinite\" values=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5;M12 4 h2 v3 h2.5 L12 11.5M12 4 h-2 v3 h-2.5 L12 11.5;M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"/></path><g stroke-dasharray=\"26\" stroke-dashoffset=\"26\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path d=\"M0 13h22\"><animate attributeName=\"d\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M0 13h22;M2 13h22;M0 13h22\"/></path><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"26;0\"/></g></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "download-off-outline": {"body": "<mask id=\"svgIDa\"><g fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"14;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"18;0\"/></path><g stroke-dasharray=\"26\" stroke-dashoffset=\"26\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path d=\"M1 13h22\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"26;0\"/></g></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "download-off-outline-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"14;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"18;0\"/><animate attributeName=\"d\" calcMode=\"linear\" dur=\"1.5s\" keyTimes=\"0;0.7;1\" repeatCount=\"indefinite\" values=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5;M12 4 h2 v3 h2.5 L12 11.5M12 4 h-2 v3 h-2.5 L12 11.5;M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"/></path><g stroke-dasharray=\"26\" stroke-dashoffset=\"26\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path d=\"M0 13h22\"><animate attributeName=\"d\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M0 13h22;M2 13h22;M0 13h22\"/></path><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"26;0\"/></g></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "download-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"14;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"18;0\"/></path></g>"}, "download-outline-loop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"14;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"18;0\"/><animate attributeName=\"d\" calcMode=\"linear\" dur=\"1.5s\" keyTimes=\"0;0.7;1\" repeatCount=\"indefinite\" values=\"M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5;M12 4 h2 v3 h2.5 L12 11.5M12 4 h-2 v3 h-2.5 L12 11.5;M12 4 h2 v6 h2.5 L12 14.5M12 4 h-2 v6 h-2.5 L12 14.5\"/></path></g>"}, "downloading-loop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"2 4\" stroke-dashoffset=\"6\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21\"><animate attributeName=\"stroke-dashoffset\" dur=\"0.6s\" repeatCount=\"indefinite\" values=\"6;0\"/></path><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.1s\" dur=\"0.3s\" values=\"30;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M12 8v7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 15.5l3.5 -3.5M12 15.5l-3.5 -3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "edit": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 21H21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"20;0\"/></path><path stroke-dasharray=\"44\" stroke-dashoffset=\"44\" d=\"M7 17V13L17 3L21 7L11 17H7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.6s\" values=\"44;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M14 6L18 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "edit-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M20 7L17 4L15 6L18 9L20 7Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 21H21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"20;0\"/></path><path stroke-dasharray=\"44\" stroke-dashoffset=\"44\" d=\"M7 17V13L17 3L21 7L11 17H7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.6s\" values=\"44;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M14 6L18 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "edit-twotone-full": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M20 7L17 4L8 13V16H11L20 7Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 21H21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"20;0\"/></path><path stroke-dasharray=\"44\" stroke-dashoffset=\"44\" d=\"M7 17V13L17 3L21 7L11 17H7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.6s\" values=\"44;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M14 6L18 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "email": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><rect width=\"18\" height=\"14\" x=\"3\" y=\"5\" stroke-dasharray=\"64\" stroke-dashoffset=\"64\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></rect><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M3 6.5L12 12L21 6.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.4s\" values=\"24;0\"/></path></g>"}, "email-opened": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" d=\"M3 8.06083C3 7.71247 3.1813 7.38921 3.47855 7.20755L12 2L20.5214 7.20755C20.8187 7.38921 21 7.71247 21 8.06083V18C21 18.5523 20.5523 19 20 19H4C3.44772 19 3 18.5523 3 18V8.06083Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M3 8.5L12 14L21 8.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.4s\" values=\"24;0\"/></path></g>"}, "email-opened-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M12 13L4 8L12 3L20 8L12 13Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" d=\"M3 8.06083C3 7.71247 3.1813 7.38921 3.47855 7.20755L12 2L20.5214 7.20755C20.8187 7.38921 21 7.71247 21 8.06083V18C21 18.5523 20.5523 19 20 19H4C3.44772 19 3 18.5523 3 18V8.06083Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M3 8.5L12 14L21 8.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.4s\" values=\"24;0\"/></path></g>"}, "email-opened-twotone-alt": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M12 15L4 10V18H20V10L12 15Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"64\" stroke-dashoffset=\"64\" d=\"M3 8.06083C3 7.71247 3.1813 7.38921 3.47855 7.20755L12 2L20.5214 7.20755C20.8187 7.38921 21 7.71247 21 8.06083V18C21 18.5523 20.5523 19 20 19H4C3.44772 19 3 18.5523 3 18V8.06083Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M3 8.5L12 14L21 8.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.4s\" values=\"24;0\"/></path></g>"}, "email-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M12 11L4 6H20L12 11Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><rect width=\"18\" height=\"14\" x=\"3\" y=\"5\" stroke-dasharray=\"64\" stroke-dashoffset=\"64\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></rect><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M3 6.5L12 12L21 6.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.4s\" values=\"24;0\"/></path></g>"}, "email-twotone-alt": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M12 13L4 8V18H20V8L12 13Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><rect width=\"18\" height=\"14\" x=\"3\" y=\"5\" stroke-dasharray=\"64\" stroke-dashoffset=\"64\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"64;0\"/></rect><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M3 6.5L12 12L21 6.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.4s\" values=\"24;0\"/></path></g>"}, "emoji-angry": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 16C8.5 15 9.79086 14 12 14C14.2091 14 15.5 15 16 16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;0\"/></path><g stroke-dasharray=\"5\" stroke-dashoffset=\"5\" stroke-width=\"1\"><path d=\"M9.5 8.5L7.5 7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"5;0\"/></path><path d=\"M14.5 8.5L16.5 7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"5;0\"/></path></g></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-angry-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 16C8.5 15 9.79086 14 12 14C14.2091 14 15.5 15 16 16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;0\"/></path><g stroke-dasharray=\"5\" stroke-dashoffset=\"5\" stroke-width=\"1\"><path d=\"M9.5 8.5L7.5 7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"5;0\"/></path><path d=\"M14.5 8.5L16.5 7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"5;0\"/></path></g></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-frown": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 16C8.5 15 9.79086 14 12 14C14.2091 14 15.5 15 16 16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-frown-open": {"body": "<mask id=\"svgIDa\"><path fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8.5 16C9 15 10 14 12 14C14 14 15 15 15.5 16M8.5 16C9.5 15.5 11 15.5 12 15.5C13 15.5 14.5 15.5 15.5 16\"/></mask><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><g fill=\"currentColor\"><rect width=\"0\" height=\"7\" x=\"6\" y=\"12\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"width\" begin=\"1s\" dur=\"0.2s\" values=\"0;12\"/></rect><ellipse cx=\"9\" cy=\"9.5\" fill-opacity=\"0\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" fill-opacity=\"0\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-frown-open-twotone": {"body": "<mask id=\"svgIDa\"><path fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8.5 16C9 15 10 14 12 14C14 14 15 15 15.5 16M8.5 16C9.5 15.5 11 15.5 12 15.5C13 15.5 14.5 15.5 15.5 16\"/></mask><g fill=\"currentColor\"><path fill-opacity=\"0\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><rect width=\"0\" height=\"7\" x=\"6\" y=\"12\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"width\" begin=\"1s\" dur=\"0.2s\" values=\"0;12\"/></rect><ellipse cx=\"9\" cy=\"9.5\" fill-opacity=\"0\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" fill-opacity=\"0\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-frown-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 16C8.5 15 9.79086 14 12 14C14.2091 14 15.5 15 16 16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-grin": {"body": "<mask id=\"svgIDa\"><path fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8 14C8.5 15.5 9.79086 17 12 17C14.2091 17 15.5 15.5 16 14M8 14C9 14.5 10 15 12 15C14 15 15 14.5 16 14\"/></mask><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><g fill=\"currentColor\"><rect width=\"0\" height=\"7\" x=\"6\" y=\"12\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"width\" begin=\"1s\" dur=\"0.2s\" values=\"0;12\"/></rect><ellipse cx=\"9\" cy=\"9.5\" fill-opacity=\"0\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" fill-opacity=\"0\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-grin-twotone": {"body": "<mask id=\"svgIDa\"><path fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8 14C8.5 15.5 9.79086 17 12 17C14.2091 17 15.5 15.5 16 14M8 14C9 14.5 10 15 12 15C14 15 15 14.5 16 14\"/></mask><g fill=\"currentColor\"><path fill-opacity=\"0\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><rect width=\"0\" height=\"7\" x=\"6\" y=\"12\" mask=\"url(#svgIDa)\"><animate fill=\"freeze\" attributeName=\"width\" begin=\"1s\" dur=\"0.2s\" values=\"0;12\"/></rect><ellipse cx=\"9\" cy=\"9.5\" fill-opacity=\"0\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" fill-opacity=\"0\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-neutral": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M8 15H16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-neutral-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M8 15H16\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"10;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-smile": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 14C8.5 15.5 9.79086 17 12 17C14.2091 17 15.5 15.5 16 14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-smile-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 14C8.5 15.5 9.79086 17 12 17C14.2091 17 15.5 15.5 16 14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><ellipse cx=\"15\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></ellipse></g>"}, "emoji-smile-wink": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 14C8.5 15.5 9.79086 17 12 17C14.2091 17 15.5 15.5 16 14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><path d=\"M15 8.5C15.8284 8.5 16.5 8.94772 16.5 9.5C16.5 10.0523 15.8284 10.5 15 10.5C14.1716 10.5 13.5 10.0523 13.5 9.5C13.5 8.94772 14.1716 8.5 15 8.5Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></path></g>"}, "emoji-smile-wink-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 14C8.5 15.5 9.79086 17 12 17C14.2091 17 15.5 15.5 16 14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><ellipse cx=\"9\" cy=\"9.5\" rx=\"1\" ry=\"1.5\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></ellipse><path d=\"M15 8.5C15.8284 8.5 16.5 8.94772 16.5 9.5C16.5 10.0523 15.8284 10.5 15 10.5C14.1716 10.5 13.5 10.0523 13.5 9.5C13.5 8.94772 14.1716 8.5 15 8.5Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></path></g>"}, "external-link": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"42\" stroke-dashoffset=\"42\" d=\"M11 5H5V19H19V13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"42;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M13 11L20 4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.3s\" values=\"12;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M21 3H15M21 3V9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "external-link-rounded": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"36\" stroke-dashoffset=\"36\" d=\"M12 5C8.13401 5 5 8.13401 5 12C5 15.866 8.13401 19 12 19C15.866 19 19 15.866 19 12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"36;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M13 11L20 4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.3s\" values=\"12;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M21 3H15M21 3V9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "facebook": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"4\"><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M17 4L15 4C12.5 4 11 5.5 11 8V20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"24;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M8 12H15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "github": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M12 4C13.6683 4 14.6122 4.39991 15 4.5C15.5255 4.07463 16.9375 3 18.5 3C18.8438 4 18.7863 5.21921 18.5 6C19.25 7 19.5 8 19.5 9.5C19.5 11.6875 19.017 13.0822 18 14C16.983 14.9178 15.8887 15.3749 14.5 15.5C15.1506 16.038 15 17.3743 15 18C15 18.7256 15 21 15 21M12 4C10.3317 4 9.38784 4.39991 9 4.5C8.47455 4.07463 7.0625 3 5.5 3C5.15625 4 5.21371 5.21921 5.5 6C4.75 7 4.5 8 4.5 9.5C4.5 11.6875 4.98301 13.0822 6 14C7.01699 14.9178 8.1113 15.3749 9.5 15.5C8.84944 16.038 9 17.3743 9 18C9 18.7256 9 21 9 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"30;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M9 19C7.59375 19 6.15625 18.4375 5.3125 17.8125C4.46875 17.1875 4.21875 16.1562 3 15.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/></path></g>"}, "github-loop": {"body": "<mask id=\"svgIDa\" width=\"24\" height=\"24\" x=\"0\" y=\"0\"><g fill=\"#fff\"><ellipse cx=\"9.5\" cy=\"9\" rx=\"1.5\" ry=\"1\"/><ellipse cx=\"14.5\" cy=\"9\" rx=\"1.5\" ry=\"1\"/></g></mask><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M12 4C13.6683 4 14.6122 4.39991 15 4.5C15.5255 4.07463 16.9375 3 18.5 3C18.8438 4 18.7863 5.21921 18.5 6C19.25 7 19.5 8 19.5 9.5C19.5 11.6875 19.017 13.0822 18 14C16.983 14.9178 15.8887 15.3749 14.5 15.5C15.1506 16.038 15 17.3743 15 18C15 18.7256 15 21 15 21M12 4C10.3317 4 9.38784 4.39991 9 4.5C8.47455 4.07463 7.0625 3 5.5 3C5.15625 4 5.21371 5.21921 5.5 6C4.75 7 4.5 8 4.5 9.5C4.5 11.6875 4.98301 13.0822 6 14C7.01699 14.9178 8.1113 15.3749 9.5 15.5C8.84944 16.038 9 17.3743 9 18C9 18.7256 9 21 9 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"30;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M9 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/><animate attributeName=\"d\" dur=\"3s\" repeatCount=\"indefinite\" values=\"M9 19c-1.406 0-2.844-.563-3.688-1.188C4.47 17.188 4.22 16.157 3 15.5;M9 19c-1.406 0-3-.5-4-.5-.532 0-1 0-2-.5;M9 19c-1.406 0-2.844-.563-3.688-1.188C4.47 17.188 4.22 16.157 3 15.5\"/></path></g><rect width=\"8\" height=\"4\" x=\"8\" y=\"11\" fill=\"currentColor\" mask=\"url(#svgIDa)\"><animate attributeName=\"y\" dur=\"10s\" keyTimes=\"0;0.45;0.46;0.54;0.55;1\" repeatCount=\"indefinite\" values=\"11;11;7;7;11;11\"/></rect>"}, "github-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M15 4.5C14.6122 4.39991 13.6683 4 12 4C10.3317 4 9.38784 4.39991 9 4.5C8.47455 4.07463 7.0625 3 5.5 3C5.15625 4 5.21371 5.21921 5.5 6C4.75 7 4.5 8 4.5 9.5C4.5 11.6875 4.98302 13.0822 6 14C7.01698 14.9178 8.1113 15.3749 9.5 15.5C8.84944 16.038 9 17.3743 9 18V22H15V18C15 17.3743 15.1506 16.038 14.5 15.5C15.8887 15.3749 16.983 14.9178 18 14C19.017 13.0822 19.5 11.6875 19.5 9.5C19.5 8 19.25 7 18.5 6C18.7863 5.21921 18.8438 4 18.5 3C16.9375 3 15.5255 4.07463 15 4.5Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M12 4C13.6683 4 14.6122 4.39991 15 4.5C15.5255 4.07463 16.9375 3 18.5 3C18.8438 4 18.7863 5.21921 18.5 6C19.25 7 19.5 8 19.5 9.5C19.5 11.6875 19.017 13.0822 18 14C16.983 14.9178 15.8887 15.3749 14.5 15.5C15.1506 16.038 15 17.3743 15 18C15 18.7256 15 21 15 21M12 4C10.3317 4 9.38784 4.39991 9 4.5C8.47455 4.07463 7.0625 3 5.5 3C5.15625 4 5.21371 5.21921 5.5 6C4.75 7 4.5 8 4.5 9.5C4.5 11.6875 4.98301 13.0822 6 14C7.01699 14.9178 8.1113 15.3749 9.5 15.5C8.84944 16.038 9 17.3743 9 18C9 18.7256 9 21 9 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"30;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M9 19C7.59375 19 6.15625 18.4375 5.3125 17.8125C4.46875 17.1875 4.21875 16.1562 3 15.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/></path></g>"}, "grid-3": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"10\" stroke-dashoffset=\"10\" stroke-linecap=\"round\"><g><circle cx=\"5\" cy=\"5\" r=\"1.5\"/><circle cx=\"12\" cy=\"5\" r=\"1.5\"/><circle cx=\"19\" cy=\"5\" r=\"1.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/></g><g><circle cx=\"5\" cy=\"12\" r=\"1.5\"/><circle cx=\"12\" cy=\"12\" r=\"1.5\"/><circle cx=\"19\" cy=\"12\" r=\"1.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.2s\" values=\"10;0\"/></g><g><circle cx=\"5\" cy=\"19\" r=\"1.5\"/><circle cx=\"12\" cy=\"19\" r=\"1.5\"/><circle cx=\"19\" cy=\"19\" r=\"1.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"10;0\"/></g></g>"}, "grid-3-filled": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\" stroke=\"currentColor\" stroke-dasharray=\"10\" stroke-dashoffset=\"10\" stroke-linecap=\"round\"><g><circle cx=\"5\" cy=\"5\" r=\"1.5\"/><circle cx=\"12\" cy=\"5\" r=\"1.5\"/><circle cx=\"19\" cy=\"5\" r=\"1.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.5s\" values=\"0;1\"/></g><g><circle cx=\"5\" cy=\"12\" r=\"1.5\"/><circle cx=\"12\" cy=\"12\" r=\"1.5\"/><circle cx=\"19\" cy=\"12\" r=\"1.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.5s\" values=\"0;1\"/></g><g><circle cx=\"5\" cy=\"19\" r=\"1.5\"/><circle cx=\"12\" cy=\"19\" r=\"1.5\"/><circle cx=\"19\" cy=\"19\" r=\"1.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.5s\" values=\"0;1\"/></g></g>"}, "grid-3-twotone": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\" stroke=\"currentColor\" stroke-dasharray=\"10\" stroke-dashoffset=\"10\" stroke-linecap=\"round\"><g><circle cx=\"5\" cy=\"5\" r=\"1.5\"/><circle cx=\"12\" cy=\"5\" r=\"1.5\"/><circle cx=\"19\" cy=\"5\" r=\"1.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></g><g><circle cx=\"5\" cy=\"12\" r=\"1.5\"/><circle cx=\"12\" cy=\"12\" r=\"1.5\"/><circle cx=\"19\" cy=\"12\" r=\"1.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.15s\" values=\"0;0.3\"/></g><g><circle cx=\"5\" cy=\"19\" r=\"1.5\"/><circle cx=\"12\" cy=\"19\" r=\"1.5\"/><circle cx=\"19\" cy=\"19\" r=\"1.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></g></g>"}, "hash": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M4 9H21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"20;0\"/></path><path d=\"M3 15H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"20;0\"/></path><path d=\"M10 3L8 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"20;0\"/></path><path d=\"M16 3L14 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"20;0\"/></path></g>"}, "hash-small": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"16\" stroke-dashoffset=\"16\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M6 9H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"16;0\"/></path><path d=\"M5 15H18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"16;0\"/></path><path d=\"M10 5L8 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"16;0\"/></path><path d=\"M16 5L14 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"16;0\"/></path></g>"}, "heart": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"30\" stroke-dashoffset=\"30\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 8C12 8 12 8 12.7578 7C13.6343 5.84335 14.9398 5 16.5 5C18.9853 5 21 7.01472 21 9.5C21 10.4251 20.7209 11.285 20.2422 12C19.435 13.206 12 21 12 21M12 8C12 8 12 8 11.2422 7C10.3657 5.84335 9.06021 5 7.5 5C5.01472 5 3 7.01472 3 9.5C3 10.4251 3.27914 11.285 3.75777 12C4.56504 13.206 12 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"30;0\"/></path>"}, "heart-filled": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M12 20L20.5 11V7L17 5.5L12 7L7 5.5L3.5 7V11L12 20Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.5s\" values=\"0;1\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"30\" stroke-dashoffset=\"30\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 8C12 8 12 8 12.7578 7C13.6343 5.84335 14.9398 5 16.5 5C18.9853 5 21 7.01472 21 9.5C21 10.4251 20.7209 11.285 20.2422 12C19.435 13.206 12 21 12 21M12 8C12 8 12 8 11.2422 7C10.3657 5.84335 9.06021 5 7.5 5C5.01472 5 3 7.01472 3 9.5C3 10.4251 3.27914 11.285 3.75777 12C4.56504 13.206 12 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"30;0\"/></path>"}, "heart-filled-half": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M3.5 11L12 20V7L7 5.5L3.5 7V11Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.5s\" values=\"0;1\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"30\" stroke-dashoffset=\"30\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 8C12 8 12 8 12.7578 7C13.6343 5.84335 14.9398 5 16.5 5C18.9853 5 21 7.01472 21 9.5C21 10.4251 20.7209 11.285 20.2422 12C19.435 13.206 12 21 12 21M12 8C12 8 12 8 11.2422 7C10.3657 5.84335 9.06021 5 7.5 5C5.01472 5 3 7.01472 3 9.5C3 10.4251 3.27914 11.285 3.75777 12C4.56504 13.206 12 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"30;0\"/></path>"}, "heart-half": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M12 8C12 8 12 8 11.2422 7C10.3657 5.84335 9.06021 5 7.5 5C5.01472 5 3 7.01472 3 9.5C3 10.4251 3.27914 11.285 3.75777 12C4.56504 13.206 12 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"30;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M12 8V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"15;0\"/></path></g>"}, "heart-half-filled": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M3.5 11L12 20V7L7 5.5L3.5 7V11Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.5s\" values=\"0;1\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M12 8C12 8 12 8 11.2422 7C10.3657 5.84335 9.06021 5 7.5 5C5.01472 5 3 7.01472 3 9.5C3 10.4251 3.27914 11.285 3.75777 12C4.56504 13.206 12 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"30;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M12 8V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"15;0\"/></path></g>"}, "heart-half-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M3.5 11L12 20V7L7 5.5L3.5 7V11Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M12 8C12 8 12 8 11.2422 7C10.3657 5.84335 9.06021 5 7.5 5C5.01472 5 3 7.01472 3 9.5C3 10.4251 3.27914 11.285 3.75777 12C4.56504 13.206 12 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"30;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M12 8V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"15;0\"/></path></g>"}, "heart-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M12 20L20.5 11V7L17 5.5L12 7L7 5.5L3.5 7V11L12 20Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"30\" stroke-dashoffset=\"30\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 8C12 8 12 8 12.7578 7C13.6343 5.84335 14.9398 5 16.5 5C18.9853 5 21 7.01472 21 9.5C21 10.4251 20.7209 11.285 20.2422 12C19.435 13.206 12 21 12 21M12 8C12 8 12 8 11.2422 7C10.3657 5.84335 9.06021 5 7.5 5C5.01472 5 3 7.01472 3 9.5C3 10.4251 3.27914 11.285 3.75777 12C4.56504 13.206 12 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"30;0\"/></path>"}, "heart-twotone-half": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M3.5 11L12 20V7L7 5.5L3.5 7V11Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"30\" stroke-dashoffset=\"30\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 8C12 8 12 8 12.7578 7C13.6343 5.84335 14.9398 5 16.5 5C18.9853 5 21 7.01472 21 9.5C21 10.4251 20.7209 11.285 20.2422 12C19.435 13.206 12 21 12 21M12 8C12 8 12 8 11.2422 7C10.3657 5.84335 9.06021 5 7.5 5C5.01472 5 3 7.01472 3 9.5C3 10.4251 3.27914 11.285 3.75777 12C4.56504 13.206 12 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"30;0\"/></path>"}, "heart-twotone-half-filled": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\"><path d=\"M3.5 11L12 20V7L7 5.5L3.5 7V11Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.5s\" values=\"0;1\"/></path><path d=\"M12 20L20.5 11V7L17 5.5L12 7V20Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"30\" stroke-dashoffset=\"30\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 8C12 8 12 8 12.7578 7C13.6343 5.84335 14.9398 5 16.5 5C18.9853 5 21 7.01472 21 9.5C21 10.4251 20.7209 11.285 20.2422 12C19.435 13.206 12 21 12 21M12 8C12 8 12 8 11.2422 7C10.3657 5.84335 9.06021 5 7.5 5C5.01472 5 3 7.01472 3 9.5C3 10.4251 3.27914 11.285 3.75777 12C4.56504 13.206 12 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"30;0\"/></path>"}, "home": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M4.5 21.5h15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M4.5 21.5V8M19.5 21.5V8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M9.5 21.5V12.5H14.5V21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.4s\" values=\"24;0\"/></path><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" stroke-width=\"2\" d=\"M2 10L12 2L22 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"30;0\"/></path></g>"}, "home-md": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"21\" stroke-dashoffset=\"21\" d=\"M5 21H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"21;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M5 21V8M19 21V8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M9 21V13H15V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.4s\" values=\"24;0\"/></path><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M2 10L12 2L22 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"26;0\"/></path></g>"}, "home-md-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 8L12 3L18 8V20H16V13L15 12H9L8 13V20H6V8Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"21\" stroke-dashoffset=\"21\" d=\"M5 21H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"21;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M5 21V8M19 21V8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M9 21V13H15V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.4s\" values=\"24;0\"/></path><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M2 10L12 2L22 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"26;0\"/></path></g>"}, "home-md-twotone-alt": {"body": "<rect width=\"4\" height=\"6\" x=\"10\" y=\"14\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></rect><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"21\" stroke-dashoffset=\"21\" d=\"M5 21H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"21;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M5 21V8M19 21V8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M9 21V13H15V21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.4s\" values=\"24;0\"/></path><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M2 10L12 2L22 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"26;0\"/></path></g>"}, "home-simple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"21\" stroke-dashoffset=\"21\" d=\"M5 21H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"21;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M5 21V8M19 21V8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M2 10L12 2L22 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"26;0\"/></path></g>"}, "home-simple-filled": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 8L12 3L18 8V20H6V8Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.5s\" values=\"0;1\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"21\" stroke-dashoffset=\"21\" d=\"M5 21H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"21;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M5 21V8M19 21V8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M2 10L12 2L22 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"26;0\"/></path></g>"}, "home-simple-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 8L12 3L18 8V20H6V8Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"21\" stroke-dashoffset=\"21\" d=\"M5 21H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"21;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M5 21V8M19 21V8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M2 10L12 2L22 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"26;0\"/></path></g>"}, "home-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M5 8.5L12 3L19 8.5V21H15V13L14 12H10L9 13V21H5V8.5Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M4.5 21.5h15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M4.5 21.5V8M19.5 21.5V8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M9.5 21.5V12.5H14.5V21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.4s\" values=\"24;0\"/></path><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" stroke-width=\"2\" d=\"M2 10L12 2L22 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"30;0\"/></path></g>"}, "home-twotone-alt": {"body": "<rect width=\"4\" height=\"8\" x=\"10\" y=\"13\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></rect><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M4.5 21.5h15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M4.5 21.5V8M19.5 21.5V8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"15;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M9.5 21.5V12.5H14.5V21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.4s\" values=\"24;0\"/></path><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" stroke-width=\"2\" d=\"M2 10L12 2L22 10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"30;0\"/></path></g>"}, "iconify1": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><g fill=\"currentColor\" fill-opacity=\"0\"><path fill-rule=\"evenodd\" d=\"M12 18C15.125 18 17.3257 15.122 17 14.5C16.6728 13.875 15.5 16 12 16C8.5 16 7.3125 13.875 7 14.5C6.6875 15.125 8.875 18 12 18Z\" clip-rule=\"evenodd\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.0s\" dur=\"0.2s\" values=\"0;1\"/></path><path d=\"M9.5 9C9.5 8.48223 9.01777 8 8.5 8C7.98223 8 7.5 8.48223 7.5 9V10.4375C7.5 10.9553 7.98223 11.5 8.5 11.5C9.01777 11.5 9.5 11.0178 9.5 10.5V9Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></path><path d=\"M16.5 9C16.5 8.48223 16.0178 8 15.5 8C14.9822 8 14.5 8.48223 14.5 9V10.4375C14.5 10.9553 14.9822 11.5 15.5 11.5C16.0178 11.5 16.5 11.0178 16.5 10.5V9Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.2s\" values=\"0;1\"/></path></g>"}, "iconify2": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"><path d=\"M4 7V21\" class=\"il-md-length-15 il-md-duration-2 il-md-delay-0\"/><path d=\"M4 3V5\" class=\"il-md-length-15 il-md-duration-2 il-md-delay-0\"/><path stroke-linecap=\"round\" d=\"M18 4.25204C17.3608 4.08751 16.6906 4 16 4C11.5817 4 8 7.58172 8 12C8 16.4183 11.5817 20 16 20C16.6906 20 17.3608 19.9125 18 19.748\" class=\"il-md-length-40 il-md-duration-3 il-md-delay-2\"/><path stroke-linecap=\"round\" d=\"M16 8C13.7909 8 12 9.79086 12 12C12 14.2091 13.7909 16 16 16C18.2091 16 20 14.2091 20 12C20 9.79086 18.2091 8 16 8Z\" class=\"il-md-length-40 il-md-duration-5 il-md-delay-5\"/></g>", "hidden": true}, "image": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" stroke-width=\"2\" d=\"M3 14V5H21V19H3V14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M3 16L7 13L10 15L16 10L21 14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.4s\" values=\"26;0\"/></path></g><circle cx=\"7.5\" cy=\"9.5\" r=\"1.5\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.4s\" values=\"0;1\"/></circle>"}, "image-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path fill=\"none\" stroke-dasharray=\"66\" stroke-dashoffset=\"66\" stroke-width=\"2\" d=\"M3 14V5H21V19H3V14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"52\" stroke-dashoffset=\"52\" d=\"M3 16L7 13L10 15L16 10L21 14V19H3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.8s\" values=\"52;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g><circle cx=\"7.5\" cy=\"9.5\" r=\"1.5\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.4s\" values=\"0;1\"/></circle>"}, "instagram": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"66\" stroke-dashoffset=\"66\" d=\"M12 3H8C5.23858 3 3 5.23858 3 8V16C3 18.7614 5.23858 21 8 21H16C18.7614 21 21 18.7614 21 16V8C21 5.23858 18.7614 3 16 3z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"66;132\"/></path><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M12 8C14.20914 8 16 9.79086 16 12C16 14.20914 14.20914 16 12 16C9.79086 16 8 14.2091 8 12C8 9.79086 9.79086 8 12 8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.4s\" values=\"26;0\"/></path></g><circle cx=\"17\" cy=\"7\" r=\"1.5\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.1s\" dur=\"0.4s\" values=\"0;1\"/></circle>"}, "laptop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"50\" stroke-dashoffset=\"50\" d=\"M12 17H5V7H19V17Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"50;0\"/></path><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 19H21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.3s\" values=\"20;0\"/></path></g>"}, "laptop-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"50\" stroke-dashoffset=\"50\" d=\"M12 17H5V7H19V17Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"50;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.0s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M3 19H21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.3s\" values=\"20;0\"/></path></g>"}, "light-dark": {"body": "<defs><mask id=\"svgIDb\"><circle cx=\"7.5\" cy=\"7.5\" r=\"5.5\" fill=\"#fff\"/><circle cx=\"7.5\" cy=\"7.5\" r=\"5.5\"><animate fill=\"freeze\" attributeName=\"cx\" dur=\"0.4s\" values=\"7.5;11\"/><animate fill=\"freeze\" attributeName=\"r\" dur=\"0.4s\" values=\"5.5;6.5\"/></circle></mask><mask id=\"svgIDc\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"9\" r=\"5.5\"><animate fill=\"freeze\" attributeName=\"cy\" begin=\"1s\" dur=\"0.5s\" values=\"9;15\"/></circle><g fill-opacity=\"0\"><use href=\"#svgIDa\" transform=\"rotate(-75 12 15)\"/><use href=\"#svgIDa\" transform=\"rotate(-25 12 15)\"/><use href=\"#svgIDa\" transform=\"rotate(25 12 15)\"/><use href=\"#svgIDa\" transform=\"rotate(75 12 15)\"/><set attributeName=\"fill-opacity\" begin=\"1.5s\" to=\"1\"/></g></g><path d=\"M0 10h26v5h-26z\"/><path fill=\"none\" stroke=\"#fff\" stroke-dasharray=\"26\" stroke-dashoffset=\"26\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M1 12h22\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"26;52\"/></path></mask><symbol id=\"svgIDa\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"1.5s\" dur=\"0.4s\" values=\"M11 18h2L12 20z;M10.5 21.5h3L12 24z\"/></path></symbol></defs><g fill=\"currentColor\"><rect width=\"13\" height=\"13\" x=\"1\" y=\"1\" mask=\"url(#svgIDb)\"/><path d=\"M-2 11h28v13h-28z\" mask=\"url(#svgIDc)\" transform=\"rotate(-45 12 12)\"/></g>"}, "light-dark-loop": {"body": "<defs><mask id=\"svgIDb\"><circle cx=\"7.5\" cy=\"7.5\" r=\"5.5\" fill=\"#fff\"/><circle cx=\"7.5\" cy=\"7.5\" r=\"5.5\"><animate fill=\"freeze\" attributeName=\"cx\" dur=\"0.4s\" values=\"7.5;11\"/><animate fill=\"freeze\" attributeName=\"r\" dur=\"0.4s\" values=\"5.5;6.5\"/></circle></mask><mask id=\"svgIDc\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"9\" r=\"5.5\"><animate fill=\"freeze\" attributeName=\"cy\" begin=\"1s\" dur=\"0.5s\" values=\"9;15\"/></circle><g><g fill-opacity=\"0\"><use href=\"#svgIDa\" transform=\"rotate(-125 12 15)\"/><use href=\"#svgIDa\" transform=\"rotate(-75 12 15)\"/><use href=\"#svgIDa\" transform=\"rotate(-25 12 15)\"/><use href=\"#svgIDa\" transform=\"rotate(25 12 15)\"/><use href=\"#svgIDa\" transform=\"rotate(75 12 15)\"/><set attributeName=\"fill-opacity\" begin=\"1.5s\" to=\"1\"/></g><animateTransform attributeName=\"transform\" dur=\"5s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"0 12 15;50 12 15\"/></g></g><path d=\"M0 10h26v5h-26z\"/><path fill=\"none\" stroke=\"#fff\" stroke-dasharray=\"26\" stroke-dashoffset=\"26\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M1 12h22\"><animate attributeName=\"d\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M0 12h22;M2 12h22;M0 12h22\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"26;52\"/></path></mask><symbol id=\"svgIDa\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"1.5s\" dur=\"0.4s\" values=\"M11 18h2L12 20z;M10.5 21.5h3L12 24z\"/></path></symbol></defs><g fill=\"currentColor\"><rect width=\"13\" height=\"13\" x=\"1\" y=\"1\" mask=\"url(#svgIDb)\"/><path d=\"M-2 11h28v13h-28z\" mask=\"url(#svgIDc)\" transform=\"rotate(-45 12 12)\"/></g>"}, "lightbulb": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"46\" stroke-dashoffset=\"46\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 17H9V14.1973C7.2066 13.1599 6 11.2208 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9C18 11.2208 16.7934 13.1599 15 14.1973V17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"46;0\"/></path><rect width=\"6\" height=\"0\" x=\"9\" y=\"20\" fill=\"currentColor\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;2\"/></rect>"}, "lightbulb-filled": {"body": "<g fill=\"currentColor\"><path fill-opacity=\"0\" stroke=\"currentColor\" stroke-dasharray=\"46\" stroke-dashoffset=\"46\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 17H9V14.1973C7.2066 13.1599 6 11.2208 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9C18 11.2208 16.7934 13.1599 15 14.1973V17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"46;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.5s\" values=\"0;1\"/></path><rect width=\"6\" height=\"0\" x=\"9\" y=\"20\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;2\"/></rect></g>"}, "lightbulb-off": {"body": "<mask id=\"svgIDa\"><path fill=\"none\" stroke=\"#fff\" stroke-dasharray=\"46\" stroke-dashoffset=\"46\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 17H9V14.1973C7.2066 13.1599 6 11.2208 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9C18 11.2208 16.7934 13.1599 15 14.1973V17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"46;0\"/></path><rect width=\"6\" height=\"0\" x=\"9\" y=\"20\" fill=\"#fff\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;2\"/></rect><g fill=\"none\" stroke-dasharray=\"26\" stroke-dashoffset=\"26\" stroke-linecap=\"round\" stroke-width=\"2\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path stroke=\"#fff\" d=\"M1 13h22\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"26;0\"/></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "lightbulb-off-filled": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><path fill-opacity=\"0\" stroke=\"#fff\" stroke-dasharray=\"46\" stroke-dashoffset=\"46\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 17H9V14.1973C7.2066 13.1599 6 11.2208 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9C18 11.2208 16.7934 13.1599 15 14.1973V17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"46;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.5s\" values=\"0;1\"/></path><rect width=\"6\" height=\"0\" x=\"9\" y=\"20\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;2\"/></rect></g><g fill=\"none\" stroke-dasharray=\"26\" stroke-dashoffset=\"26\" stroke-linecap=\"round\" stroke-width=\"2\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path stroke=\"#fff\" d=\"M1 13h22\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"26;0\"/></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "lightbulb-off-filled-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><path fill-opacity=\"0\" stroke=\"#fff\" stroke-dasharray=\"46\" stroke-dashoffset=\"46\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 17H9V14.1973C7.2066 13.1599 6 11.2208 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9C18 11.2208 16.7934 13.1599 15 14.1973V17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"46;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.5s\" values=\"0;1\"/></path><rect width=\"6\" height=\"0\" x=\"9\" y=\"20\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;2\"/></rect></g><g fill=\"none\" stroke-dasharray=\"26\" stroke-dashoffset=\"26\" stroke-linecap=\"round\" stroke-width=\"2\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path stroke=\"#fff\" d=\"M0 13h22\"><animate attributeName=\"d\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M0 13h22;M2 13h22;M0 13h22\"/></path><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.2s\" dur=\"0.2s\" values=\"26;0\"/></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "lightbulb-off-loop": {"body": "<mask id=\"svgIDa\"><path fill=\"none\" stroke=\"#fff\" stroke-dasharray=\"46\" stroke-dashoffset=\"46\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 17H9V14.1973C7.2066 13.1599 6 11.2208 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9C18 11.2208 16.7934 13.1599 15 14.1973V17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"46;0\"/></path><rect width=\"6\" height=\"0\" x=\"9\" y=\"20\" fill=\"#fff\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;2\"/></rect><g fill=\"none\" stroke-dasharray=\"26\" stroke-dashoffset=\"26\" stroke-linecap=\"round\" stroke-width=\"2\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path stroke=\"#fff\" d=\"M0 13h22\"><animate attributeName=\"d\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M0 13h22;M2 13h22;M0 13h22\"/></path><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"26;0\"/></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "lightbulb-off-twotone": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><path fill-opacity=\"0\" stroke=\"#fff\" stroke-dasharray=\"46\" stroke-dashoffset=\"46\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 17H9V14.1973C7.2066 13.1599 6 11.2208 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9C18 11.2208 16.7934 13.1599 15 14.1973V17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"46;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.15s\" values=\"0;0.3\"/></path><rect width=\"6\" height=\"0\" x=\"9\" y=\"20\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;2\"/></rect></g><g fill=\"none\" stroke-dasharray=\"26\" stroke-dashoffset=\"26\" stroke-linecap=\"round\" stroke-width=\"2\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path stroke=\"#fff\" d=\"M1 13h22\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"26;0\"/></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "lightbulb-off-twotone-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><path fill-opacity=\"0\" stroke=\"#fff\" stroke-dasharray=\"46\" stroke-dashoffset=\"46\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 17H9V14.1973C7.2066 13.1599 6 11.2208 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9C18 11.2208 16.7934 13.1599 15 14.1973V17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"46;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.15s\" values=\"0;0.3\"/></path><rect width=\"6\" height=\"0\" x=\"9\" y=\"20\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;2\"/></rect></g><g fill=\"none\" stroke-dasharray=\"26\" stroke-dashoffset=\"26\" stroke-linecap=\"round\" stroke-width=\"2\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path stroke=\"#fff\" d=\"M0 13h22\"><animate attributeName=\"d\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M0 13h22;M2 13h22;M0 13h22\"/></path><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"26;0\"/></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "lightbulb-twotone": {"body": "<g fill=\"currentColor\"><path fill-opacity=\"0\" stroke=\"currentColor\" stroke-dasharray=\"46\" stroke-dashoffset=\"46\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 17H9V14.1973C7.2066 13.1599 6 11.2208 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9C18 11.2208 16.7934 13.1599 15 14.1973V17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"46;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.15s\" values=\"0;0.3\"/></path><rect width=\"6\" height=\"0\" x=\"9\" y=\"20\" rx=\"1\"><animate fill=\"freeze\" attributeName=\"height\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;2\"/></rect></g>"}, "linkedin": {"body": "<circle cx=\"4\" cy=\"4\" r=\"2\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" dur=\"0.4s\" values=\"0;1\"/></circle><g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"4\"><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M4 10V20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M10 10V20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M10 15C10 12.2386 12.2386 10 15 10C17.7614 10 20 12.2386 20 15V20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.5s\" values=\"24;0\"/></path></g>"}, "list": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"14\" stroke-dashoffset=\"14\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M8 5H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.1s\" dur=\"0.2s\" values=\"14;0\"/></path><path d=\"M8 10H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"14;0\"/></path><path d=\"M8 15H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"14;0\"/></path><path d=\"M8 20H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"14;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><circle cx=\"4\" cy=\"5\" r=\"1\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" dur=\"0.2s\" values=\"0;1\"/></circle><circle cx=\"4\" cy=\"10\" r=\"1\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.3s\" dur=\"0.2s\" values=\"0;1\"/></circle><circle cx=\"4\" cy=\"15\" r=\"1\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></circle><circle cx=\"4\" cy=\"20\" r=\"1\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.2s\" values=\"0;1\"/></circle></g>"}, "list-3": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\"><g stroke-dasharray=\"10\" stroke-dashoffset=\"10\"><circle cx=\"5\" cy=\"5\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/></circle><circle cx=\"5\" cy=\"12\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/></circle><circle cx=\"5\" cy=\"19\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.4s\" dur=\"0.2s\" values=\"10;0\"/></circle></g><g stroke-dasharray=\"28\" stroke-dashoffset=\"28\"><rect width=\"11\" height=\"3\" x=\"9.5\" y=\"3.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.1s\" dur=\"0.5s\" values=\"28;0\"/></rect><rect width=\"11\" height=\"3\" x=\"9.5\" y=\"10.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.5s\" values=\"28;0\"/></rect><rect width=\"11\" height=\"3\" x=\"9.5\" y=\"17.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.5s\" dur=\"0.5s\" values=\"28;0\"/></rect></g></g>"}, "list-3-filled": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\" stroke=\"currentColor\" stroke-linecap=\"round\"><g stroke-dasharray=\"10\" stroke-dashoffset=\"10\"><circle cx=\"5\" cy=\"5\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.0s\" dur=\"0.5s\" values=\"0;1\"/></circle><circle cx=\"5\" cy=\"12\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.2s\" dur=\"0.5s\" values=\"0;1\"/></circle><circle cx=\"5\" cy=\"19\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.4s\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.4s\" dur=\"0.5s\" values=\"0;1\"/></circle></g><g stroke-dasharray=\"28\" stroke-dashoffset=\"28\"><rect width=\"11\" height=\"3\" x=\"9.5\" y=\"3.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.1s\" dur=\"0.5s\" values=\"28;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.0s\" dur=\"0.5s\" values=\"0;1\"/></rect><rect width=\"11\" height=\"3\" x=\"9.5\" y=\"10.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.5s\" values=\"28;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.2s\" dur=\"0.5s\" values=\"0;1\"/></rect><rect width=\"11\" height=\"3\" x=\"9.5\" y=\"17.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.5s\" dur=\"0.5s\" values=\"28;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.4s\" dur=\"0.5s\" values=\"0;1\"/></rect></g></g>"}, "list-3-twotone": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\" stroke=\"currentColor\" stroke-linecap=\"round\"><g stroke-dasharray=\"10\" stroke-dashoffset=\"10\"><circle cx=\"5\" cy=\"5\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.0s\" dur=\"0.15s\" values=\"0;0.3\"/></circle><circle cx=\"5\" cy=\"12\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.2s\" dur=\"0.15s\" values=\"0;0.3\"/></circle><circle cx=\"5\" cy=\"19\" r=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.4s\" dur=\"0.2s\" values=\"10;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.4s\" dur=\"0.15s\" values=\"0;0.3\"/></circle></g><g stroke-dasharray=\"28\" stroke-dashoffset=\"28\"><rect width=\"11\" height=\"3\" x=\"9.5\" y=\"3.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.1s\" dur=\"0.5s\" values=\"28;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.0s\" dur=\"0.15s\" values=\"0;0.3\"/></rect><rect width=\"11\" height=\"3\" x=\"9.5\" y=\"10.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.5s\" values=\"28;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.2s\" dur=\"0.15s\" values=\"0;0.3\"/></rect><rect width=\"11\" height=\"3\" x=\"9.5\" y=\"17.5\" rx=\"1.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.5s\" dur=\"0.5s\" values=\"28;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.4s\" dur=\"0.15s\" values=\"0;0.3\"/></rect></g></g>"}, "list-indented": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M8 5H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.1s\" dur=\"0.2s\" values=\"14;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M10 10H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M12 15H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M14 20H20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"8;0\"/></path></g><g fill=\"currentColor\" fill-opacity=\"0\"><circle cx=\"4\" cy=\"5\" r=\"1\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" dur=\"0.2s\" values=\"0;1\"/></circle><circle cx=\"6\" cy=\"10\" r=\"1\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.3s\" dur=\"0.2s\" values=\"0;1\"/></circle><circle cx=\"8\" cy=\"15\" r=\"1\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.2s\" values=\"0;1\"/></circle><circle cx=\"10\" cy=\"20\" r=\"1\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.2s\" values=\"0;1\"/></circle></g>"}, "loading-alt-loop": {"body": "<circle cx=\"12\" cy=\"3.5\" r=\"1.5\" fill=\"currentColor\" opacity=\"0\"><animateTransform attributeName=\"transform\" calcMode=\"discrete\" dur=\"2.4s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"0 12 12;90 12 12;180 12 12;270 12 12\"/><animate attributeName=\"opacity\" dur=\"0.6s\" keyTimes=\"0;0.5;1\" repeatCount=\"indefinite\" values=\"1;1;0\"/></circle><circle cx=\"12\" cy=\"3.5\" r=\"1.5\" fill=\"currentColor\" opacity=\"0\"><animateTransform attributeName=\"transform\" begin=\"0.2s\" calcMode=\"discrete\" dur=\"2.4s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"30 12 12;120 12 12;210 12 12;300 12 12\"/><animate attributeName=\"opacity\" begin=\"0.2s\" dur=\"0.6s\" keyTimes=\"0;0.5;1\" repeatCount=\"indefinite\" values=\"1;1;0\"/></circle><circle cx=\"12\" cy=\"3.5\" r=\"1.5\" fill=\"currentColor\" opacity=\"0\"><animateTransform attributeName=\"transform\" begin=\"0.4s\" calcMode=\"discrete\" dur=\"2.4s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"60 12 12;150 12 12;240 12 12;330 12 12\"/><animate attributeName=\"opacity\" begin=\"0.4s\" dur=\"0.6s\" keyTimes=\"0;0.5;1\" repeatCount=\"indefinite\" values=\"1;1;0\"/></circle>"}, "loading-loop": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"15\" stroke-dashoffset=\"15\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C16.9706 3 21 7.02944 21 12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"15;0\"/><animateTransform attributeName=\"transform\" dur=\"1.5s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"0 12 12;360 12 12\"/></path>"}, "loading-twotone-loop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-opacity=\".3\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"1.3s\" values=\"60;0\"/></path><path stroke-dasharray=\"15\" stroke-dashoffset=\"15\" d=\"M12 3C16.9706 3 21 7.02944 21 12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"15;0\"/><animateTransform attributeName=\"transform\" dur=\"1.5s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"0 12 12;360 12 12\"/></path></g>"}, "marker": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"44\" stroke-dashoffset=\"44\" stroke-width=\"2\" d=\"M6 14L17 3L21 7L10 18L6 14Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"44;0\"/></path><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M9 17L6.5 19.5H2.5L7 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"20;0\"/></path></g>"}, "marker-filled": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path fill=\"none\" stroke-dasharray=\"44\" stroke-dashoffset=\"44\" stroke-width=\"2\" d=\"M6 14L17 3L21 7L10 18L6 14Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"44;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M9 17L6.5 19.5H2.5L7 15Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"16;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.5s\" values=\"0;1\"/></path></g>"}, "marker-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path fill=\"none\" stroke-dasharray=\"44\" stroke-dashoffset=\"44\" stroke-width=\"2\" d=\"M6 14L17 3L21 7L10 18L6 14Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"44;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M9 17L6.5 19.5H2.5L7 15Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"16;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g>"}, "menu": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"24\" stroke-dashoffset=\"24\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M5 5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"24;0\"/></path><path d=\"M5 12H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"24;0\"/></path><path d=\"M5 19H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"24;0\"/></path></g>"}, "menu-fold-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M7 9L4 12L7 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M19 5H5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"16;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M19 12H10\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M19 19H5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"16;0\"/></path></g>"}, "menu-fold-right": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M17 9L20 12L17 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M5 5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"16;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M5 12H14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M5 19H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"16;0\"/></path></g>"}, "menu-unfold-left": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M21 9L18 12L21 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M19 5H5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"16;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M14 12H5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M19 19H5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"16;0\"/></path></g>"}, "menu-unfold-right": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M3 9L6 12L3 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M5 5H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"16;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M10 12H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"12;0\"/></path><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M5 19H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"16;0\"/></path></g>"}, "minus": {"body": "<path stroke=\"currentColor\" stroke-dasharray=\"18\" stroke-dashoffset=\"18\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5 12H19\" fill=\"currentColor\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"18;0\"/></path>"}, "minus-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M7 12H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "minus-circle-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M7 12H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"12;0\"/></path></g>"}, "moon": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\"><path d=\"M15.22 6.03L17.75 4.09L14.56 4L13.5 1L12.44 4L9.25 4.09L11.78 6.03L10.87 9.09L13.5 7.28L16.13 9.09L15.22 6.03Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.4s\" values=\"0;1\"/></path><path d=\"M19.61 12.25L21.25 11L19.19 10.95L18.5 9L17.81 10.95L15.75 11L17.39 12.25L16.8 14.23L18.5 13.06L20.2 14.23L19.61 12.25Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.1s\" dur=\"0.4s\" values=\"0;1\"/></path></g><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"56\" stroke-dashoffset=\"56\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 6 C7 12.08 11.92 17 18 17 C18.53 17 19.05 16.96 19.56 16.89 C17.95 19.36 15.17 21 12 21 C7.03 21 3 16.97 3 12 C3 8.83 4.64 6.05 7.11 4.44 C7.04 4.95 7 5.47 7 6 Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"56;0\"/></path>"}, "moon-filled": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\"><path d=\"M15.22 6.03L17.75 4.09L14.56 4L13.5 1L12.44 4L9.25 4.09L11.78 6.03L10.87 9.09L13.5 7.28L16.13 9.09L15.22 6.03Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.4s\" values=\"0;1\"/></path><path d=\"M19.61 12.25L21.25 11L19.19 10.95L18.5 9L17.81 10.95L15.75 11L17.39 12.25L16.8 14.23L18.5 13.06L20.2 14.23L19.61 12.25Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.1s\" dur=\"0.4s\" values=\"0;1\"/></path></g><g fill-opacity=\"0\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" stroke-dasharray=\"56\" stroke-dashoffset=\"56\" d=\"M7 6 C7 12.08 11.92 17 18 17 C18.53 17 19.05 16.96 19.56 16.89 C17.95 19.36 15.17 21 12 21 C7.03 21 3 16.97 3 12 C3 8.83 4.64 6.05 7.11 4.44 C7.04 4.95 7 5.47 7 6 Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"56;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.5s\" dur=\"0.5s\" values=\"0;1\"/></path></g>"}, "moon-twotone": {"body": "<g fill=\"currentColor\" fill-opacity=\"0\"><path d=\"M15.22 6.03L17.75 4.09L14.56 4L13.5 1L12.44 4L9.25 4.09L11.78 6.03L10.87 9.09L13.5 7.28L16.13 9.09L15.22 6.03Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.4s\" values=\"0;1\"/></path><path d=\"M19.61 12.25L21.25 11L19.19 10.95L18.5 9L17.81 10.95L15.75 11L17.39 12.25L16.8 14.23L18.5 13.06L20.2 14.23L19.61 12.25Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.1s\" dur=\"0.4s\" values=\"0;1\"/></path></g><g fill-opacity=\"0\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" stroke-dasharray=\"56\" stroke-dashoffset=\"56\" d=\"M7 6 C7 12.08 11.92 17 18 17 C18.53 17 19.05 16.96 19.56 16.89 C17.95 19.36 15.17 21 12 21 C7.03 21 3 16.97 3 12 C3 8.83 4.64 6.05 7.11 4.44 C7.04 4.95 7 5.47 7 6 Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"56;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.5s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g>"}, "navigation-left-down": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M21 9H12C9.23858 9 7 11.2386 7 14V20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"26;0\"/></path><g stroke-dasharray=\"8\" stroke-dashoffset=\"8\"><path d=\"M7 21L3 17\"/><path d=\"M7 21L11 17\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"8;0\"/></g></g>"}, "navigation-left-up": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M21 15H12C9.23858 15 7 12.7614 7 10V4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"26;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M7 3L3 7M7 3L11 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"8;0\"/></path></g>"}, "navigation-right-down": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M3 9H12C14.76142 9 17 11.2386 17 14V20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"26;0\"/></path><g stroke-dasharray=\"8\" stroke-dashoffset=\"8\"><path d=\"M17 21L21 17\"/><path d=\"M17 21L13 17\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"8;0\"/></g></g>"}, "navigation-right-up": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"26\" stroke-dashoffset=\"26\" d=\"M3 15H12C14.76142 15 17 12.7614 17 10V4\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"26;0\"/></path><g stroke-dasharray=\"8\" stroke-dashoffset=\"8\"><path d=\"M17 3L21 7\"/><path d=\"M17 3L13 7\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"8;0\"/></g></g>"}, "paint-drop": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"28\" stroke-dashoffset=\"28\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C12 3 19 9 19 15C19 17 18 21 12 21M12 3C12 3 5 9 5 15C5 17 6 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"28;0\"/></path>"}, "paint-drop-filled": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M12 4C12 4 18 9 18 15C18 19 15 20 12 20C9 20 6 19 6 15C6 9 12 4 12 4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.4s\" dur=\"0.5s\" values=\"0;1\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"28\" stroke-dashoffset=\"28\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C12 3 19 9 19 15C19 17 18 21 12 21M12 3C12 3 5 9 5 15C5 17 6 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"28;0\"/></path>"}, "paint-drop-half-filled": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 15C6 9 12 4 12 4C12 4 14.9522 6.46019 16.715 10L6.8347 18C6.31173 17.2671 6 16.2894 6 15Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.4s\" dur=\"0.5s\" values=\"0;1\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"28\" stroke-dashoffset=\"28\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C12 3 19 9 19 15C19 17 18 21 12 21M12 3C12 3 5 9 5 15C5 17 6 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"28;0\"/></path>"}, "paint-drop-half-filled-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 15C6 9 12 4 12 4C12 4 14.9522 6.46019 16.715 10L6.8347 18C6.31173 17.2671 6 16.2894 6 15Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.4s\" dur=\"0.5s\" values=\"0;1\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" d=\"M12 4C12 4 18 9 18 15C18 19 15 20 12 20C9 20 6 19 6 15C6 9 12 4 12 4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.4s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"28\" stroke-dashoffset=\"28\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C12 3 19 9 19 15C19 17 18 21 12 21M12 3C12 3 5 9 5 15C5 17 6 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"28;0\"/></path>"}, "paint-drop-half-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M6 15C6 9 12 4 12 4C12 4 14.9522 6.46019 16.715 10L6.8347 18C6.31173 17.2671 6 16.2894 6 15Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.4s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"28\" stroke-dashoffset=\"28\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C12 3 19 9 19 15C19 17 18 21 12 21M12 3C12 3 5 9 5 15C5 17 6 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"28;0\"/></path>"}, "paint-drop-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M12 4C12 4 18 9 18 15C18 19 15 20 12 20C9 20 6 19 6 15C6 9 12 4 12 4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.4s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"28\" stroke-dashoffset=\"28\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C12 3 19 9 19 15C19 17 18 21 12 21M12 3C12 3 5 9 5 15C5 17 6 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"28;0\"/></path>"}, "pencil": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"56\" stroke-dashoffset=\"56\" d=\"M3 21L4.99998 15L16 4C17 3 19 3 20 4C21 5 21 7 20 8L8.99998 19L3 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"56;0\"/></path><g stroke-dasharray=\"6\" stroke-dashoffset=\"6\"><path d=\"M15 5L19 9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-width=\"1\" d=\"M6 15L9 18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path></g></g>"}, "pencil-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"56\" stroke-dashoffset=\"56\" d=\"M3 21L4.99998 15L16 4C17 3 19 3 20 4C21 5 21 7 20 8L8.99998 19L3 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"56;0\"/></path><g stroke-dasharray=\"6\" stroke-dashoffset=\"6\"><path d=\"M15 5L19 9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-width=\"1\" d=\"M6 15L9 18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path></g></g><path fill=\"currentColor\" fill-opacity=\"0\" d=\"M17 4H20V7L9 18L6 15L17 4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path>"}, "pencil-twotone-alt": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"56\" stroke-dashoffset=\"56\" d=\"M3 21L4.99998 15L16 4C17 3 19 3 20 4C21 5 21 7 20 8L8.99998 19L3 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"56;0\"/></path><g stroke-dasharray=\"6\" stroke-dashoffset=\"6\"><path d=\"M15 5L19 9\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path><path stroke-width=\"1\" d=\"M6 15L9 18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path></g></g><path fill=\"currentColor\" fill-opacity=\"0\" d=\"M9 18L18 9L15 6L6 15L9 18Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path>"}, "plus": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"18\" stroke-dashoffset=\"18\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M12 5V19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.3s\" values=\"18;0\"/></path><path d=\"M5 12H19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"18;0\"/></path></g>"}, "plus-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><g stroke-dasharray=\"12\" stroke-dashoffset=\"12\"><path d=\"M12 7V17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"12;0\"/></path><path d=\"M7 12H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"12;0\"/></path></g><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path></g>"}, "plus-circle-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><g stroke-dasharray=\"12\" stroke-dashoffset=\"12\"><path d=\"M12 7V17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.8s\" dur=\"0.2s\" values=\"12;0\"/></path><path d=\"M7 12H17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"12;0\"/></path></g><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g>"}, "question": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"32\" stroke-dashoffset=\"32\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7 8C7 5.23858 9.23857 3 12 3C14.7614 3 17 5.23858 17 8C17 9.6356 16.2147 11.0878 15.0005 12C14.1647 12.6279 12 14 12 17\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"32;0\"/></path><circle cx=\"12\" cy=\"21\" r=\"1\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.5s\" dur=\"0.2s\" values=\"0;1\"/></circle>"}, "question-circle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/></path><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M8.99999 10C8.99999 8.34315 10.3431 7 12 7C13.6569 7 15 8.34315 15 10C15 10.9814 14.5288 11.8527 13.8003 12.4C13.0718 12.9473 12.5 13 12 14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.4s\" values=\"20;0\"/></path></g><circle cx=\"12\" cy=\"17\" r=\"1\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.2s\" values=\"0;1\"/></circle>"}, "question-circle-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M8.99999 10C8.99999 8.34315 10.3431 7 12 7C13.6569 7 15 8.34315 15 10C15 10.9814 14.5288 11.8527 13.8003 12.4C13.0718 12.9473 12.5 13 12 14\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.4s\" values=\"20;0\"/></path></g><circle cx=\"12\" cy=\"17\" r=\"1\" fill=\"currentColor\" fill-opacity=\"0\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1s\" dur=\"0.2s\" values=\"0;1\"/></circle>"}, "reddit": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\" fill-opacity=\"0\"><ellipse cx=\"12\" cy=\"14.71\" stroke=\"#fff\" stroke-dasharray=\"48\" stroke-dashoffset=\"48\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" rx=\"8\" ry=\"5.29\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"48;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.4s\" values=\"0;1\"/></ellipse><circle cx=\"7.24\" cy=\"11.97\" r=\"2.24\"><set attributeName=\"fill-opacity\" begin=\"1s\" to=\"1\"/><animate fill=\"freeze\" attributeName=\"cx\" begin=\"1s\" dur=\"0.2s\" values=\"7.24;3.94\"/></circle><circle cx=\"16.76\" cy=\"11.97\" r=\"2.24\"><set attributeName=\"fill-opacity\" begin=\"1s\" to=\"1\"/><animate fill=\"freeze\" attributeName=\"cx\" begin=\"1s\" dur=\"0.2s\" values=\"16.76;20.06\"/></circle><circle cx=\"18.45\" cy=\"4.23\" r=\"1.61\"><set attributeName=\"fill-opacity\" begin=\"2.6s\" to=\"1\"/></circle></g><path fill=\"none\" stroke=\"#fff\" stroke-dasharray=\"12\" stroke-dashoffset=\"12\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\".8\" d=\"M12 8.75L13.18 3.11L18.21 4.18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"2.4s\" dur=\"0.2s\" values=\"12;0\"/></path><g fill-opacity=\"0\"><circle cx=\"8.45\" cy=\"13.59\" r=\"1.61\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.4s\" values=\"0;1\"/></circle><circle cx=\"15.55\" cy=\"13.59\" r=\"1.61\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.6s\" dur=\"0.4s\" values=\"0;1\"/></circle></g><path fill=\"none\" stroke=\"#000\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-linecap=\"round\" stroke-width=\".8\" d=\"M8.47 17.52C8.47 17.52 9.41 18.58 12 18.58C14.58 18.58 15.53 17.52 15.53 17.52\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"2s\" dur=\"0.2s\" values=\"8;0\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "reddit-circle": {"body": "<mask id=\"svgIDa\"><path fill=\"#fff\" fill-opacity=\"0\" stroke=\"#fff\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.5s\" values=\"0;1\"/></path><g fill-opacity=\"0\"><ellipse cx=\"12\" cy=\"13.77\" rx=\"5.83\" ry=\"4.06\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.1s\" dur=\"0.4s\" values=\"0;1\"/></ellipse><circle cx=\"8.99\" cy=\"11.99\" r=\"1.45\"><set attributeName=\"fill-opacity\" begin=\"1.5s\" to=\"1\"/><animate fill=\"freeze\" attributeName=\"cx\" begin=\"1.5s\" dur=\"0.2s\" values=\"8.99;6.79\"/></circle><circle cx=\"15.01\" cy=\"11.99\" r=\"1.45\"><set attributeName=\"fill-opacity\" begin=\"1.5s\" to=\"1\"/><animate fill=\"freeze\" attributeName=\"cx\" begin=\"1.5s\" dur=\"0.2s\" values=\"15.01;17.21\"/></circle><circle cx=\"16.18\" cy=\"7.01\" r=\"1.04\"><set attributeName=\"fill-opacity\" begin=\"3.1s\" to=\"1\"/></circle></g><path fill=\"none\" stroke=\"#000\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\".54\" d=\"M12 9.91L12.76 6.27L16 6.98\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"2.9s\" dur=\"0.2s\" values=\"8;0\"/></path><g fill=\"#fff\" fill-opacity=\"0\"><circle cx=\"9.71\" cy=\"13.04\" r=\"1.04\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.7s\" dur=\"0.4s\" values=\"0;1\"/></circle><circle cx=\"14.29\" cy=\"13.04\" r=\"1.04\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.1s\" dur=\"0.4s\" values=\"0;1\"/></circle></g><path fill=\"none\" stroke=\"#fff\" stroke-dasharray=\"6\" stroke-dashoffset=\"6\" stroke-linecap=\"round\" stroke-width=\".54\" d=\"M9.72 15.6C9.72 15.6 10.33 16.29 12 16.291C13.67 16.29 14.28 15.6 14.28 15.6\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"2.5s\" dur=\"0.2s\" values=\"6;0\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "reddit-circle-loop": {"body": "<mask id=\"svgIDa\"><path fill=\"#fff\" fill-opacity=\"0\" stroke=\"#fff\" stroke-dasharray=\"60\" stroke-dashoffset=\"60\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.5s\" values=\"60;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.5s\" values=\"0;1\"/></path><g fill-opacity=\"0\"><ellipse cx=\"12\" cy=\"13.77\" rx=\"5.83\" ry=\"4.06\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.1s\" dur=\"0.4s\" values=\"0;1\"/></ellipse><circle cx=\"8.99\" cy=\"11.99\" r=\"1.45\"><set attributeName=\"fill-opacity\" begin=\"1.5s\" to=\"1\"/><animate fill=\"freeze\" attributeName=\"cx\" begin=\"1.5s\" dur=\"0.2s\" values=\"8.99;6.79\"/></circle><circle cx=\"15.01\" cy=\"11.99\" r=\"1.45\"><set attributeName=\"fill-opacity\" begin=\"1.5s\" to=\"1\"/><animate fill=\"freeze\" attributeName=\"cx\" begin=\"1.5s\" dur=\"0.2s\" values=\"15.01;17.21\"/></circle><circle cx=\"16.18\" cy=\"7.01\" r=\"1.04\"><set attributeName=\"fill-opacity\" begin=\"3.1s\" to=\"1\"/><animate attributeName=\"cx\" begin=\"2.9s\" dur=\"6s\" repeatCount=\"indefinite\" values=\"16.18;7.82;16.18\"/></circle></g><path fill=\"none\" stroke=\"#000\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\".54\" d=\"M12 9.91L12.76 6.27L16 6.98\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"2.9s\" dur=\"0.2s\" values=\"8;0\"/><animate attributeName=\"d\" begin=\"2.9s\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M12 9.91L12.76 6.27L16 6.98;M12 9.91L12 5.2L12 6.98;M12 9.91L11.24 6.27L8 6.98;M12 9.91L12 5.2L12 6.98;M12 9.91L12.76 6.27L16 6.98\"/></path><g fill=\"#fff\" fill-opacity=\"0\"><circle cx=\"9.71\" cy=\"13.04\" r=\"1.04\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.7s\" dur=\"0.4s\" values=\"0;1\"/></circle><circle cx=\"14.29\" cy=\"13.04\" r=\"1.04\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"2.1s\" dur=\"0.4s\" values=\"0;1\"/></circle></g><path fill=\"none\" stroke=\"#fff\" stroke-dasharray=\"6\" stroke-dashoffset=\"6\" stroke-linecap=\"round\" stroke-width=\".54\" d=\"M9.72 15.6C9.72 15.6 10.33 16.29 12 16.291C13.67 16.29 14.28 15.6 14.28 15.6\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"2.5s\" dur=\"0.2s\" values=\"6;0\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "reddit-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\" fill-opacity=\"0\"><ellipse cx=\"12\" cy=\"14.71\" stroke=\"#fff\" stroke-dasharray=\"48\" stroke-dashoffset=\"48\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" rx=\"8\" ry=\"5.29\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"48;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.4s\" values=\"0;1\"/></ellipse><circle cx=\"7.24\" cy=\"11.97\" r=\"2.24\"><set attributeName=\"fill-opacity\" begin=\"1s\" to=\"1\"/><animate fill=\"freeze\" attributeName=\"cx\" begin=\"1s\" dur=\"0.2s\" values=\"7.24;3.94\"/></circle><circle cx=\"16.76\" cy=\"11.97\" r=\"2.24\"><set attributeName=\"fill-opacity\" begin=\"1s\" to=\"1\"/><animate fill=\"freeze\" attributeName=\"cx\" begin=\"1s\" dur=\"0.2s\" values=\"16.76;20.06\"/></circle><circle cx=\"18.45\" cy=\"4.23\" r=\"1.61\"><set attributeName=\"fill-opacity\" begin=\"2.6s\" to=\"1\"/><animate attributeName=\"cx\" begin=\"2.4s\" dur=\"6s\" repeatCount=\"indefinite\" values=\"18.45;5.75;18.45\"/></circle></g><path fill=\"none\" stroke=\"#fff\" stroke-dasharray=\"12\" stroke-dashoffset=\"12\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\".8\" d=\"M12 8.75L13.18 3.11L18.21 4.18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"2.4s\" dur=\"0.2s\" values=\"12;0\"/><animate attributeName=\"d\" begin=\"2.4s\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M12 8.75L13.18 3.11L18.21 4.18;M12 8.75L12 2L12 4.18;M12 8.75L10.82 3.11L5.79 4.18;M12 8.75L12 2L12 4.18;M12 8.75L13.18 3.11L18.21 4.18\"/></path><g fill-opacity=\"0\"><circle cx=\"8.45\" cy=\"13.59\" r=\"1.61\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.2s\" dur=\"0.4s\" values=\"0;1\"/></circle><circle cx=\"15.55\" cy=\"13.59\" r=\"1.61\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.6s\" dur=\"0.4s\" values=\"0;1\"/></circle></g><path fill=\"none\" stroke=\"#000\" stroke-dasharray=\"8\" stroke-dashoffset=\"8\" stroke-linecap=\"round\" stroke-width=\".8\" d=\"M8.47 17.52C8.47 17.52 9.41 18.58 12 18.58C14.58 18.58 15.53 17.52 15.53 17.52\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"2s\" dur=\"0.2s\" values=\"8;0\"/></path></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "remove": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"22\" stroke-dashoffset=\"22\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M19 5L5 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.3s\" values=\"22;0\"/></path><path d=\"M5 5L19 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.3s\" values=\"22;0\"/></path></g>"}, "rotate-180": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"24\" stroke-dashoffset=\"24\" d=\"M12 6C15.3137 6 18 8.68629 18 12C18 15.3137 15.3137 18 12 18H9.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"24;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M9 18L12 21M9 18L12 15\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "rotate-270": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"32\" stroke-dashoffset=\"32\" d=\"M12 6C15.3137 6 18 8.68629 18 12C18 15.3137 15.3137 18 12 18C8.68629 18 6 15.3137 6 12V9.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"32;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M6 9L3 12M6 9L9 12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.6s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "rotate-90": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M12 6C15.3137 6 18 8.68629 18 12V14.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"14;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M18 15L21 12M18 15L15 12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "search": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M10.5 13.5L3 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"16;0\"/></path><path stroke-dasharray=\"40\" stroke-dashoffset=\"40\" d=\"M10.7574 13.2426C8.41421 10.8995 8.41421 7.10051 10.7574 4.75736C13.1005 2.41421 16.8995 2.41421 19.2426 4.75736C21.5858 7.10051 21.5858 10.8995 19.2426 13.2426C16.8995 15.5858 13.1005 15.5858 10.7574 13.2426Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"40;0\"/></path></g>"}, "search-filled": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"none\" stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M10.5 13.5L3 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"16;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"40\" stroke-dashoffset=\"40\" d=\"M10.7574 13.2426C8.41421 10.8995 8.41421 7.10051 10.7574 4.75736C13.1005 2.41421 16.8995 2.41421 19.2426 4.75736C21.5858 7.10051 21.5858 10.8995 19.2426 13.2426C16.8995 15.5858 13.1005 15.5858 10.7574 13.2426Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"40;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.5s\" values=\"0;1\"/></path></g>"}, "search-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"none\" stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M10.5 13.5L3 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.2s\" values=\"16;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"40\" stroke-dashoffset=\"40\" d=\"M10.7574 13.2426C8.41421 10.8995 8.41421 7.10051 10.7574 4.75736C13.1005 2.41421 16.8995 2.41421 19.2426 4.75736C21.5858 7.10051 21.5858 10.8995 19.2426 13.2426C16.8995 15.5858 13.1005 15.5858 10.7574 13.2426Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"40;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g>"}, "sun-rising-filled-loop": {"body": "<circle cx=\"12\" cy=\"32\" r=\"6\" fill=\"currentColor\"><animate fill=\"freeze\" attributeName=\"cy\" dur=\"0.6s\" values=\"32;12\"/></circle><g fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"2\" stroke-dashoffset=\"2\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.7s\" dur=\"0.2s\" values=\"M12 19v1M19 12h1M12 5v-1M5 12h-1;M12 21v1M21 12h1M12 3v-1M3 12h-1\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"2;0\"/></path><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.9s\" dur=\"0.2s\" values=\"M17 17l0.5 0.5M17 7l0.5 -0.5M7 7l-0.5 -0.5M7 17l-0.5 0.5;M18.5 18.5l0.5 0.5M18.5 5.5l0.5 -0.5M5.5 5.5l-0.5 -0.5M5.5 18.5l-0.5 0.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"2;0\"/></path><animateTransform attributeName=\"transform\" dur=\"30s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"0 12 12;360 12 12\"/></g>"}, "sun-rising-loop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"32\" r=\"5\"><animate fill=\"freeze\" attributeName=\"cy\" dur=\"0.6s\" values=\"32;12\"/></circle><g stroke-dasharray=\"2\" stroke-dashoffset=\"2\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.7s\" dur=\"0.2s\" values=\"M12 19v1M19 12h1M12 5v-1M5 12h-1;M12 21v1M21 12h1M12 3v-1M3 12h-1\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"2;0\"/></path><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.9s\" dur=\"0.2s\" values=\"M17 17l0.5 0.5M17 7l0.5 -0.5M7 7l-0.5 -0.5M7 17l-0.5 0.5;M18.5 18.5l0.5 0.5M18.5 5.5l0.5 -0.5M5.5 5.5l-0.5 -0.5M5.5 18.5l-0.5 0.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"2;0\"/></path><animateTransform attributeName=\"transform\" dur=\"30s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"0 12 12;360 12 12\"/></g></g>"}, "sun-rising-twotone-loop": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><circle cx=\"12\" cy=\"32\" r=\"5\" fill=\"currentColor\" fill-opacity=\".3\"><animate fill=\"freeze\" attributeName=\"cy\" dur=\"0.6s\" values=\"32;12\"/></circle><g fill=\"none\" stroke-dasharray=\"2\" stroke-dashoffset=\"2\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.7s\" dur=\"0.2s\" values=\"M12 19v1M19 12h1M12 5v-1M5 12h-1;M12 21v1M21 12h1M12 3v-1M3 12h-1\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"2;0\"/></path><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.9s\" dur=\"0.2s\" values=\"M17 17l0.5 0.5M17 7l0.5 -0.5M7 7l-0.5 -0.5M7 17l-0.5 0.5;M18.5 18.5l0.5 0.5M18.5 5.5l0.5 -0.5M5.5 5.5l-0.5 -0.5M5.5 18.5l-0.5 0.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"2;0\"/></path><animateTransform attributeName=\"transform\" dur=\"30s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"0 12 12;360 12 12\"/></g></g>"}, "sunny-filled": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"34\" stroke-dashoffset=\"34\" d=\"M12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"34;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.5s\" values=\"0;1\"/></path><g fill=\"none\" stroke-dasharray=\"2\" stroke-dashoffset=\"2\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.5s\" dur=\"0.2s\" values=\"M12 19v1M19 12h1M12 5v-1M5 12h-1;M12 21v1M21 12h1M12 3v-1M3 12h-1\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"2;0\"/></path><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.7s\" dur=\"0.2s\" values=\"M17 17l0.5 0.5M17 7l0.5 -0.5M7 7l-0.5 -0.5M7 17l-0.5 0.5;M18.5 18.5l0.5 0.5M18.5 5.5l0.5 -0.5M5.5 5.5l-0.5 -0.5M5.5 18.5l-0.5 0.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"2;0\"/></path></g></g>"}, "sunny-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"34\" stroke-dashoffset=\"34\" d=\"M12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"34;0\"/></path><g stroke-dasharray=\"2\" stroke-dashoffset=\"2\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.5s\" dur=\"0.2s\" values=\"M12 19v1M19 12h1M12 5v-1M5 12h-1;M12 21v1M21 12h1M12 3v-1M3 12h-1\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"2;0\"/></path><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.7s\" dur=\"0.2s\" values=\"M17 17l0.5 0.5M17 7l0.5 -0.5M7 7l-0.5 -0.5M7 17l-0.5 0.5;M18.5 18.5l0.5 0.5M18.5 5.5l0.5 -0.5M5.5 5.5l-0.5 -0.5M5.5 18.5l-0.5 0.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"2;0\"/></path></g></g>"}, "sunny-outline-loop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"34\" stroke-dashoffset=\"34\" d=\"M12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"34;0\"/></path><g stroke-dasharray=\"2\" stroke-dashoffset=\"2\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.5s\" dur=\"0.2s\" values=\"M12 19v1M19 12h1M12 5v-1M5 12h-1;M12 21v1M21 12h1M12 3v-1M3 12h-1\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"2;0\"/></path><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.7s\" dur=\"0.2s\" values=\"M17 17l0.5 0.5M17 7l0.5 -0.5M7 7l-0.5 -0.5M7 17l-0.5 0.5;M18.5 18.5l0.5 0.5M18.5 5.5l0.5 -0.5M5.5 5.5l-0.5 -0.5M5.5 18.5l-0.5 0.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"2;0\"/></path><animateTransform attributeName=\"transform\" dur=\"30s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"0 12 12;360 12 12\"/></g></g>"}, "sunny-outline-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"34\" stroke-dashoffset=\"34\" d=\"M12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"34;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke-dasharray=\"2\" stroke-dashoffset=\"2\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.5s\" dur=\"0.2s\" values=\"M12 19v1M19 12h1M12 5v-1M5 12h-1;M12 21v1M21 12h1M12 3v-1M3 12h-1\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"2;0\"/></path><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.7s\" dur=\"0.2s\" values=\"M17 17l0.5 0.5M17 7l0.5 -0.5M7 7l-0.5 -0.5M7 17l-0.5 0.5;M18.5 18.5l0.5 0.5M18.5 5.5l0.5 -0.5M5.5 5.5l-0.5 -0.5M5.5 18.5l-0.5 0.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"2;0\"/></path></g></g>"}, "sunny-outline-twotone-loop": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"34\" stroke-dashoffset=\"34\" d=\"M12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"34;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.9s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g fill=\"none\" stroke-dasharray=\"2\" stroke-dashoffset=\"2\"><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.5s\" dur=\"0.2s\" values=\"M12 19v1M19 12h1M12 5v-1M5 12h-1;M12 21v1M21 12h1M12 3v-1M3 12h-1\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"2;0\"/></path><path d=\"M0 0\"><animate fill=\"freeze\" attributeName=\"d\" begin=\"0.7s\" dur=\"0.2s\" values=\"M17 17l0.5 0.5M17 7l0.5 -0.5M7 7l-0.5 -0.5M7 17l-0.5 0.5;M18.5 18.5l0.5 0.5M18.5 5.5l0.5 -0.5M5.5 5.5l-0.5 -0.5M5.5 18.5l-0.5 0.5\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"2;0\"/></path><animateTransform attributeName=\"transform\" dur=\"30s\" repeatCount=\"indefinite\" type=\"rotate\" values=\"0 12 12;360 12 12\"/></g></g>"}, "telegram": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"16\" stroke-dashoffset=\"16\" d=\"M21 5L18.5 20M21 5L9 13.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"16;0\"/></path><path stroke-dasharray=\"22\" stroke-dashoffset=\"22\" d=\"M21 5L2 12.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"22;0\"/></path><path stroke-dasharray=\"12\" stroke-dashoffset=\"12\" d=\"M18.5 20L9 13.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.3s\" values=\"12;0\"/></path><path stroke-dasharray=\"8\" stroke-dashoffset=\"8\" d=\"M2 12.5L9 13.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.3s\" values=\"8;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 16L9 19M9 13.5L9 19\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.3s\" values=\"6;0\"/></path></g>"}, "text-box": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"62\" stroke-dashoffset=\"62\" d=\"M20 6V5C20 4.45 19.55 4 19 4H5C4.45 4 4 4.45 4 5V19C4 19.55 4.45 20 5 20H19C19.55 20 20 19.55 20 19z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"62;124\"/></path><g stroke-dasharray=\"10\" stroke-dashoffset=\"10\"><path d=\"M8 8h8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M8 12h8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"10;0\"/></path></g><path stroke-dasharray=\"7\" stroke-dashoffset=\"7\" d=\"M8 16h5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"7;0\"/></path></g>"}, "text-box-multiple": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"62\" stroke-dashoffset=\"62\" d=\"M22 4V3C22 2.45 21.55 2 21 2H7C6.45 2 6 2.45 6 3V17C6 17.55 6.45 18 7 18H21C21.55 18 22 17.55 22 17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"62;124\"/></path><g stroke-dasharray=\"10\" stroke-dashoffset=\"10\"><path d=\"M10 6h8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M10 10h8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"10;0\"/></path></g><path stroke-dasharray=\"7\" stroke-dashoffset=\"7\" d=\"M10 14h5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"7;0\"/></path><path stroke-dasharray=\"34\" stroke-dashoffset=\"34\" d=\"M2 6V21C2 21.55 2.45 22 3 22H18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.4s\" dur=\"0.4s\" values=\"34;68\"/></path></g>"}, "text-box-multiple-to-text-box-transition": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M22 4V3C22 2.45 21.55 2 21 2H7C6.45 2 6 2.45 6 3V17C6 17.55 6.45 18 7 18H21C21.55 18 22 17.55 22 17zM10 6h8M10 10h8M10 14h5\"><animateMotion fill=\"freeze\" begin=\"0.4s\" calcMode=\"linear\" dur=\"0.4s\" path=\"M0 0l-2 2\"/></path><path stroke-dasharray=\"34\" stroke-dashoffset=\"68\" d=\"M2 6V21C2 21.55 2.45 22 3 22H18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"68;34\"/></path></g>"}, "text-box-multiple-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"62\" stroke-dashoffset=\"62\" d=\"M22 4V3C22 2.45 21.55 2 21 2H7C6.45 2 6 2.45 6 3V17C6 17.55 6.45 18 7 18H21C21.55 18 22 17.55 22 17z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"62;124\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g stroke-dasharray=\"10\" stroke-dashoffset=\"10\"><path d=\"M10 6h8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M10 10h8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"10;0\"/></path></g><path stroke-dasharray=\"7\" stroke-dashoffset=\"7\" d=\"M10 14h5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"7;0\"/></path><path stroke-dasharray=\"34\" stroke-dashoffset=\"34\" d=\"M2 6V21C2 21.55 2.45 22 3 22H18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.4s\" dur=\"0.4s\" values=\"34;68\"/></path></g>"}, "text-box-multiple-twotone-to-text-box-twotone-transition": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\".3\" d=\"M22 4V3C22 2.45 21.55 2 21 2H7C6.45 2 6 2.45 6 3V17C6 17.55 6.45 18 7 18H21C21.55 18 22 17.55 22 17zM10 6h8M10 10h8M10 14h5\"><animateMotion fill=\"freeze\" begin=\"0.4s\" calcMode=\"linear\" dur=\"0.4s\" path=\"M0 0l-2 2\"/></path><path stroke-dasharray=\"34\" stroke-dashoffset=\"68\" d=\"M2 6V21C2 21.55 2.45 22 3 22H18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"68;34\"/></path></g>"}, "text-box-to-text-box-multiple-transition": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M20 6V5C20 4.45 19.55 4 19 4H5C4.45 4 4 4.45 4 5V19C4 19.55 4.45 20 5 20H19C19.55 20 20 19.55 20 19zM8 8h8M8 12h8M8 16h5\"><animateMotion fill=\"freeze\" calcMode=\"linear\" dur=\"0.4s\" path=\"M0 0l2-2\"/></path><path stroke-dasharray=\"34\" stroke-dashoffset=\"34\" d=\"M2 6V21C2 21.55 2.45 22 3 22H18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.4s\" values=\"34;68\"/></path></g>"}, "text-box-twotone": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"62\" stroke-dashoffset=\"62\" d=\"M20 6V5C20 4.45 19.55 4 19 4H5C4.45 4 4 4.45 4 5V19C4 19.55 4.45 20 5 20H19C19.55 20 20 19.55 20 19z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"62;124\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"1.3s\" dur=\"0.15s\" values=\"0;0.3\"/></path><g stroke-dasharray=\"10\" stroke-dashoffset=\"10\"><path d=\"M8 8h8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"10;0\"/></path><path d=\"M8 12h8\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.9s\" dur=\"0.2s\" values=\"10;0\"/></path></g><path stroke-dasharray=\"7\" stroke-dashoffset=\"7\" d=\"M8 16h5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1.1s\" dur=\"0.2s\" values=\"7;0\"/></path></g>"}, "text-box-twotone-to-text-box-multiple-twotone-transition": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"currentColor\" fill-opacity=\".3\" d=\"M20 6V5C20 4.45 19.55 4 19 4H5C4.45 4 4 4.45 4 5V19C4 19.55 4.45 20 5 20H19C19.55 20 20 19.55 20 19zM8 8h8M8 12h8M8 16h5\"><animateMotion fill=\"freeze\" calcMode=\"linear\" dur=\"0.4s\" path=\"M0 0l2-2\"/></path><path stroke-dasharray=\"34\" stroke-dashoffset=\"34\" d=\"M2 6V21C2 21.55 2.45 22 3 22H18\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.4s\" dur=\"0.4s\" values=\"34;68\"/></path></g>"}, "thumbs-down": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"80\" stroke-dashoffset=\"80\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 4H18L21 11V14H14L15 20L12 21L7 13H3V4H7V13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.8s\" values=\"80;0\"/></path>"}, "thumbs-down-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M3 4H7V13H3V4Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"80\" stroke-dashoffset=\"80\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 4H18L21 11V14H14L15 20L12 21L7 13H3V4H7V13\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.8s\" values=\"80;0\"/></path>"}, "thumbs-up": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"80\" stroke-dashoffset=\"80\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 11L12 3L15 4L14 10H21V13L18 20H7H3V11H7V20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.8s\" values=\"80;0\"/></path>"}, "thumbs-up-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" d=\"M7 11V20H3V11H7Z\"><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.8s\" dur=\"0.15s\" values=\"0;0.3\"/></path><path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"80\" stroke-dashoffset=\"80\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 11L12 3L15 4L14 10H21V13L18 20H7H3V11H7V20\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.8s\" values=\"80;0\"/></path>"}, "twitter": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"62\" stroke-dashoffset=\"62\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19.8906 7.34375C19.7969 7.67188 19.4001 8.50548 18.7219 9.29669C18.2698 17.9717 9.84907 20.7974 4.08456 17.8869C3.29335 16.8414 6.93856 17.2653 8.26666 15.259C3.23684 12.6876 3.63244 5.82103 4.64971 6.1036C7.02333 9.29669 10.8381 9.57926 11.4597 9.29669C11.4597 8.562 11.1489 6.97958 12.8726 5.65148C13.8616 4.94505 15.9297 4.3125 17.8047 6.34375C18.125 6.55469 18.5859 6.64844 19.2734 6.49219C19.6797 6.28125 20.2262 6.427 19.9453 7.15625\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"62;0\"/></path>"}, "twitter-twotone": {"body": "<path fill=\"currentColor\" fill-opacity=\"0\" stroke=\"currentColor\" stroke-dasharray=\"62\" stroke-dashoffset=\"62\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19.8906 7.34375C19.7969 7.67188 19.4001 8.50548 18.7219 9.29669C18.2698 17.9717 9.84907 20.7974 4.08456 17.8869C3.29335 16.8414 6.93856 17.2653 8.26666 15.259C3.23684 12.6876 3.63244 5.82103 4.64971 6.1036C7.02333 9.29669 10.8381 9.57926 11.4597 9.29669C11.4597 8.562 11.1489 6.97958 12.8726 5.65148C13.8616 4.94505 15.9297 4.3125 17.8047 6.34375C18.125 6.55469 18.5859 6.64844 19.2734 6.49219C19.6797 6.28125 20.2262 6.427 19.9453 7.15625\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.6s\" values=\"62;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.6s\" dur=\"0.15s\" values=\"0;0.3\"/></path>"}, "upload-loop": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path fill=\"none\" stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"14;0\"/></path><path fill=\"currentColor\" d=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"><animate attributeName=\"d\" calcMode=\"linear\" dur=\"1.5s\" keyTimes=\"0;0.7;1\" repeatCount=\"indefinite\" values=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5;M12 15 h2 v-3 h2.5 L12 7.5M12 15 h-2 v-3 h-2.5 L12 7.5;M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"/></path></g>"}, "upload-off-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"14;0\"/></path><path fill=\"#fff\" d=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"><animate attributeName=\"d\" calcMode=\"linear\" dur=\"1.5s\" keyTimes=\"0;0.7;1\" repeatCount=\"indefinite\" values=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5;M12 15 h2 v-3 h2.5 L12 7.5M12 15 h-2 v-3 h-2.5 L12 7.5;M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"/></path><g stroke-dasharray=\"26\" stroke-dashoffset=\"26\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path d=\"M0 13h22\"><animate attributeName=\"d\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M0 13h22;M2 13h22;M0 13h22\"/></path><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"26;0\"/></g></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "upload-off-outline": {"body": "<mask id=\"svgIDa\"><g fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"14;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"18;0\"/></path><g stroke-dasharray=\"26\" stroke-dashoffset=\"26\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path d=\"M1 13h22\"/><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"26;0\"/></g></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "upload-off-outline-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"14;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"18;0\"/><animate attributeName=\"d\" calcMode=\"linear\" dur=\"1.5s\" keyTimes=\"0;0.7;1\" repeatCount=\"indefinite\" values=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5;M12 15 h2 v-3 h2.5 L12 7.5M12 15 h-2 v-3 h-2.5 L12 7.5;M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"/></path><g stroke-dasharray=\"26\" stroke-dashoffset=\"26\" transform=\"rotate(45 13 12)\"><path stroke=\"#000\" d=\"M0 11h24\"/><path d=\"M0 13h22\"><animate attributeName=\"d\" dur=\"6s\" repeatCount=\"indefinite\" values=\"M0 13h22;M2 13h22;M0 13h22\"/></path><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"1s\" dur=\"0.2s\" values=\"26;0\"/></g></g></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}, "upload-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"14;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"18;0\"/></path></g>"}, "upload-outline-loop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"14\" stroke-dashoffset=\"14\" d=\"M6 19h12\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.4s\" values=\"14;0\"/></path><path stroke-dasharray=\"18\" stroke-dashoffset=\"18\" d=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.4s\" values=\"18;0\"/><animate attributeName=\"d\" calcMode=\"linear\" dur=\"1.5s\" keyTimes=\"0;0.7;1\" repeatCount=\"indefinite\" values=\"M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5;M12 15 h2 v-3 h2.5 L12 7.5M12 15 h-2 v-3 h-2.5 L12 7.5;M12 15 h2 v-6 h2.5 L12 4.5M12 15 h-2 v-6 h-2.5 L12 4.5\"/></path></g>"}, "uploading-loop": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-dasharray=\"2 4\" stroke-dashoffset=\"6\" d=\"M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3\"><animate attributeName=\"stroke-dashoffset\" dur=\"0.6s\" repeatCount=\"indefinite\" values=\"6;0\"/></path><path stroke-dasharray=\"30\" stroke-dashoffset=\"30\" d=\"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.1s\" dur=\"0.3s\" values=\"30;0\"/></path><path stroke-dasharray=\"10\" stroke-dashoffset=\"10\" d=\"M12 16v-7.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.5s\" dur=\"0.2s\" values=\"10;0\"/></path><path stroke-dasharray=\"6\" stroke-dashoffset=\"6\" d=\"M12 8.5l3.5 3.5M12 8.5l-3.5 3.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.7s\" dur=\"0.2s\" values=\"6;0\"/></path></g>"}, "valign-baseline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M2.5 18.5H21.5M2.5 11.5H21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"20;0\"/></path><path stroke-dasharray=\"50\" stroke-dashoffset=\"50\" stroke-width=\"2\" d=\"M12.75 6H18V18H6V6H12.75Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.4s\" values=\"50;0\"/></path></g>"}, "valign-baseline-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path fill=\"none\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M2.5 18.5H21.5M2.5 11.5H21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"20;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"50\" stroke-dashoffset=\"50\" stroke-width=\"2\" d=\"M12.75 6H18V18H6V6H12.75Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.4s\" values=\"50;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g>"}, "valign-bottom": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M2.5 15.5H21.5M2.5 8.5H21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"20;0\"/></path><path stroke-dasharray=\"50\" stroke-dashoffset=\"50\" stroke-width=\"2\" d=\"M12.75 9H18V21H6V9H12.75Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.4s\" values=\"50;0\"/></path></g>"}, "valign-bottom-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path fill=\"none\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M2.5 15.5H21.5M2.5 8.5H21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"20;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"50\" stroke-dashoffset=\"50\" stroke-width=\"2\" d=\"M12.75 9H18V21H6V9H12.75Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.4s\" values=\"50;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g>"}, "valign-middle": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M2.5 15.5H21.5M2.5 8.5H21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"20;0\"/></path><path stroke-dasharray=\"50\" stroke-dashoffset=\"50\" stroke-width=\"2\" d=\"M12.75 6H18V18H6V6H12.75Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.4s\" values=\"50;0\"/></path></g>"}, "valign-middle-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path fill=\"none\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M2.5 15.5H21.5M2.5 8.5H21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"20;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"50\" stroke-dashoffset=\"50\" stroke-width=\"2\" d=\"M12.75 6H18V18H6V6H12.75Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.4s\" values=\"50;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g>"}, "valign-top": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M2.5 15.5H21.5M2.5 8.5H21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"20;0\"/></path><path stroke-dasharray=\"50\" stroke-dashoffset=\"50\" stroke-width=\"2\" d=\"M12.75 3H18V15H6V3H12.75Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.4s\" values=\"50;0\"/></path></g>"}, "valign-top-twotone": {"body": "<g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path fill=\"none\" stroke-dasharray=\"20\" stroke-dashoffset=\"20\" d=\"M2.5 15.5H21.5M2.5 8.5H21.5\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" dur=\"0.2s\" values=\"20;0\"/></path><path fill=\"currentColor\" fill-opacity=\"0\" stroke-dasharray=\"50\" stroke-dashoffset=\"50\" stroke-width=\"2\" d=\"M12.75 3H18V15H6V3H12.75Z\"><animate fill=\"freeze\" attributeName=\"stroke-dashoffset\" begin=\"0.3s\" dur=\"0.4s\" values=\"50;0\"/><animate fill=\"freeze\" attributeName=\"fill-opacity\" begin=\"0.7s\" dur=\"0.15s\" values=\"0;0.3\"/></path></g>"}, "weather-cloudy-loop": {"body": "<mask id=\"svgIDa\"><g fill=\"#fff\"><circle cx=\"12\" cy=\"11\" r=\"6\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;11;12;13;12\"/></circle><rect width=\"10\" height=\"7\" x=\"8\" y=\"12\"/><rect width=\"16\" height=\"10\" x=\"1\" y=\"9\" rx=\"5\"><animate attributeName=\"x\" dur=\"19s\" repeatCount=\"indefinite\" values=\"1;0;1;2;1\"/></rect><rect width=\"17\" height=\"8\" x=\"6\" y=\"11\" rx=\"4\"><animate attributeName=\"x\" dur=\"23s\" repeatCount=\"indefinite\" values=\"6;5;6;7;6\"/></rect></g><circle cx=\"12\" cy=\"11\" r=\"4\"><animate attributeName=\"cx\" dur=\"30s\" repeatCount=\"indefinite\" values=\"12;11;12;13;12\"/></circle><rect width=\"8\" height=\"6\" x=\"8\" y=\"11\"><animate attributeName=\"x\" dur=\"30s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect><rect width=\"12\" height=\"6\" x=\"3\" y=\"11\" rx=\"3\"><animate attributeName=\"x\" dur=\"19s\" repeatCount=\"indefinite\" values=\"3;2;3;4;3\"/></rect><rect width=\"13\" height=\"4\" x=\"8\" y=\"13\" rx=\"2\"><animate attributeName=\"x\" dur=\"23s\" repeatCount=\"indefinite\" values=\"8;7;8;9;8\"/></rect></mask><rect width=\"24\" height=\"24\" fill=\"currentColor\" mask=\"url(#svgIDa)\"/>"}}, "aliases": {"align-right": {"parent": "align-left", "hFlip": true}, "arrow-align-bottom": {"parent": "arrow-align-left", "rotate": 3}, "arrow-align-middle": {"parent": "arrow-align-center", "rotate": 3}, "arrow-align-right": {"parent": "arrow-align-left", "hFlip": true}, "arrow-align-top": {"parent": "arrow-align-left", "rotate": 3, "hFlip": true}, "arrow-close-down": {"parent": "arrow-close-left", "rotate": 3}, "arrow-close-right": {"parent": "arrow-close-left", "hFlip": true}, "arrow-close-up": {"parent": "arrow-close-left", "rotate": 3, "hFlip": true}, "arrow-down": {"parent": "arrow-left", "rotate": 3}, "arrow-down-circle": {"parent": "arrow-left-circle", "rotate": 3}, "arrow-down-circle-twotone": {"parent": "arrow-left-circle-twotone", "rotate": 3}, "arrow-long-diagonal-rotated": {"parent": "arrow-long-diagonal", "hFlip": true}, "arrow-open-down": {"parent": "arrow-open-left", "rotate": 3}, "arrow-open-right": {"parent": "arrow-open-left", "hFlip": true}, "arrow-open-up": {"parent": "arrow-open-left", "rotate": 3, "hFlip": true}, "arrow-right": {"parent": "arrow-left", "hFlip": true}, "arrow-right-circle": {"parent": "arrow-left-circle", "hFlip": true}, "arrow-right-circle-twotone": {"parent": "arrow-left-circle-twotone", "hFlip": true}, "arrow-small-down": {"parent": "arrow-small-left", "rotate": 3}, "arrow-small-right": {"parent": "arrow-small-left", "hFlip": true}, "arrow-small-up": {"parent": "arrow-small-left", "rotate": 3, "hFlip": true}, "arrow-up": {"parent": "arrow-left", "rotate": 3, "hFlip": true}, "arrow-up-circle": {"parent": "arrow-left-circle", "rotate": 3, "hFlip": true}, "arrow-up-circle-twotone": {"parent": "arrow-left-circle-twotone", "rotate": 3, "hFlip": true}, "arrows-diagonal-rotated": {"parent": "arrows-diagonal", "hFlip": true}, "arrows-vertical": {"parent": "arrows-horizontal", "rotate": 1}, "arrows-vertical-alt": {"parent": "arrows-horizontal-alt", "rotate": 1}, "beer-alt-solid": {"parent": "beer-alt-filled"}, "beer-alt-solid-loop": {"parent": "beer-alt-filled-loop"}, "beer-solid": {"parent": "beer-filled"}, "check-list-3-solid": {"parent": "check-list-3-filled"}, "chevron-double-down": {"parent": "chevron-double-left", "rotate": 3}, "chevron-double-right": {"parent": "chevron-double-left", "hFlip": true}, "chevron-double-up": {"parent": "chevron-double-left", "rotate": 3, "hFlip": true}, "chevron-down": {"parent": "chevron-left", "rotate": 3}, "chevron-down-circle": {"parent": "chevron-left-circle", "rotate": 3}, "chevron-down-circle-twotone": {"parent": "chevron-left-circle-twotone", "rotate": 3}, "chevron-right": {"parent": "chevron-left", "hFlip": true}, "chevron-right-circle": {"parent": "chevron-left-circle", "hFlip": true}, "chevron-right-circle-twotone": {"parent": "chevron-left-circle-twotone", "hFlip": true}, "chevron-small-double-down": {"parent": "chevron-small-double-left", "rotate": 3}, "chevron-small-double-right": {"parent": "chevron-small-double-left", "hFlip": true}, "chevron-small-double-up": {"parent": "chevron-small-double-left", "rotate": 3, "hFlip": true}, "chevron-small-down": {"parent": "chevron-small-left", "rotate": 3}, "chevron-small-right": {"parent": "chevron-small-left", "hFlip": true}, "chevron-small-triple-down": {"parent": "chevron-small-triple-left", "rotate": 3}, "chevron-small-triple-right": {"parent": "chevron-small-triple-left", "hFlip": true}, "chevron-small-triple-up": {"parent": "chevron-small-triple-left", "rotate": 3, "hFlip": true}, "chevron-small-up": {"parent": "chevron-small-left", "rotate": 3, "hFlip": true}, "chevron-triple-down": {"parent": "chevron-triple-left", "rotate": 3}, "chevron-triple-right": {"parent": "chevron-triple-left", "hFlip": true}, "chevron-triple-up": {"parent": "chevron-triple-left", "rotate": 3, "hFlip": true}, "chevron-up": {"parent": "chevron-left", "rotate": 3, "hFlip": true}, "chevron-up-circle": {"parent": "chevron-left-circle", "rotate": 3, "hFlip": true}, "chevron-up-circle-twotone": {"parent": "chevron-left-circle-twotone", "rotate": 3, "hFlip": true}, "double-arrow-vertical": {"parent": "double-arrow-horizontal", "rotate": 1}, "grid-3-solid": {"parent": "grid-3-filled"}, "list-3-solid": {"parent": "list-3-filled"}}, "categories": {"Account": ["account", "account-add", "account-alert", "account-delete", "account-remove", "account-small"], "Alerts": ["alert", "alert-circle", "alert-circle-twotone", "alert-twotone", "bell", "bell-twotone", "question", "question-circle", "question-circle-twotone"], "Arrows": ["arrow-close-left", "arrow-left", "arrow-left-circle", "arrow-left-circle-twotone", "arrow-long-diagonal", "arrow-open-left", "arrow-small-left", "arrows-diagonal", "arrows-horizontal", "arrows-horizontal-alt", "chevron-double-left", "chevron-left", "chevron-left-circle", "chevron-left-circle-twotone", "chevron-small-double-left", "chevron-small-left", "chevron-small-triple-left", "chevron-triple-left", "double-arrow-horizontal", "navigation-left-down", "navigation-left-up", "navigation-right-down", "navigation-right-up"], "Cloud": ["cloud-braces-loop", "cloud-download-loop", "cloud-download-outline-loop", "cloud-loop", "cloud-off-outline-loop", "cloud-outline-loop", "cloud-print-loop", "cloud-print-outline-loop", "cloud-tags-loop", "cloud-upload-loop", "cloud-upload-outline-loop"], "Editing": ["align-center", "align-justify", "align-left", "arrow-align-center", "arrow-align-left", "check-list-3", "check-list-3-filled", "check-list-3-twotone", "clipboard", "clipboard-arrow", "clipboard-arrow-twotone", "clipboard-check", "clipboard-check-twotone", "clipboard-list", "clipboard-list-twotone", "clipboard-minus", "clipboard-minus-twotone", "clipboard-plus", "clipboard-plus-twotone", "clipboard-twotone", "document", "document-add", "document-add-twotone", "document-code", "document-code-twotone", "document-list", "document-list-twotone", "document-remove", "document-remove-twotone", "document-report", "document-report-twotone", "document-twotone", "edit", "edit-twotone", "edit-twotone-full", "grid-3", "grid-3-filled", "grid-3-twotone", "image", "image-twotone", "list", "list-3", "list-3-filled", "list-3-twotone", "list-indented", "marker", "marker-filled", "marker-twotone", "paint-drop", "paint-drop-filled", "paint-drop-half-filled", "paint-drop-half-filled-twotone", "paint-drop-half-twotone", "paint-drop-twotone", "pencil", "pencil-twotone", "pencil-twotone-alt", "rotate-180", "rotate-270", "rotate-90", "valign-baseline", "valign-baseline-twotone", "valign-bottom", "valign-bottom-twotone", "valign-middle", "valign-middle-twotone", "valign-top", "valign-top-twotone"], "Emoji": ["emoji-angry", "emoji-angry-twotone", "emoji-frown", "emoji-frown-open", "emoji-frown-open-twotone", "emoji-frown-twotone", "emoji-grin", "emoji-grin-twotone", "emoji-neutral", "emoji-neutral-twotone", "emoji-smile", "emoji-smile-twotone", "emoji-smile-wink", "emoji-smile-wink-twotone"], "Files and Folders": ["download-loop", "download-off-loop", "download-off-outline", "download-off-outline-loop", "download-outline", "download-outline-loop", "downloading-loop", "text-box", "text-box-multiple", "text-box-multiple-to-text-box-transition", "text-box-multiple-twotone", "text-box-multiple-twotone-to-text-box-twotone-transition", "text-box-to-text-box-multiple-transition", "text-box-twotone", "text-box-twotone-to-text-box-multiple-twotone-transition", "upload-loop", "upload-off-loop", "upload-off-outline", "upload-off-outline-loop", "upload-outline", "upload-outline-loop", "uploading-loop"], "Food and Drink": ["beer", "beer-alt-filled", "beer-alt-filled-loop", "beer-alt-twotone", "beer-alt-twotone-loop", "beer-filled", "beer-loop", "beer-twotone", "beer-twotone-loop", "coffee", "coffee-arrow", "coffee-arrow-filled", "coffee-arrow-twotone", "coffee-filled", "coffee-half-empty-twotone-loop", "coffee-loop", "coffee-twotone", "coffee-twotone-loop"], "Home Automation": ["lightbulb", "lightbulb-filled", "lightbulb-off", "lightbulb-off-filled", "lightbulb-off-filled-loop", "lightbulb-off-loop", "lightbulb-off-twotone", "lightbulb-off-twotone-loop", "lightbulb-twotone"], "Interface": ["cancel", "cancel-twotone", "circle", "circle-to-confirm-circle-transition", "circle-to-confirm-circle-twotone-transition", "circle-twotone", "circle-twotone-to-confirm-circle-twotone-transition", "close", "close-circle", "close-circle-twotone", "confirm", "confirm-circle", "confirm-circle-to-circle-transition", "confirm-circle-twotone", "confirm-circle-twotone-to-circle-transition", "confirm-circle-twotone-to-circle-twotone-transition", "construction", "construction-twotone", "loading-alt-loop", "loading-loop", "loading-twotone-loop", "minus", "minus-circle", "minus-circle-twotone", "plus", "plus-circle", "plus-circle-twotone", "remove", "search", "search-filled", "search-twotone"], "Navigation": ["calendar", "calendar-out", "external-link", "external-link-rounded", "hash", "hash-small", "home", "home-md", "home-md-twotone", "home-md-twotone-alt", "home-simple", "home-simple-filled", "home-simple-twotone", "home-twotone", "home-twotone-alt", "menu", "menu-fold-left", "menu-fold-right", "menu-unfold-left", "menu-unfold-right"], "Social": ["buy-me-a-coffee", "buy-me-a-coffee-filled", "buy-me-a-coffee-twotone", "discord", "discord-twotone", "email", "email-opened", "email-opened-twotone", "email-opened-twotone-alt", "email-twotone", "email-twotone-alt", "facebook", "github", "github-loop", "github-twotone", "heart", "heart-filled", "heart-filled-half", "heart-half", "heart-half-filled", "heart-half-twotone", "heart-twotone", "heart-twotone-half", "heart-twotone-half-filled", "iconify1", "instagram", "linkedin", "reddit", "reddit-circle", "reddit-circle-loop", "reddit-loop", "telegram", "thumbs-down", "thumbs-down-twotone", "thumbs-up", "thumbs-up-twotone", "twitter", "twitter-twotone"], "Technology": ["cloud", "cloud-down", "cloud-down-twotone", "cloud-filled", "cloud-twotone", "cloud-up", "cloud-up-twotone", "computer", "computer-twotone", "laptop", "laptop-twotone"], "Weather": ["cloud", "cloud-filled", "cloud-loop", "cloud-twotone", "light-dark", "light-dark-loop", "moon", "moon-filled", "moon-twotone", "sun-rising-filled-loop", "sun-rising-loop", "sun-rising-twotone-loop", "sunny-filled", "sunny-outline", "sunny-outline-loop", "sunny-outline-twotone", "sunny-outline-twotone-loop", "weather-cloudy-loop"]}, "suffixes": {"": "Outline Animation", "out": "Erase Animation", "loop": "Looping Animation", "transition": "Transition Between Icons"}, "width": 24, "height": 24}