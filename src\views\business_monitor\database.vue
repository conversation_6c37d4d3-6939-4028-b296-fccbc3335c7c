<script setup>
import { onMounted } from "vue";
import { Column, Area, Bar, Line } from "@antv/g2plot";
import api from "@/plugins/axios";
import useDateTimeStore from "@/store/modules/datetime";
import { getUTCTimestampWithOffset } from "@/utils/dayjs";
import { convertTimestampToFullDateTime } from "@/utils/unixTimestampConversion";
import { convertUTCtoLocalTime } from "@/utils/isoTimestampConversion";
import { getDatabase } from "@/api/modules/business_topo";
const timeStore = useDateTimeStore();
let sessionTopology = null;
let waitTopology = null;
let readTopology = null;
let writeTopology = null;
let waitClassification = null;
let dbLoad = null;
let lockWatch = null;
let pgSize = null;
let pgConnect = null;
let pgLock = null;
let pgIo = null;
let checkPoint = null;
let bgWriter = null;
const data = reactive({
  pgParam: {},
  oracleParam: {},
  time: [],
  oracle: {
    conversation: "",
    activeConversation: "",
    pga: "",
    sga: "",
    sessionList: [],
    waitEvent: [],
    topScan: [],
    logicRead: [],
    physicsRead: [],
    waitClass: [],
    dbLoad: [],
    lockList: [],
    topSql: [],
  },
  pg: {
    time: "",
    buffer: "",
    status: "",
    extract: "",
    goback: "",
    insert: "",
    update: "",
    delete: "",
    sizeList: [],
    connectList: [],
    lockList: [],
    databaseIo: [],
    checkPoint: [],
    bgwriter: [],
  },
});
let item = computed({
  get: function () {
    return props.item;
  },
});
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    default: {},
  },
});
onMounted(() => {
  data.time = timeStore.getUtcUnixTimestamp();
  console.log(item.value)
  if (Object.keys(item.value).length !== 0) {
    if (item.value.type === "oracle") {
      destoryPg();
      getOracle();
    } else if (item.value.type === "pg") {
      destoryOracle();
      getPg();
    }
  }
});
watch(
  () => item.value,
  (newValue, oldValue) => {
    if (newValue.type === "oracle") {
      destoryPg();
      getOracle();
    } else if (newValue.type === "pg") {
      destoryOracle();
      getPg();
    }
  }
);
watch(timeStore.$state, (newValue, oldValue) => {
  data.time = [getUTCTimestampWithOffset(newValue.begin), getUTCTimestampWithOffset(newValue.over)];
  if (Object.keys(item.value).length !== 0) {
    if (item.value.type === "oracle") {
      destoryOracle();
      getOracle();
    } else if (item.value.type === "pg") {
      destoryPg();
      getPg();
    }
  }
});
function destoryOracle() {
  if (sessionTopology) {
    sessionTopology.destroy();
    sessionTopology = null;
  }
  if (waitTopology) {
    waitTopology.destroy();
    waitTopology = null;
  }
  if (readTopology) {
    readTopology.destroy();
    readTopology = null;
  }
  if (writeTopology) {
    writeTopology.destroy();
    writeTopology = null;
  }
  if (waitClassification) {
    waitClassification.destroy();
    waitClassification = null;
  }
  if (dbLoad) {
    dbLoad.destroy();
    dbLoad = null;
  }
  if (lockWatch) {
    lockWatch.destroy();
    lockWatch = null;
  }
}
function destoryPg() {
  if (pgSize) {
    pgSize.destroy();
    pgSize = null;
  }
  if (pgConnect) {
    pgConnect.destroy();
    pgConnect = null;
  }
  if (pgLock) {
    pgLock.destroy();
    pgLock = null;
  }
  if (pgIo) {
    pgIo.destroy();
    pgIo = null;
  }
  if (checkPoint) {
    checkPoint.destroy();
    checkPoint = null;
  }
  if (bgWriter) {
    bgWriter.destroy();
    bgWriter = null;
  }
}
function getOracle() {
  data.oracleParam = {
    name: item.value.name,
    start_time: data.time[0],
    end_time: data.time[1],
    type: item.value.type,
    ip: item.value.ip
  };
  getDatabase(data.oracleParam).then((res) => {
    // api.post('business/oracle', { data: data.oracleParam }, { baseURL: '/mock/', }).then((res) => {
    data.oracle = res.data;
    initSession();
    initWait();
    initRead();
    initWrite();
    initWaitClass();
    initDb();
    initLock();
  });
}
function getPg() {
  data.pgParam = {
    name: item.value.name,
    start_time: data.time[0],
    end_time: data.time[1],
    type: item.value.type,
    ip: item.value.ip
  };
  getDatabase(data.pgParam).then((res) => {
    // api.post('business/pg', { data: data.pgParam }, { baseURL: '/mock/', }).then((res) => {
    data.pg = res.data;
    initPgSize();
    initPgConnect();
    initPgLock();
    initPgIo();
    initCheckPoint();
    initBgWriter();
  });
}
function initSession() {
  data.oracle.sessionList.forEach((element) => {
    element.time = convertUTCtoLocalTime(element.time);
  });
  sessionTopology = new Column("sessionTopology", {
    data: data.oracle.sessionList,
    isStack: true,
    xField: "time",
    yField: "count",
    seriesField: "name",
    autoFit: true,
    legend: {
      position: "top",
    },

    interactions: [{ type: "active-region", enable: false }],
    connectedArea: {
      style: (oldStyle, element) => {
        return { fill: "rgba(0,0,0,0.25)", stroke: oldStyle.fill, lineWidth: 0.5 };
      },
    },
  });

  sessionTopology.render();
}
function initWait() {
  data.oracle.waitEvent.forEach((element) => {
    element.time = convertUTCtoLocalTime(element.time);
  });
  waitTopology = new Area("waitTopology", {
    data: data.oracle.waitEvent,
    xField: "time",
    yField: "count",
    seriesField: "name",
    color: ["#6897a7", "#8bc0d6", "#60d7a7", "#dedede", "#fedca9", "#fab36f", "#d96d6f"],
    yAxis: {
      label: {
        formatter: (v) => `${(v / 1000).toFixed(0)}K`, // 除以 1000 并保留一位小数
      },
    },
    legend: {
      position: "top",
    },
  });

  waitTopology.render();
}
function initRead() {
  data.oracle.logicRead.forEach((element) => {
    element.time = convertUTCtoLocalTime(element.time);
  });
  readTopology = new Column("readTopology", {
    data: data.oracle.logicRead,
    isStack: true,
    xField: "time",
    yField: "count",
    seriesField: "name",
    autoFit: true,
    legend: {
      position: "top",
    },
    yAxis: {
      label: {
        formatter: (v) => `${(v / 1000 / 1000).toFixed(0)}Mil`, // 除以 1000 并保留一位小数
      },
    },

    interactions: [{ type: "active-region", enable: false }],
    connectedArea: {
      style: (oldStyle, element) => {
        return { fill: "rgba(0,0,0,0.25)", stroke: oldStyle.fill, lineWidth: 0.5 };
      },
    },
  });
  readTopology.render();
}
function initWrite() {
  data.oracle.physicsRead.forEach((element) => {
    element.time = convertUTCtoLocalTime(element.time);
  });
  writeTopology = new Column("writeTopology", {
    data: data.oracle.physicsRead,
    isStack: true,
    xField: "time",
    yField: "count",
    seriesField: "name",
    autoFit: true,
    legend: {
      position: "top",
    },

    interactions: [{ type: "active-region", enable: false }],
    connectedArea: {
      style: (oldStyle, element) => {
        return { fill: "rgba(0,0,0,0.25)", stroke: oldStyle.fill, lineWidth: 0.5 };
      },
    },
  });
  writeTopology.render();
}
function initWaitClass() {
  data.oracle.waitClass.forEach((element) => {
    element.time = convertUTCtoLocalTime(element.time);
  });
  waitClassification = new Area("waitClassification", {
    data: data.oracle.waitClass,
    xField: "time",
    yField: "count",
    seriesField: "name",
    color: ["#6897a7", "#8bc0d6", "#60d7a7", "#dedede", "#fedca9", "#fab36f", "#d96d6f"],
    yAxis: {
      // label: {
      //   formatter: (v) => `${(v / 1000).toFixed(0)}K`, // 除以 1000 并保留一位小数
      // },
    },
    legend: {
      position: "top",
    },
  });
  waitClassification.render();
}
function initDb() {
  data.oracle.dbLoad.forEach((element) => {
    element.time = convertUTCtoLocalTime(element.time);
  });
  dbLoad = new Area("dbLoad", {
    data: data.oracle.dbLoad,
    xField: "time",
    yField: "count",
    seriesField: "name",
    color: ["#6897a7", "#8bc0d6", "#60d7a7", "#dedede", "#fedca9", "#fab36f", "#d96d6f"],
    //   yAxis: {
    //   label: {
    //     formatter: (v) => `${(v / 1000).toFixed(0)}K`, // 除以 1000 并保留一位小数
    //   },
    // },
    legend: {
      position: "top",
    },
  });
  dbLoad.render();
}
function initLock() {
  data.oracle.lockList.forEach((element) => {
    element.time = convertUTCtoLocalTime(element.time);
  });
  lockWatch = new Area("lockWatch", {
    data: data.oracle.lockList,
    xField: "time",
    yField: "count",
    seriesField: "name",
    color: ["#6897a7", "#8bc0d6", "#60d7a7", "#dedede", "#fedca9", "#fab36f", "#d96d6f"],
    //   yAxis: {
    //   label: {
    //     formatter: (v) => `${(v / 1000).toFixed(0)}K`, // 除以 1000 并保留一位小数
    //   },
    // },
    legend: {
      position: "top",
    },
  });
  lockWatch.render();
}
function initPgSize() {
  pgSize = new Bar("pgSize", {
    data: data.pg.sizeList,
    xField: "size",
    yField: "name",
    seriesField: "name",
    legend: {
      position: "top-left",
    },
    xAxis: {
      label: {
        formatter: (v) => `${v}MB`,
      },
    },
  });
  pgSize.render();
}
function initPgConnect() {
  data.pg.connectList.forEach((element) => {
    element.time = convertTimestampToFullDateTime(element.time);
  });
  pgConnect = new Line("pgConnect", {
    data: data.pg.connectList,
    xField: "time",
    yField: "count",
    seriesField: "name",
    yAxis: {},
  });
  pgConnect.render();
}
function initPgLock() {
  data.pg.lockList.forEach((element) => {
    element.time = convertTimestampToFullDateTime(element.time);
  });
  pgLock = new Area("pgLock", {
    data: data.pg.lockList,
    xField: "time",
    yField: "count",
    seriesField: "name",
    color: ["#6897a7", "#8bc0d6", "#60d7a7", "#dedede", "#fedca9", "#fab36f", "#d96d6f"],
    yAxis: {},
    legend: {
      position: "top",
    },
  });
  pgLock.render();
}
function initPgIo() {
  data.pg.databaseIo.forEach((element) => {
    element.time = convertTimestampToFullDateTime(element.time);
  });
  pgIo = new Line("pgIo", {
    data: data.pg.databaseIo,
    xField: "time",
    yField: "count",
    seriesField: "name",
    yAxis: {
      // label: {
      //   formatter: (v) => `${(v / 1000).toFixed(0)}K`, // 除以 1000 并保留一位小数
      // },
    },
  });
  pgIo.render();
}
function initCheckPoint() {
  data.pg.checkPoint.forEach((element) => {
    element.time = convertTimestampToFullDateTime(element.time);
  });
  checkPoint = new Column("checkPoint", {
    data: data.pg.checkPoint,
    isStack: true,
    xField: "time",
    yField: "count",
    seriesField: "name",
    autoFit: true,
    legend: {
      position: "top",
    },

    interactions: [{ type: "active-region", enable: false }],
    connectedArea: {
      style: (oldStyle, element) => {
        return { fill: "rgba(0,0,0,0.25)", stroke: oldStyle.fill, lineWidth: 0.5 };
      },
    },
  });
  checkPoint.render();
}
function initBgWriter() {
  data.pg.bgwriter.forEach((element) => {
    element.time = convertTimestampToFullDateTime(element.time);
  });
  bgWriter = new Line("bgWriter", {
    data: data.pg.bgwriter,
    xField: "time",
    yField: "count",
    seriesField: "name",
    yAxis: {
      // label: {
      //   formatter: (v) => `${(v / 1000).toFixed(0)}K`, // 除以 1000 并保留一位小数
      // },
    },
  });
  bgWriter.render();
}
</script>
<template>
  <div v-show="Object.keys(item).length !== 0">
    <div v-show="item.type === 'oracle'">
      <el-row :gutter="5" class="row">
        <el-col :span="6">
          <el-row :gutter="5">
            <el-col :span="12">
              <el-card>
                <template #header>会话数</template>
                <div class="card-content">{{ data.oracle.conversation }}</div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <template #header>活动会话</template>
                <div class="card-content">{{ data.oracle.activeConversation }}</div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="5" class="row">
            <el-col :span="12">
              <el-card>
                <template #header>PGA使用</template>
                <div class="card-content">{{ data.oracle.pga }}</div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <template #header>SGA空闲</template>
                <div class="card-content">{{ data.oracle.sga }}</div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="9">
          <el-card>
            <template #header>顶级会话连接</template>
            <div id="sessionTopology" style="width: 100%; height: 200px; padding: 10px"></div>
          </el-card>
        </el-col>
        <el-col :span="9">
          <el-card>
            <template #header>等待事件</template>
            <div id="waitTopology" style="width: 100%; height: 200px; padding: 10px"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="row" :gutter="5">
        <el-col :span="4">
          <el-card>
            <template #header>Top扫描</template>
            <el-table :data="data.oracle.topScan" style="width: 100%" height="250">
              <el-table-column fixed prop="name" label="表名" />
              <el-table-column prop="cost" label="平均cost" />
              <el-table-column prop="search" label="查询次数" />
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="10">
          <el-card>
            <template #header>Top数据对象逻辑读</template>
            <div id="readTopology" class="chart"></div>
          </el-card>
        </el-col>
        <el-col :span="10">
          <el-card>
            <template #header>Top数据对象物理读</template>
            <div id="writeTopology" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="row" :gutter="5">
        <el-col :span="8">
          <el-card>
            <template #header>等待分类</template>
            <div id="waitClassification" class="chart"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <template #header>DB负载</template>
            <div id="dbLoad" class="chart"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <template #header>锁表列表</template>
            <div id="lockWatch" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="row" :gutter="5">
        <el-col :span="24">
          <el-card>
            <template #header>TopSql</template>
            <el-table :data="data.oracle.topSql" style="width: 100%" height="400">
              <el-table-column fixed prop="name" label="运行程序" />
              <el-table-column prop="sql" label="sql id" />
              <el-table-column prop="count" label="计数" />
              <el-table-column prop="cost" label="总执行次数" />
              <el-table-column prop="time" label="总消耗时间" />
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div v-show="item.type === 'pg'">
      <el-row :gutter="5" class="row">
        <el-col :span="4">
          <el-row class="row">
            <el-col :span="24">
              <el-card>
                <template #header>启动时长</template>
                <div class="pg-info">{{ data.pg.time }}</div>
              </el-card>
            </el-col>
          </el-row>
          <el-row class="row">
            <el-col :span="24">
              <el-card>
                <template #header>缓冲命中率%</template>
                <div class="pg-info">{{ data.pg.buffer }}</div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="20">
          <el-row class="row" :gutter="5">
            <el-col :span="4">
              <el-card>
                <template #header>数据库状态</template>
                <div class="pg-content">{{ data.pg.status }}</div>
              </el-card>
            </el-col>
            <el-col :span="4">
              <el-card>
                <template #header>提取行数</template>
                <div class="pg-content">{{ data.pg.extract }}</div>
              </el-card>
            </el-col>
            <el-col :span="4">
              <el-card>
                <template #header>返回行数</template>
                <div class="pg-content">{{ data.pg.goback }}</div>
              </el-card>
            </el-col>
            <el-col :span="4">
              <el-card>
                <template #header>插入行数</template>
                <div class="pg-content">{{ data.pg.insert }}</div>
              </el-card>
            </el-col>
            <el-col :span="4">
              <el-card>
                <template #header>更新行数</template>
                <div class="pg-content">{{ data.pg.update }}</div>
              </el-card>
            </el-col>
            <el-col :span="4">
              <el-card>
                <template #header>删除行数</template>
                <div class="pg-content">{{ data.pg.delete }}</div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row class="row">
        <el-col :span="24">
          <el-card>
            <template #header>数据库大小</template>
            <div id="pgSize" style="width: 100%; height: 300px; padding: 10px"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="row">
        <el-col :span="24">
          <el-card>
            <template #header>数据库连接详情</template>
            <div id="pgConnect" style="width: 100%; height: 300px; padding: 10px"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="5" class="row">
        <el-col :span="12">
          <el-card>
            <template #header>锁统计</template>
            <div id="pgLock" style="width: 100%; height: 300px; padding: 10px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>数据库IO</template>
            <div id="pgIo" style="width: 100%; height: 300px; padding: 10px"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="5" class="row">
        <el-col :span="12">
          <el-card>
            <template #header>CheckPoint进程</template>
            <div id="checkPoint" style="width: 100%; height: 300px; padding: 10px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>BgWriter进程详情</template>
            <div id="bgWriter" style="width: 100%; height: 300px; padding: 10px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
  <div v-show="Object.keys(item).length === 0" class="empty">请选择一个数据库进行点击查看！</div>
</template>
<style scoped>
.empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  height: 400px;
  color: var(--el-text-color-placeholder);
}

:deep(.el-card__body) {
  padding: 0px;
}

:deep(.el-card__header) {
  padding: 10px;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  justify-content: center;
}

.chart {
  width: 100%;
  height: 250px;
  padding: 10px
}

.card-content {
  font-size: 26px;
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.row {
  padding-top: 5px;
}

.pg-info {
  font-size: 26px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pg-content {
  font-size: 50px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
