import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取服务器top10
 * @param {*} data
 */
export function getServerByType(data) {
  return api.get("/asset_analysis/get_server_info_top/" + data + '/');
}

/**
 * 获取数据库top10
 * @param {*} data
 */
export function getDatabaseByType(data) {
  return api.get("/asset_analysis/get_database_info_top/" + data + '/');
}

/**
 * 获取中间件top10
 * @param {*} data  
 * @returns
 */
export function getMiddlewareByType(data) {
  return api.get("/asset_analysis/get_middleware_info_top/" + data + '/');
}
/**
 * 获取服务器系统
 * @param {*} data  
 * @returns
 */
export function getSystemPercentage() {
  return api.get("/asset_analysis/get_server_operation_system_percentage/");
}
/**
 * 获取物理资源使用率
 * @param {*} data  
 * @returns
 */
export function getResourcePercentage() {
  return api.get("/asset_analysis/get_disk_cpu_memory_data/");
}
/**
 * 获取服务器明细
 * @param {*} data  
 * @returns
 */
export function getServerDetail() {
  return api.get("/asset_analysis/get_server_hardware_information/");
}
/**
 * 获取数据库明细
 * @param {*} data  
 * @returns
 */
export function getDatabaseDetail() {
  return api.get("/asset_analysis/get_database_hardware_information/");
}
/**
 * 获取中间件明细
 * @param {*} data  
 * @returns
 */
export function getMiddlewareDetail() {
  return api.get("/asset_analysis/get_midllware_hardware_information/");
}
/**
 * 获取网络设备明细
 * @param {*} data  
 * @returns
 */
export function getNetworkDetail() {
  return api.get("/asset_analysis/get_network_hardware_information/");
}
/**
 * 获取一周内预警次数
 * @param {*} data  
 * @returns
 */
export function getProblemDetail() {
  return api.get("/asset_analysis/weekly_problem_growth_trend/");
}
/**
 * 导出excel
 * @param {*} data  
 * @returns
 */
export function exportExcel(data) {
  return api.get(`/asset_analysis/export_asset_information_with_excel/?type=${data}`);
}