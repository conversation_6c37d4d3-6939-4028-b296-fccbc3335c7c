<template>
  <!-- 对象 -->
  <template v-if="isObject">
    <!-- 有一种是特殊的  extensions 字段-->
    <template v-if="columnname == 'extensions'">
      <el-button type="primary" plain @click="extensionsDialogView(columndata)"> 查看 </el-button>
    </template>
    <template v-else>
      <Tag v-for="item in columndata" :fieldname="columnname" :value="item"></Tag>
    </template>
  </template>
  <template v-else> <Tag :fieldname="columnname" :value="columndata"></Tag></template>
  <el-dialog v-model="store.extensionsDialog" append-to-body>
    <el-table :data="store.extensionsDialogData">
      <el-table-column label="名称" prop="extension_name" align="center" />
      <el-table-column label="描述" prop="description" align="center" />
      <el-table-column label="版本" align="center">
        <template #default="scoped">
          <el-tag> {{ scoped.row.extversion.major }}.{{ scoped.row.extversion.minor }} </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="所属命名空间" prop="nspname" align="center" />
    </el-table>
  </el-dialog>
</template>

<script setup lang="ts">
import Tag from "./tag.vue";
const store = reactive({
  extensionsDialog: false,
  extensionsDialogData: [],
});

const props = defineProps<{
  columndata?: any;
  columnname?: string;
}>();

const isObject = computed(() => {
  return typeof props.columndata == "object";
});

/**
 * 扩展视图
 */
function extensionsDialogView(item) {
  store.extensionsDialog = true;
  store.extensionsDialogData = item;
}
</script>

<style scoped></style>
