import Mock from 'mockjs'

export default [
  {
    url: '/mock/server/topo',
    method: 'post',
    response: {
      error: '',
      status: 200,
      data: {
       
      }
    }
  },
  {
    // 编辑业务拓扑节点的接口
    url: '/home_page/edit_business_topology',
    method: 'post',
    response: {
      error: '',
      status: 200,
      message: '业务编辑成功',
      data: {}
    }
  },
  {
    // 删除业务拓扑节点的接口
    url: '/home_page/delete_business_topology',
    method: 'post',
    response: {
      error: '',
      status: 200,
      message: '业务删除成功',
      data: {}
    }
  }
]