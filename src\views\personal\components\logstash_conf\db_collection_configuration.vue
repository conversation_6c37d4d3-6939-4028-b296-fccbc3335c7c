<script setup name="ExampleList">
import eventBus from "@/utils/eventBus";
import { Plus, Search, Connection } from "@element-plus/icons-vue";
import Storage from "@/utils/storage";
import { getAllAnsibleHostIP } from "@/api/modules/configuration/oracleDb4bix";
import {
  GetLogstashBindIp,
  GetLogstashConfig,
  BindLogstashIp,
  DealLogstashConfig,
} from "@/api/modules/configuration/oracleLogstash";
import { DbType } from "@/constants/cmdb";
import { deepClone } from "@/utils";

const router = useRouter();

const data = ref({
  loading: false,
  formMode: "router",
  // 搜索
  search: {
    dbip: "",
  },
  // 列表数据
  allList: [],
  pg_config_list: [],
  oracle_config_list: [],
  ConnstrToPassword: [],
  ip_binding: "",
  host_name: [],
  activeName: DbType.Oracle,
});

onMounted(() => {
  getDataList();
  getAllAnsibleHostIP().then((res) => {
    res.data.forEach((item) => {
      data.value.host_name.push(item);
    });
  });
});

onBeforeUnmount(() => {
  if (data.value.formMode === "router") {
    eventBus.off("get-data-list");
  }
});

// 获取列表
function getDataList() {
  data.value.loading = true;
  GetLogstashBindIp().then((res) => {
    if (res.data != "") {
      data.value.ip_binding = res.data;
    }
  });
  GetLogstashConfig()
    .then((res) => {
      res.data.oracle_config_list.map((item, index) => {
        item.id = index;
      });
      res.data.pg_config_list.map((item, index) => {
        item.id = index;
      });
      data.value.allList = res.data;
      data.value.oracle_config_list = res.data.oracle_config_list;
      data.value.pg_config_list = res.data.pg_config_list;
    })
    .finally(() => {
      data.value.loading = false;
    });
}

// 新增
function onCreate() {
  if (data.value.ip_binding == "") {
    ElMessage.warning({
      message: "请先绑定服务器ip",
      center: true,
    });
    return;
  }
  Storage.local.set(
    "cookieData",
    JSON.stringify({
      dataList: data.value.allList,
      isEdit: false,
      dbtype: data.value.activeName,
    })
  );
  router.push({
    name: "logstashCreate",
  });
}

// 编辑
function onEdit(id) {
  Storage.local.set(
    "cookieData",
    JSON.stringify({
      dataList: data.value.allList,
      id: id,
      isEdit: true,
      dbtype: data.value.activeName,
    })
  );
  router.push({
    name: "logstashEdit",
  });
}

// 删除
function onDel(id) {
  let params = deepClone(data.value.allList);
  if (data.value.activeName == DbType.Oracle) {
    params.oracle_config_list.splice(
      params.oracle_config_list.findIndex((item) => item.id === id),
      1
    );
  } else {
    params.pg_config_list.splice(
      params.pg_config_list.findIndex((item) => item.id === id),
      1
    );
  }
  data.value.loading = true;
  DealLogstashConfig(params)
    .then(() => {
      getDataList();
    })
    .finally(() => {
      data.value.loading = false;
    });
}

// 绑定
function bind_logstash_server() {
  data.value.loading = true;
  BindLogstashIp(data.value.ip_binding)
    .then((res) => {
      getDataList();
    })
    .catch(() => {
      data.value.oracle_config_list = [];
      data.value.pg_config_list = [];
    })
    .finally(() => {
      data.value.loading = false;
    });
}
// 查询
function queryData() {
  if (data.value.search.dbip === "" || data.value.search.dbip == null) {
    getDataList();
    return;
  }
  // 搜索
  let oracle_config_list = [];
  data.value.oracle_config_list.filter((item) => {
    if (item?.dbip.indexOf(data.value.search.dbip) !== -1) {
      oracle_config_list.push(item);
    }
  });
  let pg_config_list = [];
  data.value.pg_config_list.filter((item) => {
    if (item?.dbip.indexOf(data.value.search.dbip) !== -1) {
      pg_config_list.push(item);
    }
  });
  data.value.oracle_config_list = oracle_config_list;
  data.value.pg_config_list = pg_config_list;
}
</script>

<template>
  <div>
    <div class="mb-20px">
      <div class="flex">
        <div class="flex mr-20px">
          <div class="w-220px mr-18px">
            <el-select v-model="data.ip_binding" filterable placeholder="请选择绑定ip">
              <el-option label="本机" value="localhost" />
              <el-option v-for="item in data.host_name" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <el-button type="primary" @click="bind_logstash_server()" :icon="Connection">切换绑定ip</el-button>
        </div>
        <el-button v-auth="['admin']" type="primary" @click="onCreate" :icon="Plus">新增采集信息</el-button>
      </div>
      <div class="w-322px mt-18px flex">
        <el-input
          v-model="data.search.dbip"
          placeholder="请输入ip进行查询"
          clearable
          @keydown.enter="queryData()"
          class="mr-20px"
        />
        <el-button type="primary" @click="queryData()" :icon="Search">筛选</el-button>
      </div>
    </div>

    <el-tabs v-model="data.activeName" type="border-card" class="demo-tabs">
      <el-tab-pane label="Oracle" :name="DbType.Oracle">
        <el-table
          v-loading="data.loading"
          class="list-table"
          :data="data.oracle_config_list"
          border
          stripe
          highlight-current-row
        >
          <el-table-column prop="dbip" label="ip地址" />
          <el-table-column prop="port" label="连接端口" />
          <el-table-column prop="user" label="数据库连接用户名" />
          <el-table-column prop="dbname" label="数据库名称" />
          <el-table-column prop="instance" label="数据库实例名" />
          <el-table-column label="操作" width="250" align="center" fixed="right">
            <template #default="scope">
              <el-button v-auth="['admin']" type="primary" size="small" plain @click="onEdit(scope.row.id)">
                编 辑
              </el-button>
              <el-button v-auth="['admin']" type="danger" size="small" plain @click="onDel(scope.row.id)">
                删 除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="Pg" :name="DbType.Pg">
        <el-table
          v-loading="data.loading"
          class="list-table"
          :data="data.pg_config_list"
          border
          stripe
          highlight-current-row
        >
          <el-table-column prop="dbip" label="ip地址" />
          <el-table-column prop="port" label="连接端口" />
          <el-table-column prop="user" label="数据库连接用户名" />
          <el-table-column prop="dbname" label="数据库名称" />
          <el-table-column prop="instance" label="数据库实例名" />
          <el-table-column label="操作" width="250" align="center" fixed="right">
            <template #default="scope">
              <el-button v-auth="['admin']" type="primary" size="small" plain @click="onEdit(scope.$index)">
                编 辑
              </el-button>
              <el-button v-auth="['admin']" type="danger" size="small" plain @click="onDel(scope.$index)">
                删 除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
}
</style>
