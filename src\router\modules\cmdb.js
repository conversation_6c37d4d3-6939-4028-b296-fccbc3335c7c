const Layout = () => import("@/layout/index.vue");
const children = [
  {
    path: "/businessInformation",
    name: "cmdb_business_information",
    component: () => import("@/views/homepage/cmdb_business/informationBusiness.vue"),
    meta: {
      title: "业务类型",
      auth: ['admin', "cmdb_business_information.browse"],
    },
  },
  {
    path: "serverInformation",
    name: "cmdb_server_information",
    component: () => import("@/views/homepage/cmdb_server/informationServer.vue"),
    meta: {
      title: "服务器",
      auth: ['admin', "cmdb_server_information.browse"],
    },
    children: [
      {
        path: "ssh",
        name: "ssh",
        component: () => import("@/views/homepage/cmdb_server/ssh/index.vue"),
        meta: {
          sidebar: false,
          title: "ssh连接",
          activeMenu: "/cmdb/serverInformation",
        },
      },
      {
        path: "createServer",
        name: "cmdb_createServer",
        component: () => import("@/views/homepage/cmdb_server/createServer.vue"),
        meta: {
          sidebar: false,
          title: "新增服务器",
          activeMenu: "/cmdb/serverInformation",
        },
      },
    ],
  },
  {
    path: "databaseInformation",
    name: "cmdb_database_information",
    component: () => import("@/views/homepage/cmdb_database/informationDatabase.vue"),
    meta: {
      title: "数据库",
      auth: ['admin', "cmdb_database_information.browse"],
    },
    children: [
      {
        path: "createDatabase",
        name: "cmdb_createDatabase",
        component: () => import("@/views/homepage/cmdb_database/createDatabase.vue"),
        meta: {
          sidebar: false,
          title: "新增数据库",
          activeMenu: "/cmdb/databaseInformation",
        },
      },
    ],
  },
  {
    path: "middlewareInformation",
    name: "cmdb_middleware_information",
    component: () => import("@/views/homepage/cmdb_middleware/informationMiddleware.vue"),
    meta: {
      title: "中间件",
      auth: ['admin', "cmdb_middleware_information.browse"],
    },
    children: [
      {
        path: "configfileInformation",
        name: "cmdb_check_config_file",
        component: () => import("@/views/homepage/cmdb_middleware/configfile.vue"),
        meta: {
          title: "配置文件",
          sidebar: false,
        },
      },
      {
        path: "createMiddleware",
        name: "cmdb_createMiddleware",
        component: () => import("@/views/homepage/cmdb_middleware/createMiddleware.vue"),
        meta: {
          sidebar: false,
          title: "新增中间件",
          activeMenu: "/cmdb/middlewareInformation",
        },
      },
    ],
  },

  {
    path: "networkInformation",
    name: "cmdb_network_information",
    component: () => import("@/views/homepage/cmdb_network_equipment/informationNetwork.vue"),
    meta: {
      title: "网络设备",
      auth: ['admin', "cmdb_network_information.browse"],
    },
    children: [
      {
        path: "create_network_equipment",
        name: "cmdb_network_equipment",
        component: () => import("@/views/homepage/cmdb_network_equipment/createNetworkEquip.vue"),
        meta: {
          sidebar: false,
          title: "新增网络设备",
          activeMenu: "/cmdb/networkInformation",
        },
      },
    ],
  },

  {
    path: "businessSoftInformation",
    name: "cmdb_business_soft_information",
    component: () => import("@/views/model_configuration/cmdb_business_soft/informationBusinessSoft.vue"),
    meta: {
      title: "应用服务",
      auth: ['admin', "cmdb_business_soft_information.browse"],
    },
    children: [
      {
        path: "detailBusiness",
        name: "cmdb_detailBusiness",
        component: () => import("@/views/homepage/cmdb_business/informationBusiness.vue"),
        meta: {
          sidebar: false,
          title: "业务详情",
          activeMenu: "/cmdb/businessSoftInformation",
        },
      },
      {
        path: "create_business_soft",
        name: "cmdb_business_soft",
        component: () => import("@/views/model_configuration/cmdb_business_soft/createBusinessSoft.vue"),
        meta: {
          title: "新增应用服务",
          sidebar: false,
          activeMenu: "/cmdb/businessSoftInformation",
        },
      },

    ],
  },

  {
    path: "clusterInformation",
    name: "cmdb_cluster_information",
    component: () => import("@/views/homepage/cmdb_cluster/informationCluster.vue"),
    meta: {
      title: "集群",
      auth: ['admin', "cmdb_cluster_information.browse"],
    },
    children: [
      {
        path: "create_cluster",
        name: "cmdb_create_cluster",
        component: () => import("@/views/homepage/cmdb_cluster/createCluster.vue"),
        meta: {
          sidebar: false,
          title: "新增集群",
          activeMenu: "/cmdb/clusterInformation",
        },
      },
      {
        path: "asset_cluster",
        name: "cmdb_asset_cluster",
        component: () => import("@/views/homepage/cmdb_cluster/assetCluster.vue"),
        meta: {
          sidebar: false,
          title: "集群详情",
          activeMenu: "/cmdb/clusterInformation",
        },
      },
    ],
  },
  // {
  //       path: "storage_device",
  //       name: "cmdb_asset_storage",
  //       component: () => import("@/views/homepage/cmdb_storage/index.vue"),
  //       meta: {
  //         title: "存储设备",
  //       },
  //     },
  {
    path: "interfaceInformation",
    name: "cmdb_interface_information",
    component: () => import("@/views/homepage/cmdb_service_interface/informationInterface.vue"),
    meta: {
      title: "服务接口",
      auth: ['admin', "cmdb_interface_information.browse"],
    },
    children: [
      {
        path: "createInterface",
        name: "cmdb_create_interface",
        component: () => import("@/views/homepage/cmdb_service_interface/createInterface.vue"),
        meta: {
          sidebar: false,
          title: "新增服务接口",
          activeMenu: "/cmdb/interfaceInformation",
        },
      },
    ],
  },
  {
    path: "asset",
    name: "cmdb_asset",
    component: () => import("@/views/homepage/cmdb_asset/asset.vue"),
    meta: {
      sidebar: false,
      title: "资产详情",
      auth: ['admin', "cmdb.browse"],
    },
  },

];

export default {
  path: "/cmdb",
  component: Layout,
  redirect: "/dashboard",
  name: "cmdb",
  meta: {
    auth: ['admin', "cmdb.browse"],
    title: "资源管理",
    icon: "server_information",
  },
  children,
};
