<script setup name="BusinessMonitorWarning">
import { onMounted, reactive, ref, watch, nextTick, onUnmounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { BusinessLog } from "@/api/modules/elasticsearch_api_management/elastic";
import Topology from "./components/topology/newBusinessTopology.vue";
import storage from "@/utils/storage";
import { usePagination } from "@/utils/composables";
import useDateTimeStore from "@/store/modules/datetime";
import { formatLocalTime, getUTCTimestampWithOffset } from "@/utils/dayjs";
import { cmdbGetBusinessMessage } from "@/api/modules/cmdb/business";
import { cmdbGetAllServerList } from "@/api/modules/cmdb/server";
import Overview from "./components/serverDetail/overview.vue";
import AssetDetail from "../homepage/cmdb_asset/asset_detail.vue";

const timeStore = useDateTimeStore();
const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();
const options = [
  {
    value: "灾难",
    label: "灾难",
  },
  {
    value: "严重",
    label: "严重",
  },
  {
    value: "一般严重",
    label: "一般严重",
  },
  {
    value: "警告",
    label: "警告",
  },
];
const router = useRouter();
const serverDrawer = ref(false);
const route = useRoute();

// 使用ref而不是reactive，以便更好地处理tab状态
const activeTab = ref(localStorage.getItem('business-monitor-active-tab') || "业务拓扑");

const data = reactive({
  routeParam: { id: "", item: "" },
  param: { hostip: "", hostid: "", hosttype: "", zabbixId: "" },
  database: {
    name: "",
    list: [],
    select: {},
  },
  middleware: {
    name: "",
    list: [],
    select: {},
  },
  selectBasic: "",
  basicInformation: [],
  logLoding: false,
  serverItem: {},
  time: [],
  title: "",
  level: ["灾难", "严重", "一般严重", "警告"],
  dataList: [],
  warning_list: [],
  allListList: [],
  log_list: [],
  allLogList: [],
});

// 添加计算属性serverName，用于显示当前选中服务器的名称
const serverName = computed(() => {
  // 首先尝试从基本信息列表中找到匹配的服务器
  if (data.selectBasic && data.basicInformation && data.basicInformation.length > 0) {
    const selectedServer = data.basicInformation.find(item => item.id === data.selectBasic);
    if (selectedServer) {
      return selectedServer.name;
    }
  }
  
  // 如果在列表中找不到，尝试从localStorage中获取
  const savedName = localStorage.getItem('current-server-name');
  if (savedName) {
    return savedName;
  }
  
  // 如果都找不到，返回默认文本
  return data.selectBasic ? `服务器(ID:${data.selectBasic})` : "请选择服务器";
});

// 监听activeTab变化，保存到localStorage
watch(activeTab, (newVal) => {
  localStorage.setItem('business-monitor-active-tab', newVal);
});

// 监听选择状态变化，保存到localStorage
watch(() => data.selectBasic, (newVal) => {
  if (newVal) {
    localStorage.setItem('business-monitor-server-select', newVal);
  }
});

// 确保在组件挂载时从localStorage获取正确的tab值和选择状态
onMounted(() => {
  // 首先恢复选项卡状态
  const savedTab = localStorage.getItem('business-monitor-active-tab');
  if (savedTab) {
    activeTab.value = savedTab;
  }
  
  // 然后初始化其他数据
  data.routeParam = route.query;
  data.time = timeStore.getUtcUnixTimestamp();
  data.title = route.query.item;
  
  // 获取基本信息
  getBasicInformation();
  
  // 获取其他数据
  getData();
});

// 添加onUnmounted钩子，组件卸载时清除localStorage
onUnmounted(() => {
  // 清理所有相关存储项
  localStorage.removeItem('business-monitor-active-tab');
  localStorage.removeItem('business-monitor-server-select');
  localStorage.removeItem('business-monitor-database-select');
  localStorage.removeItem('business-monitor-middleware-select');
  localStorage.removeItem('current-server-name');
});

watch(timeStore.$state, (newValue) => {
  data.time = [getUTCTimestampWithOffset(newValue.begin), getUTCTimestampWithOffset(newValue.over)];
  getLog();
});

function getBasicInformation() {
  cmdbGetBusinessMessage(data.routeParam.id).then((res) => {
    if (res.data.length !== 0) {
      let serverList = [];
      let databaseList = [];
      let middlewareList = [];
      
      // 处理返回的数据
      res.data.forEach((element) => {
        if (element.type === "服务器") {
          // 注意：使用server_id作为ID，不是id
          serverList.push({ 
            name: element.name, 
            id: element.server_id, // 服务器使用server_id作为唯一标识
            ip: element.ip 
          });
        } else if (element.type === "数据库") {
          databaseList.push({
            name: element.name,
            id: element.relation_id,
            ip: element.ip,
            type: element.install_software,
          });
        } else if (element.type === "中间件") {
          middlewareList.push({
            name: element.name,
            id: element.relation_id,
            ip: element.ip,
            type: element.install_software,
          });
        }
      });
      
      // 记录可用的服务器列表和保存的选择状态
      const savedServerSelect = localStorage.getItem('business-monitor-server-select');
      
      // 先设置列表数据
      data.basicInformation = serverList;
      data.database.list = databaseList;
      data.middleware.list = middlewareList;
      
      // 判断是否有可用的服务器
      if (serverList.length === 0) {
        ElMessage.error("未找到可用的服务器");
        return;
      }
      
      // 选择默认服务器或使用保存的选择
      if (savedServerSelect && serverList.some(item => String(item.id) === savedServerSelect)) {
        // 使用保存的服务器选择
        data.selectBasic = savedServerSelect;
      } else {
        // 默认选择第一个服务器
        data.selectBasic = String(serverList[0].id);
      }
      
      // 调用changeBasic设置服务器参数
      nextTick(() => {
        changeBasic();
      });
    } else {
      ElMessage.error("未匹配到服务器，请先给程序绑定服务器");
    }
  });
}

// 恢复备选服务器选择方案
function fallbackServerSelection(serverList, savedServerSelect) {
  // 在基本信息中检查
  const serverExists = serverList.some(item => String(item.id) === savedServerSelect);
  
  if (savedServerSelect && serverExists) {
    // 如果保存的服务器存在于列表中，使用保存的值
    data.selectBasic = String(savedServerSelect);
  } else if (serverList.length > 0) {
    // 否则使用默认值(第一个服务器)
    data.selectBasic = String(serverList[0].id);
  } else {
    ElMessage.error("无法找到有效的服务器");
    return;
  }
  
  // 设置服务器参数
  nextTick(() => {
    changeBasic();
  });
}

// 恢复getSelectServer函数
function getSelectServer() {
  cmdbGetAllServerList().then((res) => {
    // 首先尝试通过server_id字段匹配
    let item = res.data.find((item) => item.server_id === Number(data.selectBasic));
    
    // 如果找不到，尝试通过id字段匹配
    if (!item) {
      item = res.data.find((item) => String(item.id) === String(data.selectBasic));
    }
    
    // 添加对item是否存在的检查
    if (item) {
      // 确保值是字符串类型
      data.param.hostip = item.ip;
      data.param.hostid = String(item.id);
      data.param.hosttype = item.system_type;
      data.param.zabbixId = String(item.zabbix_id || "");
    } else {
      // 如果找不到匹配的服务器，给出错误提示并记录日志
      ElMessage.warning("未找到匹配的服务器信息，请重新选择");
      
      // 如果data.basicInformation中有服务器，选择第一个
      if (data.basicInformation.length > 0) {
        data.selectBasic = String(data.basicInformation[0].id);
        // 重置选择状态到localStorage
        localStorage.setItem('business-monitor-server-select', data.selectBasic);
        // 递归调用自身重新尝试获取服务器信息
        getSelectServer();
      } else {
        // 重置参数，避免使用错误的值
        data.param.hostip = "";
        data.param.hostid = "";
        data.param.hosttype = "";
        data.param.zabbixId = "";
      }
    }
  });
}

function getData() {
  data.warning_list = JSON.parse(storage.local.get("problem")) || [];
  data.allListList = JSON.parse(storage.local.get("problem")) || [];
}

function dataFilter(dataList, params) {
  let list = dataList;
  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });
  return {
    list: pageList,
    total: list.length,
  };
}

function goBack() {
  router.go(-1);
}

function paging() {
  data.logLoding = true;
  let param = getParams();
  data.log_list = dataFilter(data.allLogList, param).list;
  pagination.value.total = dataFilter(data.allLogList, param).total;
  data.logLoding = false;
}

function getLog() {
  let startTime = data.time[0];
  let overTime = data.time[1];
  let params = { business_name: data.title, start_time: startTime, end_time: overTime };
  BusinessLog(params).then((res) => {
    data.allLogList = res.data.log_list;
    paging();
  });
}

function changLevel() {
  data.warning_list = data.allListList.filter((obj) => data.level.includes(obj.priority));
}

function sizeChange(size) {
  onSizeChange(size).then(() => {
    paging();
  });
}

function currentChange(page) {
  onCurrentChange(page).then(() => {
    paging();
  });
}

function handleTabClick(tab) {
  const newTabName = tab.props.name;
  activeTab.value = newTabName;
  
  // 在选项卡切换后，根据选项卡类型加载相应数据
  nextTick(() => {
    if (newTabName === "业务日志") {
      // 只有切换到业务日志标签页时才加载日志数据
      getLog();
    } else if (newTabName === "基本信息" || newTabName === "服务器") {
      // 如果基本信息尚未加载，则加载
      if (data.basicInformation.length === 0) {
        getBasicInformation();
      }
    }
  });
}

function changeBasic() {
  // 找到对应的服务器对象，以便后续显示名称
  const selectedServer = data.basicInformation.find((item) => String(item.id) === String(data.selectBasic));
  
  if (selectedServer) {
    // 保存选择状态和服务器名称到localStorage
    localStorage.setItem('business-monitor-server-select', String(data.selectBasic));
    localStorage.setItem('current-server-name', selectedServer.name);
  } 
  
  // 获取服务器详细信息
  getSelectServer();
  
  // 如果当前是业务日志标签页，才更新日志
  if (activeTab.value === '业务日志') {
    getLog();
  }
}
</script>

<template>
  <div class="business-monitor-container">
    <page-main>
      <div class="page-header">
        <div class="header-left">
          <h2 class="page-title">{{ data.title }}</h2>
          <div class="page-subtitle">业务监控详情</div>
        </div>
        <el-button class="back-button" type="primary" plain @click="goBack()">
          <el-icon><svg-icon name="ep:arrow-left" /></el-icon>
          返回
        </el-button>
      </div>
      
      <el-card class="main-card" shadow="hover">
        <el-tabs 
          v-model="activeTab" 
          type="border-card" 
          class="custom-tabs" 
          @tab-click="handleTabClick"
        >
          <el-tab-pane label="业务拓扑" name="业务拓扑">
            <div v-if="activeTab === '业务拓扑'" class="tab-content">
              <div class="section-header">
                <div class="section-line"></div>
                <span class="section-title">业务拓扑</span>
              </div>
              <Topology :businessId="route.query.id" :title="data.title"></Topology>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="基本信息" name="基本信息">
            <div class="tab-content">
              <div class="section-header">
                <div class="section-line"></div>
                <span class="section-title">基本信息</span>
                <el-select
                  v-model="data.selectBasic"
                  :placeholder="serverName || '请选择服务器'"
                  @change="changeBasic"
                  class="server-select"
                >
                  <el-option 
                    v-for="item in data.basicInformation" 
                    :key="item.id" 
                    :label="item.name" 
                    :value="String(item.id)"
                  />
                </el-select>
              </div>
              <AssetDetail
                v-if="activeTab === '基本信息'"
                :type="data.param.hosttype"
                :id="data.param.hostid"
                :ip="data.param.hostip"
              ></AssetDetail>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="服务器" name="服务器">
            <div class="tab-content">
              <div class="section-header">
                <div class="section-line"></div>
                <span class="section-title">服务器监控</span>
              </div>
              <Overview v-if="activeTab === '服务器'"></Overview>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="业务日志" name="业务日志">
            <div v-if="activeTab === '业务日志'" class="tab-content">
              <div class="section-header">
                <div class="section-line"></div>
                <span class="section-title">业务日志</span>
              </div>
              
              <el-table 
                v-loading="data.logLoding" 
                :data="data.log_list" 
                border 
                stripe
                highlight-current-row
                class="log-table"
              >
                <el-table-column prop="log_time" label="告警时间" width="180">
                  <template #default="scope">
                    <el-tag size="small" type="info" effect="plain">
                      {{ formatLocalTime(scope.row.log_time) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="app_method" label="方法" width="200" />
                <el-table-column label="IP" width="150" prop="ip">
                  <template #default="scope">
                    <el-tag size="small" type="success" effect="plain">{{ scope.row.ip }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="message" label="日志内容" show-overflow-tooltip />
              </el-table>
              
              <el-pagination
                :current-page="pagination.page"
                :total="pagination.total"
                :page-size="pagination.size"
                :page-sizes="pagination.sizes"
                :layout="pagination.layout"
                :hide-on-single-page="false"
                background
                class="pagination"
                @size-change="sizeChange"
                @current-change="currentChange"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </page-main>

    <ServerDrawer v-model="serverDrawer" :item="data.serverItem"></ServerDrawer>
  </div>
</template>

<style lang="scss" scoped>
.business-monitor-container {
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 10px 16px;
  font-weight: 500;
  transition: all 0.3s;
  
  &:hover {
    transform: translateX(-3px);
  }
  
  .el-icon {
    margin-right: 5px;
  }
}

.main-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.custom-tabs {
  :deep(.el-tabs__header) {
    background-color: #f5f7fa;
    border-radius: 8px 8px 0 0;
    margin: 0;
  }
  
  :deep(.el-tabs__item) {
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
    
    &.is-active {
      color: #409eff;
      font-weight: 600;
    }
    
    &:hover {
      color: #409eff;
    }
  }
  
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    background-color: #e4e7ed;
  }
}

.tab-content {
  padding: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.section-line {
  width: 4px;
  height: 18px;
  background: linear-gradient(to bottom, #409eff, #79bbff);
  border-radius: 2px;
  margin-right: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.server-select {
  margin-left: auto;
  width: 240px;
}

.log-table {
  margin-top: 16px;
  border-radius: 4px;
  
  :deep(th.el-table__cell) {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
    padding: 12px 0;
  }
  
  :deep(.el-table__row) {
    transition: all 0.2s;
    
    &:hover > td {
      background-color: #f5f7fa;
    }
  }
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-tag) {
  border-radius: 4px;
}
</style>
