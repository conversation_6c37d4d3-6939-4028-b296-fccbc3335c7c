<script setup>
import { onMounted } from "vue";
import { Line, Column } from "@antv/g2plot";
import api from "@/plugins/axios";
import useDateTimeStore from "@/store/modules/datetime";
import { getUTCTimestampWithOffset } from "@/utils/dayjs";
import { convertTimestampToFullDateTime } from "@/utils/unixTimestampConversion";
import { convertUTCtoLocalTime } from "@/utils/isoTimestampConversion";
import { getIisAnalysis } from "@/api/modules/business_topo";
const timeStore = useDateTimeStore();
let iisParam = {};
let iisQueue = null;
let iisConnect = null;
let resoponseTime = null;
let iisCpu = null;
const data = reactive({
  time: [],
  iis: {
    queueList: [],
    connectList: [],
    responseList: [],
    cpuList: [],
    responseTable: [],
    errorList: [],
  },
});
let item = computed({
  get: function () {
    return props.item;
  },
});
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    default: {},
  },
});
onMounted(() => {
  data.time = timeStore.getUtcUnixTimestamp();
  if (Object.keys(item.value).length !== 0) {
    if (item.value.type === "iis") {
      destoryIis();
      getIis();
    } else {
      destoryIis();
    }
  }
});
watch(timeStore.$state, (newValue, oldValue) => {
  data.time = [getUTCTimestampWithOffset(newValue.begin), getUTCTimestampWithOffset(newValue.over)];
  if (Object.keys(item.value).length !== 0) {
    if (item.value.type === "iis") {
      destoryIis();
      getIis();
    }
  }
});
watch(
  () => item.value,
  (newValue, oldValue) => {
    if (newValue.type === "iis") {
      destoryIis();
      getIis();
    } else {
      destoryIis();
    }
  }
);
function destoryIis() {
  if (iisQueue) {
    iisQueue.destroy();
    iisQueue = null;
  }
  if (iisConnect) {
    iisConnect.destroy();
    iisConnect = null;
  }
  if (resoponseTime) {
    resoponseTime.destroy();
    resoponseTime = null;
  }
  if (iisCpu) {
    iisCpu.destroy();
    iisCpu = null;
  }
}
function getIis() {
  iisParam = { name: item.value.name, start_time: data.time[0], end_time: data.time[1], ip: item.value.ip };
  getIisAnalysis(iisParam).then((res) => {
    // api.post('business/iis', { data: iisParam }, { baseURL: '/mock/', }).then((res) => {
    data.iis = res.data;
    initIisQueue();
    initResoponseTime();
    initIisCpu();
    initIisConnect();
  });
}
function initIisQueue() {
  data.iis.queueList.forEach((element) => {
    element.time = convertTimestampToFullDateTime(element.time);
  });
  iisQueue = new Line("iisQueue", {
    data: data.iis.queueList,
    padding: "auto",
    xField: "time",
    yField: "value",
    xAxis: {
      // type: 'timeCat',
      tickCount: 5,
    },
  });
  iisQueue.render();
}
function initResoponseTime() {
  data.iis.responseList.forEach((element) => {
    element.time = convertUTCtoLocalTime(element.time);
  });
  resoponseTime = new Column("resoponseTime", {
    data: data.iis.responseList,
    xField: "time",
    yField: "sales",
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    yAxis: {
      label: {
        formatter: (v) => `${(v / 1000).toFixed(1)}ms`, // 除以 1000 并保留一位小数
      },
    },
    meta: {
      type: {
        alias: "类别",
      },
      sales: {
        alias: "时间",
      },
    },
  });

  resoponseTime.render();
}
function initIisCpu() {
  data.iis.cpuList.forEach((element) => {
    element.time = convertTimestampToFullDateTime(element.time);
  });
  iisCpu = new Line("iisCpu", {
    data: data.iis.cpuList,
    padding: "auto",
    xField: "time",
    yField: "value",
    xAxis: {
      // type: 'timeCat',
      tickCount: 5,
    },
  });
  iisCpu.render();
}
function initIisConnect() {
  data.iis.connectList.forEach((element) => {
    element.time = convertTimestampToFullDateTime(element.time);
  });
  iisConnect = new Line("iisConnect", {
    data: data.iis.connectList,
    padding: "auto",
    xField: "time",
    yField: "value",
    xAxis: {
      // type: 'timeCat',
      tickCount: 5,
    },
  });
  iisConnect.render();
}
</script>
<template>
  <div v-show="Object.keys(item).length !== 0">
    <div v-show="item.type === 'iis'">
      <el-row class="row" :gutter="5">
        <el-col :span="12">
          <el-card>
            <template #header>IIS队列</template>
            <div id="iisQueue" class="chart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>IIS当前连接数</template>
            <div id="iisConnect" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="row" :gutter="5">
        <el-col :span="12">
          <el-card>
            <template #header>主机响应时间</template>
            <div id="resoponseTime" class="chart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>IIS CPU时间</template>
            <div id="iisCpu" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="row" :gutter="5">
        <el-col :span="24">
          <el-card>
            <template #header>正常情况下响应时间大于1秒的日志</template>
            <el-table :data="data.iis.responseTable" style="width: 100%" height="400">
              <el-table-column fixed label="时间">
                <template #default="scope">{{ convertUTCtoLocalTime(scope.row.time) }}</template>
              </el-table-column>
              <el-table-column prop="ip" label="IP" />
              <el-table-column prop="name" label="主机名" />
              <el-table-column prop="cost" label="耗时（ms）" />
              <el-table-column prop="interface" label="接口名称" />
              <el-table-column prop="method" label="方法" />
              <el-table-column prop="port" label="接收端口" />
            </el-table>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="row" :gutter="5">
        <el-col :span="24">
          <el-card>
            <template #header>IIS错误日志</template>
            <el-table :data="data.iis.errorList" style="width: 100%" height="400">
              <el-table-column fixed label="时间">
                <template #default="scope">{{ convertUTCtoLocalTime(scope.row.time) }}</template>
              </el-table-column>
              <el-table-column prop="ip" label="接收IP" />
              <el-table-column prop="name" label="主机名" />
              <el-table-column prop="code" label="状态码" />
              <el-table-column prop="sendIp" label="发送IP" />
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div v-show="item.type === 'Nginx'" class="empty">Nginx暂时还未开发！</div>
  </div>
  <div v-show="Object.keys(item).length === 0" class="empty">请选择一个中间件进行点击查看！</div>
</template>
<style scoped>
.empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  height: 400px;
  color: var(--el-text-color-placeholder);
}

:deep(.el-card__body) {
  padding: 0px;
}

.chart {
  width: 100%;
  height: 250px;
  padding: 10px
}

:deep(.el-card__header) {
  padding: 10px;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  justify-content: center;
}

.card-content {
  font-size: 26px;
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.row {
  padding-top: 5px;
}

.pg-info {
  font-size: 26px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pg-content {
  font-size: 50px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
