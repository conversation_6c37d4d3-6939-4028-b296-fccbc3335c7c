import Mock from 'mockjs'

// 生成时间标签
function generateTimeLabels(hoursCount) {
  const result = [];
  const now = new Date();
  for (let i = 0; i < hoursCount; i++) {
    const time = new Date(now.getTime() - (hoursCount - i - 1) * 60 * 60 * 1000);
    const hour = time.getHours().toString().padStart(2, '0');
    result.push(`${hour}:00`);
  }
  return result;
}

// 生成时间序列数组 [{time:'时间',value:数值}]
function generateTimeSeriesArray(count) {
  const timeLabels = generateTimeLabels(count);
  const result = [];

  for (let i = 0; i < count; i++) {
    result.push({
      time: timeLabels[i],
      value: Math.floor(Math.random() * 200) + 20
    });
  }

  return result;
}

// 生成随机数据数组
function generateRandomDataArray(count, min, max) {
  const result = [];
  for (let i = 0; i < count; i++) {
    result.push(Math.floor(Math.random() * (max - min + 1) + min));
  }
  return result;
}

// 源IP数据 - 限制为2个IP
const sourceIpData = {
  // IP列表 - 只保留两个IP
  ips: ['************', '************'],
  // 按IP分类的Nginx应用响应时间 (按时间序列数组形式)
  responseTimeByIp: {
    '************': generateTimeSeriesArray(24),
    '************': generateTimeSeriesArray(24)
  },
  // 按IP分类的nginx应用响应次数
  responseCountByIp: {
    '************': Math.floor(Math.random() * 10000) + 1000,
    '************': Math.floor(Math.random() * 10000) + 1000
  },
  // 按IP和时间的nginx调用响应大小时间序列 (按时间序列数组形式)
  responseSizeTimeSeriesByIp: {
    '************': generateTimeSeriesArray(24),
    '************': generateTimeSeriesArray(24)
  },
  // 按IP分类的nginx调用响应大小分布
  responseSizeByIp: {
    '************': {
      totalSize: Math.floor(Math.random() * 100) + 10,
      distribution: [
        { range: '0-10KB', count: Math.floor(Math.random() * 1000) + 100 },
        { range: '10-50KB', count: Math.floor(Math.random() * 800) + 100 },
        { range: '50-100KB', count: Math.floor(Math.random() * 500) + 100 },
        { range: '100-500KB', count: Math.floor(Math.random() * 300) + 50 },
        { range: '500KB+', count: Math.floor(Math.random() * 100) + 10 }
      ]
    },
    '************': {
      totalSize: Math.floor(Math.random() * 100) + 10,
      distribution: [
        { range: '0-10KB', count: Math.floor(Math.random() * 1000) + 100 },
        { range: '10-50KB', count: Math.floor(Math.random() * 800) + 100 },
        { range: '50-100KB', count: Math.floor(Math.random() * 500) + 100 },
        { range: '100-500KB', count: Math.floor(Math.random() * 300) + 50 },
        { range: '500KB+', count: Math.floor(Math.random() * 100) + 10 }
      ]
    }
  }
};

// 日志数据
const logData = {
  // 访问日志
  access: [
    { time: '2023-06-16 08:46:01', source_ip: '************', request_method: 'GET', request_url: '/api/patient/records/12345', status: '200', response_size: '45KB', response_time: '32ms' },
    { time: '2023-06-16 08:46:02', source_ip: '************', request_method: 'POST', request_url: '/api/patient/vitals/54321', status: '201', response_size: '32KB', response_time: '45ms' },
    { time: '2023-06-16 08:46:15', source_ip: '************', request_method: 'GET', request_url: '/api/medications/list', status: '200', response_size: '78KB', response_time: '38ms' },
    { time: '2023-06-16 08:46:30', source_ip: '************', request_method: 'GET', request_url: '/api/appointments/today', status: '200', response_size: '56KB', response_time: '29ms' },
    { time: '2023-06-16 08:47:10', source_ip: '************', request_method: 'POST', request_url: '/api/billing/submit', status: '200', response_size: '42KB', response_time: '78ms' },
    { time: '2023-06-16 08:47:45', source_ip: '************', request_method: 'GET', request_url: '/api/lab/results/recent', status: '200', response_size: '128KB', response_time: '56ms' },
    { time: '2023-06-16 08:48:12', source_ip: '************', request_method: 'PUT', request_url: '/api/patient/update', status: '400', response_size: '12KB', response_time: '45ms' },
    { time: '2023-06-16 08:48:30', source_ip: '************', request_method: 'DELETE', request_url: '/api/appointments/cancel/12345', status: '200', response_size: '8KB', response_time: '32ms' },
    { time: '2023-06-16 08:49:15', source_ip: '************', request_method: 'GET', request_url: '/api/nonexistent', status: '404', response_size: '6KB', response_time: '25ms' },
    { time: '2023-06-16 08:49:45', source_ip: '************', request_method: 'POST', request_url: '/api/auth/login', status: '401', response_size: '4KB', response_time: '30ms' },
    { time: '2023-06-16 08:50:20', source_ip: '************', request_method: 'GET', request_url: '/api/admin/users', status: '403', response_size: '5KB', response_time: '28ms' },
    { time: '2023-06-16 08:51:05', source_ip: '************', request_method: 'POST', request_url: '/api/billing/process', status: '500', response_size: '2KB', response_time: '120ms' },
    { time: '2023-06-16 08:51:30', source_ip: '************', request_method: 'GET', request_url: '/api/dashboard/summary', status: '200', response_size: '85KB', response_time: '45ms' },
    { time: '2023-06-16 08:52:10', source_ip: '************', request_method: 'GET', request_url: '/api/patient/history/54321', status: '200', response_size: '156KB', response_time: '68ms' },
    { time: '2023-06-16 08:52:45', source_ip: '************', request_method: 'POST', request_url: '/api/prescription/new', status: '201', response_size: '35KB', response_time: '42ms' }
  ],
  // 错误日志
  error: [
    { time: '2023-06-16 08:45:12', level: 'ERROR', system: '预约管理接口', message: '无法连接预约数据服务 (111: Connection refused)', api_endpoint: '/api/appointments', source_ip: '************' },
    { time: '2023-06-16 08:43:22', level: 'ERROR', system: '支付接口', message: '医保结算接口响应超时 (110: Connection timed out)', api_endpoint: '/api/billing', source_ip: '************' },
    { time: '2023-06-16 08:50:35', level: 'WARNING', system: '用户验证', message: '用户认证失败: 密码不正确', api_endpoint: '/api/auth/login', source_ip: '************' },
    { time: '2023-06-16 08:51:02', level: 'ERROR', system: '数据服务', message: '数据库连接失败: 连接池已满', api_endpoint: '/api/patient/records', source_ip: '************' },
    { time: '2023-06-16 08:51:18', level: 'CRITICAL', system: '系统监控', message: '服务器内存使用率超过95%', api_endpoint: '/api/admin', source_ip: '************' },
    { time: '2023-06-16 08:52:30', level: 'ERROR', system: '药品管理', message: '药品库存查询异常: 表锁定', api_endpoint: '/api/medications', source_ip: '************' },
    { time: '2023-06-16 08:53:15', level: 'WARNING', system: '安全监控', message: '检测到多次失败的登录尝试，IP已被临时封锁', api_endpoint: '/api/auth/login', source_ip: '************' },
    { time: '2023-06-16 08:53:48', level: 'ERROR', system: '医嘱系统', message: '医嘱提交失败: 表单验证错误', api_endpoint: '/api/prescription/new', source_ip: '************' },
    { time: '2023-06-16 08:54:22', level: 'ALERT', system: '网络监控', message: '内网DNS解析异常，影响服务发现', api_endpoint: '/api/system', source_ip: '************' },
    { time: '2023-06-16 08:55:10', level: 'ERROR', system: '影像系统', message: 'PACS服务器连接中断，无法获取影像数据', api_endpoint: '/api/lab/images', source_ip: '************' }
  ]
}

export default [
  {
    url: '/mock/nginx/list',
    method: 'get',
    response: {
      data: {
        business_list: ['EMR系统', 'HIS系统', 'RIS系统']
      }
    }
  },
  {
    url: '/mock/backup/detail',
    method: 'get',
    response: option => {
      let info = AllList.filter(item => item.id == option.query.id)
      return {
        error: '',
        status: 1,
        data: info[0]
      }
    }
  },
  {
    url: '/mock/nginx/information',
    method: 'post',
    response: {
      data: {
        proxy_host: '5',
        redirect_host: '3',
        stream_host: '0',
        not_find_host: '0',
        information: [
          { ip: '***********', location: '/etc/nginx/conf.d', name: 'EMR系统', port: '80', ssl: false, log: '/var/log', status: true, version: '1.0' },
          { ip: '***********', location: '/etc/nginx/conf.d', name: 'EMR系统', port: '80', ssl: false, log: '/var/log', status: true, version: '1.0' }
        ]
      }
    }
  },
  {
    url: '/mock/nginx/basic',
    method: 'post',
    response: {
      data: [
        { name: 'worker_processes', value: 'auto' },
        { name: 'events', value: '{ worker_connections 1024; accept_mutex on; }' }
      ]
    }
  },
  {
    url: '/mock/nginx/http',
    method: 'post',
    response: {
      data: [
        { name: 'include', value: 'mime.types' },
        { name: 'default_type', value: 'application/octet-stream' }
      ]
    }
  },
  {
    url: '/mock/nginx/proxy',
    method: 'post',
    response: {
      data: [
        {
          type: 'http/https',
          ip: '***********',
          port: '80',
          domain: 'example.com',
          moda: ['default'],
          ssl: true,
          config: [{
            url: '/',
            type: '动态http',
            target: {
              url: '***************:100',
              type: ['Host参数'],
              param: '$host'
            }
          }, {
            url: '/job',
            type: '动态http',
            target: {
              url: '***************:200',
              type: ['Host参数'],
              param: '$host'
            }
          }],
          status: false,
          descripe: '这是一个示例描述',
        },
        {
          type: 'http/https',
          ip: '***********',
          port: '80',
          domain: 'example2.com',
          moda: ['default'],
          ssl: false,
          config: [{
            url: '/',
            type: '动态http',
            target: {
              url: '***************:100',
              type: ['Host参数'],
              param: '$host'
            }
          }],
          status: true,
          descripe: '这是另一个示例描述',
        },
      ]
    }
  },
  {
    url: '/mock/nginx/load',
    method: 'post',
    response: {
      data: [
        {
          type: 'http/https',
          name: '名称1',
          strategy: '默认',
          config: [
            {
              ip: '***********',
              port: '80',
              height: '21',
              max_fail: '12',
              max_connect: '32',
              wait_time: '12',
              status: 'down',
              param: '',
            },
          ],
          descripe: '这是一个示例描述',
          operation: '编辑/删除'
        },
        {
          type: 'http/https',
          name: '名称2',
          strategy: '默认',
          config: [
            {
              ip: '***********',
              port: '80',
              height: '21',
              max_fail: '12',
              max_connect: '32',
              wait_time: '12',
              status: 'down',
              param: '',
            }
          ],
          descripe: '这是另一个示例描述',
          operation: '编辑/删除'
        },
        // 可以添加更多的数据行
      ]
    }
  },
  {
    url: '/mock/nginx/stream',
    method: 'post',
    response: {
      data: [
        { name: 'log_format basic', value: '$remote_addr [$time_local] $protocol $status $bytes_sent $bytes_received $session_time "$upstream_addr" "$upstream_bytes_sent" "$upstream_bytes_received" "$upstream_connect_time"' },
        { name: 'access_log basic', value: 'C:/nginxWebUI-3.3.9/log/stream_access.log basic' },
        { name: 'open_log_file_cache', value: 'off' }
      ]
    }
  },
  {
    url: '/mock/nginx/log',
    method: 'post',
    response: option => {
      const {
        start_time,         // 开始时间
        end_time,           // 结束时间
        ip,                 // IP地址
        log_type = 'access', // 日志类型: access 或 error
        status,             // HTTP状态码（仅访问日志）
        level,              // 错误级别（仅错误日志）
        api_path,           // API接口路径
        min_response_time,  // 最小响应时间（仅访问日志）
        max_response_time,  // 最大响应时间（仅访问日志）
        query,              // 搜索关键字
        page = 1,           // 当前页码
        page_size = 10      // 每页条数
      } = option.body

      let filteredLogs = logData[log_type] || []

      // 应用时间筛选 - 处理时间戳格式
      if (start_time) {
        // 时间戳可能是数字或字符串，需要统一处理
        const startDate = new Date(typeof start_time === 'number' ? start_time : parseInt(start_time));
        filteredLogs = filteredLogs.filter(log => new Date(log.time) >= startDate);
      }

      if (end_time) {
        const endDate = new Date(typeof end_time === 'number' ? end_time : parseInt(end_time));
        filteredLogs = filteredLogs.filter(log => new Date(log.time) <= endDate);
      }

      // IP筛选
      if (ip) {
        filteredLogs = filteredLogs.filter(log => log.source_ip === ip);
      }

      // 关键字搜索
      if (query) {
        filteredLogs = filteredLogs.filter(log => {
          // 对于错误日志，搜索message
          if (log_type === 'error' && log.message) {
            return log.message.toLowerCase().includes(query.toLowerCase());
          }
          // 对于访问日志，搜索请求URL
          if (log_type === 'access' && log.request_url) {
            return log.request_url.toLowerCase().includes(query.toLowerCase());
          }
          return false;
        });
      }

      // API路径筛选
      if (api_path) {
        filteredLogs = filteredLogs.filter(log =>
          (log.request_url || log.api_endpoint || '').includes(api_path)
        );
      }

      // 访问日志特有的过滤条件
      if (log_type === 'access') {
        if (status) {
          // 支持按状态码组筛选 (如 2xx, 3xx 等)
          if (status.endsWith('xx')) {
            const prefix = status.charAt(0);
            filteredLogs = filteredLogs.filter(log =>
              log.status && log.status.toString().startsWith(prefix)
            );
          } else {
            filteredLogs = filteredLogs.filter(log => log.status === status);
          }
        }
        if (min_response_time) {
          filteredLogs = filteredLogs.filter(log => {
            const responseTime = parseInt(log.response_time);
            return !isNaN(responseTime) && responseTime >= parseInt(min_response_time);
          });
        }
        if (max_response_time) {
          filteredLogs = filteredLogs.filter(log => {
            const responseTime = parseInt(log.response_time);
            return !isNaN(responseTime) && responseTime <= parseInt(max_response_time);
          });
        }
      }

      // 错误日志特有的过滤条件
      if (log_type === 'error' && level) {
        filteredLogs = filteredLogs.filter(log => log.level === level);
      }

      // 计算总数
      const total = filteredLogs.length;

      // 应用分页
      const currentPage = parseInt(page);
      const pageSize = parseInt(page_size);
      const start = (currentPage - 1) * pageSize;
      const end = start + pageSize;
      const logs = filteredLogs.slice(start, end);

      // 返回带分页信息的数据
      return {
        error: '',
        status: 1,
        data: logs,
        total: total,
        page: currentPage,
        page_size: pageSize
      }
    }
  },
  {
    url: '/mock/backup/delete',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        isSuccess: true
      }
    }
  },
  {
    url: '/mock/nginx/analysis',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data:{
  "statusCode": [
    {
      "code": 200,
      "count": 577122,
      "percentage": 0.99548
    },
    {
      "code": 204,
      "count": 1821,
      "percentage": 0.00314
    },
    {
      "code": 500,
      "count": 614,
      "percentage": 0.00106
    },
    {
      "code": 302,
      "count": 121,
      "percentage": 0.00021
    },
    {
      "code": 401,
      "count": 25,
      "percentage": 0.00004
    },
    {
      "code": 400,
      "count": 18,
      "percentage": 0.00003
    },
    {
      "code": 502,
      "count": 13,
      "percentage": 0.00002
    },
    {
      "code": 404,
      "count": 8,
      "percentage": 0.00001
    }
  ],
  "api": [
    {
      "name": "/api/ZlhisInterface/GetUnclearedPatiInfo",
      "count": 236186,
      "avgTime": 0.009145347490434103,
      "error_counts": 0
    },
    {
      "name": "/api/ZlhisInterface/GetOrderFeeInfo",
      "count": 25233,
      "avgTime": 0.03948658509085555,
      "error_counts": 0
    },
    {
      "name": "/api/ZlhisInterface/GetAllBalanceBycondition",
      "count": 18352,
      "avgTime": 0.09619006103024391,
      "error_counts": 0
    },
    {
      "name": "/api/ZlRegister/GetBalanceAndPayByBillOriginId",
      "count": 16847,
      "avgTime": 0.11426960310388531,
      "error_counts": 0
    },
    {
      "name": "/api/ZlStandardIntegratedPlatform/GetEInvoiceInfo",
      "count": 16693,
      "avgTime": 0.023893069071012435,
      "error_counts": 0
    }
  ],
  "ipResponseTime": [
    {
      "range": "0-20ms",
      "value": 24873
    },
    {
      "range": "20-50ms",
      "value": 15634
    },
    {
      "range": "50-100ms",
      "value": 45892
    },
    {
      "range": "100-200ms",
      "value": 28931
    },
    {
      "range": "200-300ms",
      "value": 16234
    },
    {
      "range": "300-500ms",
      "value": 12156
    },
    {
      "range": "500-800ms",
      "value": 8245
    },
    {
      "range": "800-1000ms",
      "value": 4789
    },
    {
      "range": "1000-2000ms",
      "value": 2652
    },
    {
      "range": "2000-5000ms",
      "value": 831
    },
    {
      "range": ">5000ms",
      "value": 164
    }
  ],
  "responseCount": [
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 13767
    },
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 2559
    },
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 1783
    },
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 1491
    },
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "**********",
      "value": 1001
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 1681
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 1604
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 1457
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 1457
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "**********",
      "value": 1392
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 1866
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 1751
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 1736
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 1541
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 1493
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 1989
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 1721
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 1625
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 1551
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 1520
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 1994
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 1316
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 1190
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "**********",
      "value": 1099
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 961
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "**********",
      "value": 886
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 505
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 364
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 346
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 307
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "**********",
      "value": 707
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "************",
      "value": 365
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "************",
      "value": 318
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "************",
      "value": 295
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "************",
      "value": 243
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "************",
      "value": 2863
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "************",
      "value": 2862
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "************",
      "value": 1244
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "************",
      "value": 1069
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "**********",
      "value": 976
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 51005
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 9910
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 9390
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 7865
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 7360
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 53021
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 13209
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 12326
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 9102
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 4109
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 46330
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 4420
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 3833
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 3166
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 1869
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 48331
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 5923
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 5622
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 5588
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 4924
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 23074
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 2991
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 2423
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 1512
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 1378
    }
  ],
  "clientAnalysis": [
    {
      "clientType": "************",
      "count": 235528,
      "os": "Other",
      "browser": "Other",
      "browserVersion": "未知"
    },
    {
      "clientType": "************",
      "count": 45687,
      "os": "Other",
      "browser": "Other",
      "browserVersion": "未知"
    },
    {
      "clientType": "************",
      "count": 42809,
      "os": "Other",
      "browser": "Other",
      "browserVersion": "未知"
    },
    {
      "clientType": "************",
      "count": 32760,
      "os": "Other",
      "browser": "Other",
      "browserVersion": "未知"
    },
    {
      "clientType": "************",
      "count": 30739,
      "os": "Other",
      "browser": "Other",
      "browserVersion": "未知"
    }
  ],
  "responseSize": [
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 0
    },
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 14
    },
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 10
    },
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 13
    },
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "**********",
      "value": 2
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 3
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 7
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 27
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "**********",
      "value": 2
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 7
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 3
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 35
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 5
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 10
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 3
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 35
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 7
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 21
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 2
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 28
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "**********",
      "value": 2
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "**********",
      "value": 2
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 15
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 6
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "**********",
      "value": 2
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "************",
      "value": 26
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "************",
      "value": 7
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "************",
      "value": 33
    },
    {
      "time": "2025-05-14T20:00:00.000Z",
      "ip": "************",
      "value": 6
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "************",
      "value": 51
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "************",
      "value": 55
    },
    {
      "time": "2025-05-14T22:00:00.000Z",
      "ip": "**********",
      "value": 2
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 0
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 6
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 9
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 7
    },
    {
      "time": "2025-05-15T00:00:00.000Z",
      "ip": "************",
      "value": 7
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 0
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 21
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 9
    },
    {
      "time": "2025-05-15T02:00:00.000Z",
      "ip": "************",
      "value": 7
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 0
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 18
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 3
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 22
    },
    {
      "time": "2025-05-15T04:00:00.000Z",
      "ip": "************",
      "value": 9
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 0
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 31
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 8
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 7
    },
    {
      "time": "2025-05-15T06:00:00.000Z",
      "ip": "************",
      "value": 6
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 0
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 13
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 16
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 22
    },
    {
      "time": "2025-05-15T08:00:00.000Z",
      "ip": "************",
      "value": 7
    }
  ],
  "responseTimeByIp": [
    // ************ - 主要服务器，响应时间稳定在40-60ms
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 45.2
    },
    {
      "time": "2025-05-14T09:00:00.000Z",
      "ip": "************",
      "value": 43.8
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 52.3
    },
    {
      "time": "2025-05-14T11:00:00.000Z",
      "ip": "************",
      "value": 48.7
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 38.5
    },
    {
      "time": "2025-05-14T13:00:00.000Z",
      "ip": "************",
      "value": 42.1
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 65.4
    },
    {
      "time": "2025-05-14T15:00:00.000Z",
      "ip": "************",
      "value": 58.9
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 51.2
    },
    {
      "time": "2025-05-14T17:00:00.000Z",
      "ip": "************",
      "value": 47.6
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 44.3
    },
    {
      "time": "2025-05-14T19:00:00.000Z",
      "ip": "************",
      "value": 41.8
    },
    // ************ - 备用服务器，响应时间波动较大
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 82.5
    },
    {
      "time": "2025-05-14T09:00:00.000Z",
      "ip": "************",
      "value": 95.3
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 78.9
    },
    {
      "time": "2025-05-14T11:00:00.000Z",
      "ip": "************",
      "value": 88.4
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 102.7
    },
    {
      "time": "2025-05-14T13:00:00.000Z",
      "ip": "************",
      "value": 92.1
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 85.6
    },
    {
      "time": "2025-05-14T15:00:00.000Z",
      "ip": "************",
      "value": 98.4
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 89.2
    },
    {
      "time": "2025-05-14T17:00:00.000Z",
      "ip": "************",
      "value": 93.7
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 87.5
    },
    {
      "time": "2025-05-14T19:00:00.000Z",
      "ip": "************",
      "value": 91.8
    },
    // ************ - 负载均衡服务器，响应时间中等
    {
      "time": "2025-05-14T08:00:00.000Z",
      "ip": "************",
      "value": 65.4
    },
    {
      "time": "2025-05-14T09:00:00.000Z",
      "ip": "************",
      "value": 68.2
    },
    {
      "time": "2025-05-14T10:00:00.000Z",
      "ip": "************",
      "value": 62.7
    },
    {
      "time": "2025-05-14T11:00:00.000Z",
      "ip": "************",
      "value": 71.5
    },
    {
      "time": "2025-05-14T12:00:00.000Z",
      "ip": "************",
      "value": 69.8
    },
    {
      "time": "2025-05-14T13:00:00.000Z",
      "ip": "************",
      "value": 64.3
    },
    {
      "time": "2025-05-14T14:00:00.000Z",
      "ip": "************",
      "value": 67.9
    },
    {
      "time": "2025-05-14T15:00:00.000Z",
      "ip": "************",
      "value": 72.4
    },
    {
      "time": "2025-05-14T16:00:00.000Z",
      "ip": "************",
      "value": 66.8
    },
    {
      "time": "2025-05-14T17:00:00.000Z",
      "ip": "************",
      "value": 70.2
    },
    {
      "time": "2025-05-14T18:00:00.000Z",
      "ip": "************",
      "value": 63.5
    },
    {
      "time": "2025-05-14T19:00:00.000Z",
      "ip": "************",
      "value": 68.7
    }
  ],
  "avargeResponse": [
    {
      "timeSeries": [
        {
          "time": "2025-05-14T08:00:00.000Z",
          "value": 0.0083
        },
        {
          "time": "2025-05-14T10:00:00.000Z",
          "value": 0.0222
        },
        {
          "time": "2025-05-14T12:00:00.000Z",
          "value": 0.0153
        },
        {
          "time": "2025-05-14T14:00:00.000Z",
          "value": 0.0862
        },
        {
          "time": "2025-05-14T16:00:00.000Z",
          "value": 0.0277
        },
        {
          "time": "2025-05-14T18:00:00.000Z",
          "value": 0.0251
        },
        {
          "time": "2025-05-14T20:00:00.000Z",
          "value": 0.041
        },
        {
          "time": "2025-05-14T22:00:00.000Z",
          "value": 0.0725
        },
        {
          "time": "2025-05-15T00:00:00.000Z",
          "value": 0.0084
        },
        {
          "time": "2025-05-15T02:00:00.000Z",
          "value": 0.0086
        },
        {
          "time": "2025-05-15T04:00:00.000Z",
          "value": 0.008
        },
        {
          "time": "2025-05-15T06:00:00.000Z",
          "value": 0.0122
        },
        {
          "time": "2025-05-15T08:00:00.000Z",
          "value": 0.0084
        }
      ],
      "service": "/api/ZlhisInterface/GetUnclearedPatiInfo",
      "calls": 236186,
      "avgTime": "0"
    },
    {
      "timeSeries": [
        {
          "time": "2025-05-14T08:00:00.000Z",
          "value": 0.0351
        },
        {
          "time": "2025-05-14T10:00:00.000Z",
          "value": 0.0364
        },
        {
          "time": "2025-05-14T12:00:00.000Z",
          "value": 0.0327
        },
        {
          "time": "2025-05-14T14:00:00.000Z",
          "value": 0.0394
        },
        {
          "time": "2025-05-14T16:00:00.000Z",
          "value": 0.0318
        },
        {
          "time": "2025-05-14T18:00:00.000Z",
          "value": 0.0384
        },
        {
          "time": "2025-05-14T20:00:00.000Z",
          "value": 0.0347
        },
        {
          "time": "2025-05-14T22:00:00.000Z",
          "value": 0.0356
        },
        {
          "time": "2025-05-15T00:00:00.000Z",
          "value": 0.0369
        },
        {
          "time": "2025-05-15T02:00:00.000Z",
          "value": 0.0334
        },
        {
          "time": "2025-05-15T04:00:00.000Z",
          "value": 0.0333
        },
        {
          "time": "2025-05-15T06:00:00.000Z",
          "value": 0.0834
        },
        {
          "time": "2025-05-15T08:00:00.000Z",
          "value": 0.0517
        }
      ],
      "service": "/api/ZlhisInterface/GetOrderFeeInfo",
      "calls": 25233,
      "avgTime": "0"
    },
    {
      "timeSeries": [
        {
          "time": "2025-05-14T08:00:00.000Z",
          "value": 0.0898
        },
        {
          "time": "2025-05-14T10:00:00.000Z",
          "value": 0.0887
        },
        {
          "time": "2025-05-14T12:00:00.000Z",
          "value": 0.0873
        },
        {
          "time": "2025-05-14T14:00:00.000Z",
          "value": 0.0837
        },
        {
          "time": "2025-05-14T16:00:00.000Z",
          "value": 0.1466
        },
        {
          "time": "2025-05-14T18:00:00.000Z",
          "value": 0.0805
        },
        {
          "time": "2025-05-14T20:00:00.000Z",
          "value": 0.0823
        },
        {
          "time": "2025-05-14T22:00:00.000Z",
          "value": 0.0758
        },
        {
          "time": "2025-05-15T00:00:00.000Z",
          "value": 0.0876
        },
        {
          "time": "2025-05-15T02:00:00.000Z",
          "value": 0.0945
        },
        {
          "time": "2025-05-15T04:00:00.000Z",
          "value": 0.0882
        },
        {
          "time": "2025-05-15T06:00:00.000Z",
          "value": 0.1328
        },
        {
          "time": "2025-05-15T08:00:00.000Z",
          "value": 0.0868
        }
      ],
      "service": "/api/ZlhisInterface/GetAllBalanceBycondition",
      "calls": 18352,
      "avgTime": "0"
    },
    {
      "timeSeries": [
        {
          "time": "2025-05-14T08:00:00.000Z",
          "value": 0.1103
        },
        {
          "time": "2025-05-14T10:00:00.000Z",
          "value": 0.106
        },
        {
          "time": "2025-05-14T12:00:00.000Z",
          "value": 0.1087
        },
        {
          "time": "2025-05-14T14:00:00.000Z",
          "value": 0.1042
        },
        {
          "time": "2025-05-14T16:00:00.000Z",
          "value": 0.1075
        },
        {
          "time": "2025-05-14T18:00:00.000Z",
          "value": 0.1217
        },
        {
          "time": "2025-05-14T20:00:00.000Z",
          "value": 0.0908
        },
        {
          "time": "2025-05-14T22:00:00.000Z",
          "value": 0.1119
        },
        {
          "time": "2025-05-15T00:00:00.000Z",
          "value": 0.1098
        },
        {
          "time": "2025-05-15T02:00:00.000Z",
          "value": 0.1126
        },
        {
          "time": "2025-05-15T04:00:00.000Z",
          "value": 0.1146
        },
        {
          "time": "2025-05-15T06:00:00.000Z",
          "value": 0.1311
        },
        {
          "time": "2025-05-15T08:00:00.000Z",
          "value": 0.1135
        }
      ],
      "service": "/api/ZlRegister/GetBalanceAndPayByBillOriginId",
      "calls": 16847,
      "avgTime": "0"
    },
    {
      "timeSeries": [
        {
          "time": "2025-05-14T08:00:00.000Z",
          "value": 0.0175
        },
        {
          "time": "2025-05-14T10:00:00.000Z",
          "value": 0.0175
        },
        {
          "time": "2025-05-14T12:00:00.000Z",
          "value": 0.0166
        },
        {
          "time": "2025-05-14T14:00:00.000Z",
          "value": 0.0163
        },
        {
          "time": "2025-05-14T16:00:00.000Z",
          "value": 0.0178
        },
        {
          "time": "2025-05-14T18:00:00.000Z",
          "value": 0.0158
        },
        {
          "time": "2025-05-14T20:00:00.000Z",
          "value": 0.0161
        },
        {
          "time": "2025-05-14T22:00:00.000Z",
          "value": 0.0158
        },
        {
          "time": "2025-05-15T00:00:00.000Z",
          "value": 0.0173
        },
        {
          "time": "2025-05-15T02:00:00.000Z",
          "value": 0.0174
        },
        {
          "time": "2025-05-15T04:00:00.000Z",
          "value": 0.0174
        },
        {
          "time": "2025-05-15T06:00:00.000Z",
          "value": 0.0521
        },
        {
          "time": "2025-05-15T08:00:00.000Z",
          "value": 0.0215
        }
      ],
      "service": "/api/ZlStandardIntegratedPlatform/GetEInvoiceInfo",
      "calls": 16693,
      "avgTime": "0"
    }
  ],
  "businessHealth": [
    {
      "moduleName": "/api/ZlhisInterface/GetUnclearedPatiInfo",
      "avgResponseTime": 0.009,
      "callVolume": 236186,
      "errorRate": 0,
      "healthScore": 100,
      "status": "good"
    },
    {
      "moduleName": "/api/ZlhisInterface/GetOrderFeeInfo",
      "avgResponseTime": 0.039,
      "callVolume": 25233,
      "errorRate": 0,
      "healthScore": 100,
      "status": "good"
    },
    {
      "moduleName": "/api/ZlhisInterface/GetAllBalanceBycondition",
      "avgResponseTime": 0.096,
      "callVolume": 18352,
      "errorRate": 0,
      "healthScore": 100,
      "status": "good"
    },
    {
      "moduleName": "/api/ZlRegister/GetBalanceAndPayByBillOriginId",
      "avgResponseTime": 0.114,
      "callVolume": 16847,
      "errorRate": 0,
      "healthScore": 100,
      "status": "good"
    },
    {
      "moduleName": "/api/ZlStandardIntegratedPlatform/GetEInvoiceInfo",
      "avgResponseTime": 0.024,
      "callVolume": 16693,
      "errorRate": 0,
      "healthScore": 100,
      "status": "good"
    }
  ]
}
    }
  }
]
