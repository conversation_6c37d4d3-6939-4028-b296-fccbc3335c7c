import pinia from "@/store";
import useSettingsStore from "@/store/modules/settings";

// 固定路由（默认路由），
let constantRoutes = [
  {
    path: "/",
    redirect: "/dashboard",
  },
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/login.vue"),
    meta: {
      whiteList: true,
      title: "登录",
      i18n: "route.login",
    },
  },
  {
    path: "/:all(.*)*",
    name: "notFound",
    component: () => import("@/views/[...all].vue"),
    meta: {
      title: "找不到页面",
    },
  },
  {
    path: "/error",
    name: "error",
    component: () => import("@/views/error.vue"),
    meta: {
      title: "系统出现异常",
    },
  },
  {
    path: "/error_product",
    name: "error_product",
    component: () => import("@/views/error_product.vue"),
    meta: {
      title: "系统出现异常",
    },
  },
  {
    path: "/error_time",
    name: "error_time",
    component: () => import("@/views/error_time.vue"),
    meta: {
      title: "系统出现异常",
    },
  },
  {
    path: "/config",
    name: "config",
    component: () => import("@/views/config.vue"),
    meta: {
      title: "配置页面",
    },
  },
  {
    path: "/global_settings",
    name: "global_settings",
    component: () => import("@/views/global_settings/index.vue"),
    meta: {
      title: "配置页面",
    },
  },
  {
    path: "/version",
    name: "version",
    component: () => import("@/views/version.vue"),
    meta: {
      title: "版本日志",
    },
  },
];

// 系统路由
let systemRoutes = [
  {
    path: "/dashboard",
    component: () => import("@/layout/index.vue"),
    meta: {
      title: () => {
        return useSettingsStore().dashboard.title;
      },
      copyright: false,
      breadcrumb: false,
    },
    children: [
      {
        path: "/dashboard",
        name: "dashboard",
        component: () => import("@/views/dashbord/index.vue"),
        meta: {
          title: () => {
            return useSettingsStore().dashboard.title;
          },
          i18n: "route.dashboard",
          breadcrumb: false,
        },
      },
    ],
  },
  {
    path: "/personal",
    component: () => import("@/layout/index.vue"),
    redirect: "/personal/setting",
    meta: {
      title: "个人中心",
      breadcrumb: false,
    },
    children: [
      {
        path: "setting",
        name: "personalSetting",
        component: () => import("@/views/personal/setting.vue"),
        meta: {
          title: "个人设置",
          i18n: "route.personal.setting",
          cache: "personalEditPassword",
        },
      },
      {
        path: "edit/password",
        name: "personalEditPassword",
        component: () => import("@/views/personal/edit.password.vue"),
        meta: {
          title: "修改密码",
          i18n: "route.personal.editpassword",
        },
      },
      {
        path: "notification",
        name: "personalNotification",
        component: () => import("@/views/personal/notification.vue"),
        meta: {
          title: "通知中心",
          i18n: "route.personal.notification",
        },
      },
    ],
  },
  {
    path: "/reload",
    component: () => import("@/layout/index.vue"),
    meta: {
      title: "重新加载",
      breadcrumb: false,
    },
    children: [
      {
        path: "",
        name: "reload",
        component: () => import("@/views/reload.vue"),
        meta: {
          title: "重新加载",
          breadcrumb: false,
        },
      },
    ],
  },
];
// // 示例代码路由
// const exampleCode =
// {
//   path: "/exampleCode",
//   component: () => import("@/layout/index.vue"),
//   name: "example_code",
//   meta: {
//     title: "示例代码",
//     icon: "configuration",
//   },
//   children: [
//     {
//       path: 'dagre-d3',
//       name: 'dagre-d3',
//       component: () => import('@/views/business_monitor/components/topology/dagre.vue'),
//       meta: {
//         title: 'dagre-d3',
//         copyright: false,
//         cache: true,
//       }

//     },
//     {
//       path: "asset1",
//       name: "cmdb_asset1",
//       component: () => import("@/views/homepage/cmdb_asset/asset_detail.vue"),
//       meta: {
//         title: "资产详情",
//       },
//     },],
// }

import ResourceManagement from "./modules_new/resource_management";
import SystemManagement from "./modules_new/system_management";
import Aotumation from "./modules_new/aotumation";
import DataAnalysis from "./modules_new/data_analysis";
import MonitorCenter from "./modules_new/monitor_center";
import OperationAlanalysis from "./modules_new/operational_analysis";
import SpecialMonitoring from "./modules_new/special_monitoring";
import ReportManager from "./modules_new/report_manager";
// 动态路由（异步路由、导航栏路由）
// 主导航并非路由的一部分，它只是将路由模块进行归类，这么做的目的是方便调整单个路由模块的展示位置，并且不会影响路由路径。
let asyncRoutes = [
  {
    meta: {
      title: "联壹科技",
    },
    children: [
      // 资源管理
      ResourceManagement,
      // 运营分析
      OperationAlanalysis,
      // 告警中心
      MonitorCenter,
      //专项监控
      SpecialMonitoring,
      //报表管理
      ReportManager,
      // 运维管理
      Aotumation,

      // 可视化
      DataAnalysis,

      // 系统管理
      SystemManagement,
    ],
  },
];
export { constantRoutes, systemRoutes, asyncRoutes };
