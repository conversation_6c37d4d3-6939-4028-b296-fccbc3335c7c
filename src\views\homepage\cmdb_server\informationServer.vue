<script setup name="HomepageInformation">
import { onMounted, reactive, ref, onUnmounted, watch } from "vue";
import { useAuth, usePagination } from "@/utils/composables";
import {
  cmdbGetAllServerList,
  cmdbBatchDelServerMessage,
  cmdbBatchImportServerMessage,
  cmdbUpdateServerAsset,
} from "@/api/modules/cmdb/server";
import SshCompent from "./ssh/compent/index.vue";
import ZabbixResult from "@/containers/zabbix_resulte_dialog/index.vue";
import MoreOperations from "../components/more_operations.vue";
import { getEnvViteApiBaseurl } from "@/utils/axios";
import { ssh } from "@/api/modules/cmdb/resource";
import { GetZabbixTemplate } from "@/api/modules/zabbix_api_management/zabbix";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowDown, View, Edit, Location, Bell, CirclePlusFilled, Refresh } from "@element-plus/icons-vue";
import * as xlsx from "xlsx";
// import { heartbeatTest, zabbixStatus } from "@/api/modules/cache";
import SocketUtil from "@/utils/cmdb_utils";
import { showNotification } from "@/plugins/element-ui/index";
import { goToPageAssetDetail, pageChangeNum, addZabbixForResource, showConfirmationDialog } from "../components/utils";
import { ResourceTypeEnum } from "../cmdb_asset/components/constants";
import BindDialog from "./bind_dialog.vue";
import { bindTypeEnum } from "../components/constants";
import { deepClone } from "@/utils";
import CreateServer from "@/views/homepage/cmdb_server/createServer.vue";
const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();
const router = useRouter();
const { auth } = useAuth();

const sshDialog = ref(false);
const zabbixLoading = ref(false);
const zabbixResultDialog = ref(false);
const zabbixResultData = ref([]);
const sshIp = ref("");
let baseURL = getEnvViteApiBaseurl();

// 定义localStorage的key
const STORAGE_KEY = 'cmdb_server_search_condition';

const data = reactive({
  serverLoading: false,
  bindDialog: false,
  bindDataType: "",
  bindDataServerId: "",
  title: "",
  dataList: [],
  allList: [],
  bindDataTemplateList: [],
  template_list_all: [],
  search: {
    searchName: "",
    system_type: "",
    status: "",
    isSearch: false,
  },
  ssh: {
    ip: "",
    user: "",
    password: "",
  },
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },
  drawer: false,
  router_id: 0,
});

// 监听搜索条件变化，更新localStorage
watch(() => data.search, (newVal) => {
  if (newVal.searchName || newVal.system_type || newVal.status) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify({
      searchName: newVal.searchName,
      system_type: newVal.system_type,
      status: newVal.status,
      isSearch: newVal.isSearch
    }));
  }
}, { deep: true });

onMounted(() => {
  data.title = ResourceTypeEnum.server;
  
  // 从localStorage获取筛选条件
  const savedSearch = localStorage.getItem(STORAGE_KEY);
  if (savedSearch) {
    try {
      const parsedSearch = JSON.parse(savedSearch);
      data.search.searchName = parsedSearch.searchName || '';
      data.search.system_type = parsedSearch.system_type || '';
      data.search.status = parsedSearch.status || '';
      data.search.isSearch = parsedSearch.isSearch || false;
    } catch (e) {
      console.error('解析保存的筛选条件出错', e);
    }
  }
  
  getData();
  getTemplate();
});

// 组件卸载时清除localStorage
onUnmounted(() => {
  localStorage.removeItem(STORAGE_KEY);
});

SocketUtil.socket.onmessage = (recv) => {
  let res = JSON.parse(recv.data);
  switch (res.message.status_code) {
    case 200:
      showNotification("采集成功", res.message.message, "success");
      break;
    case 500:
      showNotification("采集失败", res.message.message, "error");
      break;
  }
};

//获取监控模板
function getTemplate() {
  GetZabbixTemplate().then((res) => {
    data.template_list = res.data;
    data.template_list_all = res.data;
  });
}

//加载初始化数据
function getData() {
  data.serverLoading = true;
  cmdbGetAllServerList()
    .then((res) => {
      data.dataList = res.data;
      data.dataList.forEach((item) => {
        item.group_list = item.group_list.split(",");
      });
      if (data.dataList.length == 0) {
        data.dataList = [];
        return;
      }

      data.allList = data.dataList;
      // 如果有筛选条件，应用筛选
      if (data.search.isSearch) {
      searchByName();
      } else {
        changePageNum(data.allList);
      }
    })
    .finally(() => {
      data.serverLoading = false;
    });
}

function changePageNum(lists) {
  let res = pageChangeNum(lists, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
  data.serverLoading = false;
}

const drawerTitle = computed(() => {
  return data.router_id === 0 ? "新增服务器" : "编辑服务器";
});

//新增页面侧滑打开
function create() {
  data.drawer = true;
  data.router_id = 0;
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

//筛选函数
function queryData() {
  if (data.search.searchName === "" && data.search.system_type == "" && data.search.status == "") {
    getData();
    data.search.isSearch = false;
  } else {
    searchByName();
    data.search.isSearch = true;
  }
}
// 搜索
function searchByName() {
  let list = deepClone(data.allList);
  data.serverLoading = true;
  const searchStr = data.search.searchName.trim();
  if (searchStr) {
    const ipRegex = /^[\d.]+$/;
    if (ipRegex.test(searchStr)) {
      list = list.filter((item) => {
        return item.ip.trim().indexOf(searchStr) != -1;
      });
    } else {
      list = list.filter((item) => {
        return item.server_name.trim().toLowerCase().indexOf(searchStr.toLowerCase()) !== -1;
      });
    }
  }

  if (data.search.system_type) {
    list = list.filter((item) => {
      return item.system_type.toLowerCase() == data.search.system_type.toLowerCase();
    });
  }

  if (data.search.status) {
    list = list.filter((item) => {
      return item.status == data.search.status;
    });
  }

  changePageNum(list);
}

function editServer(row) {
  data.router_id = row.server_id;
  data.drawer = true;
}

//批量删除
function batchDel() {
  let list = [];
  ElMessageBox.confirm("是否删除选中服务器？", "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    data.batch.selectionDataList.forEach((item) => {
      list.push({ server_id: item.server_id, zabbix_id: item.zabbix_id });
    });
    cmdbBatchDelServerMessage(list).then((res) => {
      if (res.status_code == 200) {
        getData();
      }
    });
  });
}

//下载模板
function downloadTemplate() {
  let a = document.createElement("a");
  a.href = "./static/hostTemplate.xlsx";
  a.download = "导入模板下载.xlsx";
  a.style.display = "none";
  document.body.appendChild(a);
  a.click();
  a.remove();
}

//批量上传服务器
function handleUpload() {
  const fileInput = document.createElement("input");
  fileInput.type = "file";
  fileInput.accept = ".xlsx, .xls";
  fileInput.addEventListener("change", (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();
    reader.onload = (e) => {
      const datas = new Uint8Array(e.target.result);
      const workbook = xlsx.read(datas, { type: "array" });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const excelData = xlsx.utils.sheet_to_json(worksheet, {
        header: 1,
        blankrows: false,
      });
      excelData.shift();
      let arr = [];
      if (inspectExcel(excelData) == true) {
        excelData.forEach((item) => {
          let excelForm = {};
          (excelForm.server_id = 0),
            (excelForm.zabbix_id = 0),
            (excelForm.server_name = item[1]),
            (excelForm.ip = item[2]),
            (excelForm.group_list = item[3]),
            (excelForm.system_type = item[4]),
            (excelForm.username = item[5]),
            (excelForm.password = item[6]),
            (excelForm.port = item[7]);
          if (item[4].toLowerCase() == "linux") {
            excelForm.template_list = ["服务器-Linux-基础监控模板"];
          } else {
            excelForm.template_list = ["服务器-Windows-基础监控模板"];
          }
          arr.push(excelForm);
        });
        const notification = showNotification("导入中", "文件正在导入中", "info");
        cmdbBatchImportServerMessage(arr).then((res) => {
          notification.close();
          getData();
        });
      } else {
        ElMessage.error("导入出错，文件中有未填写的内容或者格式错误");
      }
    };
    reader.readAsArrayBuffer(file);
  });
  fileInput.click();
}

//对文件内容进行校验
function inspectExcel(array) {
  // 获取第一个元素的长度
  const firstElementLength = array[0].length;
  // 遍历数组，检查每个元素的长度是否与第一个元素相同
  for (let i = 1; i < array.length; i++) {
    if (array[i].length !== firstElementLength) {
      // 如果长度不一致，返回false
      return false;
    }
  }
  // 如果所有元素的长度都一致，返回true
  return true;
}

//绑定资源时，筛选监控模板
function filterTemplate(softType) {
  return data.template_list_all.filter((item) => item.name.includes(`${softType}-`));
}

function openBindSoftDialog(row, type) {
  data.bindDataType = type;
  if (type !== ResourceTypeEnum.businessApp) {
    data.bindDataTemplateList = filterTemplate(type);
  }
  data.bindDataServerId = row.server_id;
  data.bindDialog = true;
}

// 跳转详情
function jumpAssetDetail(row, isMonitoring = false) {
  const queryParams = { item: data.title, id: row.zabbix_id, ip: row.ip, relation_id: row.relation_id };
  goToPageAssetDetail(queryParams, isMonitoring);
}

/**
 * 是否拥有权限
 */
const isHavingAuth = computed(() => {
  return auth(["admin", "cmdb_server_information.excute", "create_task.browse"]);
});

//执行任务
function toExcute(row) {
  const ip = row.ip;
  const queryParams = {
    ip,
  };
  if (row.group_list.length != 0) {
    queryParams.group = row.group_list[0];
  } else {
    queryParams.group = "";
  }
  router.push({
    name: "create_task",
    query: queryParams,
  });
}

function sshLink(item) {
  sshIp.value = item.ip;
  sshDialog.value = true;
}

//SSH连接验证
const sshSubmit = (value) => {
  let params = value;
  params.ip = sshIp.value;
  ssh(params).then((res) => {
    let date = new Date();
    date.setTime(date.getTime() + 1 * 60 * 1000);
    const expires = ";expires=" + date.toUTCString();
    if (res.data.user) {
      document.cookie = "userdata=" + res.data.user + expires + ";path=/".trim();
      router.push({
        name: "ssh",
        query: { ip: sshIp.value },
      });
    }
  });
};

// 安装所有
function installAll() {
  let list = data.batch.selectionDataList.filter((item) => item.status === "offmonitor");
  if (list.length == 0) {
    ElMessage.error("请检查是否有未安装监控服务的设备");
    return;
  }

  let list_template = list.filter((item) => {
    return !item.template_list || item.template_list.length == 0;
  });
  console.log(list_template);
  if (list_template.length != 0) {
    let server_name = list_template.map((item) => item.server_name);

    showNotification(
      "注意",
      "以下服务器没有添加监控模板：[" + server_name.join(",") + "],请注意！",
      "error",
      2000,
      "top-right"
    );
    return;
  }
  ElMessageBox.confirm("已安装的不会重复安装", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      addZabbix(list);
    })
    .catch(() => {});
}

/**
 * item 数据
 *is_skip 是否跳过 指代的是跳过监控脚本步骤
 */
function install(item, is_skip = false) {
  if (!item.template_list || item.template_list.length == 0) {
    ElMessage.error("服务器" + item.ip + "未配置监控模板，请配置！");
    return;
  }
  addZabbix([item], is_skip);
}

//安装zabbix监控
async function addZabbix(ipList, is_skip = false) {
  let ip_information_list = [];
  ipList.forEach((item) => {
    ip_information_list.push({
      ip: item.ip,
      type: "server",
      group_name: item.group_list[0],
      id: item.zabbix_id,
    });
  });
  let params = {
    ip_information_list,
    resource_type_name: "server",
    is_skip_ansible_script_execution: is_skip,
  };

  const confirm = await showConfirmationDialog(
    is_skip ? "确定只添加zabbix主机吗？添加主机后，安装监控只能从自动运维功能安装" : ""
  );
  if (confirm) {
    zabbixLoading.value = true;
    zabbixResultDialog.value = true;
    zabbixResultData.value = [];
    try {
      zabbixResultData.value = await addZabbixForResource(params);
    } catch (error) {}
    zabbixLoading.value = false;
    getData();
  } else {
    ElMessage.info("已取消!");
  }
}

//更新详情
function refreshAsset(row) {
  let ipList = [];
  ipList.push(row.ip);
  cmdbUpdateServerAsset(ipList).then((res) => {
    showNotification("提示", res.message, "info");
  });
}

//导出CMDB信息为excel表
function exportCMDB() {
  //   fetch("http://***************:8099/api/server/export_cmdb_hardware_information")
  fetch(baseURL + "/server/export_cmdb_hardware_information/")
    .then((response) => {
      if (!response.ok) {
        throw new Error("网络响应存在问题");
      }
      return response.blob(); // 将响应转换为 Blob
    })
    .then((blob) => {
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "servers_with_cmdb.xlsx"; // 设置下载文件的名称
      document.body.appendChild(a);
      a.click(); // 模拟点击下载
      document.body.removeChild(a);
      URL.revokeObjectURL(url); // 释放对象 URL
    })
    .catch((error) => {
      console.error("fetch请求出现问题:", error);
    });
}
</script>

<template>
  <div>
    <!-- 服务器展示 -->
    <page-main :title="data.title">
      <search-bar>
        <el-form :model="data.search" label-width="100px" label-suffix="：">
          <el-row>
            <el-col :span="6">
              <el-form-item label="ip或名称">
                <el-input
                  v-model="data.search.searchName"
                  placeholder="输入ip或名称,支持模糊查询"
                  clearable
                  @keyup.enter="queryData"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="系统">
                <el-select v-model="data.search.system_type" placeholder="请选择" clearable>
                  <el-option label="linux" value="linux" />
                  <el-option label="windows" value="windows" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="监控状态">
                <el-select v-model="data.search.status" placeholder="请选择" clearable>
                  <el-option label="已启用" value="online" />
                  <el-option label="停用的" value="offline" />
                  <el-option label="未监控" value="offmonitor" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-space wrap class="pl-10px">
                <el-button type="primary" @click="queryData()">
                  <template #icon>
                    <el-icon>
                      <svg-icon name="ep:search" />
                    </el-icon>
                  </template>
                  筛选
                </el-button>
              </el-space>
            </el-col>
          </el-row>
        </el-form>
      </search-bar>
      <div class="flex justify-between">
        <el-space wrap>
          <el-button type="primary" @click="create">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            新增
          </el-button>
          <el-button type="primary" @click="downloadTemplate()">
            <template #icon>
              <el-icon>
                <svg-icon name="mdi:arrow-collapse-down" />
              </el-icon>
            </template>
            下载模板
          </el-button>
          <el-button type="primary" @click="handleUpload">
            <template #icon>
              <el-icon>
                <svg-icon name="ant-design:import-outlined" />
              </el-icon>
            </template>
            导入资源
          </el-button>
          <el-button type="primary" @click="exportCMDB">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:download" />
              </el-icon>
            </template>
            导出CMDB信息
          </el-button>
          <el-button type="primary" @click="installAll" :disabled="!data.batch.selectionDataList.length">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:coordinate" />
              </el-icon>
            </template>
            批量安装
          </el-button>

          <el-button-group v-if="data.batch.enable">
            <el-button type="danger" :disabled="!data.batch.selectionDataList.length" @click="batchDel()">
              删除
            </el-button>
          </el-button-group>
        </el-space>
      </div>

      <el-table
        v-loading="data.serverLoading"
        class="list-table"
        :data="data.dataList"
        border
        stripe
        highlight-current-row
        @selection-change="data.batch.selectionDataList = $event"
      >
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column prop="ip" label="IP" align="center" />
        <el-table-column prop="server_name" label="名称" align="center">
          <template #default="scope">
            <el-link @click="jumpAssetDetail(scope.row)" type="primary">
              {{ scope.row.server_name }}
            </el-link>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="group_list" label="分组" align="center">
          <template #default="scope">
            <span v-for="(item, index) in scope.row.group_list" :key="index">
              <el-tag size="small">
                {{ item }}
              </el-tag>
            </span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="system_type" label="系统" align="center" /> -->
        <el-table-column label="系统/版本" align="center">
          <template #default="scoped">
            <span v-if="scoped.row.os_system_type !== scoped.row.system_type">
              {{ scoped.row.os_system_type }}
            </span>
            <span v-else>{{ scoped.row.system_type }}</span>
          </template>
        </el-table-column>

        <el-table-column label="监控状态" width="105px" align="center">
          <template #default="scoped">
            <el-tag v-if="scoped.row.status == 'online'">已启用</el-tag>
            <el-tag v-else-if="scoped.row.status == 'offmonitor'" type="info" effect="dark">未监控</el-tag>
            <el-tag v-else type="danger">停用的</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="连接状态" width="90px" align="center">
          <template #default="scoped">
            <el-tag v-if="scoped.row.isnline == true" type="success">在线</el-tag>
            <el-tag v-else type="danger">离线</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="400px">
          <template #default="scoped">
            <el-button type="primary" plain @click="editServer(scoped.row)" style="margin-left: 5px">编辑</el-button>
            <el-dropdown trigger="click">
              <el-button>
                绑定
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :icon="CirclePlusFilled"
                    @click="openBindSoftDialog(scoped.row, bindTypeEnum.database)"
                  >
                    数据库
                  </el-dropdown-item>
                  <el-dropdown-item
                    :icon="CirclePlusFilled"
                    @click="openBindSoftDialog(scoped.row, bindTypeEnum.middleware)"
                  >
                    中间件
                  </el-dropdown-item>
                  <el-dropdown-item
                    :icon="CirclePlusFilled"
                    @click="openBindSoftDialog(scoped.row, bindTypeEnum.businessApp)"
                  >
                    业务应用
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <MoreOperations>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-if="scoped.row.status == 'offmonitor'"
                  :icon="Location"
                  @click="install(scoped.row)"
                >
                  安装监控
                </el-dropdown-item>

                <el-dropdown-item
                  v-if="scoped.row.status == 'offmonitor'"
                  :icon="Location"
                  @click="install(scoped.row, true)"
                >
                  添加zabbix主机
                </el-dropdown-item>

                <el-dropdown-item
                  v-if="scoped.row.isnline == true && isHavingAuth"
                  :icon="Edit"
                  @click="toExcute(scoped.row)"
                >
                  执行任务
                </el-dropdown-item>

                <el-dropdown-item
                  v-if="scoped.row.system_type.toLowerCase() == 'linux' && scoped.row.isnline == true"
                  :icon="Bell"
                  @click="sshLink(scoped.row)"
                >
                  ssh连接
                </el-dropdown-item>
                <el-dropdown-item
                  :icon="View"
                  @click="jumpAssetDetail(scoped.row, true)"
                  v-if="scoped.row.status == 'online' || scoped.row.status == 'offline'"
                >
                  最新数据
                </el-dropdown-item>
                <el-dropdown-item :icon="Refresh" @click="refreshAsset(scoped.row)">更新详情</el-dropdown-item>
              </el-dropdown-menu>
            </MoreOperations>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>
    <el-drawer v-model="data.drawer" size="50%" :destroy-on-close="true">
      <template #header>
        <h4>{{ drawerTitle }}</h4>
      </template>
      <el-divider class="cancel_top" />
      <CreateServer v-model="data.router_id" @closeDialog="data.drawer = false" @getResource="getData()"></CreateServer>
    </el-drawer>
    <!-- 绑定 数据库、中间件、业务应用-->
    <BindDialog
      v-model="data.bindDialog"
      :bindDataType="data.bindDataType"
      :bindDataTemplateList="data.bindDataTemplateList"
      :bindDataServerId="data.bindDataServerId"
    ></BindDialog>
    <!-- 服务器ssh连接 -->
    <SshCompent v-model="sshDialog" @sshSubmit="sshSubmit"></SshCompent>
    <!-- 监控流程显示 -->
    <ZabbixResult v-model="zabbixResultDialog" :step-data="zabbixResultData" :loading="zabbixLoading"></ZabbixResult>
  </div>
</template>
<style scoped>
.cancel_top {
  margin-top: 0px;
}
</style>
