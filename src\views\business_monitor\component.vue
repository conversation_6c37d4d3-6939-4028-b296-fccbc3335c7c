<script setup>
// const router = useRouter()
import { onMounted, reactive } from "vue";
import { usePagination } from "@/utils/composables";
import { pageChangeNum } from "@/views/homepage/components/utils";
import { getBusinessAnalysis } from "@/api/modules/business_topo/index.js"
import * as XLSX from 'xlsx';
const data = reactive({
  loading: false,
  serverList: [],
  databaseList: [],
  allServerList: [],
  allDatabaseList: [],
  search: {
    searchName: "",
  },
  time: 30,
})
const allMap = {
  '服务器': 'allServerList',
  '数据库': 'allDatabaseList',
};
const map = {
  '服务器': 'serverList',
  '数据库': 'databaseList',
};

const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();
const emit = defineEmits(["update:modelValue"]);

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
  },
});
onMounted(() => {
  getData();
});
function getData() {
  data.loading = true
  const map = {
    '服务器': getServerList,
    '数据库': getDatabaseList,
  }
  map[props.item]()
}
function getServerList() {
  let params = { type: props.item, past_days: data.time }
  getBusinessAnalysis(params).then((res) => {
    data.serverList = res.data
    data.allServerList = data.serverList
    changePageNum(data.allServerList)
  })
}
function getDatabaseList() {
  let params = { type: props.item, past_days: data.time }
  getBusinessAnalysis(params).then((res) => {
    data.databaseList = res.data
    data.allDatabaseList = data.databaseList
    changePageNum(data.allDatabaseList)
  })

}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.searchName !== '') {
      searchName();
    } else {
      changePageNum(data[allMap[props.item]]);
    }
  });
}
// 当前页码切换（翻页）
function currentChange(page) {
  onCurrentChange(page).then(() => {
    if (data.search.searchName !== '') {
      searchName();
    } else {
      changePageNum(data[allMap[props.item]]);
    }
  });
}

function changePageNum(lists) {
  let res = pageChangeNum(lists, getParams());
  data[map[props.item]] = res.list;
  pagination.value.total = res.total;
  data.loading = false;
}
function searchName() {
  // 根据 props.item 的值确定数组
  if (data.search.searchName !== "") {
    // 获取对应的数组
    const list = data[allMap[props.item]]; // 获取对应类型的数组键名
    // 根据搜索条件进行模糊匹配
    data[map[props.item]] = list.filter(item =>
      item.name.toLowerCase().includes(data.search.searchName.toLowerCase())
    );
    changePageNum(data[map[props.item]])
  } else {
    // 如果搜索框为空，恢复所有数据
    data[map[props.item]] = data[allMap[props.item]];
    changePageNum(data[map[props.item]])
  }
}
function exportExcelByTable() {
  const headers = ['名称', '在线数据', '离线数据', '增量数据', '资源类型'];
  const ws = XLSX.utils.aoa_to_sheet([headers, ...data[allMap[props.item]].map(item => [
    item.name,
    item.online_data,
    item.off_line_data,
    item.increatement_data,
    item.resource_type
  ])]);
  // 创建新的 workbook
  const wb = XLSX.utils.book_new();
  // 将 worksheet 添加到 workbook 中
  XLSX.utils.book_append_sheet(wb, ws, 'Data');
  // 生成并下载 Excel 文件
  XLSX.writeFile(wb, `${props.item}.xlsx`);
}
function changeTime() {
  getData()
}
</script>

<template>
  <div>
    <div class="flex justify-between custom-padding">
      <el-space wrap>
        <el-button @click="exportExcelByTable" type="primary">
          <template #icon>
            <el-icon>
              <svg-icon name="ep:download" />
            </el-icon>
          </template>
          导出excel
        </el-button>
        <div class="w-220px">
          <el-input v-model="data.search.searchName" placeholder="请输入名称来进行筛选" clearable />
        </div>
        <el-button type="primary" @click="searchName">
          <template #icon>
            <el-icon>
              <svg-icon name="ep:search" />
            </el-icon>
          </template>
          筛选
        </el-button>
      </el-space>
      <el-space wrap>
        <el-radio-group v-model="data.time" size="small" @change="changeTime">
          <el-radio-button label="一个月" :value="30" />
          <el-radio-button label="两个月" :value="60" />
          <el-radio-button label="半年" :value="180" />
          <el-radio-button label="一年" :value="365" />
        </el-radio-group>

      </el-space>
    </div>
    <div v-if="props.item === '服务器'">
      <el-table v-loading="data.loading" :data="data.serverList" border>
        <el-table-column property="name" label="服务器名称" align="center" />
        <el-table-column label="服务器类型" align="center">
          <template #default="scoped">
            <el-tag type="primary">{{ scoped.row.resource_type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column property="online_data" label="在线数据量" align="center" />
        <el-table-column property="off_line_data" label="离线数据量" align="center" />
        <el-table-column property="increatement_data" label="数据月增量" align="center" />
      </el-table>
    </div>
    <div v-else-if="props.item === '数据库'">
      <el-table v-loading="data.loading" :data="data.databaseList" border>
        <el-table-column property="name" label="数据库名称" align="center" />
        <el-table-column label="数据库类型" align="center">
          <template #default="scoped">
            <el-tag type="primary">{{ scoped.row.resource_type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column property="online_data" label="在线数据量" align="center" />
        <el-table-column property="off_line_data" label="离线数据量" align="center" />
        <el-table-column property="increatement_data" label="数据月增量" align="center" />
      </el-table>
    </div>
    <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
      :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="paginationTable"
      background @size-change="sizeChange" @current-change="currentChange" />
  </div>
</template>

<style lang="scss" scoped>
.custom-padding {
  padding-bottom: 20px;
  /* 设置底部内边距为20px */
}
</style>
