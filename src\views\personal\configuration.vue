<route>
{
    name: 'configuration',
    meta: {
        title: "修改配置"
    }
}
</route>

<script setup name="PersonalEditPassword">
import { onMounted } from "vue";
import ConfigForm from "@/containers/config/index.vue";
import DB4BIX from "./components/db4bix_conf/list.vue";
import DbCollectionConfiguration from "./components/logstash_conf/db_collection_configuration.vue";

const route = useRoute();

const activeName = ref("first");

onMounted(() => {
  if (route.query.activeName) {
    activeName.value = route.query.activeName;
  }
});
</script>
<template>
  <div>
    <page-main>
      <el-tabs type="card" v-model="activeName">
        <el-tab-pane label="基础配置" name="first">
          <ConfigForm :firstConfig="false"></ConfigForm>
        </el-tab-pane>
        <el-tab-pane label="Oracle数据库监控配置" name="second">
          <DB4BIX v-if="activeName == 'second'"></DB4BIX>
        </el-tab-pane>
        <el-tab-pane label="数据库采集配置" name="third">
          <DbCollectionConfiguration v-if="activeName == 'third'"></DbCollectionConfiguration>
        </el-tab-pane>
      </el-tabs>
    </page-main>
  </div>
</template>
<style scoped lang="scss">
.button_positon {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
