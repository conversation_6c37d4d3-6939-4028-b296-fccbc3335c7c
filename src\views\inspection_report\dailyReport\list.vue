<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
import { reactive, ref } from "@vue/reactivity";
import {
  getAllDailyReports,
  pushSelectedDailyReports,
  viewDailyReport,
  getCurrnetDailyReport,
  pushCurrentDailyReport,
} from "@/api/modules/inspection_report/dailyReports";
import { usePagination } from "@/utils/composables";
import printJS from "print-js";
import { ElLoading, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const iframe = ref({
  src: "",
  loading: true,
});
const data = reactive({
  showDialog: false,
  title: "",
  loading: false,
  // 表格是否自适应高度
  tableAutoHeight: false,
  // 搜索
  search: {
    keyWord: "",
    keyTime: "",
    isSearch: false,
  },
  searchFold: true,
  // 列表数据
  tableList: {},
  allList: {},
  batchCheck: [],
  pushLoading: false,
  pushButton: false,
  isNaN: false,
  //日报时间年份
  timeRanges: [],
  initTime: "",
});

const centerDialogVisible = ref(false);

onMounted(() => {
  getDataList();
});

function getDataList() {
  getAllDailyReports().then((res: any) => {
    if (res.data.length != 0) {
      //时间排序
      let allList = res.data.sort((a, b) => {
        const dateA = new Date(a.substr(a.indexOf("-") + 1, 10));
        const dateB = new Date(b.substr(b.indexOf("-") + 1, 10));
        return dateB - dateA;
      });
      //去重
      let list: any = Array.from(new Set(res.data.map((item) => item.substr(item.indexOf("-") + 1, 7))));
      //存储日报年份
      data.timeRanges = list;
      //if判断是否首次加载，否，则避免刷新后出现不同月份得日报出现。
      if (data.initTime == "") {
        data.search.keyTime = list[0];
        data.initTime = list[0];
      }
      data.allList = list.reduce((acc, item) => {
        acc[item] = allList.filter((elment) => elment.includes(item));
        return acc;
      }, {});
      data.isNaN = true;
    }
    data.tableList[data.search.keyTime] = data.allList[data.search.keyTime];
  });
}

const dailyReport = ref("");

//查看html
function CheckDReport(row) {
  iframe.value.loading = true;
  dailyReport.value = "";
  viewDailyReport(row.slice(0, -5))
    .then((res) => {
      dailyReport.value = res.data;
    })
    .finally(() => {
      iframe.value.loading = false;
    });
  data.title = row.slice(0, -5) + "详情";
  centerDialogVisible.value = true;
}

//筛选数据
function queryData() {
  data.tableList = {};
  if (data.search.keyWord === "" && data.search.keyTime === data.initTime) {
    //直接通过allList引用
    data.tableList[data.search.keyTime] = data.allList[data.search.keyTime];
    data.search.isSearch = false;
  } else {
    searchName();
    data.search.isSearch = true;
  }
}

//TODO:根据日期和名称进行赛选
function searchName() {
  data.loading = true;
  let list: any = {};
  //深拷贝，保证data.allList数据得完整性
  let originData = JSON.parse(JSON.stringify(data.allList));
  //输入名称为空，直接针对keyTime进行筛选
  if (data.search.keyWord == "") {
    list[data.search.keyTime] = originData[data.search.keyTime];
  } else {
    //主要依照输入的名称进行赛选
    Object.keys(originData).forEach((key) => {
      list[key] = originData[key].filter((item) => {
        return item.includes(data.search.keyWord);
      });
      if (list[key].length != 0) {
        data.search.keyTime = key;
      } else {
        delete list[key];
      }
    });
  }
  data.tableList = list;
  setTimeout(() => {
    data.loading = false;
  }, 1000);
}

/**
 * 打印日报按钮操作
 * @description:
 * @param {*} row
 * @return {*}
 */
function printDReport(row) {
  // 获取日报详情
  const daily_name = row.slice(0, -5);
  dailyReport.value = "";
  viewDailyReport(daily_name)
    .then((res) => {
      dailyReport.value = res.data;
      printdaily(daily_name);
    })
    .finally(() => {
      iframe.value.loading = false;
    });
}

/**
 * 打印
 * @description:
 * @return {*}
 */
function printdaily(daily_name) {
  const loading = ElLoading.service({
    lock: true,
    text: "打印中...",
    background: "rgba(0, 0, 0, 0.7)",
  });
  setTimeout(() => {
    loading.close();
  }, 2000);
  setTimeout(() => {
    nextTick(() => {
      printJS({
        printable: "printRegion",
        type: "html",
        documentTitle: daily_name,
        style: ["@page { size: A4; margin: 10mm;} body {margin: 5mm;} h4 {margin:5mm}"],
      });
    });
  }, 1500);
}

//批量推送日报公共方法
function pushDailyReport(pushFiles) {
  pushSelectedDailyReports({ data: { file_name_list: pushFiles } }).then((res) => {
    ElMessageBox.alert("日报推送成功，请注意查收！", "提示", {
      confirmButtonText: "确认",
      type: "success",
      center: true,
    });
  });
}

//单个推送
function pushOneDReports(item) {
  pushDailyReport([item]);
}

//批量推送
function batchPushDReports() {
  pushDailyReport(data.batchCheck);
}

//获取当前最新日报
function checkNewDReport() {
  iframe.value.loading = true;
  dailyReport.value = "";
  const saveToLocal = (save) => {
    centerDialogVisible.value = true;
    data.title = "当前状态日报内容";
    getCurrnetDailyReport({ data: { save_to_local: save } })
      .then((res) => {
        dailyReport.value = res.data;
        data.pushButton = true;
        if (save) {
          getDataList();
        }
      })
      .finally(() => {
        iframe.value.loading = false;
      });
  };
  ElMessageBox.confirm("是否保存为日报文件？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      saveToLocal(true);
    })
    .catch(() => {
      saveToLocal(false);
    });
}

//推送当前日报信息
function pushCurrentStatusReport() {
  data.pushLoading = true;
  pushCurrentDailyReport()
    .then(() => getDataList())
    .finally(() => {
      data.pushLoading = false;
    });
}

function handleClose() {
  console.log("取消");
  dailyReport.value = "";
  data.pushButton = false;
  centerDialogVisible.value = false;
}
</script>

<template>
  <div>
    <page-main title="日报管理">
      <div class="flex justify-between">
        <el-space wrap>
          <el-button :disabled="data.batchCheck.length == 0" @click="batchPushDReports" type="primary" plain>
            推送日报
          </el-button>
          <el-button @click="checkNewDReport" type="primary" plain>查看最新日报</el-button>
        </el-space>
        <div>
          <el-form label-suffix=":" :inline="true" class="el-form-inline">
            <el-form-item label="时期">
              <el-select style="width: 160px" v-model="data.search.keyTime" @change="queryData">
                <el-option v-for="(item, index) in data.timeRanges" :key="index" :label="item" :value="item" />
              </el-select>
            </el-form-item>

            <el-form-item label="名称">
              <el-input
                v-model="data.search.keyWord"
                placeholder="请输入日报名进行查询"
                clearable
                @keydown.enter="queryData()"
              />
            </el-form-item>

            <el-button type="primary" @click="queryData()" :icon="Search">筛选</el-button>
          </el-form>
        </div>
      </div>

      <el-divider border-style="dashed" />
      <div v-if="data.isNaN" v-for="[key, value] in Object.entries(data.tableList)" :key="key">
        <div style="padding-top: 10px">
          <div class="vertical-line"></div>
          <span class="sub-title">{{ key }}</span>
        </div>
        <el-row :gutter="10">
          <el-col class="div-row" v-for="(item, index) in value" :key="index" :span="4">
            <el-card class="custom-card">
              <el-checkbox v-model="data.batchCheck" :label="item" />
              <div slot="header">
                <el-tooltip :content="item" placement="top">
                  <p class="title">{{ item }}</p>
                </el-tooltip>
              </div>
              <div class="flex b-t-1 items-center pt-4px text-center text-14px b-t-[#d0d0d5]">
                <div
                  class="b-r-1 b-r-gray w-33% text-center cursor-pointer hover:text-blue"
                  @click.stop="CheckDReport(item)"
                >
                  查看
                </div>
                <div class="b-r-1 b-r-gray w-33% cursor-pointer hover:text-blue" @click="pushOneDReports(item)">
                  推送
                </div>
                <div class="w-33% cursor-pointer hover:text-blue" @click="printDReport(item)">打印</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <el-empty />
      </div>
      <el-dialog
        v-model="centerDialogVisible"
        width="80%"
        :title="data.title"
        :destroy-on-close="true"
        @close="handleClose"
      >
        <div class="h-75vh relative">
          <div v-loading="iframe.loading" class="h-[calc(100%-40px)] overflow-auto">
            <p v-html="dailyReport"></p>
          </div>

          <div class="flex justify-center mt-5 absolute bottom-0px w-full">
            <el-button
              type="primary"
              plain
              @click="pushCurrentStatusReport"
              :loading="data.pushLoading"
              v-if="data.pushButton"
            >
              推送至企业微信
            </el-button>
            <el-button @click="handleClose">取消</el-button>
          </div>
        </div>
      </el-dialog>
    </page-main>
    <div id="printRegion" class="fixed left-[-9999px] top-[-99999px] w-900px">
      <div v-html="dailyReport"></div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.form_css {
  margin-bottom: 1px;
}
.custom-card {
  border-radius: 10px;
  // max-width: 300px;
  min-width: 170px;
  :deep(.el-card__body) {
    padding: 10px;
  }
}
.vertical-line {
  border-left: 4px solid #337ecc;
  /* 2px 宽度的加粗直线，黑色 */
  height: 13px;
  /* 设置高度 */
  display: inline-block;
}

.sub-title {
  display: inline-block;
  /* 设置为内联块级元素 */
  margin-left: 5px;
  /* 添加一些左边距以分隔线和文本 */
  font-size: 18px;
  font-weight: bold;
}
.div-row {
  padding-top: 10px;
}
.title {
  font-weight: bold;
  font-family: Arial, sans-serif;
  display: block;
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
  margin-top: 1px;
}
.absolute-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 0;
  }

  .page-main {
    // 让 page-main 的高度自适应
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;

    .search-container {
      margin-bottom: 0;
    }
  }
}

.page-main {
  .el-divider {
    margin-inline: -20px;
    width: calc(100% + 40px);
  }
}

.el-dialog__body {
  height: 50vh;
  overflow: auto;
}
.el-form-inline {
  :deep {
    .el-form-item {
      width: auto;
      margin-bottom: 0px;
    }
  }
}
</style>
