<script setup name="IframeLayout">
import { useRouter } from "vue-router";
import { Menu, Delete, Plus } from "@element-plus/icons-vue";
import BusinessResourceDetailsDrawer from "./components/business_resource_details_drawer.vue";
import { cmdbBusinessDelMessage, getBusinessFromConsole, getBusinessLog } from "@/api/modules/cmdb/business";
import { batchImportServerInfoToUpdate } from "@/api/modules/cmdb/server";
import { batchImportDatabaseInfoToUpdate } from "@/api/modules/cmdb/resource";
import { markRaw, onUnmounted, watch } from "vue";
import * as xlsx from "xlsx";
import { GetAllBussiness } from "@/api/modules/topology/topology";
import storage from "@/utils/storage";
import CreateForm from "../homepage/cmdb_business/components/createForm.vue";
import ConsoleDialog from "@/containers/service_console_info/index.vue";
import { ElMessage } from "element-plus";
import { exportBusinessResource } from "@/utils/importAndExport";

// 定义localStorage的key
const STORAGE_KEY = 'home_search_condition';

const showDrawer = ref(false);
const businessID = ref(0);
const data = reactive({
  businessName: "",
  radio: 4,
  dataList: {
    中联产品: [],
  },
  allList: {},
  search: {
    name: "",
    status: "all",
  },
  serverItem: {},

  showConsole: false,
});

// 监听搜索条件变化，更新localStorage
watch(() => data.search, (newVal) => {
  if (newVal.name || newVal.status !== "all") {
    localStorage.setItem(STORAGE_KEY, JSON.stringify({
      name: newVal.name,
      status: newVal.status
    }));
  }
}, { deep: true });

const serverDrawer = ref(false);
const statusOption = [
  { label: "全部", value: "all" },
  { label: "正常", value: "success" },
  { label: "灾难", value: "danger" },
  { label: "故障", value: "warning" },
  { label: "停用", value: "down" },
];
const isNoneData = computed(() => {
  return Object.values(data.dataList).every((arr) => arr.length === 0);
});

const imglogo = new URL("../../assets/images/business-logo.png", import.meta.url).href;
const router = useRouter();
onMounted(() => {
  // 从localStorage获取筛选条件
  const savedSearch = localStorage.getItem(STORAGE_KEY);
  if (savedSearch) {
    try {
      const parsedSearch = JSON.parse(savedSearch);
      data.search.name = parsedSearch.name || '';
      data.search.status = parsedSearch.status || 'all';
    } catch (e) {
      console.error('解析保存的筛选条件出错', e);
    }
  }
  
  getData();
});

// 组件卸载时清除localStorage
onUnmounted(() => {
  localStorage.removeItem(STORAGE_KEY);
});

//
function getData() {
  GetAllBussiness().then((res) => {
    data.dataList = sortByNumberDesc(res.data, "problem").reduce((acc, curr) => {
      const key = curr.belong;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(curr);
      return acc;
    }, {});
    data.allList = data.dataList;
    
    // 如果有筛选条件，应用筛选
    if (data.search.name || data.search.status !== "all") {
      search();
    }
  });
}
function sortByNumberDesc(arr, prop) {
  return arr.sort((a, b) => b[prop] - a[prop]);
}
function selectResource() {
  if (data.businessName !== "") {
    router.push({
      path: "/cmdb/createBusiness",
      query: { businessName: data.businessName },
    });
  } else {
    ElMessage.error("业务名称不能为空！");
  }
}

function editBussiness(item) {
  businessID.value = item.business_id;
  showDrawer.value = true;
}

function onClickWarningMonitior(item) {
  storage.local.set("problem", JSON.stringify(item.problemList));
  router.push({
    path: "/monitor_center/WarningMonitior",
    query: {
      item: item.name,
      id: item.business_id,
    },
  });
}
function onClickDelete(item) {
  ElMessageBox.confirm(`确认要删除${item.name}这个业务吗？`, "警告", {
    type: "warning",
    icon: markRaw(Delete),
  })
    .then(() => {
      cmdbBusinessDelMessage([item.business_id]).then((res) => {
        getData();
      });
    })
    .catch(() => {
      ElMessage.info("删除取消");
    });
}
function getSuccess(obj) {
  return Object.values(obj).flatMap((arr) => arr.filter((item) => item.status === "success")).length;
}
function getWarning(obj) {
  return Object.values(obj).flatMap((arr) =>
    arr.filter((item) => item.status === "danger" || item.status === "warning")
  ).length;
}
function getDown(obj) {
  return Object.values(obj).flatMap((arr) => arr.filter((item) => item.status === "down")).length;
}
//筛选
function search() {
  // name 和 status 均为"空"值时
  if (data.search.name === "" && data.search.status === "all") {
    data.dataList = data.allList;
    return;
  }

  const filteredData = JSON.parse(JSON.stringify(data.allList));

  // name 不为空值，进行模糊匹配
  if (data.search.name != "") {
    Object.keys(data.allList).forEach((key) => {
      filteredData[key] = data.allList[key].filter(
        (item) => item.name.toLowerCase().includes(data.search.name.toLowerCase()) // 模糊匹配，忽略大小写
      );
    });
  }

  // status 不为空值，进行模糊匹配
  if (data.search.status != "all") {
    Object.keys(filteredData).forEach((key) => {
      filteredData[key] = filteredData[key].filter(
        (item) => item.status.toLowerCase().includes(data.search.status.toLowerCase()) // 模糊匹配，忽略大小写
      );
    });
  }

  data.dataList = filteredData;
}
// 切换搜索状态
function toogleSearch(status) {
  if (status == data.search.status) {
    data.search.status = "all";
    search();
  } else {
    data.search.status = status;
    search();
  }
}

function getColor(item) {
  if (item === 0) {
    return "color:#67C23A";
  } else if (1 < item && item < 4) {
    return "color:#E6A23C";
  } else if (3 < item) {
    return "color:#F56C6C";
  } else {
    return "color:#E6A23C";
  }
}
function getLength(obj) {
  return Object.values(obj).reduce((acc, arr) => acc + arr.length, 0);
}
function getServerNumber(obj) {
  return Object.values(obj)
    .flatMap((arr) => arr.map((item) => item.list)) // 获取每个对象中的 list 数组
    .flat() // 扁平化二维数组，变成一维
    .filter((item) => item.type === "服务器").length; // 过滤出 type 为 "服务器" 的项 // 返回数量
}
function getBussinessType(obj) {
  return Object.keys(obj).length;
}
function openDraw(item) {
  data.serverItem = item;
  serverDrawer.value = true;
}
function resetBusinessId() {
  businessID.value = 0;
}
function handleRequestCompleted() {
  getData();
}
function getServerCount(list) {
  return list.filter((item) => item.type === "服务器").length;
}

function businessLog() {
  getBusinessLog().then((res) => {
    if (res.status_code == 200) {
      ElMessage({
        message: res.message,
        type: "success",
      });
    } else {
      ElMessage.error(res.message);
    }
  });
}

//导入资源
function importExcelOfResource() {
  const fileInput = document.createElement("input");
  fileInput.type = "file";
  fileInput.accept = ".xlsx, .xls";
  fileInput.addEventListener("change", (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();
    reader.onload = (e) => {
      const datas = new Uint8Array(e.target.result);
      const workbook = xlsx.read(datas, { type: "array" });
      const serverSheet = workbook.Sheets[workbook.SheetNames[1]];
      const dbSheet = workbook.Sheets[workbook.SheetNames[2]];
      const serverData = xlsx.utils.sheet_to_json(serverSheet, {
        header: 1,
        blankrows: false,
      });
      const dbData = xlsx.utils.sheet_to_json(dbSheet, {
        header: 1,
        blankrows: false,
      });
      serverData.shift();
      dbData.shift();
      batchImportServerToUpdate(serverData);
      batchImportDatabaseToUpdate(dbData);
    };
    reader.readAsArrayBuffer(file);
  });
  fileInput.click();
}

//批量更新服务器信息
function batchImportServerToUpdate(serverList) {
  let serverInfoList = [];

  serverList.forEach((item) => {
    let serverInfo = {};
    serverInfo.server_id = 0;
    serverInfo.zabbix_id = 0;
    serverInfo.ip = item[0];
    serverInfo.system_type = item[1];
    serverInfo.server_name = item[2];
    serverInfo.username = item[3];
    serverInfo.password = item[4];
    if (item[1].toLowerCase() == "linux") {
      serverInfo.template_list = ["服务器-Linux-基础监控模板"];
      serverInfo.group_list = "内网linux服务器";
      serverInfo.port = "22";
    } else {
      serverInfo.template_list = ["服务器-Windows-基础监控模板"];
      serverInfo.group_list = "内网windows服务器";
      serverInfo.port = "5985";
    }
    serverInfoList.push(serverInfo);
  });

  batchImportServerInfoToUpdate(serverInfoList).then((res) => {
    if (res.status_code == 200) {
      ElMessage.success("服务器更新成功");
    }
  });
}

function batchImportDatabaseToUpdate(databaseList) {
  let dbInfoList = [];
  databaseList.forEach((item) => {
    let dbInfo = {};
    dbInfo.relation_id = 0;
    dbInfo.zabbix_id = 0;
    dbInfo.server_id = 0;
    dbInfo.software_id = 0;
    dbInfo.extra_parameters.databaseName = item[2];
    if (item[1].toLowerCase() == "oracle") {
      dbInfo.extra_parameters.group_name = "oracle数据库";
      dbInfo.extra_parameters.template_list = ["数据库-Oracle-基础监控模板"];
      dbInfo.configuration_group = [
        ["INSTANCE", "实例名", item[5]],
        ["USER", "用户名", "zlmonitor"],
        ["PASSWORD", "密码", "zlsoft2016"],
        ["PORT", "端口号", "1521"],
        ["NAME", "数据库名称", item[2]],
      ];
    } else {
      dbInfo.extra_parameters.group_name = "pg数据库";
      dbInfo.extra_parameters.template_list = ["数据库-PG-基础监控模板"];
      dbInfo.configuration_group = [
        ["INSTANCE", "数据库实例名", item[2]],
        ["URI", "数据库访问地址路径", `tcp://${item[0]}:5432`],
        ["USER", "用户名", "zlmonitor"],
        ["PASSWORD", "密码", "zlsoft2016"],
        ["PORT", "端口号", "5432"],
      ];
    }
    dbInfoList.push({
      type: "数据库",
      data_resource: dbInfo,
    });
  });
  batchImportDatabaseInfoToUpdate(dbInfoList).then((res) => {
    if (res.status_code == 200) {
      ElMessage.success("数据库更新成功");
    }
  });
}
</script>

<template>
  <div>
    <page-main class="main">
      <div class="overview-header pt-20px">
        <el-row :gutter="40">
          <el-col :span="4">
            <div class="overview-header-title">
              <div class="overview-header-first bg-[#076dba]"></div>
              <span class="overview-header-head">接入业务数量</span>
              <div class="overview-header-second">
                <span class="overview-number">{{ getLength(data.allList) }}</span>
                <span class="overview-descripe">个</span>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="overview-header-title">
              <div class="overview-header-first bg-[#f545b7]"></div>
              <span class="overview-header-head">产品分类</span>
              <div class="overview-header-second">
                <span class="overview-number">{{ getBussinessType(data.allList) }}</span>
                <span class="overview-descripe">种</span>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="overview-header-title">
              <div class="overview-header-first bg-[#cc64d3]"></div>
              <span class="overview-header-head">服务器数量</span>
              <div class="overview-header-second">
                <span class="overview-number">{{ getServerNumber(data.allList) }}</span>
                <span class="overview-descripe">个</span>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="overview-header-title cursor-pointer" @click="toogleSearch('success')" title="查看正常业务">
              <div class="overview-header-first bg-[#67c23a]"></div>
              <span class="overview-header-head">运行业务数量</span>
              <div class="overview-header-second">
                <span class="overview-number">{{ getSuccess(data.allList) }}</span>
                <span class="overview-descripe">个</span>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="overview-header-title cursor-pointer" @click="toogleSearch('warning')" title="查看故障业务">
              <div class="overview-header-first bg-[#e6a23c]"></div>
              <span class="overview-header-head">故障业务数量</span>
              <div class="overview-header-second">
                <span class="overview-number">{{ getWarning(data.allList) }}</span>
                <span class="overview-descripe">个</span>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="overview-header-title cursor-pointer" @click="toogleSearch('down')" title="查看停用业务">
              <div class="overview-header-first bg-[#909399]"></div>
              <span class="overview-header-head">停用业务数量</span>
              <div class="overview-header-second">
                <span class="overview-number">{{ getDown(data.allList) }}</span>
                <span class="overview-descripe">个</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-divider></el-divider>
      <div class="main-content">
        <el-button type="primary" @click="showDrawer = true">新增业务</el-button>
        <el-button type="primary" @click="data.showConsole = true" :icon="Plus">服务控制台导入</el-button>
        <el-button type="primary" @click="businessLog" :icon="Plus">业务日志采集</el-button>
        <el-button type="primary" @click="exportBusinessResource">导出业务资源</el-button>
        <el-button type="primary" @click="importExcelOfResource">导入资源</el-button>
        <el-popover placement="bottom" title="选择布局" :width="300" trigger="click">
          <template #reference>
            <el-button circle style="float: right" type="primary" :icon="Menu" />
          </template>
          <el-radio-group v-model="data.radio">
            <el-radio :value="3" :label="3">8个</el-radio>
            <el-radio :value="4" :label="4">6个</el-radio>
            <el-radio :value="6" :label="6">4个</el-radio>
            <el-radio :value="8" :label="8">3个</el-radio>
          </el-radio-group>
        </el-popover>
        <div class="search-contain" style="float: right">
          <div class="search">
            <el-select
              v-model="data.search.status"
              placeholder="请选择业务状态"
              style="width: 150px"
              @change="search()"
            >
              <el-option v-for="item in statusOption" :key="item" :label="item.label" :value="item.value" />
            </el-select>
            <el-input
              style="width: 250px; padding-left: 10px"
              v-model="data.search.name"
              placeholder="请输入业务名称进行查询"
              clearable
            ></el-input>
            <el-button type="primary" style="margin-left: 10px" @click="search()">查询</el-button>
          </div>
        </div>
        <div v-if="!isNoneData" v-for="(value, key) in data.dataList">
          <div style="padding-top: 10px">
            <div class="vertical-line"></div>
            <span class="sub-title">{{ key }}</span>
          </div>
          <el-row :gutter="10">
            <el-col class="div-row" :span="data.radio" v-for="(item, index) in value">
              <el-card class="custom-card" :id="'card' + key + index">
                <div slot="header" class="card-header flex justify-between">
                  <span class="title" :title="item.name">{{ item.name }}</span>
                  <div class="flex-shrink-0 flex">
                    <div
                      :class="
                        item.status === 'success'
                          ? 'status-circle'
                          : item.status === 'warning'
                          ? 'warning-circle'
                          : item.status === 'danger'
                          ? 'danger-circle'
                          : 'down-circle'
                      "
                    ></div>
                    <span class="descripe">
                      {{
                        item.status === "success"
                          ? "运行"
                          : item.status === "warning"
                          ? "故障"
                          : item.status === "danger"
                          ? "灾难"
                          : "停用"
                      }}
                    </span>
                  </div>
                </div>
                <hr class="hr-solid" />
                <div class="card-content" @click="openDraw(item)">
                  <img :src="imglogo" class="banner" />
                  <el-divider direction="vertical"></el-divider>
                  <div class="card-text">
                    <p class="problem" :style="getColor(item.problem)">问题：{{ item.problem }}</p>
                    <p class="module">服务器：{{ getServerCount(item.list) }}</p>
                  </div>
                </div>
                <div class="flex b-t-1 items-center pt-4px text-center text-12px b-t-[#d0d0d5]">
                  <div
                    class="b-r-1 b-r-gray w-33% text-center cursor-pointer hover:text-blue"
                    @click.stop="onClickWarningMonitior(item)"
                  >
                    查看
                  </div>
                  <div class="b-r-1 b-r-gray w-33% cursor-pointer hover:text-blue" @click="editBussiness(item)">
                    编辑
                  </div>
                  <div class="w-33% cursor-pointer hover:text-red" @click="onClickDelete(item)">删除</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        <div v-else>
          <el-empty :image-size="200" />
        </div>
      </div>
    </page-main>

    <!-- 业务资源详情抽屉 -->
    <BusinessResourceDetailsDrawer v-model="serverDrawer" :item="data.serverItem"></BusinessResourceDetailsDrawer>
    <!-- 创建业务抽屉 -->
    <CreateForm
      v-model="showDrawer"
      :businessId="businessID"
      @close-drawer="resetBusinessId"
      @requestCompleted="handleRequestCompleted"
    ></CreateForm>
    <ConsoleDialog v-model="data.showConsole" @requestCompleted="handleRequestCompleted"></ConsoleDialog>
  </div>
</template>

<style lang="scss" scoped>
.overview-header {
  margin-top: -20px;

  /* 垂直居中 */
  .overview-header-title {
    text-align: center;
    line-height: 40px;

    .overview-header-head {
      font-size: 18px;
      font-weight: bold;
      font-family: Arial, sans-serif;
    }

    .overview-header-first {
      display: inline-block;
      border-radius: 50%;
      width: 7px;
      height: 7px;
      margin-right: 10px;
      margin-bottom: 3px;
    }

    .overview-header-second {
      .overview-number {
        font-size: 24px;
        font-weight: bold;
        font-family: Arial, sans-serif;
      }

      .overview-descripe {
        font-size: 14px;
        padding-left: 5px;
        font-weight: bold;
        font-family: Arial, sans-serif;
      }
    }
  }
}

.title-content {
  border-radius: 10px;
  min-width: 300px;
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 14px;
  margin-bottom: 5px;
  padding: 10px;
}

.left-icon {
  width: 90px;
  height: 90px;
  margin-right: 40px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.right-overview {
  flex: 1;
}

.number {
  font-size: 35px;
  padding-right: 12px;
  font-weight: bold;
}

.descripe-title {
  letter-spacing: 1px;
  font-weight: bold;
  -webkit-font-smoothing: antialiased;
  font-family: arial;
  font-size: 18px;
  letter-spacing: 3px;
}

.right-overview h3 {
  font-size: 18px;
  margin-bottom: 10px;
}

.right-overview p {
  font-size: 14px;
  color: #999;
}

.main {
  // margin: 0px;
  // height: 869px;
  // overflow: auto;
}

.icon {
  margin-left: auto;
  cursor: pointer;
}

.custom-card {
  border-radius: 10px;
  // max-width: 300px;
  min-width: 170px;
}

.card-header {
  display: flex;
  align-items: center;
  line-height: 1px;
}

.vertical-line {
  border-left: 4px solid #337ecc;
  /* 2px 宽度的加粗直线，黑色 */
  height: 13px;
  /* 设置高度 */
  display: inline-block;
}

.sub-title {
  display: inline-block;
  /* 设置为内联块级元素 */
  margin-left: 5px;
  /* 添加一些左边距以分隔线和文本 */
  font-size: 18px;
  font-weight: bold;
}

.hr-solid {
  border: 0;
  border-top: 1px solid #d0d0d5;
}

.status-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #67c23a;
  margin-left: 10px;
  outline: 2px solid #d1edc4;
}

.warning-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #e6a23c;
  margin-left: 10px;
  outline: 2px solid #f3d19e;
}

.danger-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #f56c6c;
  margin-left: 10px;
  outline: 2px solid #fab6b6;
}

.down-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #909399;
  margin-left: 10px;
  outline: 2px solid #c8c9cc;
}

.card-content {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
  line-height: 5px;
  cursor: pointer;
}

.card-text {
  flex: 1;
  line-height: 10px;
  text-align: center;
}

.banner {
  position: relative;
  right: inherit;
  width: 17%;
  height: 17%;
  max-width: 100px;
  max-height: 50px;
}

.descripe {
  font-size: 14px;
  padding: 5px;
  color: #909399;
}

.problem {
  font-weight: bold;
  color: red;
  font-family: Arial, sans-serif;
  /* 设置为黑体字体，如果浏览器不支持黑体，会使用默认的sans-serif字体 */
}

.title {
  font-weight: bold;
  font-family: Arial, sans-serif;
  display: block;
  line-height: 25px;
  @include text-overflow;
}

:deep(.el-card__body) {
  padding: 10px;
}

:deep(.el-divider--horizontal) {
  margin: 12px 0;
}

.div-row {
  padding-top: 10px;
}

.module {
  font-weight: bold;
  // color: #409EFF;
  font-family: Arial, sans-serif;
  font-size: 15px;
}

.information {
  display: flex;
  height: 300px;

  .half-left {
    flex: 1;

    .warning-title {
      padding-left: 110px;
      font-weight: bold;
      font-family: Arial, sans-serif;
      font-size: 16px;
    }
  }

  .half-right {
    flex: 1;

    .warning-title {
      padding-left: 110px;
      font-weight: bold;
      font-family: Arial, sans-serif;
      font-size: 16px;
    }
  }
}

.scrollbar-demo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  text-align: center;
  border-bottom: 1px solid #c0c4cc;
}

.date-tag {
  margin-right: 10px;
}
</style>
