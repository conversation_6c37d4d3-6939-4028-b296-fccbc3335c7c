<script setup name="TemplateConfigurationMonitorTemplateList">
import eventBus from "@/utils/eventBus";
import { usePagination } from "@/utils/composables";
import FormMode from "./components/FormMode/index.vue";
import { GetZabbixTemplate, addZabbixTemplate } from "@/api/modules/zabbix_api_management/zabbix";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
// const route = useRoute()

const data = ref({
  loading: false,
  /**
   * 详情展示模式
   * router 路由跳转
   * dialog 对话框
   * drawer 抽屉
   */
  formMode: "router",
  dialogUpload: false,
  // 详情
  formModeProps: {
    visible: false,
    id: "",
  },
  allList: [],
  // 搜索
  search: {
    title: "",
  },
  // 批量操作
  batch: {
    enable: false,
    selectionDataList: [],
  },
  // 列表数据
  dataList: [],
});

onMounted(() => {
  getDataList();
  if (data.value.formMode === "router") {
    eventBus.on("get-data-list", () => {
      getDataList();
    });
  }
});

onBeforeUnmount(() => {
  if (data.value.formMode === "router") {
    eventBus.off("get-data-list");
  }
});

function getDataList() {
  data.value.loading = true;
  GetZabbixTemplate().then((res) => {
    data.value.loading = false;
    data.value.allList = res.data;
    paging();
  });
}
function paging() {
  let params = getParams();
  let result = data_Filter(data.value.allList, params);
  data.value.dataList = result.list;
  pagination.value.total = result.total;
}
function data_Filter(dataList, params) {
  let list = dataList;
  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });

  pageList.forEach((item) => {
    item.params = item.params;
  });
  return {
    list: pageList,
    total: list.length,
  };
}
// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.value.search.title == "") {
      paging();
    } else {
      search();
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.value.search.title == "") {
      paging();
    } else {
      search();
    }
  });
}

// 字段排序
function sortChange(prop, order) {
  onSortChange(prop, order).then(() => getDataList());
}

function onCreate() {
  data.value.dialogUpload = true;
}

function onEdit(row) {
  if (data.value.formMode === "router") {
    router.push({
      name: "editMonitorTemplate",
      params: {
        id: row.id,
      },
    });
  } else {
    data.value.formModeProps.id = row.id;
    data.value.formModeProps.visible = true;
  }
}

function onDel(row) {
  ElMessageBox.confirm(`确认删除「${row.title}」吗？`, "确认信息")
    .then(() => {
      api
        .post(
          "template_configuration/monitor_template/delete",
          {
            id: row.id,
          },
          {
            baseURL: "/mock/",
          }
        )
        .then(() => {
          getDataList();
          ElMessage.success({
            message: "模拟删除成功",
            center: true,
          });
        });
    })
    .catch(() => {});
}
function beforeUpload(file) {
  const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
  const whiteList = ["json"];
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElMessage.warning({
      message: "上传的文件只允许是json文件",
      center: true,
    });
    return false;
  }
}
function chooseFile(file) {
  // data.value.loading = true
  data.value.dialogUpload = false;
  var forms = new FormData();
  var configs = {
    headers: { "Content-Type": "multipart/form-data" },
  };
  forms.append("templateFile", file.file);
  const notification = ElNotification({
    title: "上传中",
    message: "文件正在上传中",
    position: "bottom-right",
    type: "info",
    duration: 0,
  });
  addZabbixTemplate(forms, configs)
    .then((res) => {
      if (res.data != "导入成功") {
        notification.close();
        ElNotification({
          title: "上传失败",
          message: "模板文件上传失败",
          position: "bottom-right",
          type: "error",
        });
      } else {
        notification.close();
        ElNotification({
          title: "上传成功",
          message: res.data,
          position: "bottom-right",
          type: "success",
        });
        getDataList();
      }
    })
    .catch(() => {
      notification.close();
      ElNotification({
        title: "文件上传出现异常",
        message: "文件上传出现异常，请查看后端接口",
        position: "bottom-right",
        type: "error",
      });
    });
}
function search() {
  if (data.value.search.title == "") {
    getDataList();
  } else {
    let keyword = data.value.search.title;
    let regex = new RegExp(keyword, "i");
    let params = getParams();
    let list = data_Filter(
      data.value.allList.filter((item) => regex.test(item.name)),
      params
    );
    data.value.dataList = list.list;
    pagination.value.total = list.total;
  }
}
</script>

<template>
  <div>
    <page-main title="监控模板管理">
      <div class="flex justify-between">
        <el-space wrap>
          <el-button type="primary" @click="onCreate">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            导入监控模板
          </el-button>
        </el-space>
        <el-space wrap>
          <div class="w-220px">
            <el-input
              v-model="data.search.title"
              placeholder="请输入名称，支持模糊查询"
              clearable
              @keyup.enter="search()"
            />
          </div>
          <el-button type="primary" @click="search()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>

      <!-- <batch-action-bar v-if="data.batch.enable" :data="data.dataList" :selection-data="data.batch.selectionDataList" @check-all="$refs.table.toggleAllSelection()" @check-null="$refs.table.clearSelection()">
                <el-button size="default">单个批量操作按钮</el-button>
                <el-button-group>
                    <el-button size="default">批量操作按钮组1</el-button>
                    <el-button size="default">批量操作按钮组2</el-button>
                </el-button-group>
            </batch-action-bar> -->
      <el-table
        ref="table"
        v-loading="data.loading"
        class="list-table"
        :data="data.dataList"
        border
        stripe
        highlight-current-row
        @sort-change="sortChange"
        @selection-change="data.batch.selectionDataList = $event"
      >
        <!-- <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed /> -->
        <el-table-column prop="templateid" label="模板id" />
        <el-table-column prop="name" label="名称" />
        <!-- <el-table-column label="操作" width="250" align="center" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" size="small" plain @click="onEdit(scope.row)">编 辑</el-button>
                        <el-button type="danger" size="small" plain @click="onDel(scope.row)">删 除</el-button>
                    </template>
                </el-table-column> -->
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="pagination"
        background
        @current-change="currentChange"
        @size-change="sizeChange"
      />
    </page-main>
    <el-dialog
      v-model="data.dialogUpload"
      title="上传"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="data.form"
        :rules="data.rules"
        label-position="left"
        label-width="120px"
        label-suffix="："
      >
        <el-form-item label="导入模板文件">
          <file-upload
            class="upload-demo"
            :max="1"
            :ext="['json']"
            :size="500"
            :auto-upload="true"
            type="file"
            accept=".json"
            action=""
            :before-upload="beforeUpload"
            :http-request="chooseFile"
          />
        </el-form-item>
      </el-form>
    </el-dialog>
    <FormMode
      v-if="['dialog', 'drawer'].includes(data.formMode)"
      :id="data.formModeProps.id"
      v-model="data.formModeProps.visible"
      :mode="data.formMode"
      @success="getDataList"
    />
  </div>
</template>

<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
}
</style>
