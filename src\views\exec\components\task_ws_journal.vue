<template>
  <el-drawer v-model="showDrawer" size="900px" title="执行日志">
    <div class="absolute top-18px left-120px">
      <el-button type="primary" size="small" @click="cleantask()">清除已完成记录</el-button>
      <el-button type="primary" size="small" @click="cleantask(true)">清除所有记录</el-button>
    </div>

    <div class="flex" v-if="data.taskList.length > 0">
      <!-- 左侧边栏 任务列表-->
      <div class="w-130px flex-shrink-0">
        <el-tabs tab-position="left" class="h-[calc(100vh-120px)] w-full" @tab-click="handleClick" v-model="activeName">
          <el-tab-pane :label="item.name" v-for="(item, index) in data.taskList" :key="index" :name="index">
            {{ item }}
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="h-[calc(100vh-120px)] overflow-auto ml-10px flex-1">
        <!-- 右侧 日志详情列表-->
        <div v-if="data.taskJournalList.length > 0" class="w-full whitespace-pre-wrap break-words">
          <div v-for="(item, index) in data.taskJournalList" :key="index">
            <div v-html="item.ansiHtml"></div>
            <br />
          </div>
        </div>
        <div v-else><el-empty description="暂无数据" /></div>
      </div>
    </div>
    <div v-else>
      <el-empty description="暂无数据" />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import useTaskJournalStore from "@/store/modules/taskJournal";
import { computed, onMounted, reactive } from "vue";

import { AnsiUp } from "ansi_up";
import { toRaw } from "vue";
import { EndFlagWord } from "@/utils/websocket";
const activeName = ref(0);

interface Prop {
  modelValue: boolean;
}
const props = withDefaults(defineProps<Prop>(), {
  modelValue: false,
});
const emits = defineEmits(["update:modelValue"]);

const showDrawer = computed({
  set: () => {
    emits("update:modelValue");
  },
  get: () => {
    return props.modelValue;
  },
});

const taskJournalStore = useTaskJournalStore();

interface TaskJournalList {
  content: string;
  time: string;
  ansiHtml: string;
}

const data = reactive({
  taskJournalList: [] as TaskJournalList[],
  taskList: [] as any,
});

const taskinterval = ref(null);

onMounted(() => {
  getTaskList();
});

/**
 * @description: 获取列表
 * @return {*}
 */
function getTaskList() {
  data.taskList = taskJournalStore.getTaskList;
  getStoreJournalList(activeName.value);
}

const currentTask = (index) => {
  return data.taskList[index] || {};
};

/**
 * @description: 左侧tab切换
 * @param {*} nav
 * @return {*}
 */
const handleClick = (tab, event) => {
  clearInterval(taskinterval.value);
  getStoreJournalList(tab.index);
};

/**
 * @description: 任务日志
 * @return {*}
 */
function getStoreJournalList(index) {
  data.taskJournalList = [];
  const currentTaskTemp = toRaw(currentTask(index));
  let dataList = [];
  dataList = taskJournalStore.getTaskJournalList(currentTaskTemp.wsUrl || "");
  // 对颜色代码进行转义
  const ansi_up = new AnsiUp();
  dataList.forEach((element) => {
    element.ansiHtml = ansi_up.ansi_to_html(element.content);
  });
  data.taskJournalList = dataList;
  loopGetTask(currentTaskTemp);
}

/**
 * @description: 轮询
 * @return {*}
 */
function loopGetTask(currentTaskTemp) {
  taskinterval.value = setInterval(() => {
    const dataListval = taskJournalStore.getTaskJournalList(currentTaskTemp.wsUrl || "");
    // 对颜色代码进行转义
    const ansi_up = new AnsiUp();
    dataListval.forEach((element) => {
      element.ansiHtml = ansi_up.ansi_to_html(element.content);
    });
    data.taskJournalList = dataListval;
    // 结束标志后清除定时器
    if (dataListval[dataListval.length - 1]?.content == EndFlagWord) {
      clearInterval(taskinterval.value);
    }
  }, 1000);
}

/**
 * clean已完成的作业日志
 * @description:
 * @return {*}
 */
function cleantask(isAll = false) {
  const taskList = taskJournalStore.getTaskList;
  let taskListTemp = [];
  taskList.forEach((item, index) => {
    // 移除已经完成的
    if (isAll) {
      taskJournalStore.removeWslogCache(item.wsUrl);
    } else {
      if (item.wsStatus == "finish") {
        taskJournalStore.removeWslogCache(item.wsUrl);
      } else {
        taskListTemp.push(item);
      }
    }
  });
  data.taskList = taskListTemp;
  taskJournalStore.editTask(taskListTemp);
  activeName.value = 0;
  getStoreJournalList(activeName.value);
}

onUnmounted(() => {
  clearInterval(taskinterval.value);
});
</script>
<style lang="scss" scoped>
:deep {
  .el-tabs--left .el-tabs__item.is-left,
  .el-tabs--right .el-tabs__item.is-left {
    justify-content: start;
    width: 100%;
    white-space: break-spaces;
    height: auto;
    margin-top: 8px;
  }
}
</style>
