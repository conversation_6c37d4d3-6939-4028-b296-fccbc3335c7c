<script setup name="IframeLayout">
import { GetGrafanaList } from "@/api/modules/screen_management/screen";
import { useRouter } from "vue-router";
import storage from "@/utils/storage";

const router = useRouter();

const options = [
  {
    value: 0,
    label: "关闭",
  },
  {
    value: 30000,
    label: "30秒",
  },
  {
    value: 60000,
    label: "1分钟",
  },
  {
    value: 120000,
    label: "2分钟",
  },
  {
    value: 300000,
    label: "5分钟",
  },
  {
    value: 900000,
    label: "15分钟",
  },
  {
    value: 1800000,
    label: "30分钟",
  },
  {
    value: 3600000,
    label: "1小时",
  },
];
let url = storage.local.get("VITE_APP_VISUALIZATION");
const data = reactive({
  autoplay: true,
  interval: 120000,
});
const dataList = ref([]);

onMounted(() => {
  getDataList();
});

/**
 * @description: 获取列表
 * @return {*}
 */
function getDataList() {
  GetGrafanaList().then((res) => {
    if (res.data == null || res.data.length == 0) {
      ElMessageBox.confirm("播放列表并未正确的配置成功，请在大屏可视化界面先进行配置", "警告", {
        confirmButtonText: "确定",
        showCancelButton: false,
        closeOnPressEscape: false,
        closeOnClickModal: false,
        showClose: false,
        type: "warning",
        center: true,
      }).then(() => {
        router.push({
          name: "player_visualization",
        });
      });
    } else {
      res.data.forEach((element) => {
        element.iframeurl = url + element.url + "?orgId=1" + "&kiosk=tv";
      });
      dataList.value = res.data;
    }
  });
}

/**
 * @description: 切换刷新间隔 如果是0那就关闭自动播放
 * @return {*}
 */
function changTime() {
  if (data.interval == 0) {
    data.autoplay = false;
  } else {
    data.autoplay = true;
  }
}
function change(index) {
  data.index = index;
  getData();
}
</script>

<template>
  <div>
    <div class="refresh-header items-center" ref="refresh">
      <div class="">自动切换间隔：</div>
      <el-select
        style="padding-right: 10px; width: 100px"
        v-model="data.interval"
        placeholder="刷新间隔"
        @change="changTime"
      >
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <router-link to="/data_analysis/player_visualization" class="mr-10px">
        <el-button>数字大屏配置</el-button>
      </router-link>
    </div>

    <div class="iframe" v-if="dataList.length > 0">
      <el-carousel :autoplay="data.autoplay" :interval="data.interval" height="100%">
        <el-carousel-item v-for="item in dataList" :key="item">
          <iframe :src="item.iframeurl" frameborder="0" importance="low" />
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.iframe,
iframe {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  padding-bottom: 32px;
}

.el-carousel {
  height: 100%;

  .el-carousel__container {
    height: 100%;
  }
  :deep {
    .el-carousel__button {
      background-color: var(--g-theme-color);
    }
  }
}
.refresh-header {
  display: flex;
  justify-content: flex-end;
}
</style>
