<script setup>
import { onMounted, reactive, ref, watch } from 'vue';
import api from "@/plugins/axios";
import { useAuth, usePagination } from "@/utils/composables";
const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();
import useDateTimeStore from "@/store/modules/datetime";
import { getUTCTimestampWithOffset } from "@/utils/dayjs";
import { pageChangeNum } from "@/views/homepage/components/utils";
import { getNginxLog } from "@/api/modules/nginx_management";
const emit = defineEmits(['resetGraph']);
const timeStore = useDateTimeStore();
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  overview: {
    type: Object,
    default: {},
  }
})

// 日志数据
const data = reactive({
  currentLog: '',
  logTitle: '',
  logTypes: [
    { name: 'error_log', label: '错误日志', path: '/var/log/nginx/error.log' }
  ],
  activeLog: 'error_log',
  searchQuery: '',
  dateRange: '',
  time: [],
  allList: [],
  dataList: [],
  loading: false,
  total: 0,
  page: 1,
  limit: 10,
  search: ''
})

// 初始化时间
data.time = [getUTCTimestampWithOffset(timeStore.begin), getUTCTimestampWithOffset(timeStore.over)];

// 监听时间变化
watch(timeStore.$state, (newValue, oldValue) => {
  data.time = [getUTCTimestampWithOffset(newValue.begin), getUTCTimestampWithOffset(newValue.over)];
  getDataList();
});

// 获取日志数据
function getDataList() {
  data.loading = true;
  let params = {
    start_time: data.time[0],
    end_time: data.time[1],
    ip: props.item.ip || ''
  }

  getNginxLog(params).then((res) => {
    let result = res.data;

    // 应用搜索过滤
    if (data.searchQuery) {
      const query = data.searchQuery.toLowerCase();
      result = result.filter((log) => log.message.toLowerCase().includes(query));
    }

    // 保存所有数据
    data.allList = result;

    // 使用封装的分页组件
    changePageNum(result);
  }).finally(() => {
    data.loading = false;
  });
}

// 分页处理
function changePageNum(lists) {
  let res = pageChangeNum(lists, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.searchQuery) {
      getDataList();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.searchQuery) {
      getDataList();
    } else {
      changePageNum(data.allList);
    }
  });
}


// 清除搜索
function clearSearch() {
  data.searchQuery = '';
  getDataList();
}

// 监听搜索和过滤变化
function handleSearchChange() {
  pagination.value.page = 1;
  getDataList();
}

onMounted(() => {
  getDataList();
});
</script>

<template>
  <div>
    <div class="dashboard-section">


      <!-- 搜索区域 -->
      <div class="filter-section">
        <el-input v-model="data.searchQuery" placeholder="搜索错误信息" clearable @input="handleSearchChange"
          @clear="clearSearch" class="search-input">
          <template #prefix>
            <el-icon><svg-icon name="ep:search" /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 错误日志表格 -->
      <el-table :data="data.dataList" style="width: 100%" border stripe highlight-current-row v-loading="data.loading">
        <el-table-column prop="time" label="时间" width="180" />
        <el-table-column prop="level" label="级别" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.level === 'error' ? 'danger' :
              scope.row.level === 'warn' ? 'warning' :
                scope.row.level === 'crit' ? 'danger' :
                  scope.row.level === 'alert' ? 'danger' : 'info'">
              {{ scope.row.level }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="错误信息" show-overflow-tooltip />
      </el-table>

      <!-- 分页 -->
      <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
        :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="paginationTable"
        background @size-change="sizeChange" @current-change="currentChange" />
    </div>
  </div>
</template>

<style scoped>
.dashboard-section {
  margin-bottom: 32px;
}

.log-type-selector {
  margin-bottom: 16px;
}

/* Section Header */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 24px 0 16px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  position: relative;
  padding-left: 12px;
}

.section-header h2::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #409eff;
  border-radius: 2px;
}

.filter-section {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.search-input {
  min-width: 250px;
  flex: 2;
}

.paginationTable {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filter-section {
    flex-direction: column;
  }

  .search-input {
    width: 100%;
  }
}
</style>
