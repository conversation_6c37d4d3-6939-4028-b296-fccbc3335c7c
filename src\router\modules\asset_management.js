const Layout = () => import("@/layout/index.vue");
const IframeLayout = () => import("@/layout/iframe.vue");
let children = [
  {
    path: "asset_screen",
    name: "asset_screen",
    component: () => import("@/views/resource_management/large_screen/large.vue"),
    meta: {
      title: "资产管理大屏",
      copyright: false,
      auth: ["admin", "asset_screen.browse"],
    },
  },
];

export default {
  path: "/resourceManagement",
  redirect: "/resourceCode/asset_screen",
  component: Layout,
  name: "resourceManagement",
  meta: {
    // auth: ['admin', "analysis.browse"],
    title: "资产管理",
    icon: "analysis",
    auth: ["admin", "resourceManagement.browse"],
  },
  children,
};
