export function convertTimestampToFullDateTime(timestamp) {
  // 将 Unix 时间戳转换为 Date 对象
  const date = new Date(timestamp * 1000);  // 转换为毫秒

  // 获取年月日并补零处理
  const month = String(date.getMonth() + 1).padStart(2, '0');  // 获取月份，补零
  const day = String(date.getDate()).padStart(2, '0');  // 获取日期，补零

  // 获取小时和分钟并补零处理
  const hours = String(date.getHours()).padStart(2, '0');  // 获取小时，补零
  const minutes = String(date.getMinutes()).padStart(2, '0');  // 获取分钟，补零

  // 返回 "MM/DD HH:MM" 格式的字符串
  return `${month}/${day} ${hours}:${minutes}`;
}