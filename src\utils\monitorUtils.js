import { showNotification } from "@/plugins/element-ui/index";
import { ElMessage, ElMessageBox } from "element-plus";
import { addMonitoring } from "@/api/modules/cmdb/resource";

/**
 * 一键全部安装zabbix-agent客户端
 * @param {*} allList
 * @returns
 */
export function allInstallAgent(allList, resourceType) {
  let list = [];
  list = allList.filter((item) => item.status == "offmonitor");
  if (list.length == 0) {
    ElMessage.error("请检查是否有未安装监控服务的设备");
    return;
  }
  let ip_information_list = [];
  list.forEach((item) => {
    ip_information_list.push({
      ip: item.ip,
      type: resourceType,
      group_name: item.group_name,
    });
  });
  addZabbix(ip_information_list);
}
/**
 * 执行安装监控函数
 * @param {*} params
 */
async function addZabbix(params) {
  console.log(2);
  // addMonitoring(params).then((res) => {
  //   console.log(3);
  //   console.log(res);
  //   result = res;
  // });
  // console.log("addZabbix:");
  // console.log(result);
  // return result;
  try {
    const result = await addMonitoring(params);
    console.log(3);
    console.log(result);
    return result;
  } catch (error) {
    // 处理错误
    console.error(error);
    throw error;
  }
}
/**
 * 单个主机资源安装监控
 * @param {*} item
 * @returns
 */
export async function oneInstallAgent(templateList, hostIp, resourceType, groupName) {
  console.log(1);
  if (templateList.length == 0) {
    ElMessage.error("数据库未配置监控模板，请配置！");
    return;
  }
  let ip_information_list = [
    {
      ip: hostIp,
      type: resourceType,
      group_name: groupName,
    },
  ];
  let params = {
    ip_information_list,
    resource_type_name: resourceType,
  };
  // return await addMonitoring(params);
  let result = await addMonitoring(params);
  // await addMonitoring(params).then((res) => {
  //   console.log(2);
  //   result = res;
  // });
  console.log(result);
  return result;
}
