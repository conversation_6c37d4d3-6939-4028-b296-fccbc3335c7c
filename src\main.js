import { createApp } from 'vue'
import App from './App.vue'
const app = createApp(App)
// 全局右键事件
import onContextMenu from "@/utils/contextmenus"
app.config.globalProperties.$onContextMenu = onContextMenu

import pinia from './store'
app.use(pinia)
import router from './router'
app.use(router)

import { useI18n } from './locales'
useI18n(app)

/* importElementPlusPlaceholder */
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
import ContextMenu from '@imengyu/vue3-context-menu'
app.use(ContextMenu)

// 自定义指令
import directive from '@/utils/directive'
directive(app)

// 错误日志上报
import errorLog from '@/utils/error.log'
errorLog(app)

// 加载 svg 图标
import 'virtual:svg-icons-register'

// 加载 iconify 图标
import { downloadAndInstall } from '@/iconify'
import icons from '@/iconify/index.json'
if (icons.useType === 'offline') {
  for (const info of icons.collections) {
    downloadAndInstall(info)
  }
}

// 全局样式
import 'uno.css'
import '@/assets/styles/globals.scss'
import storage from '@/utils/storage'
import useDateTimeStore from './store/modules/datetime';
const dateTimeStore = useDateTimeStore();
dateTimeStore.initializeOverTime();
// import { init as initApm } from '@elastic/apm-rum'

// var apm = initApm({

//   // 设置所需的服务名称（允许使用的字符：a-z、A-Z、0-9、-、_ 和空格）
//   serviceName: 'job',

//   // 设置定制 APM Server URL（默认值：http://localhost:8200）
//   serverUrl: storage.local.get('VITE_APM_SERVERURL'),

//   // 设置服务版本（源地图功能要求）
//   serviceVersion: '',

//   // 设置服务环境
//   environment: 'production'
// })
// app.use(apm)
// PWA
// import './pwa'
app.mount('#app')
