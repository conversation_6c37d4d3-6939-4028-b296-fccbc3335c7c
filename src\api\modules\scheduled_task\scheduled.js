import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取所有服务器等资源
 * @param {*} data
 * @returns
 */
export function getResource(data) {
  return api.post("/ansible_task/get_all_resource/", JSON.stringify(data));
}

/**
 *
 * @returns 获取所有的定时任务
 */
export function getAllScheduledTasks() {
  return api.get("/scheduled_task/get_all_scheduled_task/");
}

/**
 * 停止定时任务
 * @param {*} data
 * @returns
 */
export function stopScheduledTaskByName(function_name) {
  return api.get(`/scheduled_task/recover_scheduled_by_name/${function_name}`);
}

/**
 * 恢复定时任务
 * @param {*} data
 * @returns
 */
export function recoverScheduledTaskByName(function_name) {
  return api.get(`/scheduled_task/stop_scheduled_by_name/${function_name}`);
}
