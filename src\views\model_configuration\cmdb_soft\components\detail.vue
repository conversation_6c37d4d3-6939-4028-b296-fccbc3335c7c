<script setup>
import { cmdbGetSoftMessageById } from "@/api/modules/model_configuration/cmdb_soft";
import { computed, onMounted, reactive } from "vue";

const props = defineProps({
  soft_id: {
    type: Number,
    default: 0,
  },
});

const data = reactive({
  assetTitle: "",
  software_name: "",
  software_type: "",
  extra_params: [],
});
onMounted(() => {
  console.log(props.soft_id);
  if (props.soft_id != 0) {
    getSoftAsset();
  }
});
function getSoftAsset() {
  cmdbGetSoftMessageById(props.soft_id).then((res) => {
    data.software_name = res.data.software_name;
    data.software_type = res.data.software_type;
    res.data.configuration_group.forEach((item) => {
      const obj = {
        label: item[0],
        key: item[1],
        value: item[2],
      };
      data.extra_params.push(obj);
    });
  });
}
</script>
<template>
  <div>
    <el-divider content-position="left"><span class="font_css">基础信息</span></el-divider>
    <el-descriptions direction="vertical" :column="2" border>
      <el-descriptions-item label="软件名称">
        {{ data.software_name }}
      </el-descriptions-item>
      <el-descriptions-item label="软件类型">
        {{ data.software_type }}
      </el-descriptions-item>
    </el-descriptions>
    <el-divider content-position="left"><span class="font_css">配置项</span></el-divider>
    <el-table :data="data.extra_params" style="width: 100%">
      <el-table-column label="参数名称(CN)">
        <template #default="scope">
          <el-input v-if="scope.row.isEdit" v-model="scope.row.key" size="small" />
          <span v-else>{{ scope.row.key }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参数名称(EN)">
        <template #default="scope">
          <el-input v-if="scope.row.isEdit" v-model="scope.row.label" size="small" />
          <span v-else>{{ scope.row.label }}</span>
        </template>
      </el-table-column>

      <el-table-column label="示例参数">
        <template #default="scope">
          <el-input v-if="scope.row.isEdit" v-model="scope.row.value" size="small" />
          <span v-else>{{ scope.row.value }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<style scoped lang="scss">
.font_css {
  font-size: 20px;
  // font-family: SimSun;
  font-weight: bold;
  // padding-bottom: 10px;
}
</style>
