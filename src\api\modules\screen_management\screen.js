import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取grafana列表
 * @returns
 */
export function GetGrafana() {
  return api.get("/panel/grafana");
}

/**
 * 获取大屏播放顺序
 * @returns
 */
export function GetGrafanaList() {
  return api.get("/panel/playback");
}

/**
 * 获取面板选择器信息
 * @param {*} dashboard_uid
 * @returns
 */
export function GetGrafanaVar(dashboard_uid) {
  return api.patch("/panel/grafana" + "?dashboard_uid=" + dashboard_uid);
}

/**
 * 获取kibana面板信息
 * @returns
 */
export function GetKibana() {
  return api.get("/panel/kibana", {});
}

/**
 * 修改大屏播放顺序
 * @param {*} data
 * @returns
 */
export function SubmitGrafanaList(data) {
  return api.post("/panel/playback", { data: JSON.stringify(data) });
}
