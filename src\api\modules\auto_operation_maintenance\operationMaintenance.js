import api from "@/plugins/axios/index";
import axios from "axios";

/*************************************配置管理：运维功能包******************************************/
/**
 *获取所有的playbook功能包
 * @returns
 */
export function GetAllPlaybook() {
  return api.get("/ansible_task/function_pack_management");
}

/**
 * 发布功能包
 * @param {*} data
 * @returns
 */
export function UploadFunction(data) {
  return api.post("/ansible_task/function_pack_management", JSON.stringify(data));
}

/**
 * 取消发布功能包欸
 * @param {*} data
 * @returns
 */
export function CanclePublishPlaybook(object_id, data) {
  const param = {
    "operation": 'update',
    "value": data
  }
  return api.post(`/playbook_describe_model/${object_id}/`, JSON.stringify(param));
}

/**
 * 删除功能包
 * @param {*} data
 * @returns
 */
export function DeletePlaybook(data) {
  return api.post("/ansible_task/function_pack_management/del_playbook/", data);
}

/**
 * 上传功能包.zip
 * @param {*} params
 * @returns
 */
export function UploadFunctionFile(params) {
  return api.post("/ansible_task/function_pack_management/file", params);
}

/***********************************自动运维*********************************/
/**
 * 获取已经发布的功能包
 * @returns
 */
export function GetPublishPlaybook() {
  return api.get("/ansible_task/publish_management");
}

/**
 * 获取功能包配置参数
 * @param {*} data
 * @returns
 */
export function GetPlaybookParams(data) {
  return api.patch("/ansible_task/playbook", JSON.stringify(data));
}

/**
 * 提交执行任务
 * @param {*} data
 * @returns
 */
export function SumbitTask(data) {
  return api.post("/ansible_task/playbook", JSON.stringify(data));
}

/**
 * 获取历史执行结果
 * @param {*} data
 * @returns
 */
export function GetTaskHistory(data) {
  return api.post("/ansible_task/execution_history", JSON.stringify(data));
}

/**
 * 单个删除执行结果
 * @param {*} model_name
 * @param {*} data
 * @returns
 */
export function DeletTask(data) {
  const param = {
    "operation": 'delete',
    "value": data
  }
  return api.post(`/exec_ansible_task_model/${data}/`, param);
}

/**
 * 批量删除
 * @param {*} data 
 * @returns 
 */
export function batchDeleteTask(data) {
  const param = {
    "operation": 'delete',
    "value": data
  }
  return api.post(`/exec_ansible_task_model/`, param);
}
