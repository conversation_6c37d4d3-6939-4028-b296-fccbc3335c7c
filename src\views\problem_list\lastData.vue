<template>
  <div>
    <page-main :title="'最新数据'">
      <LastDataTableList
        :hostip="data.routeParam.hostip"
        :zabbix_id="data.routeParam.zabbix_id"
        :hostid="data.routeParam.hostid"
      ></LastDataTableList>
    </page-main>
  </div>
</template>
<script setup name="IframeLayout" lang="ts">
import LastDataTableList from "./components/last_data_table_list.vue";
const route = useRoute();
const data = reactive({
  routeParam: { hostip: "", zabbix_id: "", hostid: "" },
});
onBeforeMount(() => {
  data.routeParam = route.query;
});
onMounted(() => {
  // data.routeParam = route.query;
});
</script>

<style lang="scss" scoped>
.text-tag {
  ::v-deep span {
    @apply w-100px;
    @include text-overflow;
  }
}

.iframe,
iframe {
  position: absolute;
  z-index: 1;
  width: 100%;
  min-height: 750px;
}

.back-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

.green-block {
  width: 10px;
  /* 设置宽度 */
  height: 10px;
  /* 设置高度 */
  background-color: rgb(50, 182, 235);
  /* 设置背景颜色为绿色 */
  margin-right: 5px;
  /* 设置右侧间距 */
  clip-path: circle(50%);
  display: inline-block;
}

.data-wrapper {
  display: inline-block;
  //   padding-left: 30px;
}

.data-text {
  margin: 0;
  font-size: 16px;
  display: inline-block;
}

.unit-text {
  margin-top: 5px;
  display: inline-block;
}

.yello-block {
  width: 10px;
  /* 设置宽度 */
  height: 10px;
  /* 设置高度 */
  background-color: yellow;
  /* 设置背景颜色为绿色 */
  margin-right: 5px;
  /* 设置右侧间距 */
  clip-path: circle(50%);
  display: inline-block;
  margin-left: 20px;
}

.red-block {
  width: 10px;
  /* 设置宽度 */
  height: 10px;
  /* 设置高度 */
  background-color: red;
  /* 设置背景颜色为绿色 */
  margin-right: 5px;
  /* 设置右侧间距 */
  clip-path: circle(50%);
  display: inline-block;
  margin-left: 20px;
}
</style>
