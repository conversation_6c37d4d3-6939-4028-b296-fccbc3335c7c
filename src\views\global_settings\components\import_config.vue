<template>
  <div v-loading="formLoading">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-divider content-position="left">
          <el-switch inline-prompt v-model="consoleInfo.enableConsoleInfo" active-text="是" inactive-text="否" />
          <span class="setting_block_title">导入服务控制台数据</span>
        </el-divider>
        <el-card>
          <el-form
            :model="consoleInfo"
            label-width="auto"
            ref="consoleInfoFormRef"
            :rules="consoleInfoFormRules"
            v-show="consoleInfo.enableConsoleInfo"
          >
            <el-form-item label="IP" prop="ip">
              <el-input v-model="consoleInfo.ip" />
            </el-form-item>
            <el-form-item label="端口" prop="port">
              <el-input v-model="consoleInfo.port" />
            </el-form-item>
            <el-form-item label="账户" prop="username">
              <el-input v-model="consoleInfo.username" />
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input v-model="consoleInfo.password" />
            </el-form-item>
          </el-form>
          <div v-show="!consoleInfo.enableConsoleInfo">不导入服务器控制台数据.</div>
        </el-card>
      </el-col>
      <!-- <el-col :span="12">
        <el-divider content-position="left">
          <span class="setting_block_title">软件清单导入</span>
        </el-divider>
        <el-card>
          <div>
            <el-upload drag :http-request="uploadTxtfile" :show-file-list="false" accept=".txt" ref="uploadRef">
              <el-icon v-if="softfile == ''"><Plus /></el-icon>
              <span v-else>{{ softfile }}</span>

              <template #tip>
                <div class="el-upload__tip text-red">选择并上传 .txt 文件</div>
              </template>
            </el-upload>
          </div>
        </el-card>
      </el-col> -->
    </el-row>
  </div>
</template>
<script setup lang="ts">
// import { Plus } from "@element-plus/icons-vue";
// import { cmdbBatchImportSoftMessage } from "@/api/modules/model_configuration/cmdb_soft";
import { getBusinessFromConsole } from "@/api/modules/cmdb/business";

const consoleInfoFormRules = {
  ip: [{ required: true, message: "请输入服务控制台IP", trigger: "blur" }],
  port: [{ required: true, message: "请输入服务控制台端口", trigger: "blur" }],
  username: [{ required: true, message: "请输入服务控制台账户", trigger: "blur" }],
  password: [{ required: true, message: "请输入服务控制台密码", trigger: "blur" }],
};
const formLoading = ref(false);
const consoleInfo = reactive({
  enableConsoleInfo: true,
  ip: "",
  port: "",
  username: "",
  password: "",
});
// const softfile = ref();
// const isUpdateSoft = ref(false);
//导入软件清单
// function uploadTxtfile(options) {
//   console.log(options);
//   const { file } = options;
//   softfile.value = file.name;
//   let formData = new FormData();
//   formData.append("data", file);
//   cmdbBatchImportSoftMessage(formData)
//     .then((res) => {
//       isUpdateSoft.value = true;
//     })
//     .finally(() => {
//       isUpdateSoft.value = true;
//     });
// }
const emits = defineEmits(["next-step"]);
// 定义表单引用
const consoleInfoFormRef = ref(null);
async function saveConsoleConfig() {
  // // 判断是否导入软件清单
  // if (!isUpdateSoft.value) {
  //   ElMessage.warning("请上传软件清单");
  //   return;
  // }
  // 开启服务控制台
  if (consoleInfo.enableConsoleInfo) {
    // 这里是校验表单
    await consoleInfoFormRef.value.validate(async (valid) => {
      if (valid) {
        formLoading.value = true;
        await getBusinessFromConsole({ login_information_list: [consoleInfo] })
          .then((res) => {
            ElMessage.success("保存成功");
            emits("next-step");
          })
          .finally(() => {
            formLoading.value = false;
          });
      } else {
        ElMessage.error("请检查表单数据");
        return;
      }
    });
  } else {
    emits("next-step");
  }
}
defineExpose({ saveConsoleConfig, formLoading });
</script>

<style scoped></style>
