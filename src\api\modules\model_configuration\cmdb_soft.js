import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取所有软件
 * @returns
 */
export function cmdbGetAllSoftList() {
  //获取所有软件信息
  return api.get("/software/list");
}
/**
 * 通过id获取软件详情
 * @param {*} id
 * @returns
 */
export function cmdbGetSoftMessageById(id) {
  return api.get("/software" + "?software_id=" + id);
}
/**
 * 修改软件信息
 * @param {*} data
 * @returns
 */
export function cmdbUpdateSoftMessage(data) {
  //修改软甲信息
  return api.post("/software/update_cmdb_software/", JSON.stringify(data));
}
/**
 * 添加软件信息
 * @param {*} data
 * @returns
 */
export function cmdbAddSoftMessage(data) {
  //添加软件信息
  return api.post("/software", JSON.stringify(data));
}
/**
 * 批量删除软件信息
 * @param {*} data
 * @returns
 */
export function cmdbBatchDelSoftMessage(data) {
  //批量删除软件信息
  return api.post("/software/delete_cmdb_software/", data);
}
/**
 * 导出软件清单
 * @returns
 */
export function cmdbBatchExportSoftMessage() {
  return api.patch("/software/list");
}
/**
 * 导入软件清单
 * @param {*} params
 * @returns
 */
export function cmdbBatchImportSoftMessage(params) {
  //导入软件清单模板
  return api.post("/software/list", params);
}
/**
 * 根据软件类型获取软件列表
 * @param {*} software_type
 * @returns
 */
export function cmdbGetSoftListByType(software_type) {
  //获取软件信息
  return api.post("/software/get_software_name_list/", JSON.stringify(software_type));
}

/**
 * 新增集群时，获取分类类型
 * @returns
 */
export function getClustertype() {
  return api.get("/software/cluster");
}
