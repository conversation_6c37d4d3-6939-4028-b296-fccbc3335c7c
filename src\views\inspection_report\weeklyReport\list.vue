<route lang="yaml">
meta:
  enabled: false
</route>

<script setup>
import eventBus from "@/utils/eventBus";
import { queryEnvConfigInfo } from "@/api/modules/configuration/env_config";
import { getReportForSelectedInterval, pushSelectedWeeklyReports } from "@/api/modules/inspection_report/weeklyReport";
import { usePagination } from "@/utils/composables";
import { ref } from "@vue/reactivity";
import { ElMessageBox } from "element-plus";
import { onMounted } from "vue";
import { login_url } from "@/api/modules/user_api_management/users";
import { getEnvViteApiBaseurl } from "@/utils/axios";

import { pageChangeNum } from "@/views/homepage/components/utils";
import { MonthShortcuts } from "@/constants/element";

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
let baseURL = getEnvViteApiBaseurl();

const isPush = ref();

const data = ref({
  loading: false,
  /**
   * 详情展示模式
   * router 路由跳转
   * dialog 对话框
   * drawer 抽屉
   */
  formMode: "router",
  // 详情
  formModeProps: {
    visible: false,
    id: "",
  },
  // 搜索
  search: {
    dateRange: [],
    isSearch: false,
  },

  editTemplate: false,
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },

  // 列表数据
  tableList: [],
  allList: [],
});

onMounted(() => {
  queryData();
  getWeeklyConfig();
});

onBeforeUnmount(() => {
  if (data.value.formMode === "router") eventBus.off("get-data-list");
});
// 获取周报配置
function getWeeklyConfig() {
  queryEnvConfigInfo().then((res) => {
    isPush.value = res.data.weekly_config?.push_type;
  });
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.value.search.isSearch == true) {
      searchMonthFun();
    } else {
      paging(data.value.allList);
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    console.log(data.value.search.isSearch);
    if (data.value.search.isSearch == true) {
      searchMonthFun();
    } else {
      paging(data.value.allList);
    }
  });
}
// 分页
function paging(lists) {
  let result = pageChangeNum(lists, getParams());
  data.value.tableList = result.list;
  pagination.value.total = result.total;
}

// 搜索月份
function searchMonthFun() {
  if (data.value.search.dateRange) {
    data.value.search.isSearch = true;
    let list = [];
    const [startMonth, endMonth] = data.value.search.dateRange.map((month) => new Date(month));

    // 设置起始月的1号
    startMonth.setDate(1);
    // 设置结束月的最后一天
    endMonth.setMonth(endMonth.getMonth() + 1, 0);

    data.value.allList.filter((item) => {
      const testDate = new Date(item.time.split("->")[1]);
      console.log(testDate, startMonth, endMonth);
      if (testDate >= startMonth && testDate <= endMonth) {
        list.push(item);
      }
    });
    paging(list);
  } else {
    data.value.search.isSearch = false;
    queryData();
  }
}

//时间范围筛选周报
function queryData() {
  getReportForSelectedInterval().then((res) => {
    data.value.allList = res.data.report_result_list;

    let result = pageChangeNum(res.data.report_result_list || [], getParams());
    data.value.tableList = result.list;
    pagination.value.total = result.total;
  });
}

//查看周报PDF
function CheckWReport(row) {
  // console.log(row);
  if (data.value.formMode === "router") {
    router.push({
      name: "report_detail",
      query: row,
    });
  } else {
    data.value.formModeProps.id = row.id;
    data.value.formModeProps.visible = true;
  }
}

//批量推送周报
function batchPushReport() {
  //处理数据
  const pushFiles = [];
  for (let i = 0; i < data.value.batch.selectionDataList.length; i++) {
    pushFiles.push(data.value.batch.selectionDataList[i].name);
  }
  //请求url推送周报
  pushSelectedWeeklyReports(pushFiles).then((res) => {
    // console.log(res);
    if (res.state == "true") {
      ElMessageBox.alert("周报推送成功，请注意查收！", "提示", {
        confirmButtonText: "确认",
        type: "success",
        center: true,
      });
    }
  });
}

//批量下载周报
function batchDownloadReport() {
  ElMessageBox.alert("周报下载中请注意！", "提示", {
    confirmButtonText: "确认",
    type: "warning",
    center: true,
  });
  for (let i = 0; i < data.value.batch.selectionDataList.length; i++) {
    let filename = data.value.batch.selectionDataList[i].name;
    fetch(baseURL + login_url["download_pdf"] + "?filename=" + filename)
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        link.remove();
      })
      .catch((error) => {
        console.error("下载文件时发生错误：", error); // 显示错误消息或执行其他处理
      });
  }
}
</script>

<template>
  <div>
    <page-main title="周报管理">
      <!-- 批量操作功能按钮 -->
      <el-space wrap>
        <div>
          <el-date-picker
            v-model="data.search.dateRange"
            type="monthrange"
            unlink-panels
            range-separator="-"
            start-placeholder="开始月"
            end-placeholder="结束月"
            :shortcuts="MonthShortcuts"
            @change="searchMonthFun"
            format="YYYY-MM"
            value-format="YYYY-MM"
          />
        </div>
        <div>
          <el-button-group v-if="data.batch.enable">
            <el-button
              size="default"
              :disabled="!data.batch.selectionDataList.length"
              @click="batchPushReport()"
              v-if="isPush"
            >
              推送周报
            </el-button>
            <el-button size="default" :disabled="!data.batch.selectionDataList.length" @click="batchDownloadReport()">
              下载周报
            </el-button>
          </el-button-group>
        </div>
      </el-space>
      <el-table
        v-loading="data.loading"
        class="list-table"
        :data="data.tableList"
        border
        stripe
        highlight-current-row
        @selection-change="data.batch.selectionDataList = $event"
      >
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column prop="name" label="名称" align="center" />

        <el-table-column prop="time" label="所属时间" align="center" />

        <el-table-column label="操作" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" plain @click="CheckWReport(scope.row)">查看周报</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="pagination"
        background
        @current-change="currentChange"
        @size-change="sizeChange"
      />
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
}

.custom-notification {
  .el-notification__group {
    top: 50%;
    transform: translateY(-50%);
  }
}

p {
  color: red;
  font-size: 16px;
}
</style>
