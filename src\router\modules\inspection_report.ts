const Layout = () => import("@/layout/index.vue");

const children = [
  {
    path: "report_list",
    name: "report_list",
    component: () => import("@/views/inspection_report/weeklyReport/list.vue"),
    meta: {
      title: "周报管理",
      auth: ["admin", "report_list.browse"],
    },
  },
  {
    path: "report_detail",
    name: "report_detail",
    component: () => import("@/views/inspection_report/weeklyReport/detail.vue"),
    meta: {
      title: "查看周报",
      sidebar: false,
      activeMenu: "/inspection_report/report_list",
      auth: ["admin", "report_list.browse"],
    },
  },
  {
    path: "daily_list",
    name: "daily_list",
    component: () => import("@/views/inspection_report/dailyReport/list.vue"),
    meta: {
      title: "日报管理",
      auth: ["admin", "daily_list.browse"],
    },
  },
  {
    path: "daily_detail",
    name: "daily_detail",
    component: () => import("@/views/inspection_report/dailyReport/detail.vue"),
    meta: {
      title: "日报查看",
      copyright: false,
      sidebar: false,
      auth: ["admin", "daily_list.browse"],
    },
  },
  {
    path: "custom_inspection",
    name: "custom_inspection",
    component: () => import("@/views/inspection_report/custom_inspection/list.vue"),
    meta: {
      title: "自定义巡检",
      auth: ["admin", "custom_inspection.browse"],
    },
  },
];

export default {
  path: "/inspection_report",
  component: Layout,
  name: "inspection_report",
  redirect: "/inspection_report/report_list",
  meta: {
    auth: ["admin", "inspection_report.browse"],
    title: "巡检报告",
    icon: "configuration",
  },
  children,
};
