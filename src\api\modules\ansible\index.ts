import api from "@/plugins/axios";

/**
 * 工具类
 */
export function execAnsibleOperation(data) {
  // 单个函数执行
  return api.post("/ansible_task/exec_ansible_operation", { data: JSON.stringify(data) });
}

/**
 * 获取ansible 执行的日志
 * @returns
 */
export function getRunnerLog(data) {
  return api.get("/ansible_task/log", { params: data });
}

/**
 * 清除ansible日志
 * @returns
 */
export function clearLog(data) {
  return api.post("/ansible_task/log/clear_ansibl_runner_log/", data);
}
