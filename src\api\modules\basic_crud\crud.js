import api from "@/plugins/axios/index";

/**
 * 模型：['user_model', 'playbook_describe_model', 'exec_ansible_task_model', 'resource_type_model']
 */

/**
 * 通过Id获取模型详情 资源详情
 */
export function GetModelDetailById(id) {
  return api.get(`/resource_type_model/${id}/`);
}

/**
 * 获取模型列表信息：
 * @param {*} model
 * @returns
 */
export function GetModelListFilter(data) {
  return api.get("/resource/get?type=" + data);
}

/**
 * 通过id删除模型信息
 * @param {*} model
 * @param {*} id
 * @returns
 */
export function DeleteModelById(id) {
  const param = {
    "operation": 'delete',
    "value": id
  }
  return api.post(`/resource_type_model/${id}/`, param);
}
