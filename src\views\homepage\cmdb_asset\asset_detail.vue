<template>
  <div v-if="id !== ''">
    <div v-loading="store.listLoading" v-if="store.listLoading" class="h-400px"></div>
    <div v-for="(item, index) in assetDetails" :key="index" :id="item.unique">
      <H3 marker aligned marker-type="primary">{{ item.title }}</H3>
      <!-- 标准的描述列表 -->
      <div v-if="item.type == BlockTypeEnum.nomal">
        <el-descriptions direction="vertical" :column="8" border>
          <el-descriptions-item v-for="(desc, key) in item.data" :key="key" :label="desc.name_zh">
            <Tag :fieldname="key" :value="desc.value"></Tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 表格 -->
      <div v-if="item.type == BlockTypeEnum.table">
        <el-table
          :data="!!!item.data.tableData.length || item.data.tableData.length == 0 ? [] : item.data.tableData"
          style="width: 100%"
          border
          max-height="500"
          :header-cell-style="{ background: 'var(--el-color-primary-light-8)' }"
        >
          <el-table-column :label="itemtable" v-for="(itemtable, key) in item.data.tableColumn">
            <template #default="scoped">
              <TableColumn :columndata="scoped.row[key]" :columnname="key"></TableColumn>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 多维表格 -->
      <div v-if="item.type == BlockTypeEnum.nestedtable">
        <el-table
          :data="!!!item.data.tableData.length || item.data.tableData.length == 0 ? [] : item.data.tableData"
          style="width: 100%"
          border
          max-height="500"
          :header-cell-style="{ background: 'var(--el-color-primary-light-8)' }"
        >
          <el-table-column v-for="(itemtable, key) in item.data.tableColumn" :label="itemtable">
            <template #default="scoped">
              <template v-if="typeof scoped.row[key] == 'object' && scoped.row[key]['type'] == BlockTypeEnum.table">
                <el-table
                  :data="
                    !!!scoped.row[key].data.tableData.length || scoped.row[key].data.tableData.length == 0
                      ? []
                      : scoped.row[key].data.tableData
                  "
                  height="400px"
                  border
                >
                  <el-table-column
                    :label="itemtableC"
                    :prop="keyC"
                    v-for="(itemtableC, keyC) in scoped.row[key].data.tableColumn"
                  ></el-table-column>
                </el-table>
              </template>
              <template v-else>
                {{ scoped.row[key] }}
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
  <div v-else class="empty">请选择一个服务器进行点击查看！</div>
  <!-- 右侧锚点 -->
  <div class="fixed z-9999 right-20px bottom-60px flex flex-col items-center position">
    <el-button
      class="mb-10px ml-0"
      size="large"
      @click="pagePositioning(item.id)"
      circle
      v-for="item in getAnchorList"
      :key="item.id"
    >
      <template #icon>
        <el-tooltip class="box-item" effect="dark" :content="item.achorName" placement="left">
          <!-- <div class="w-20px">{{ item.achorName.substring(0, 4) }}</div> -->
          <el-icon size="30px">
            <svg-icon :name="getSvgIconName(item.id)"></svg-icon>
          </el-icon>
        </el-tooltip>
      </template>
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { H3 } from "vexip-ui";
import Tag from "./components/tag.vue";
import TableColumn from "./components/table_column.vue";
import { BlockTypeEnum, ResourceTypeEnum, SvgIconNameList, defaultSvgIconName } from "./components/constants";
import { getcmdbresourcedetail } from "@/api/modules/cmdb/resource";
import { cmdbGetServerAutoCmddbById } from "@/api/modules/cmdb/server";
import { ServerTypeEnum } from "@/constants/cmdb";

const assetDetails = ref();

const store = reactive({
  listLoading: false,
});
let id = computed({
  get: function () {
    return props.id;
  },
});
const props = defineProps<{
  type: string;
  id?: string;
  ip?: string;
}>();
watch(
  () => props.id,
  (newValue, oldValue) => {
    if (newValue) {
      getresourcedetail();
    }
  },
  { immediate: true }
);
// 获取资源详情数据 服务器和数据库的接口不一样的
function getresourcedetail() {
  let callingApi = getcmdbresourcedetail;
  let params = {};
  if (
    props.type == ResourceTypeEnum.server ||
    [ServerTypeEnum.linux, ServerTypeEnum.windows].includes(props.type.toLocaleLowerCase())
  ) {
    callingApi = cmdbGetServerAutoCmddbById;
    params.ip = props.ip;
  } else {
    params.id = props.id;
  }
  store.listLoading = true;
  callingApi(params)
    .then((res) => {
      assetDetails.value = res.data;
    })
    .finally(() => {
      store.listLoading = false;
    });
}

/**
 * 锚点跳转
 */
function pagePositioning(domId) {
  const dom = document.getElementById(domId);
  dom.scrollIntoView({ behavior: "smooth" });
}

// 获取锚点数据
const getAnchorList = computed(() => {
  let anchorList = [];
  for (const key in toRaw(assetDetails.value)) {
    const item = assetDetails.value[key];
    if (item.unique) {
      anchorList.push({ achorName: item.title, id: item.unique });
    }
  }
  return anchorList;
});

// 获取对应svgicon的名称
function getSvgIconName(name) {
  return SvgIconNameList[name] ? SvgIconNameList[name] : defaultSvgIconName;
}
defineExpose({ getresourcedetail });
</script>

<style scoped lang="scss">
.position {
  .el-button {
    margin-left: 0 !important;
  }
}

.empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  height: 400px;
  color: var(--el-text-color-placeholder);
}
</style>
