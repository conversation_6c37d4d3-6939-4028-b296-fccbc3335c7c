<script setup>
import Script from './script.vue'
import Backup from './backup.vue'
const activeName = ref('')
onMounted(() => {
  activeName.value = '脚本管理'
});

</script>
<template>
  <div>
    <page-main>
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="脚本管理" name="脚本管理">
          <Script v-if="activeName === '脚本管理'"></Script>
        </el-tab-pane>
        <el-tab-pane label="备份管理" name="备份管理">
          <Backup v-if="activeName === '备份管理'"></Backup>
        </el-tab-pane>
      </el-tabs>
    </page-main>
  </div>
</template>
<style scoped lang="scss">
.text {
  max-width: 80%;
  @include text-overflow;
}

.button_css {
  float: right;
  position: absolute;
  top: 20px;
  right: 10px;
}

.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70vh;
}

.page-content {
  flex-direction: column;
  align-items: center;
}

.form_css {
  margin-bottom: 1px;
}
</style>
