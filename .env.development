NODE_ENV = development
BaseIp = *************
# 页面标题
VITE_APP_TITLE = 一体化运维平台
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = http://$BaseIp/api
#kafka地址
VITE_KAFKA_BASEURL = http://$BaseIp/api
VITE_ROOM_BASEURL = http://$BaseIp/room
VITE_SSH_WEBSOCKET = ws://$BaseIp/webssh/
VITE_CMDB_WEBSOCKET = ws://$BaseIp/ws/scheduled_task_result/collect 
VITE_SCHEDULE_WEBSOCKET = ws://$BaseIp/ws/scheduled_task_result/task_result
# 是否开启代理
VITE_OPEN_PROXY = true
