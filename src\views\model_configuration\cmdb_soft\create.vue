<script setup name="HomepageCreate">
import { onMounted, reactive } from "vue";
import { uploadZipFile } from "@/api/modules/file";
import {
  cmdbGetSoftMessageById,
  cmdbUpdateSoftMessage,
  cmdbAddSoftMessage,
} from "@/api/modules/model_configuration/cmdb_soft";
import { showNotification } from "@/plugins/element-ui";
import { ElMessage } from "element-plus";

const router = useRouter();

const route = useRoute();

const formRef = ref();
const data = reactive({
  file: {},
  opt: "",
  title: "",
  isSoftAdd: false,
  isServerAdd: false,
  extra_params: [],
  extra_parameters: "",

  softForm: {
    software_name: "",
    software_type: "",
    id: 0,
    configuration_group: [],
  },
  forRules: {
    software_name: [{ required: true, message: "请输入软件名称", trigger: "blur" }],
    software_type: [{ required: true, message: "请选择软件类型", trigger: "blur" }],
  },
});

onMounted(() => {
  data.opt = route.query.opt;
  if (route.query.id) {
    data.title = "修改软件";
    getData();
  } else {
    data.title = "创建软件";
  }
});

function goBack() {
  router.go(-1);
}
function getData() {
  cmdbGetSoftMessageById(route.query.id).then((res) => {
    data.softForm = res.data;
    res.data.configuration_group.forEach((item) => {
      const obj = {
        label: item[0],
        key: item[1],
        value: item[2],
      };
      data.extra_params.push(obj);
    });
  });
}

//提交软件信息

function AddSoft() {
  formRef.value.validate((valid) => {
    if (valid) {
      let isTrue = true;
      if (data.extra_params.length != 0) {
        data.extra_params.forEach((item) => {
          for (const key in item) {
            if (item[key] === "" || item[key] === "待填写") {
              isTrue = false;
            }
          }
          item.label = item.label.trim();
          item.value = item.value.trim();
          item.key = item.key.trim();
        });
      }
      if (isTrue == true) {
        data.softForm.configuration_group = data.extra_params.map((item) => [item.label, item.key, item.value]);
        const apiFunction = route.query.id ? cmdbUpdateSoftMessage : cmdbAddSoftMessage;
        apiFunction(data.softForm).then((res) => {
          if (res.status_code == 200) {
            //需要处理没有上传文件时，跳过文件上传
            if (Object.keys(data.file).length != 0) {
              uploadZip(data.file);
            }
            goBack();
          }
        });
      } else {
        ElMessage.error(`添加失败，请填写配置项或确保参数不为空。`);
      }
    }
  });
}

let canAddExtraParam = computed(() => {
  return data.extra_params.every((item) => {
    return !item.isEdit;
  });
});

function addExtraParams() {
  data.extra_params.push({
    isEdit: true,
    label: "",
    key: "",
    value: "",
  });
}

function addParams(data) {
  data.isEdit = false;
  if (data.label == "" || data.key == "") {
    ElMessage.warning({
      message: "自定义字段不能置空",
      center: true,
    });
    data.label = "待填写";
    data.key = "待填写";
  }
}

function removeExtraParams(index, row) {
  data.extra_params.splice(index, 1);
}

function beforeUpload(file) {
  const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
  const whiteList = ["zip"];
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElMessage.warning({
      message: "上传的文件只允许是zip文件",
      center: true,
    });
    return false;
  }
}
//上传文件
function uploadZip(file) {
  let forms = new FormData();
  forms.append("zipFile", file.raw);
  const notification = showNotification("上传中", "文件正在上传中", "info");
  //上传文件接口
  uploadZipFile(forms)
    .then((res) => {
      notification.close();
      showNotification(res.message, res.data, "success");
    })
    .catch((res) => {
      notification.close();
      showNotification(res.message, res.data, "error");
    });
}
//监听文件组件，将文件内容复制给预设参数
function handleFileChange(file) {
  data.file = file;
  console.log(data.file);
}
</script>
<template>
  <div>
    <!-- 添加软件 -->
    <page-main :title="data.title">
      <div>
        <el-row type="flex" justify="center">
          <el-col :md="18" :sm="12">
            <el-form :model="data.softForm" label-width="150px" ref="formRef" :rules="data.forRules">
              <el-form-item label="软件分类" prop="software_type">
                <el-select
                  v-model="data.softForm.software_type"
                  filterable
                  allow-create
                  default-first-option
                  :reserve-keyword="false"
                >
                  <el-option label="数据库" value="数据库" />
                  <el-option label="中间件" value="中间件" />
                  <el-option label="业务应用" value="业务应用" />
                </el-select>
              </el-form-item>
              <el-form-item label="名称" prop="software_name">
                <el-input v-model="data.softForm.software_name" placeholder="请输入软件名称" />
              </el-form-item>
              <el-form-item label="参数">
                <el-table :data="data.extra_params" style="width: 100%">
                  <el-table-column label="名称(CN)">
                    <template #default="scope">
                      <el-input v-if="scope.row.isEdit" v-model="scope.row.key" size="small" />
                      <span v-else>{{ scope.row.key }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="名称(EN)">
                    <template #default="scope">
                      <el-input v-if="scope.row.isEdit" v-model="scope.row.label" size="small" />
                      <span v-else>{{ scope.row.label }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="示例参数">
                    <template #default="scope">
                      <el-input v-if="scope.row.isEdit" v-model="scope.row.value" size="small" />
                      <span v-else>{{ scope.row.value }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="200" align="center">
                    <template #default="scope">
                      <template v-if="scope.row.isEdit">
                        <el-button type="primary" plain size="small" @click="addParams(scope.row)">保存</el-button>
                      </template>
                      <template v-else>
                        <el-button type="primary" plain size="small" @click="scope.row.isEdit = true">编辑</el-button>
                        <el-popconfirm
                          title="是否要删除此行？"
                          style="margin-left: 10px"
                          @confirm="removeExtraParams(scope.$index, scope.row)"
                        >
                          <template #reference>
                            <el-button type="danger" plain size="small">删除</el-button>
                          </template>
                        </el-popconfirm>
                      </template>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button :disabled="!canAddExtraParam" style="margin-top: 20px; width: 100%" @click="addExtraParams">
                  <template #icon>
                    <el-icon>
                      <svg-icon name="i-ep:plus" />
                    </el-icon>
                  </template>
                  新增
                </el-button>
              </el-form-item>
              <el-form-item label="采集功能包(可选)">
                <file-upload
                  class="upload-demo"
                  :max="1"
                  :ext="['zip']"
                  :size="500"
                  :auto-upload="false"
                  type="file"
                  accept=".zip"
                  action=""
                  :before-upload="beforeUpload"
                  :http-request="uploadZip"
                  @change="handleFileChange"
                />
              </el-form-item>
            </el-form>
            <div class="submit_button">
              <el-button type="primary" @click="AddSoft">添加</el-button>
              <el-button type="primary" @click="goBack">取消</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
.line {
  max-width: 5%;
}

.fixed-textarea textarea {
  height: 200px;
  /* 设置合适的高度值 */
}
.submit_button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
