<script setup>
import { usePagination } from "@/utils/composables";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
import { ArrowDown, Remove, Location, Pointer } from "@element-plus/icons-vue";
import { addZabbixForResource, showConfirmationDialog } from "../components/utils";
import MoreOperations from "../components/more_operations.vue";
const data = reactive({
  search: {
    searchName: "",
    isSearch: false,
  },
  batch: {
    enable: true,
    selectionDataList: [],
  },
  form: {
    name: "",
    ip: "",
    group: "",
    template: "",
  },
  allList: [],
  dataList: [],
  obj: {},
});
onMounted(() => {
  getData();
});
const dialogVisible = ref(false);
const drawer = ref(false);
function getData() {
  let list = [
    {
      name: "存储**************",
      status: "online",
      group: "存储设备",
      ip: "**************",
      power: "success",
      total: 50,
      use: 35,
      time: "120天",
      template: "SNMP Oracle SL3000",
      CPU: "2%",
    },
    {
      name: "存储**************",
      status: "online",
      group: "存储设备",
      ip: "**************",
      power: "success",
      total: 30,
      use: 19,
      time: "47天",
      template: "Lenovo EMC PX4-300d",
      CPU: "5%",
    },
    {
      name: "存储**************",
      status: "online",
      group: "存储设备",
      ip: "**************",
      power: "success",
      total: 10,
      use: 9,
      time: "35天",
      template: "HP MSA",
      CPU: "6%",
    },
    {
      name: "存储**************",
      status: "off",
      group: "存储设备",
      ip: "**************",
      power: "off",
      total: 0,
      use: 0,
      time: "0天",
      template: "SNMP Oracle SL3000",
      CPU: "0%",
    },
    {
      name: "存储**************",
      status: "online",
      group: "存储设备",
      ip: "**************",
      power: "success",
      total: 1,
      use: 0.2,
      time: "69天",
      template: "IBM S 7l",
      CPU: "5%",
    },
  ];
  data.allList = list;
  paging();
}

function data_Filter(dataList, params) {
  let list = dataList;

  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });

  pageList.forEach((item) => {
    item.params = item.params;
  });
  return {
    list: pageList,
    total: list.length,
  };
}
// 每页数量切换
function paging() {
  let params = getParams();
  let dataList = data_Filter(data.allList, params);
  data.dataList = dataList.list;
  pagination.value.total = dataList.total;
}
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      queryData();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）

function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      queryData();
    } else {
      changePageNum(data.allList);
    }
  });
}
function createInterface() {
  router.push({
    path: "/cmdb/interfaceInformation/createInterface",
  });
}
function editInterface(row) {
  (data.form.name = row.name),
    (data.form.ip = row.ip),
    (data.form.template = row.template),
    (data.form.group = row.group);
  dialogVisible.value = true;
}
//筛选数据
function queryData() {
  if (data.search.searchName == "") {
    data.search.isSearch = false;
    getData();
    return;
  }
  let list = [];
  const searchStr = data.search.searchName.trim();
  const ipRegex = /^[\d.]+$/;
  if (ipRegex.test(searchStr)) {
    list = data.allList.filter((item) => {
      return item.ip.trim().indexOf(searchStr) != -1;
    });
  } else {
    list = data.allList.filter((item) => {
      return item.name.trim().toLowerCase().indexOf(searchStr.toLowerCase()) !== -1;
    });
  }
  data.search.isSearch = true;
  changePageNum(list);
}
//批量删除
function batchDel() {
  data.batch.selectionDataList.forEach((element) => {
    data.dataList = removeObjectFromArray(data.dataList, element);
  });
}
function removeObjectFromArray(arr, obj) {
  return arr.filter((item) => item !== obj);
}
//单个删除
function oneDel(id) {
  console.log(id);
  let list = [{ relation_id: 0, zabbix_id: id }];
  delFunc(list);
}
//安装单个监控
function installZabbix(item) {
  addZabbix([item]);
}
//批量安装
function allInstallZabbix() {
  let list = [];
  list = data.dataList.filter((item) => item.status == "offmonitor");
  if (list.length == 0) {
    ElMessage.error("请检查是否有未安装监控服务的设备");
    return;
  }
  addZabbix(ip_information_list);
}

//安装agent
async function addZabbix(list) {
  let ip_information_list = [];
  list.forEach((item) => {
    ip_information_list.push({
      ip: item.ip,
      type: "service",
      group_name: item.group_name,
      id: item.zabbix_id
    });
  });
  let params = {
    ip_information_list,
  };
  const confirm = await showConfirmationDialog();
  if (confirm) {
    zabbixLoading.value = true;
    zabbixResultDialog.value = true;
    try {
      zabbixResultData.value = await addZabbixForResource(params);
    } catch (error) { }
    zabbixLoading.value = false;
    getData();
  } else {
    ElMessage.info("已取消监控安装!");
  }
}

function colors(item) {
  if (item <= 50) {
    return "success";
  } else if (item > 50 && item <= 80) {
    return "warning";
  } else if (item > 80) {
    return "exception";
  } else {
    return "success";
  }
}
function getDisk(total, use) {
  const totalValue = parseFloat(total);
  const remainingValue = parseFloat(use);
  const percentage = (remainingValue / totalValue) * 100;
  return parseInt(isNaN(percentage) ? 0 : percentage.toFixed(0));
}
const deleteRow = (index) => {
  data.dataList.splice(index, 1);
};
function addDialogVisible() {
  (data.form.name = ""),
    (data.form.group = ""),
    (data.form.template = ""),
    (data.form.ip = ""),
    (dialogVisible.value = true);
}
function look(item) {
  drawer.value = true;
  data.obj = item;
}
</script>
<template>
  <div>
    <page-main title="存储设备">
      <div class="flex justify-between">
        <el-space wrap>
          <el-button type="primary" @click="addDialogVisible">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            新增
          </el-button>
          <el-button type="primary" @click="allInstallZabbix">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:coordinate" />
              </el-icon>
            </template>
            一键安装所有
          </el-button>

          <el-button type="danger" :disabled="!data.batch.selectionDataList.length" @click="batchDel">
            批量删除
          </el-button>
        </el-space>
        <el-space wrap>
          <div class="w-220px">
            <el-input v-model="data.search.searchName" placeholder="输入ip或名称,支持模糊查询" clearable @keyup.enter="queryData" />
          </div>
          <el-button type="primary" @click="queryData()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>

      <el-table border class="list-table" stripe highlight-current-row :data="data.dataList"
        @selection-change="data.batch.selectionDataList = $event">
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column label="服务名称" prop="name" align="center" />
        <el-table-column label="IP" prop="ip" align="center" />
        <el-table-column width="75px" label="CPU" prop="CPU" align="center"></el-table-column>
        <el-table-column label="模板" prop="template" align="center">
          <template #default="scoped">
            <el-tag>{{ scoped.row.template }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="电源状态" width="85px" align="center">
          <template #default="scoped">
            <el-tag v-if="scoped.row.power === 'success'" type="success">正常</el-tag>
            <el-tag v-else type="danger">关闭</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="磁盘空间(已使用/总共)" align="center">
          <template #default="scoped">
            <el-progress :text-inside="true" :stroke-width="24" :percentage="getDisk(scoped.row.total, scoped.row.use)"
              :status="colors(getDisk(scoped.row.total, scoped.row.use))">
              <span>{{ `${getDisk(scoped.row.total, scoped.row.use)}%,共${scoped.row.total}T` }}</span>
            </el-progress>
          </template>
        </el-table-column>
        <el-table-column label="监控状态" width="85px" align="center">
          <template #default="scoped">
            <el-tag v-if="scoped.row.status == 'online'">已启用</el-tag>
            <el-tag v-else-if="scoped.row.status == 'offmonitor'" type="info" effect="dark">未监控</el-tag>
            <el-tag v-else type="danger">停用的</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="启动时间" width="85px" align="center" prop="time"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scoped">
            <el-button @click="editInterface(scoped.row)" plain type="primary">编辑</el-button>
            <MoreOperations>
              <el-dropdown-menu>
                <el-dropdown-item v-if="scoped.row.status == 'offmonitor'" :icon="Location"
                  @click="installZabbix(scoped.row)">
                  安装监控
                </el-dropdown-item>
                <el-dropdown-item :icon="Pointer" @click="look(scoped.row)">查看</el-dropdown-item>
                <el-dropdown-item :icon="Remove" @click.prevent="deleteRow(scoped.$index)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </MoreOperations>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
        :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="paginationTable"
        background @size-change="sizeChange" @current-change="currentChange" />
    </page-main>
    <el-dialog v-model="dialogVisible" title="新增存储设备" width="500" :destroy-on-close="true">
      <el-form :model="data.form" label-width="auto" style="max-width: 400px">
        <el-form-item label="存储名称">
          <el-input v-model="data.form.name" />
        </el-form-item>
        <el-form-item label="IP">
          <el-input v-model="data.form.ip" />
        </el-form-item>
        <el-form-item label="分组">
          <el-input v-model="data.form.group" />
        </el-form-item>
        <el-form-item label="存储模板">
          <el-select v-model="data.form.template" placeholder="请选择模板">
            <el-option label="SNMP Oracle SL3000" value="SNMP Oracle SL3000" />
            <el-option label="IBM S 7l" value="IBM S 7l" />
            <el-option label="HP MSA" value="HP MSA" />
            <el-option label="Lenovo EMC PX4-300d" value="Lenovo EMC PX4-300d" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogVisible = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-drawer size="70%" v-model="drawer" :title="data.obj.name">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-card>
            <template #header><span>健康状态</span></template>
            <template #default>
              <div class="body-card">
                <div class="status">
                  <span class="name">Controller 0A</span>
                  <span class="statu"><el-tag type="success">正常</el-tag></span>
                </div>
                <div class="status">
                  <span class="name">Controller 0B</span>
                  <span class="statu"><el-tag type="success">正常</el-tag></span>
                </div>
              </div>
            </template>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <template #header><span>总容量</span></template>
            <template #default>
              <div class="total">
                <span class="name">{{ data.obj.total }}</span>
                <span class="statu">TIB</span>
              </div>
            </template>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <template #header><span>已使用容量</span></template>
            <template #default>
              <div class="total">
                <span class="name">{{ data.obj.use }}</span>
                <span class="statu">TIB</span>
              </div>
            </template>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="10" style="padding-top: 20px">
        <el-col :span="12">
          <el-card>
            <template #header><span>CPU-存储</span></template>
            <template #default>
              <div class="dashboard">
                <el-progress type="dashboard" :percentage="7">
                  <template #default="{ percentage }">
                    <span class="percentage-value">{{ percentage }}%</span>
                    <span class="percentage-label">Controller 0A</span>
                  </template>
                </el-progress>
                <el-progress type="dashboard" :percentage="7">
                  <template #default="{ percentage }">
                    <span class="percentage-value">{{ percentage }}%</span>
                    <span class="percentage-label">Controller 0B</span>
                  </template>
                </el-progress>
              </div>
            </template>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header><span>内存-存储</span></template>
            <template #default>
              <div class="dashboard">
                <el-progress type="dashboard" :percentage="64">
                  <template #default="{ percentage }">
                    <span class="percentage-value">{{ percentage }}%</span>
                    <span class="percentage-label">Controller 0A</span>
                  </template>
                </el-progress>
                <el-progress type="dashboard" :percentage="64">
                  <template #default="{ percentage }">
                    <span class="percentage-value">{{ percentage }}%</span>
                    <span class="percentage-label">Controller 0B</span>
                  </template>
                </el-progress>
              </div>
            </template>
          </el-card>
        </el-col>
      </el-row>
    </el-drawer>
  </div>
</template>
<style scoped>
.status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.body-card {
  height: 70px;
}

.name {
  font-size: 30px;
  font-weight: bold;
  font-family: "黑体", "SimHei", "STHeiti", "Microsoft JhengHei", sans-serif;
}

.statu {
  font-size: 16px;
  font-weight: bold;
  font-family: "黑体", "SimHei", "STHeiti", "Microsoft JhengHei", sans-serif;
  padding-left: 20px;
}

.total {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70px;
}

.dashboard {
  display: flex;
  justify-content: space-around;
}

.percentage-value {
  display: block;
  margin-top: 10px;
  font-size: 28px;
}

.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}

.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}

.demo-progress .el-progress--circle {
  margin-right: 15px;
}
</style>
