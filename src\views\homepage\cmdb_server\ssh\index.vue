<script setup name="ResourceListServerInformationIndex">
import "xterm/css/xterm.css";
import { Terminal } from "xterm";
import { Toast } from "@/plugins/element-ui";

const route = useRoute();

const xtermRef = ref(null);
onBeforeUnmount(() => {
  socket.close();
});

const message = { status: 0, data: null, cols: null, rows: null };

function get_term_size() {
  var init_width = 9;
  var init_height = 17;

  var windows_width = window.innerWidth;
  var windows_height = window.innerHeight;

  return {
    cols: Math.floor(windows_width / init_width),
    rows: Math.floor(windows_height / init_height),
  };
}

const term = new Terminal({
  parent: document.getElementById("xterm"),
  fontSize: 14,
  cursorBlink: true,
  cols: get_term_size()["cols"],
  rows: get_term_size()["rows"],
  useStyle: true,
  cursorBlink: true,
});

const user = getCookie();
function getCookie() {
  const value = document.cookie.split(";").map((cookie) => cookie.trim());
  console.log(value);
  let userdata;
  value.forEach((item) => {
    console.log(item);
    if (item.startsWith("userdata=")) {
      userdata = item.substring("userdata=".length);
    }
  });
  return userdata;
}
const socket = new WebSocket(
  `${import.meta.env.VITE_SSH_WEBSOCKET}?ip=${route.query.ip}&width=${get_term_size().cols}&height=${
    get_term_size().rows
  }&verify_code=${user}`
);
socket.onopen = () => {
  term.open(document.getElementById("xterm"));
  message["status"] = 0;
  message["data"] = "";
  var send_data = JSON.stringify(message);
  try {
    socket.send(send_data);
  } catch (error) {
    Toast.warning("连接出现异常，即将关闭");
  }
};

socket.onmessage = (recv) => {
  var data = JSON.parse(recv.data);
  var message = data.message;
  var status = data.status;
  if (status === 0) {
    term.write(message);
  } else {
    ElMessage.error(message);
    socket.close();
  }
};

term.onData((data) => {
  message["status"] = 0;
  message["data"] = data;
  var send_data = JSON.stringify(message);
  try {
    socket.send(send_data);
  } catch (error) {
    Toast.warning("连接出现异常，即将关闭");
  }
});
</script>

<template>
  <div>
      <div>
        <div id="xterm" ref="xtermRef"></div>
      </div>
  </div>
</template>

<style lang="scss" scoped>
// scss
</style>
