<script setup>
import useSettingsStore from "@/store/modules/settings";
import { onMounted, reactive } from "vue";
import { GetZabbixTemplate } from "@/api/modules/zabbix_api_management/zabbix";
import { cmdbGetServerMessageById, cmdbAddServerMessage, cmdbUpdateServerMessage } from "@/api/modules/cmdb/server";
import { validateIP } from "./utils";

const router = useRouter();
const route = useRoute();
const formRef = ref();
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(["closeDialog", "getResource", "update:modelValue"]);
const data = reactive({
  extra_params: [],
  serverForm: {
    ip: "", //ip
    server_name: "", //服务器名称
    system_type: "", //系统类型
    port: "", //系统端口
    username: "", //账户
    password: "", //密码
    group_list: "内网服务器", //分组
    template_list: [],
    server_id: 0,
    zabbix_id: 0,
  },

  cloneButton: false,
  disableSelect: false,
  createPage: false,
  submitLoading: false,
  forRules: {
    ip: [{ required: true, message: "请输入正确的服务器IP", trigger: "blur", validator: validateIP }],
    server_name: [{ required: true, message: "请输入服务器名", trigger: "blur" }],
    system_type: [{ required: true, message: "请选择服务器类型", trigger: "blur" }],
    port: [{ required: true, message: "请输入服务器端口", trigger: "blur" }],
    username: [{ required: true, message: "请输入服务器账户", trigger: "blur" }],
    password: [{ required: true, message: "请输入服务器密码", trigger: "blur" }],
    group_list: [{ required: true, message: "请选择服务器分组", trigger: "blur" }],
  },
});

onMounted(() => {
  if (props.modelValue != 0) {
    data.disableSelect = true;
    data.cloneButton = true;
    getData();
  } else {
    data.createPage = true;
  }
  getTemplate();
});
function getData() {
  cmdbGetServerMessageById(props.modelValue).then((res) => {
    data.serverForm = res.data;
  });
}
function getTemplate() {
  GetZabbixTemplate().then((res) => {
    data.template_list = filterTemp(res.data);
  });
}
function filterTemp(list) {
  return list.filter((item) => item.name.includes("服务器-"));
}

function systemChange(val) {
  if (val == "Windows") {
    data.serverForm.port = "5985";
    data.serverForm.template_list = ["服务器-Windows-基础监控模板"];
  } else {
    data.serverForm.port = "22";
    data.serverForm.template_list = ["服务器-Linux-基础监控模板"];
  }
  data.serverForm.group_list = `内网${val.toLowerCase()}服务器`;
}

function addServer() {
  formRef.value.validate((valid) => {
    if (!valid) return;
    data.submitLoading = true;
    try {
      console.log(props.modelValue);
      //判断是否修改操作，后续选择执行函数
      const isUpdate = props.modelValue !== 0;
      const apiFunc = isUpdate ? cmdbUpdateServerMessage : cmdbAddServerMessage;
      apiFunc(data.serverForm).then((res) => {
        if (res.status_code == 200) {
          emit("getResource");
          returnPrePage();
        }
      });
    } finally {
      data.submitLoading = false;
    }
  });
}

function returnPrePage() {
  emit("closeDialog");
}

//克隆主机信息,重新对id赋值
function cloneHostInfo() {
  emit("update:modelValue", 0);
  console.log(props.modelValue);
  data.serverForm.zabbix_id = 0;
  data.serverForm.server_id = 0;
  data.cloneButton = false;
  data.disableSelect = false;
}
</script>
<template>
  <div>
    <el-row type="flex" justify="center">
      <el-col>
        <el-form :model="data.serverForm" label-width="100px" ref="formRef" :rules="data.forRules" style="margin: 20px">
          <el-form-item label="IP" prop="ip">
            <el-input v-model="data.serverForm.ip" placeholder="请输入服务器IP" :disabled="data.disableSelect" />
          </el-form-item>
          <el-form-item label="名称" prop="server_name">
            <el-input v-model="data.serverForm.server_name" placeholder="请输入服务器名称" />
          </el-form-item>
          <el-form-item label="系统类型" prop="system_type">
            <el-select
              v-model="data.serverForm.system_type"
              placeholder="请选择系统类型"
              @change="systemChange($event)"
            >
              <el-option label="Linux" value="Linux" />
              <el-option label="Windows" value="Windows" />
            </el-select>
          </el-form-item>
          <el-form-item label="系统端口" prop="port">
            <el-input v-model="data.serverForm.port" placeholder="请输入服务器的连接端口" />
          </el-form-item>

          <el-form-item label="登录账户" prop="username">
            <el-input v-model="data.serverForm.username" placeholder="请输入账户" />
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input type="password" show-password v-model="data.serverForm.password" placeholder="请输入密码" />
          </el-form-item>
          <el-form-item label="群组" prop="group_list">
            <el-input v-model="data.serverForm.group_list" placeholder="多个分组用“,”隔开，例如:内网服务器,Linux" />
          </el-form-item>
          <el-form-item label="监控模板" prop="template_list">
            <el-select v-model="data.serverForm.template_list" filterable multiple placeholder="请选择监控模板">
              <el-option
                v-for="(item, index) in data.template_list"
                :label="item.name"
                :value="item.name"
                :key="index"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="submit_button">
          <el-button type="primary" @click="addServer" :loading="data.submitLoading">提交</el-button>
          <el-button @click="cloneHostInfo" v-if="data.cloneButton" type="primary" plain>克隆</el-button>
          <el-button @click="returnPrePage">取消</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<style lang="scss">
.submit_button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
