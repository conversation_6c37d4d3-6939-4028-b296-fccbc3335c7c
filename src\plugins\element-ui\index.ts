export {
  ElButton,
  ElCascader,
  ElCheckbox,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElImage,
  ElInput,
  ElPopover,
  ElSelect,
  ElTag,
  ElTooltip,
  ElLoading as Loading,
  ElEmpty,
  ElMessage as Toast,
} from "element-plus";

// 还可以继续自定义封装一些方法，
export function showNotification(title, message, type = "success", duration = 2000, position = "bottom-right") {
  // ElNotification有回调方法，例如close，所以需要返回
  return ElNotification({
    title: title,
    message: message,
    position: position as any,
    type: type,
    duration: duration,
  });
}
