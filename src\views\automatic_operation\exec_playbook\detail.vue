<script setup name="ExampleDetail">
import useSettingsStore from '@/store/modules/settings'
import eventBus from '@/utils/eventBus'
import { useTabbar } from '@/utils/composables'
import Storage from '@/utils/storage'

const route = useRoute()
const router = useRouter()

const settingsStore = useSettingsStore()
const formRef = ref()

function onSubmit() {
    formRef.value.submit(() => {
        eventBus.emit('get-data-list')
        goBack()
    })
}

function onCancel() {
    goBack()
}

// 返回列表页
function goBack() {
    if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
        useTabbar().close({ name: 'db4bixList' })
        Storage.local.remove('cookieData')
    } else {
        router.push({ name: 'db4bixList' })
        Storage.local.remove('cookieData')
    }
}
</script>

<template>
    <div>
        
        <page-main :title="route.name == 'db4bixCreate' ? '新增监控数据库信息' : '编辑监控数据库信息'">
            <el-row>
                <el-col :md="24" :lg="16">
                    <DetailForm ref="formRef" />
                </el-col>
            </el-row>
        </page-main>
        <fixed-action-bar>
            <el-button type="primary" size="large" @click="onSubmit">提交</el-button>
            <el-button size="large" @click="onCancel">取消</el-button>
        </fixed-action-bar>
    </div>
</template>

    <style lang="scss" scoped>
    // scss
    </style>
