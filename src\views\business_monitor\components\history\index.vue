<script setup name="history">
import { formatLocalTime } from "@/utils/dayjs";
import { onMounted, reactive } from "vue";
const emit = defineEmits(["update:modelValue"]);
let myVisible = computed({
  get: function () {
    return props.modelValue;
  },
  set: function (val) {
    emit("update:modelValue", val);
  },
});
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
  },
});

onMounted(() => {});
function close() {
  myVisible.value = false;
}
</script>

<template>
  <div>
    <el-dialog v-model="props.modelValue" title="历史告警" @closed="close()">
      <el-table :data="props.item">
        <el-table-column property="duration" label="持续时间">
          <template #default="scope">
            <p style="font-size: 16px; font-weight: bold">
              {{ scope.row.duration[0] }}{{ scope.row.duration[1] }}{{ scope.row.duration[2]
              }}{{ scope.row.duration[3] }}
            </p>
          </template>
        </el-table-column>
        <el-table-column property="status" label="状态">
          <template #default="scope">
            <el-tag type="danger" v-if="scope.row.status == '问题'">未解决</el-tag>
            <el-tag type="success" v-else-if="scope.row.status == '已解决'">已解决</el-tag>
            <el-tag type="info" v-else>{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="标签">
          <template #default="scope">
            <el-tag style="margin-top: 3px" v-for="(item, index) in scope.row.tags" :key="index">
              {{ item.tag }}:{{ item.value }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column property="startime" label="开始时间">
          <template #default="scope">{{ formatLocalTime(scope.row.startime) }}</template>
        </el-table-column>
        <el-table-column property="endtime" label="结束时间">
          <template #default="scope">{{ formatLocalTime(scope.row.endtime) }}</template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
