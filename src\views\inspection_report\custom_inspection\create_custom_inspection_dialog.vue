<template>
  <el-drawer v-model="visible" title="新增自定义巡检" direction="rtl" :before-close="closeDialog" size="66%">
    <el-form :model="data.form" label-width="120px" ref="inspectionForm" :rules="data.formRules">
      <el-form-item label="时间窗口" prop="last_days">
        <el-select v-model="data.form.last_days" placeholder="选择时间窗口" size="large" style="width: 240px">
          <el-option v-for="item in LastDaysOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="巡检主体" prop="businesses">
        <div class="b-gray b-1 w-100%">
          <div class="header bg-#f8f9fa h-36px px-14px py-8px flex items-center">
            <span class="mr-10px">业务列表</span>
            <el-checkbox :true-label="true" :false-label="false" @change="toggleAll">全选</el-checkbox>
          </div>
          <div class="content w-100% px-14px">
            <div class="px-16px py-10px">
              <el-checkbox-group size="large" v-model="data.form.businesses" v-if="data.businessList.length > 0">
                <el-checkbox
                  v-for="business in data.businessList"
                  :key="business.business_id"
                  :label="business.name"
                  class="w-20%"
                >
                  {{ business.name }}
                </el-checkbox>
              </el-checkbox-group>
              <el-empty v-else description="暂无业务"></el-empty>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input type="textarea" v-model="data.form.remarks" placeholder="请输入备注" maxlength="200" rows="4" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="text-center">
        <el-button type="primary" @click="confirmDialog" :loading="data.submitLoading">确定</el-button>
        <el-button @click="closeDialog" class="mr-50px">取消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { createInspectionReport } from "@/api/modules/inspection_report/custom_inspection";
import { LastDaysOptions } from "./constants";
import { GetAllBussiness } from "@/api/modules/topology/topology";
import { Toast } from "@/plugins/element-ui";
import { CollapsePanel, Collapse } from "vexip-ui";
import "vexip-ui/css/index.css";
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: false,
  },
});
const emits = defineEmits(["update:modelValue", "refreshList"]);
const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emits("update:modelValue", val);
  },
});
const data = reactive({
  businessListLoading: false,
  submitLoading: false,
  activeTab: "server",
  form: {
    businesses: [],
    remarks: "",
    last_days: 30,
  },
  formRules: {
    businesses: [{ required: true, message: "请选择至少一个业务", trigger: "change" }],
    last_days: [{ required: true, message: "巡检周期不能为空", trigger: "change" }],
  },
  businessList: [] as any,
});
const expanded = ref(1);
const inspectionForm = ref();

// 方法：全选/反选所有项
function toggleAll(val) {
  console.log(val);
  data.form.businesses = [];
  if (val) {
    data.businessList.forEach((item) => {
      data.form.businesses.push(item.name);
    });
  }
}
onMounted(() => {});

watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    if (newValue) {
      getBusinessesList();
    }
  }
);

// 获取业务 列表
function getBusinessesList() {
  data.businessListLoading = true;
  GetAllBussiness()
    .then((res) => {
      data.businessList = res.data;
    })
    .finally(() => {
      data.businessListLoading = false;
    });
}

// 提交
function confirmDialog() {
  inspectionForm.value.validate((valid) => {
    if (valid) {
      data.submitLoading = true;
      const from = {
        system_list: data.form.businesses,
        describ: data.form.remarks,
        last_days: data.form.last_days,
      };
      createInspectionReport(from)
        .then((res) => {
          emits("refreshList");
        })
        .finally(() => {
          data.submitLoading = false;
          closeDialog();
        });
    } else {
      console.log("Error in form validation!");
      return false;
    }
  });
}

// 清空表单
function resetForm() {
  inspectionForm.value.resetFields();
}

// 关闭弹窗
function closeDialog() {
  visible.value = false;
  resetForm();
}

//
function changeCollapse(expanded) {
  console.log(expanded);
}
</script>

<style scoped lang="scss"></style>
