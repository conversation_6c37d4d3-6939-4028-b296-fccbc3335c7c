import api from "@/plugins/axios";

/**
 * 获取告警规则的主机列表
 * @returns
 */
export function getHostsList() {
  return api.get("/zabbix_alarm_configuration_management/get_hosts_info/");
}

export interface DisableMonitoringParams {
  host_ids: number[]; // 根据实际情况调整数组元素类型
  status: boolean; // 建议替换具体类型，如 boolean 或 string
}
export const MonitoringStatus = {
  disable: false, //停用
  enable: true, //启用
};
/**
 * 停用主机监控/启用
 * @returns
 */
export function setHostsdisableMonitoring({ host_ids, status }: DisableMonitoringParams) {
  return api.post("/zabbix_alarm_configuration_management/set_hosts_monitoring_status/ ", {
    host_ids: host_ids,
    enabled: status,
  });
}

export interface GetHostsMonitoringInfoParams {
  tags?: []; //(string): 以逗号分隔的标签列表，用于过滤监控项。例如: tag1,tag2
  host_id: string;
  item_name?: string;
}
/**
 * 获取指定主机的所有监控项信息，包括监控项名称、是否启用、最新数据和关联的标签
 */
export function getHostsMonitoringInfo(data: GetHostsMonitoringInfoParams) {
  return api.post(`/zabbix_alarm_configuration_management/get_host_items_info/`, data);
}

/**
 * 获取指定主机的所有触发器信息，包括触发器名称、定时任务（表达式）、是否启用。
 */
export function getHostsTriggers(data: { host_id: string; trigger_name?: string; priority?: string }) {
  return api.post(`/zabbix_alarm_configuration_management/get_host_triggers/`, data);
}
