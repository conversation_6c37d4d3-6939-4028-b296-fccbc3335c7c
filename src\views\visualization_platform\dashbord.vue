<script setup name="IframeLayout">
import { useRouter, useRoute } from 'vue-router';
import { ArrowLeft } from '@element-plus/icons-vue';
import useSettingsStore from '@/store/modules/settings'
import { useTabbar } from '@/utils/composables'
const router = useRouter()
const route = useRoute()
const iframeRef = ref()
const iframe = ref({
    loading: true,
    src: ''
})
const timer=reactive({
    timeInter:null
})
const data = reactive({
 dataList:[],
});
const settingsStore = useSettingsStore()
onMounted(() => {
getData()
})
function getData(){
 iframe.value.src = route.query.url
 console.log(iframe.value.src)
    iframeRef.value.onload = () => {
        iframe.value.loading = false
    }
}
 function goBack(){
      if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs)
    useTabbar().close({ name: 'analysis_platform' })

  else
    router.push({ name: 'analysis_platform' })
    }
</script>

<template>
    <div>
<div v-loading="iframe.loading" class="iframe">
        <iframe ref="iframeRef" id="myIframe" :src="iframe.src" frameborder="0" />
        <el-button class="back-button" :icon="ArrowLeft" @click="goBack" circle></el-button>
    </div>
    </div>
</template>

<style lang="scss" scoped>
.iframe,
iframe {
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
}
.back-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}
</style>
