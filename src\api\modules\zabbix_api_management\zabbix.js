import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 通过ip获取hostId
 * @param {*} data
 * @returns
 */
export function getHostidByIp(data) {
  return api.post("/zabbix/monitor", data);
}

/**
 * 根据名称筛选出来的id获取最新数据
 * @param {*} data
 * @returns
 */
export function getLastData(data) {
  return api.post("/zabbix/get_latest_monitoring_data_of_the_host/", JSON.stringify(data));
}

/**
 * 获取监控项历史数据
 * @param {*} data  
 * itemid 
 * time_begin 时间戳
 * time_end 时间戳
 * @returns
 */
export function getTrendData(data) {
  return api.patch("/zabbix/monitor", JSON.stringify(data));
}

/**
 * 获取主机信息和告警信息
 * （拓扑图）
 * @param {*} data
 * @returns
 */
export function getProblem(data) {
  //zabbix最新数据
  return api.post("/zabbix/topology", JSON.stringify(data));
}

/**
 * 获取所有zabbix监控模板名称和id
 * @returns
 */
export function GetZabbixTemplate() {
  return api.get("/zabbix/templat");
}

/**
 * 获取业务监控top图
 * @returns
 */
export function getTopology() {
  return api.get("/zabbix/topology");
}

/**
 * 增加zabbix模板
 * @param {*} params 
 * @returns 
 */
export function addZabbixTemplate(params) {
  return api.post("/zabbix/templat", params);
}

/**
 * 获取zabbix分组下的zabbix主机列表
 * @returns 
 */
export function getZabbixMonitor() {
  return api.get('zabbix/monitor')
}


/**
 * 获取最新数据的tag标签
 */
export function getZabbixLatestTarget(params) {
  return api.post('/zabbix/get_latest_monitoring_data_of_targets/', params)
}