一体化运维平台 版本发布说明
当前版本：3.2
version 3.2 发布时间：
主要修改：
1. 修改已存在的bug
2. 调整整体导航栏结构
3. 调整资源管理下的新增编辑页面为弹窗展示


version: 3.1.4.2 发布时间: 2025年2月17日
主要修改：
1. 更改数据库的心跳检测接口
2. 登录的时候提示定期修改密码
bug修改：
1. 问题清单严重性筛选后分页不对
2. 最新数据从问题清单跳转过来后接口 重复调用。
3. 字典管理-删除搜索后的字典会删除其他的字典
4. 优化最新数据的历史折线图，
5. 新增自定义巡检选择业务增加业务全选

后端：
1.修改数据库连接状态
2.优化服务控制台导入
3.调整巡检报告及其模板
4.zabbix最新数据越界
5.用户名密码加密，过期修改密码
6.业务日志的全部采集
7.服务接口的zabbix监控状态
8.Oracle数据库的logstash模板
9.添加连接池管理软件的安装配置


version: ******* 发布时间: 2024年12月10日
主要修改：
1. 新增自定义巡检，将巡检报告生成为zip压缩包，可直接查看
2. axios修改：对于blob文件下载的兼容
3. 更换获取所有备份接口
4. 问题清单增加严重级别筛选
bug修改
1. 备份管理兼容空数据0
2. 字典管理-新增的字典项无法修改
3. 日报管理列表搜索部分调整，enter键会触发表单提交
4. 服务器列表导出cmdb接口修改
5. 用户列表的筛选只支持筛选一次，不能再筛选的结果上再次筛选其他的
6. 修改运维功能列表的分组函数【object.group】，兼容低版本浏览器



由中联联壹科技开发