<script setup>
import { getBusinessFromConsole } from "@/api/modules/cmdb/business";
const ruleForm = ref();
const data = reactive({
  consoleInfo: {
    ip: "",
    port: "",
    username: "",
    password: "",
  },
  submitLoading: false,
  formRules: {
    ip: [{ required: true, message: "请输入服务控制台IP", trigger: "blur" }],
    port: [{ required: true, message: "请输入服务控制台端口", trigger: "blur" }],
    username: [{ required: true, message: "请输入服务控制台账户", trigger: "blur" }],
    password: [{ required: true, message: "请输入服务控制台密码", trigger: "blur" }],
  },
});

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["update:modelValue", "requestCompleted"]);
let myVisible = computed({
  get: function () {
    return props.modelValue;
  },
  set: function (val) {
    emit("update:modelValue", val);
  },
});

//提交控制台信息导入业务数据
function submitConsoleinfo() {
  ruleForm.value.validate((valid) => {
    if (valid) {
      data.submitLoading = true;
      getBusinessFromConsole({ login_information_list: [data.consoleInfo] })
        .then((res) => {
          emit("requestCompleted");
          emit("update:modelValue");
        })
        .finally(() => {
          data.submitLoading = false;
        });
    }
  });
}
</script>

<template>
  <div>
    <el-dialog v-model="myVisible" title="控制台登录信息" :destroy-on-close="true" width="500">
      <el-form :model="data.consoleInfo" label-width="auto" ref="ruleForm" :rules="data.formRules">
        <el-form-item label="IP" prop="ip">
          <el-input v-model="data.consoleInfo.ip" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="data.consoleInfo.port" />
        </el-form-item>
        <el-form-item label="账户" prop="username">
          <el-input v-model="data.consoleInfo.username" />
        </el-form-item>
        <el-form-item label="密码" prop="password" show-password type="password">
          <el-input v-model="data.consoleInfo.password" />
        </el-form-item>
      </el-form>
      <div class="button-style">
        <el-button type="primary" plain @click="submitConsoleinfo" :loading="data.submitLoading">提交</el-button>
        <el-button @click="emit('update:modelValue')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.button-style {
  display: flex;
  justify-content: center;
}
</style>
