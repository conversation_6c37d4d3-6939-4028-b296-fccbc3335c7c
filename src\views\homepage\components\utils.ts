import router from "@/router";
import { ElMessage, ElMessageBox } from "element-plus";
import { addMonitoring, cmdbDeleteResource, cmdbUpdateResource, cmdbAddResource } from "@/api/modules/cmdb/resource";
import { cmdbGetSoftMessageById } from "@/api/modules/model_configuration/cmdb_soft";
import { Toast } from "@/plugins/element-ui";
/**
 * 跳转页面 进入资源详情
 * @param queryParams
 */
export function goToPageAssetDetail(queryParams, isMonitoring = false) {
    if (isMonitoring) {
        queryParams.tabpage = "second";
    }
    router.push({
        path: "/cmdb/asset",
        query: queryParams,
    });
}

//隐藏密码敏感信息

export function hideSensitiveInfo(row) {
    if (row.key.includes("密码")) {
        return "*".repeat(row.value.length);
    }
    return row.value;
}

//分页

export function pageChangeNum(dataList, params) {
    let list = dataList;
    let pageList = list.filter((item, index) => {
        return index >= params.from && index < params.from + params.limit;
    });
    pageList.forEach((item) => {
        item.params = item.params;
    });
    return {
        list: pageList,
        total: list.length,
    };
}

//资源批量删除

export function deleteResource(type, selectList) {
    return new Promise((resolve, reject) => {
        let isDelete = false;
        ElMessageBox.confirm("是否删除选中" + `${type}` + "？", "注意", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(async () => {
                await cmdbDeleteResource({ type: type, id_list: selectList }).then((res) => {
                    if (res.status_code == 200) {
                        isDelete = true;
                        resolve(isDelete);
                    } else {
                        resolve(isDelete);
                    }
                });
            })
            .catch(() => {
                ElMessage.info("取消删除！");
            });
    });
}

//安装监控
export function addZabbixForResource(params) {
    return new Promise((resolve, reject) => {
        addMonitoring(params)
            .then((res) => {
                if (res.data.status == "error") {
                    Toast.error(res.data.message);
                }
                resolve(res.data.data.step_information);
            })
            .catch((res) => {
                reject(res?.data?.data?.step_information || "");
            });
    });
}
// message 传入的描述信息，默认为 确定要安装监控代理吗？
export function showConfirmationDialog(message = "") {
    if (!message) {
        message = "确定要安装监控代理吗？";
    }
    return new Promise((resolve, reject) => {
        ElMessageBox.confirm(message, "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(() => {
                resolve(true); // 用户点击确定
            })
            .catch(() => {
                resolve(false); // 用户点击取消或关闭
            });
    });
}

//新增信息 （数据库、中间件、业务应用）
export function addResourceInfo(softInfo, data, isUpdate, resourceType) {
    return new Promise((resolve, reject) => {
        let isTrue = true;
        if (softInfo.length != 0) {
            softInfo.forEach((item) => {
                for (const key in item) {
                    if (item[key] === undefined || item[key] === "" || item[key] === "未知" || item[key] === "待填写") {
                        isTrue = false;
                        return;
                    }
                }
                item.label = item.label.trim();
                item.value = item.value.trim();
                item.key = item.key.trim();
            });
        }
        if (!isTrue) {
            ElMessage.error(`参数列表中有包含空字符串或 '未知','待填写'的内容。`);
            reject();
            return;
        }
        data.configuration_group = softInfo.map((item) => [item.label, item.key, item.value]);
        const apiFunction = isUpdate ? cmdbUpdateResource : cmdbAddResource;
        let submitInfo = {
            relation_id: data.relation_id,
            zabbix_id: data.zabbix_id,
            extra_parameters: {
                databaseName: data.databaseName,
                group_name: data.group_name,
                template_list: data.template_list,
            },
            configuration_group: data.configuration_group,
        };
        if (resourceType === "网络设备") {
            submitInfo.extra_parameters.ip = data.ip;
        } else {
            submitInfo.software_id = data.software_id;
            submitInfo.server_id = data.server_id;
        }
        apiFunction({
            type: resourceType,
            data_resource: submitInfo,
        })
            .then((res) => {
                resolve(res);
            })
            .catch(() => {
                reject();
            });
    });
}

//筛选获取软件参数
export async function getSoftParams(softList, softwareName) {
    let temp = softList.find((item) => item.software_name == softwareName);
    let paramsList = [];
    if (temp) {
        await cmdbGetSoftMessageById(temp.id)
            .then((res) => {
                res.data.configuration_group.forEach((item) => {
                    //解构赋值
                    const [label, key, value] = item;
                    paramsList.push({ label, key, value });
                });
            })
            .catch(() => {});
    }
    return {
        id: temp.id,
        paramsList,
    };
}
