import Storage from '@/utils/storage'
const useWarningListStore = defineStore(
    // 唯一ID
    'warningList',
    {
        state: () => ({
            // 消息
            message: [],
        }),
        getters: {
            // 未读通知总数
            total() {
                return this.message.length
            }
        },
        actions: {
            // 初始化，获取所有通知的未读数量
            init() {
                this.getUnreadMessage()
            },
            // 获取未读消息数
            getUnreadMessage() {
                let ws = new WebSocket(import.meta.env.VITE_SCHEDULE_WEBSOCKET)
              ws.onmessage = (e) => {
                if (Storage.local.get("warningList")) {
                  this.message = JSON.parse(Storage.local.get("warningList"))
                    this.message.push({ status: JSON.parse(e.data).message.status, message: JSON.parse(e.data).message.message })
                    Storage.local.set("warningList", JSON.stringify(this.message))
                } else {
                    Storage.local.set("warningList", JSON.stringify([]))
                }
                }
            },
        }
    }
)

export default useWarningListStore
