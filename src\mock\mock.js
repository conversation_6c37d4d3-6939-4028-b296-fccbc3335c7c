import Mock from 'mockjs'
const Random = Mock.Random
const AllList = []
for (let i = 0; i < 50; i++) {
  AllList.push(Mock.mock({
    id: '@id',
    title: '@ctitle(10, 20)'
  }))
}
export default [{
  url: '/mock/dictionary/create',
  method: 'post',
  response: {
    error: '',
    status: 1,
    data: {
      isSuccess: true,
      data: Random.natural(100, 1000),
    },
  },
},
{
  url: '/mock/dictionary/edit',
  method: 'post',
  response: {
    error: '',
    status: 1,
    data: {
      isSuccess: true,
    },
  },
},
{
  url: '/mock/dictionary/delete',
  method: 'post',
  response: {
    error: '',
    status: 1,
    data: {
      isSuccess: true,
    },
  },
},
{
  url: '/mock/dictionary/list',
  method: 'get',
  response: {
    error: '',
    status: 1,
    data: [{
      id: 2,
      dictionary_name: '系统类型',
      dictionary_code: 'system_type',
      dictionary_value: []
    },
    {
      id: 1,
      dictionary_name: '业务产品索引',
      dictionary_code: 'business_product',
      dictionary_value: [
        {
          id: 1,
          key: '中联专业版电子病历系统',
          value: 'zlemr',

        },
        {
          id: 2,
          key: '中联专业版临生免系统',
          value: 'zllab',

        },
        {
          id: 3,
          key: '中联专业版临生免系统',
          value: 'zllab',

        },
      ],
    },
    ]
  }
},]
//   [
//     "id":1,
//     "dictionary_soft_name":"业务产品索引",
//     "dictionary_sort_value":{
//     "中联专业版电子病历系统":"zlemr",
//     "中联专业版临生免系统":"zllab",
//     "中联专业体检系统":"zlpeis",
//     "一体化护理评分评估":"zlscore",
//     "中联专业版挂号系统":"zlrgs",
//     "处方开立权限":"zldap",
//     "数据处理平台":"ZLDPPRunService",
//     "中联报表系统":"zlrpts",
//     "统一数据源":"zluds",
//     "网页组件服务":"zlwebtoolserver",
//     "一张纸门诊医生工作站":"zlonepaper"
//     }
// ]