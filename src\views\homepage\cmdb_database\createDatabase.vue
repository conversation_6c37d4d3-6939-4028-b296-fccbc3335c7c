<script setup>
import useSettingsStore from "@/store/modules/settings";
import { computed, onMounted, reactive } from "vue";
import { GetZabbixTemplate } from "@/api/modules/zabbix_api_management/zabbix";
import { getServerNameList } from "@/api/modules/cmdb/server";
import { cmdbAddResource, cmdbUpdateResource, cmdbGetResourceAsset } from "@/api/modules/cmdb/resource";
import { cmdbGetSoftListByType } from "@/api/modules/model_configuration/cmdb_soft";
import { showNotification } from "@/plugins/element-ui";
import { hideSensitiveInfo, addResourceInfo, getSoftParams } from "../components/utils";
import { async } from "@antv/x6/lib/registry/marker/main";

const router = useRouter();
const route = useRoute();
const formRef = ref();
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(["closeDialog", "getResource", "update:modelValue"]);
const data = reactive({
  db_template_list: [],
  template_list: [],
  extra_params: [],
  soft_extra_params: [],
  //获取软件列表
  softList: [],
  serverList: [], //服务器列表
  databaseForm: {
    relation_id: 0,
    software_id: 0,
    server_id: "",
    databaseName: "",
    databaseSoft: "",
    template_list: [],
    group_name: "",
    configuration_group: [],
    zabbix_id: 0,
  },

  cloneButton: false,
  submitLoading: false,
  forRules: {
    databaseName: [{ required: true, message: "请输入数据库名称", trigger: "blur" }],
    server_id: [{ required: true, message: "请选择绑定服务器", trigger: "blur" }],
    databaseSoft: [{ required: true, message: "请选择数据库软件", trigger: "blur" }],
    serverName: [{ required: true, message: "请输入服务器名", trigger: "blur" }],
    db_template_list: [{ required: true, message: "请选择监控模板", trigger: "blur" }],
    group_name: [{ required: true, message: "请填写分组", trigger: "blur" }],
  },
});
onMounted(() => {
  if (props.modelValue != 0) {
    getData();
    data.databaseForm.relation_id = props.modelValue;
    data.cloneButton = true;
  }

  getSoftServerList();
  getTemplate();
});

//获取数据库具体信息
function getData() {
  cmdbGetResourceAsset({ type: "数据库", relation_id: props.modelValue }).then((res) => {
    data.databaseForm = res.data;
    res.data.configuration_group_en.forEach((item) => {
      const obj = {
        label: item[0],
        key: item[1],
        value: item[2],
      };
      data.soft_extra_params.push(obj);
    });
  });
}
//获取数据库软件集合
function getSoftServerList() {
  cmdbGetSoftListByType({ type: "数据库" }).then((res) => {
    data.softList = res.data;
  });
  getServerNameList().then((res) => {
    data.serverList = res.data;
  });
}
//异步获取软件参数
async function pushExtraParams(value) {
  data.databaseForm.group_name = `${value}数据库`;
  data.soft_extra_params = [];
  const paramsResult = await getSoftParams(data.softList, value);
  data.databaseForm.software_id = paramsResult.id;
  data.soft_extra_params = paramsResult.paramsList;
}

//保存数据库额外的CI项
function addParams(data) {
  data.isEdit = false;
  if (data.label == "" || data.key == "") {
    ElMessage.warning({
      message: "自定义字段不能置空",
      center: true,
    });
    data.label = "待填写";
    data.value = "待填写";
  }
}

//移除数据库额外CI项
function removeDBExtraParams(index, row) {
  data.soft_extra_params.splice(index, 1);
}

//添加数据库
function addDatabase() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      data.submitLoading = true;
      const isUpdate = props.modelValue !== 0;
      try {
        const submitResult = await addResourceInfo(data.soft_extra_params, data.databaseForm, isUpdate, "数据库");
        if (submitResult.status_code == 200) {
          if (!isUpdate) {
            showNotification("提示", "正在采集数据库信息中...", "info");
          }
          emit("getResource");
          returnPrePage();
        }
      } catch (error) {}

      data.submitLoading = false;
    }
  });
}

function goBack() {
  router.go(-1);
}

function getTemplate() {
  GetZabbixTemplate().then((res) => {
    data.db_template_list = filterTemp(res.data);
    data.template_list = res.data;
  });
}
function filterTemp(list) {
  return list.filter((item) => item.name.includes("数据库-"));
}

function returnPrePage() {
  emit("closeDialog");
}

//克隆主机信息
function handleClone() {
  emit("update:modelValue", 0);
  data.databaseForm.relation_id = 0;
  data.databaseForm.zabbix_id = 0;
  data.cloneButton = false;
}
</script>
<template>
  <div>
    <el-row type="flex" justify="center">
      <el-col>
        <el-form :model="data.databaseForm" label-width="100px" ref="formRef" :rules="data.forRules">
          <el-row>
            <el-form-item label="所属服务器" prop="server_id">
              <el-select filterable v-model="data.databaseForm.server_id">
                <el-option v-for="item in data.serverList" :label="item.server_name" :value="item.id" :key="item.id" />
              </el-select>
            </el-form-item>
          </el-row>

          <el-row>
            <el-form-item label="软件" prop="databaseSoft">
              <el-select v-model="data.databaseForm.databaseSoft" placeholder="请选择软件" @change="pushExtraParams">
                <el-option
                  v-for="item in data.softList"
                  :label="item.software_name"
                  :value="item.software_name"
                  :key="item.software_name"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-form-item label="业务名称" prop="databaseName">
            <el-input v-model="data.databaseForm.databaseName" placeholder="例如：his数据库" />
          </el-form-item>

          <el-form-item label="组别" prop="group_name">
            <el-input v-model="data.databaseForm.group_name" placeholder="多个分组用“,”隔开，例如:内网服务器,Linux" />
          </el-form-item>
          <el-form-item label="监控模板" prop="template_list">
            <el-select v-model="data.databaseForm.template_list" filterable multiple placeholder="请选择监控模板">
              <el-option
                v-for="(item, index) in data.db_template_list"
                :label="item.name"
                :value="item.name"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="参数">
            <el-table :data="data.soft_extra_params" style="width: 100%">
              <el-table-column label="名称(CN)">
                <template #default="scope">
                  <el-input v-if="scope.row.isEdit && !scope.row.key" v-model="scope.row.key" size="small" />
                  <span v-else>{{ scope.row.key }}</span>
                </template>
              </el-table-column>

              <el-table-column label="值">
                <template #default="scope">
                  <el-input
                    v-if="scope.row.isEdit"
                    v-model="scope.row.value"
                    size="small"
                    :type="scope.row.key.includes('密码') ? 'password' : 'text'"
                  />
                  <span v-else>{{ hideSensitiveInfo(scope.row) }}</span>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="200" align="center">
                <template #default="scope">
                  <template v-if="scope.row.isEdit">
                    <el-button type="primary" plain size="small" @click="addParams(scope.row)">保存</el-button>
                  </template>
                  <template v-else>
                    <el-button type="primary" plain size="small" @click="scope.row.isEdit = true">编辑</el-button>
                    <el-popconfirm
                      title="是否要删除此行？"
                      style="margin-left: 10px"
                      @confirm="removeDBExtraParams(scope.$index, scope.row)"
                    >
                      <template #reference>
                        <el-button type="danger" plain size="small">删除</el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <div class="submit_button">
          <el-button type="primary" @click="addDatabase" :loading="data.submitLoading">提交</el-button>
          <el-button type="primary" plain @click="handleClone" v-if="data.cloneButton">克隆</el-button>
          <el-button @click="returnPrePage">取消</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<style scoped lang="scss">
.submit_button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
