<template>
  <div class="w-85% m-a pt-20px items-center flex justify-center flex-col">
    <div class="w-full mt-10px">
      <el-steps :active="activeStep" align-center simple>
        <el-step title="数据源与监控配置" :icon="Link"></el-step>
        <el-step title="周报日报配置" :icon="DocumentRemove"></el-step>
        <el-step title="服务控制台导入" :icon="Discount"></el-step>
      </el-steps>
      <!-- 主体 -->
      <div class="">
        <!-- 根据当前步骤显示不同的内容 -->
        <!-- 数据源与监控配置的内容 -->
        <div v-show="activeStep === stepEnum.source">
          <source-config ref="sourceConfigRef" @next-step="nextStep"></source-config>
        </div>

        <!-- 周报配置的内容 -->
        <div v-show="activeStep === stepEnum.report">
          <report-config ref="reportConfigRef" @next-step="nextStep" />
        </div>

        <!-- 服务控制台导入 -->
        <div v-show="activeStep === stepEnum.import">
          <import-config ref="importConfigRef" @next-step="completingConfiguration" />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="text-center w-full mt-18px">
        <el-button @click="prevStep" v-if="activeStep > 0">上一步</el-button>
        <el-button type="primary" @click="handleSaveSetting(activeStep)" :loading="isButtonDisable">
          保存配置,
          <span v-if="activeStep == stepEnum.import">进入系统</span>
          <span v-else>进入下一步</span>
        </el-button>
      </div>
    </div>
    <el-dialog
      title="温馨提示"
      v-model="initializeCacheDialog"
      width="400px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="flex flex-row items-center">
        <el-icon class="is-loading" size="40">
          <Loading />
        </el-icon>
        <div>初始化配置完成后，页面加载较慢，请稍等！</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Link, DocumentRemove, Discount, Loading } from "@element-plus/icons-vue";

import useGlobalConfig from "./util";
import "vexip-ui/css/index.css";

import { checkConfigIsComplete, getConfigForWeb } from "@/api/modules/configuration/env_config";

import SourceConfig from "./components/source_config.vue";
import ReportConfig from "./components/report_config.vue";
import ImportConfig from "./components/import_config.vue";
import storage from "@/utils/storage";

const { stepEnum, activeStep } = useGlobalConfig();
const importConfigRef = ref(null);
const reportConfigRef = ref(null);
const sourceConfigRef = ref(null);
const initializeCacheDialog = ref(false);
onMounted(() => {});

const isButtonDisable = computed(() => {
  if (stepEnum.source == activeStep.value) {
    return sourceConfigRef.value?.formLoading ?? false;
  }
  if (stepEnum.report == activeStep.value) {
    return reportConfigRef.value?.formLoading ?? false;
  }
  if (stepEnum.import == activeStep.value) {
    return importConfigRef.value?.formLoading ?? false;
  }
});
function prevStep() {
  if (activeStep.value > 0) {
    activeStep.value--;
  }
}
function nextStep() {
  if (activeStep.value < stepEnum.import) {
    activeStep.value++;
  }
}
/**
   * 保存配置    分为三个步骤，
   1：数据源配置以及监控
   2: 周报日报配置
   3: 需要导入的资源配置：控制台，软件模板
    */
function handleSaveSetting(val) {
  switch (val) {
    case stepEnum.source:
      sourceConfigRef.value.saveSourceConfig();
      break;
    case stepEnum.report:
      reportConfigRef.value.saveReportconfig();
      break;
    case stepEnum.import:
      importConfigRef.value.saveConsoleConfig();
      break;
    default:
      break;
  }
}

const router = useRouter();

function completingConfiguration() {
  // const notification = showNotification('温馨提示', '初始化配置完成后，页面加载较慢，请稍等！', 'info')
  initializeCacheDialog.value = true;
  checkConfigIsComplete()
    .then((res) => {
      nextTick(() => {
        if (res.data == true) {
          getConfigForWeb().then((res) => {
            Object.keys(res.data).forEach((key) => {
              storage.local.set(key, res.data[key]);
            });

            router
              .push({
                name: "login",
              })
              .finally(() => {
                initializeCacheDialog.value = false;
              });
          });
        } else {
          ElMessage.error("初始化出错");
        }
      });
    })
    .finally(() => {
      initializeCacheDialog.value = false;
    });
}
</script>

<style scoped lang="scss">
:deep {
  .el-card__body {
    padding: 10px !important;
  }
}
.setting_block_title {
  @apply text-18px font-bold mt-16px;
}
</style>
