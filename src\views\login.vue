<route>
{
    meta: {
        whiteList: true,
        title: "登录",
        constant: true,
        layout: false
    }
}
</route>

<script setup name="Login">
import useSettingsStore from "@/store/modules/settings";
import useUserStore from "@/store/modules/user";
import storage from "@/utils/storage";
import { license_info } from "@/store/modules/license";
import { login_url } from "@/api/modules/user_api_management/users";
import { getEnvViteApiBaseurl } from "@/utils/axios";

const route = useRoute();
const router = useRouter();
const settingsStore = useSettingsStore();
const userStore = useUserStore();
const data = reactive({
  captchaUrl: "",
  title: "",
  editPasswordDialog: false,
});
const banner = new URL("../assets/images/login-banner.png", import.meta.url).href;
// 新增定时器相关代码
const timer = ref(null);
const setupTimer = () => {
  timer.value = setInterval(() => {
    refreshCaptcha();
  }, 600000); // 1000毫秒=1秒， 10分钟
};
const clearTimer = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};
const newPassword = (rule, value, callback) => {
  var reg1 = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*.])[\da-zA-Z~!@#$%^&*.]{8,}$/; //密码必须是8位以上、必须含有字母、数字、特殊符号
  if (!reg1.test(value)) {
    callback(new Error("密码必须是8位以上、必须含有字母、数字、特殊符号"));
  } else {
    callback();
  }
};

// 登录
const loginFormRef = ref();
const loginForm = ref({
  name: storage.local.get("login_account"),
  password: "",
  remember: storage.local.has("login_account"),
  code: "",
});
const loginRules = ref({
  name: [{ required: true, trigger: "blur", message: "请输入用户名" }],
  password: [
    { required: true, trigger: "blur", message: "请输入密码" },
    { required: true, trigger: "blur", validator: newPassword },
  ],
  code: [{ required: true, trigger: "blur", message: "请输入验证码" }],
});

const loading = ref(false);
const passwordType = ref("password");
const redirect = ref(null);
const passwordRef = ref();

const isLocked = computed(() => userStore.isLocked);
const loginAttempts = computed(() => userStore.loginAttempts);
function handleLogin() {
  if (isLocked.value) {
    return;
  }
  loginFormRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      setTimeout(refreshCaptcha, 500);
      userStore
        .login(loginForm.value)
        .then((res) => {
          if (res.status_code === 402) {
            ElMessage({
              message: "请定期修改密码！",
              type: "warning",
            });
            data.editPasswordDialog = true;
            return;
          }
          if (storage.local.get("user") == null) {
            loading.value = false;
          } else {
            if (loginForm.value.remember) {
              storage.local.set("login_account", loginForm.value.name);
            } else {
              storage.local.remove("login_account");
            }
            loading.value = false;
            // location.reload()
            router.push(redirect.value);
          }
        })
        .catch((res) => {
          loading.value = false;
        });
    }
  });
}

onMounted(() => {
  // 初始化定时器
  setupTimer();
  redirect.value = route.query.redirect ?? "/";
  router.push(redirect.value);
  data.captchaUrl = getEnvViteApiBaseurl() + login_url["captcha"];
  data.title = license_info.value.license.appName;
  checkedLocked();
});
onUnmounted(() => {
  clearTimer();
});
function showPassword() {
  passwordType.value = passwordType.value === "password" ? "" : "password";
  nextTick(() => {
    passwordRef.value.focus();
  });
}
// 修改验证码刷新方法
const handleManualRefresh = () => {
  clearTimer();
  refreshCaptcha();
  setupTimer();
};
function refreshCaptcha() {
  data.captchaUrl = getEnvViteApiBaseurl() + login_url["captcha"] + "?" + new Date();
}

// 刷新页面后检查锁定状态，如果当前时间减去最后一个登录尝试时间超过锁定时间，移除锁定状态，否则就延时执行
function checkedLocked() {
  const datetime = new Date().getTime();
  const lastAttemptTime = parseInt(storage.local.get("lastAttemptTime")) || 0;
  const tiemleng = datetime - lastAttemptTime;

  if (tiemleng < userStore.lockingDuration) {
    setTimeout(() => {
      userStore.resetLoginAttempts();
    }, lastAttemptTime + userStore.lockingDuration - datetime);
  } else {
    userStore.resetLoginAttempts();
  }
}

const validatePassword = (rule, value, callback) => {
  if (value !== form.value.newpassword) {
    callback(new Error("密码不一致"));
  } else {
    callback();
  }
};
const formRef = ref();
const form = ref({
  password: "",
  newpassword: "",
  checkpassword: "",
});
const rules = ref({
  password: [{ required: true, message: "请输入原密码", trigger: "blur" }],
  newpassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { required: true, trigger: "blur", validator: newPassword },
  ],
  checkpassword: [{ required: true, message: "请输入新密码", trigger: "blur" }, { validator: validatePassword }],
});
function onSubmit() {
  formRef.value.validate((valid) => {
    if (valid) {
      (form.value.name = loginForm.value.name),
        userStore
          .editPassword(form.value)
          .then(() => {
            data.editPasswordDialog = false;
            location.reload();
          })
          .catch(() => {});
    }
  });
}
</script>

<template>
  <div>
    <div class="bg-banner" />
    <i18n-selector class="i18n-selector">
      <el-icon>
        <svg-icon name="i-ri:translate" />
      </el-icon>
    </i18n-selector>
    <div id="login-box">
      <div class="login-banner">
        <img :src="banner" class="banner" />
      </div>
      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on">
        <div class="title-container">
          <h3 class="title">{{ data.title }}</h3>
        </div>
        <div>
          <el-form-item prop="name">
            <el-input
              ref="name"
              v-model="loginForm.name"
              @keyup.enter="handleLogin"
              placeholder="请输入账号"
              type="text"
              tabindex="1"
              autocomplete="on"
            >
              <template #prefix>
                <el-icon>
                  <svg-icon name="i-ri:user-3-fill" />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              ref="passwordRef"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="请输入密码"
              tabindex="2"
              autocomplete="on"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <el-icon>
                  <svg-icon name="i-ri:lock-2-fill" />
                </el-icon>
              </template>
              <template #suffix>
                <el-icon @click="showPassword">
                  <svg-icon :name="passwordType === 'password' ? 'i-ri:eye-close-line' : 'i-ri:eye-line'" />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="code">
            验证码：
            <el-input
              placeholder="输入验证码"
              style="width: 150px; margin-right: 50px"
              v-model="loginForm.code"
              @keyup.enter="handleLogin"
            ></el-input>
            <img title="点击刷新" :src="data.captchaUrl" @click="handleManualRefresh" class="cursor-pointer" />
          </el-form-item>
        </div>
        <div class="flex-bar">
          <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
        </div>
        <el-button :loading="loading" type="primary" size="large" style="width: 100%" @click.prevent="handleLogin">
          登录
        </el-button>
        <p v-if="isLocked" class="color-[#F56C6C]">失败次数过多，账户已锁定，请3分钟后重试。</p>
        <p v-else-if="loginAttempts > 0 && !isLocked" class="color-[#F56C6C]">
          登陆失败{{ loginAttempts }}次，超过5次将会锁定账户。
        </p>
      </el-form>
    </div>
    <Copyright v-if="settingsStore.copyright.enable" />
    <el-dialog
      v-model="data.editPasswordDialog"
      title="修改密码"
      width="30%"
      :show-close="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div class="form-container">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="原密码" prop="password">
            <el-input v-model="form.password" type="password" placeholder="请输入原密码" />
          </el-form-item>
          <el-form-item label="新密码" prop="newpassword">
            <el-input v-model="form.newpassword" type="password" placeholder="请输入原密码" />
          </el-form-item>
          <el-form-item label="确认新密码" prop="checkpassword">
            <el-input v-model="form.checkpassword" type="password" placeholder="请输入原密码" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
[data-mode="mobile"] {
  #login-box {
    position: relative;
    width: 100%;
    height: 100%;
    top: inherit;
    left: inherit;
    transform: translateX(0) translateY(0);
    flex-direction: column;
    justify-content: start;
    border-radius: 0;
    box-shadow: none;

    .login-banner {
      width: 100%;
      padding: 20px 0;

      .banner {
        position: relative;
        right: inherit;
        width: 100%;
        max-width: 375px;
        margin: 0 auto;
        display: inherit;
        top: inherit;
        transform: translateY(0);
      }
    }

    .login-form {
      width: 100%;
      min-height: auto;
      padding: 30px;
    }
  }

  .copyright {
    position: relative;
    bottom: 0;
    padding: 20px 0;
  }
}

:deep(input[type="password"]::-ms-reveal) {
  display: none;
}

:deep(.i18n-selector) {
  position: absolute;
  z-index: 1;
  top: 20px;
  right: 20px;
  cursor: pointer;
  font-size: 18px;
  color: var(--el-text-color-primary);
}

.bg-banner {
  position: fixed;
  z-index: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, var(--g-app-bg), var(--g-main-bg));
}

#login-box {
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  background-color: var(--g-app-bg);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--el-box-shadow);

  .login-banner {
    position: relative;
    width: 450px;
    background-color: var(--g-main-bg);
    overflow: hidden;

    .banner {
      width: 100%;
      @include position-center(y);
    }

    .logo {
      position: absolute;
      top: 20px;
      left: 20px;
      width: 30px;
      height: 30px;
      border-radius: 4px;
      background: url("../assets/images/logo.png") no-repeat;
      background-size: contain;
      box-shadow: var(--el-box-shadow-light);
    }
  }

  .login-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 500px;
    width: 500px;
    padding: 50px;
    overflow: hidden;

    .title-container {
      position: relative;

      .title {
        display: flex;
        justify-content: center;
        /* 水平居中 */
        font-size: 1.3em;
        color: var(--el-text-color-primary);
        margin: 0 auto 30px;
        font-weight: bold;
      }
    }
  }

  .el-form-item {
    margin-bottom: 24px;

    :deep(.el-input) {
      height: 48px;
      line-height: inherit;
      width: 100%;

      input {
        height: 48px;
      }

      .el-input__prefix,
      .el-input__suffix {
        display: flex;
        align-items: center;
      }

      .el-input__prefix {
        left: 10px;
      }

      .el-input__suffix {
        right: 10px;
      }
    }
  }

  :deep(.el-divider__text) {
    background-color: var(--g-app-bg);
  }

  .flex-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .sub-link {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    font-size: 14px;
    color: var(--el-text-color-secondary);

    .text {
      margin-right: 10px;
    }
  }
}

.copyright {
  position: absolute;
  bottom: 30px;
  width: 100%;
  margin: 0;
}

.form-container {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
  height: 100%;
  /* 确保容器占满整个对话框的高度 */
}
</style>
