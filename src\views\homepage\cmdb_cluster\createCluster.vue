<script setup>
import { onMounted, reactive, ref } from "vue";
import { useTabbar } from "@/utils/composables";
import useSettingsStore from "@/store/modules/settings";

import { GetZabbixTemplate } from "@/api/modules/zabbix_api_management/zabbix";
import { getServerNameList } from "@/api/modules/cmdb/server";
import { getNginxFile } from "@/api/modules/nginx/nginx";
import { getClustertype } from "@/api/modules/model_configuration/cmdb_soft";
import { ResourceTypeEnum } from "../cmdb_asset/components/constants";
import { addNewCluster } from "@/api/modules/cmdb/resource";
import { DeleteModelById, GetModelDetailById } from "@/api/modules/basic_crud/crud";
import { ElMessage } from "element-plus";
import { dataType } from "element-plus/es/components/table-v2/src/common";
import { hideSensitiveInfo } from "../components/utils";

const router = useRouter();
const route = useRoute();
const settingsStore = useSettingsStore();
const formRef = ref();

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(["closeDialog", "getResource", "update:modelValue"]);

const data = reactive({
  loading: false,
  extra_paramters: [],

  template_list: [],
  template_list_all: [],
  serverList: [],
  clusterForm: {
    name: "", //集群名称
    group_name: "", //分组名称
    virtual_ip: "", //虚拟ip
    cluster_type: "",
    nodes: [],
    extra_param: {
      cluster_ip_list: [],
      cluster_type: [],
    },
    system_type: [],
  },
  zabbix_template: [],
  cloneButton: false,
  db_cluster: false,

  clusterType: [], //集群类型
  group: [], //集群类型筛选
  showEldraw: false,
  submitLoading: false,
  formRules: {
    name: [{ required: true, message: "请输入集群名称", trigger: "blur" }],
    group_name: [{ required: true, message: "请输入集群群组", trigger: "blur" }],
    virtual_ip: [{ required: true, message: "请输入集群虚拟IP", trigger: "blur" }],
    nodes: [{ required: true, message: "请选择集群节点", trigger: "blur" }],
    cluster_type: [{ required: true, message: "请选择集群类型", trigger: "blur" }],
    system_type: [{ required: true, message: "请选择系统类型", trigger: "blur" }],
  },
});
onMounted(() => {
  console.log(props.modelValue);
  if (props.modelValue != 0) {
    getClusterAsset();

    data.cloneButton = true;
  }

  getServerList();
  getTemplate();
  getType();
});
//获取集群信息
function getClusterAsset() {
  GetModelDetailById(props.modelValue).then((res) => {
    if (res.status_code == 200) {
      data.clusterForm = {
        name: res.data.name,
        group_name: res.data.group_name,
        virtual_ip: res.data.ip,
        system_type: res.data.system,
        nodes: res.data.extra_parameters.cluster_ip_list,
        cluster_type: res.data.category,
        extra_param: res.data.extra_parameters,
      };
      data.zabbix_template = res.data.zabbix_template_name;
      data.group = res.data.extra_parameters.cluster_type;
      data.extra_paramters = res.data.extra_parameters.extra_paramters;
      if (res.data.category == "oracle") {
        data.db_cluster = true;
      }
    }
  });
}
//获取说有监控模板
function getTemplate() {
  GetZabbixTemplate().then((res) => {
    data.template_list = res.data;
    data.template_list_all = res.data;
  });
}
//获取节点服务器信息
function getServerList() {
  getServerNameList().then((res) => {
    data.serverList = res.data;
  });
}
//获取集群类型
function getType() {
  getClustertype().then((res) => {
    data.clusterType = res.data;
  });
}
function goBack() {
  router.go(-1);
}
//添加集群信息
function addCluster() {
  formRef.value.validate((valid) => {
    if (valid) {
      data.submitLoading = true;
      data.clusterForm.extra_param.cluster_ip_list = data.clusterForm.nodes;
      if (data.clusterForm.cluster_type.toLowerCase() == "oracle") {
        data.extra_paramters.forEach((item) => {
          data.clusterForm.extra_param[item.label] = item.value;
        });
        data.clusterForm.extra_param.extra_paramters = data.extra_paramters;
      }
      data.clusterForm.extra_param.cluster_type = data.group;
      data.clusterForm.extra_param.zabbix_template_name = data.zabbix_template;
      addNewCluster(data.clusterForm)
        .then((res) => {
          if (res.status_code == 200) {
            emit("getResource");
            returnPrePage();
          }
        })
        .finally(() => (data.submitLoading = false));
    }
  });
}
//修改集群信息
function editCluster() {
  DeleteModelById(props.modelValue).then((res) => {
    if (res.status_code == 200) {
      addCluster();
    }
  });
}

function submitMethod() {
  if (props.modelValue != 0) {
    editCluster();
    return;
  }
  addCluster();
}
const TypeEnum = {
  middleware: "middleware",
  database: "db",
};

//根据集群类型进行模板筛选
function filterTemplate(type) {
  let templateName = "";
  if (type === TypeEnum.middleware) {
    templateName = ResourceTypeEnum.middleware;
  } else {
    templateName = ResourceTypeEnum.database;
  }
  return data.template_list_all.filter((item) => item.name.includes(`${templateName}-`));
}

//通过判断是否为oracle集群,来显示参数
function changeType() {
  const isOracle = data.group[1];
  data.template_list = filterTemplate(data.group[0]);
  data.clusterForm.cluster_type = isOracle;
  if (isOracle.toLowerCase() == "oracle") {
    data.db_cluster = true;
    data.extra_paramters = [
      { isEdit: false, label: "SERVICE_NAME", key: "数据库服务名", value: "" },
      { isEdit: false, label: "PORT", key: "端口", value: "" },
      { isEdit: false, label: "USER", key: "账户", value: "" },
      { isEdit: false, label: "PASSWORD", key: "密码", value: "" },
      { isEdit: false, label: "INSTANCE", key: "实例", value: "" },
    ];
  } else {
    data.db_cluster = false;
  }
  data.clusterForm.group_name = isOracle.toLowerCase() + "集群";
}
let canAddExtraParam = computed(() => {
  return data.extra_paramters.every((item) => {
    return !item.isEdit;
  });
});

//保存额外参数
function addParams(data) {
  data.isEdit = false;
  if (data.value == "" || data.value == "待填写") {
    ElMessage.warning({
      message: "必须参数不能为空",
      center: true,
    });
    data.value = "待填写";
  }
}

//删除额外参数
function removeExtraParams(index) {
  data.extra_paramters.splice(index, 1);
}

function returnPrePage() {
  emit("closeDialog");
}

//克隆主机信息
function handleClone() {
  delete route.query.id;
  data.cloneButton = false;
}
</script>
<template>
  <div>
    <el-row justify="center" type="flex">
      <el-col>
        <el-form ref="formRef" label-width="100px" :model="data.clusterForm" :rules="data.formRules">
          <el-form-item label="集群名称" prop="name">
            <el-input v-model="data.clusterForm.name" placeholder="请输入集群名称" />
          </el-form-item>
          <el-form-item label="虚拟IP" prop="virtual_ip">
            <el-input placeholder="请输入集群虚拟IP" v-model="data.clusterForm.virtual_ip" />
          </el-form-item>

          <el-form-item label="节点列表" prop="nodes">
            <el-select multiple clearable filterable v-model="data.clusterForm.nodes">
              <el-option
                v-for="item in data.serverList"
                :label="item.server_name"
                :value="item.server_ip"
                :key="item.server_name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="集群类型" prop="cluster_type">
            <el-cascader v-model="data.group" :options="data.clusterType" @change="changeType" style="width: 100%" />
          </el-form-item>
          <el-form-item label="群组" prop="group_name">
            <el-input placeholder="请输入分组" v-model="data.clusterForm.group_name" />
          </el-form-item>
          <el-form-item prop="system_type" label="系统类型">
            <el-select v-model="data.clusterForm.system_type">
              <el-option label="Linux" value="Linux" />
              <el-option label="Windows" value="Windows" />
            </el-select>
          </el-form-item>
          <el-form-item label="监控模板" prop="zabbix_tamplate">
            <el-select v-model="data.zabbix_template" filterable multiple>
              <el-option
                v-for="(item, index) in data.template_list"
                :label="item.name"
                :value="item.name"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <div v-if="data.db_cluster">
            <el-form-item label="参数">
              <el-table :data="data.extra_paramters">
                <el-table-column prop="label" label="名称(EN)" align="center">
                  <template #default="scoped">
                    <el-input v-if="scoped.row.isEdit && !scoped.row.label" v-model="scoped.row.label" size="small" />
                    <span v-else>{{ scoped.row.label }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="key" label="名称(CN)" align="center">
                  <template #default="scoped">
                    <el-input v-if="scoped.row.isEdit && !scoped.row.key" v-model="scoped.row.key" size="small" />
                    <span v-else>{{ scoped.row.key }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="value" label="值" align="center">
                  <template #default="scoped">
                    <el-input
                      v-if="scoped.row.isEdit"
                      v-model="scoped.row.value"
                      size="small"
                      :type="scoped.row.key.includes('密码') ? 'password' : 'text'"
                    />
                    <span v-else>{{ hideSensitiveInfo(scoped.row) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                  <template #default="scoped">
                    <template v-if="scoped.row.isEdit">
                      <el-button type="primary" plain size="small" @click="addParams(scoped.row)">保存</el-button>
                    </template>
                    <template v-else>
                      <el-button type="primary" plain size="small" @click="scoped.row.isEdit = true">编辑</el-button>
                      <el-popconfirm
                        title="是否要删除此行？"
                        style="margin-left: 10px"
                        @confirm="removeExtraParams(scoped.$index, scoped.row)"
                      >
                        <template #reference>
                          <el-button type="danger" plain size="small">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </div>
        </el-form>
        <div class="submit_button">
          <el-button type="primary" @click="submitMethod" :loading="data.submitLoading">提交</el-button>
          <el-button type="primary" v-if="data.cloneButton" plain @click="handleClone">克隆</el-button>

          <el-button @click="returnPrePage">取消</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<style scoped lang="scss">
.submit_button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
// @import "../../components/form.css";
</style>
