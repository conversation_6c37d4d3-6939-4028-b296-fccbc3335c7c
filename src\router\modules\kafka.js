const Layout = () => import("@/layout/index.vue");
let children = [
  {
    path: "index",
    name: "kafka_index",
    component: () => import("@/views/kafka/index.vue"),
    meta: {
      title: "kafka",
      copyright: false,
      auth: ["admin", "kafka_index.browse"],
    },
  },
  {
    path: "theme",
    name: "kafka_theme",
    // component: () => import('@/views/kafka/index.vue'),
    meta: {
      title: "主题",
      copyright: false,
      auth: ["admin", "kafka_theme.browse"],
    },
    children: [
      {
        path: "theme_list",
        name: "kafka_theme_list",
        component: () => import("@/views/kafka/theme/list.vue"),
        meta: {
          title: "主题列表",
          copyright: false,
          auth: ["admin", "kafka_theme_list.browse"],
        },
      },
      {
        path: "theme_search",
        name: "kafka_theme_search",
        component: () => import("@/views/kafka/theme/search.vue"),
        meta: {
          title: "查询",
          copyright: false,
          auth: ["admin", "kafka_theme_search.browse"],
        },
      },
    ],
  },
  {
    path: "kafka_monitor",
    name: "kafka_monitor",
    // component: () => import('@/views/kafka/link.vue'),
    meta: {
      title: "监视器",
      copyright: false,
      auth: ["admin", "kafka_monitor.browse"],
    },
    children: [
      {
        path: "produce",
        name: "kafka_monitor_produce",
        component: () => import("@/views/kafka/monitor/produce.vue"),
        meta: {
          title: "生产",
          copyright: false,
          auth: ["admin", "kafka_monitor_produce.browse"],
        },
      },
      {
        path: "consumption",
        name: "kafka_monitor_consume",
        component: () => import("@/views/kafka/monitor/consumption.vue"),
        meta: {
          title: "消费",
          copyright: false,
          auth: ["admin", "kafka_monitor_consume.browse"],
        },
      },
      {
        path: "delay",
        name: "kafka_monitor_load",
        component: () => import("@/views/kafka/monitor/delay.vue"),
        meta: {
          title: "延迟",
          copyright: false,
          auth: ["admin", "kafka_monitor_load.browse"],
        },
      },
    ],
  },
  {
    path: "kafka_link",
    name: "kafka_link",
    component: () => import("@/views/kafka/link.vue"),
    meta: {
      title: "kafka连接",
      copyright: false,
      auth: ["admin", "kafka_link.browse"],
    },
  },
  {
    path: "kafka_cluster",
    name: "kafka_cluster",
    component: () => import("@/views/kafka/cluster.vue"),
    meta: {
      title: "集群",
      copyright: false,
      auth: ["admin", "kafka_cluster.browse"],
    },
  },
];

export default {
  path: "/kafka",
  redirect: "/kafka/kafka_link",
  name: "kafka",
  component: Layout,
  meta: {
    auth: ["admin", "kafka.browse"],
    title: "消息流视图",
    // icon: 'Kafka',
  },
  children,
};
