import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 查询选择时间区间内的周报信息
 * @param {*} data
 * @returns
 */
export function getReportForSelectedInterval() {
  return api.get("/weekly_report/list/");
}

/**
 * （批量）推送周报
 * @param {*} data
 * @returns
 */
export function pushSelectedWeeklyReports(data) {
  return api.post("/weekly_report/push_weekly_report/", JSON.stringify(data));
}

/**
 * 获取周报资源地址
 * @param {*} data
 * @returns
 */
export function getWeeklyReportSrc(data) {
  return api.get("/weekly_report" + "?filename=" + data);
}

/**
 * 测试周报
 * @returns 
 */
export function testWeeklyReportsPushConfig() {
  return api.get("/weekly_report");
}


//查看周报
export function ViewWeeklyReport(data) {
  return api.post("/weekly_report/view_pdf", { data: JSON.stringify(data) });
}
