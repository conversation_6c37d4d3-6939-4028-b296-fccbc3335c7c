<template>
  <template v-if="tagField.includes(fieldname)">
    <template v-if="SuccessAttributeList.includes(value?.toLocaleLowerCase)">
      <el-tag type="success"> {{ value }}</el-tag>
    </template>
    <template v-else-if="DangerAttributeList.includes(value?.toLocaleLowerCase)">
      <el-tag type="danger"> {{ value }}</el-tag>
    </template>
    <el-tag v-else> {{ value }}</el-tag>
  </template>
  <template v-else>
    <div class="whitespace-break-spaces">{{ value }}</div>
  </template>
</template>

<script setup lang="ts">
import { tagField, SuccessAttributeList, DangerAttributeList } from "./constants";

const props = defineProps<{
  fieldname?: any;
  value?: any;
}>();
</script>

<style scoped></style>
