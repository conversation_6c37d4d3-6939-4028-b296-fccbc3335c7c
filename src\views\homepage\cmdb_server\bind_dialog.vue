<template>
  <el-dialog v-model="showDialog" width="80%" :destroy-on-close="true" :close-on-click-modal="false">
    <div>
      <el-row type="flex" justify="center">
        <el-col :span="16">
          <el-form ref="formRef" :model="data.databaseForm" :rules="data.formRules" label-width="150px">
            <el-form-item label="软件" prop="databaseSoft">
              <el-select
                placeholder="请选择软件"
                v-model="data.databaseForm.databaseSoft"
                @change="pushExtraParams"
                style="width: 647.86px"
              >
                <el-option
                  v-for="item in data.softList"
                  :label="item.software_name"
                  :value="item.software_name"
                  :key="item.software_name"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="props.bindDataType !== bindTypeEnum.middleware" label="名称" prop="databaseName">
              <el-input v-model="data.databaseForm.databaseName" placeholder="请输入业务名称" />
            </el-form-item>

            <el-form-item label="组别" prop="group_name">
              <el-input v-model="data.databaseForm.group_name" placeholder="多个分组用“,”隔开，例如:内网服务器,Linux" />
            </el-form-item>
            <el-form-item label="监控模板" prop="template_list" v-if="props.bindDataType !== bindTypeEnum.businessApp">
              <el-select v-model="data.databaseForm.template_list" filterable multiple style="width: 647.86px">
                <el-option
                  v-for="(item, index) in props.bindDataTemplateList"
                  :label="item.name"
                  :value="item.name"
                  :key="index"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="参数">
              <el-table :data="data.extra_params" style="width: 100%">
                <el-table-column label="名称(CN)">
                  <template #default="scope">
                    <el-input v-if="scope.row.isEdit && !scope.row.key" v-model="scope.row.key" size="small" />
                    <span v-else>{{ scope.row.key }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="值">
                  <template #default="scope">
                    <el-input
                      v-if="scope.row.isEdit"
                      v-model="scope.row.value"
                      size="small"
                      :type="scope.row.key.includes('密码') ? 'password' : 'text'"
                    />
                    <span v-else>{{ hideSensitiveInfo(scope.row) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" align="center">
                  <template #default="scope">
                    <template v-if="scope.row.isEdit">
                      <el-button type="primary" plain size="small" @click="addParams(scope.row)">保存</el-button>
                    </template>
                    <template v-else>
                      <el-button type="primary" plain size="small" @click="scope.row.isEdit = true">编辑</el-button>
                      <el-popconfirm
                        title="是否要删除此行？"
                        style="margin-left: 10px"
                        @confirm="removeExtraParams(scope.$index, scope.row)"
                      >
                        <template #reference>
                          <el-button type="danger" plain size="small">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-form>
          <div class="flex justify-center mt-20px">
            <el-button type="primary" @click="bindSoftware">添加</el-button>
            <el-button type="primary" @click="closeDialog">取消</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { showNotification } from "@/plugins/element-ui";
import { addResourceInfo, getSoftParams, hideSensitiveInfo } from "../components/utils";
import { bindTypeEnum } from "../components/constants";
import { cmdbGetSoftListByType } from "@/api/modules/model_configuration/cmdb_soft";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  bindDataType: {
    type: String,
  },
  bindDataTemplateList: {
    type: Array,
  },
  bindDataServerId: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(["update:modelValue"]);
const showDialog = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
});
const data = reactive({
  extra_params: [],
  databaseForm: {
    server_id: 0,
    relation_id: 0,
    zabbix_id: 0,
    software_id: 0,
    databaseName: "",
    databaseSoft: "",

    group_name: "",
    template_list: [],
    configuration_group: [],
  },
  softList: [],
  formRules: {
    databaseName: [{ required: true, message: "请输入业务名称", trigger: "blur" }],
    databaseSoft: [{ required: true, message: "请选择软件", trigger: "blur" }],
    group_name: [{ required: true, message: "请进行分组", trigger: "blur" }],
  },
});
watch(
  () => showDialog.value,
  (newValue, oldValue) => {
    if (newValue) {
      cmdbGetSoftListByType({ type: props.bindDataType }).then((res) => {
        data.softList = res.data;
      });
      data.databaseForm.server_id = props.bindDataServerId;
    }
  }
);
const formRef = ref();
function bindSoftware() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const isUpdate = false;
      try {
        const submitResult = await addResourceInfo(data.extra_params, data.databaseForm, isUpdate, props.bindDataType);
        if (submitResult.status_code == 200) {
          if (!isUpdate) {
            showNotification("提示", "正在采集数据库信息中...", "info");
            closeDialog();
          }
        }
      } catch (error) {}
    }
  });
}
function closeDialog() {
  showDialog.value = false;
  data.extra_params = [];
}

//删除
function removeExtraParams(index, row) {
  data.extra_params.splice(index, 1);
}
//增加
function addParams(data) {
  data.isEdit = false;
  if (data.value == "") {
    ElMessage.warning({
      message: "值不能为空",
      center: true,
    });
    data.value = "待填写";
  }
}
//添加额外参数
async function pushExtraParams() {
  data.extra_params = [];
  if (props.bindDataType == bindTypeEnum.businessApp) {
    data.databaseForm.group_name = bindTypeEnum.other;
  } else {
    data.databaseForm.group_name = data.databaseForm.databaseSoft + props.bindDataType;
  }
  const paramsResult = await getSoftParams(data.softList, data.databaseForm.databaseSoft);
  data.databaseForm.software_id = paramsResult.id;
  data.extra_params = paramsResult.paramsList;
}
</script>

<style scoped></style>
