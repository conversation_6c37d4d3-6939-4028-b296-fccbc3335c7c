<script setup>
import { ref, onMounted, reactive, watch, computed, nextTick } from 'vue';
import vueMxObject from "@/utils/VueMxGraphLoader";
import { getNginxTopology, nginxAnalysis } from "@/api/modules/nginx_management";
const { vueMxClient, vueMxUtils, vueMxGraph } = vueMxObject;
import api from "@/plugins/axios";
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
// 添加对话框属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ip: {
    type: String,
    default: ''
  }
});

// 添加更新事件
const emit = defineEmits(['update:modelValue', 'update:ip']);

// 计算属性控制对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value);
    // 当对话框关闭时，清除localStorage中的状态和拓扑图加载状态
    if (!value) {
      localStorage.removeItem('nginx_topology_visible');
      localStorage.removeItem('nginx_topology_ip');
      topologyLoaded.value = false; // 重置标志
    }
  }
});

// 添加一个标志变量，用于跟踪是否已加载拓扑图
const topologyLoaded = ref(false);

// 监听 IP 变化
watch(() => props.ip, (newIp) => {
  if (newIp && dialogVisible.value && !topologyLoaded.value) {
    drawTopology();
  }
});

// 监听对话框显示状态
watch(dialogVisible, (newVal) => {
  if (newVal && props.ip && !topologyLoaded.value) {
    drawTopology();
  }
});

// 组件挂载时初始化
onMounted(() => {
  // 检查localStorage中是否有存储的拓扑图状态
  const isVisible = localStorage.getItem('nginx_topology_visible') === 'true';
  const storedIp = localStorage.getItem('nginx_topology_ip');
  
  if (isVisible && storedIp) {
    // 如果有IP参数使用存储的IP，否则使用props中的IP
    const ipToUse = storedIp || props.ip;
    if (ipToUse) {
      // 更新当前IP和对话框状态
      emit('update:ip', ipToUse);
      emit('update:modelValue', true);
      // 延迟调用绘制函数，确保DOM已经更新
      nextTick(() => {
        if (!topologyLoaded.value) {
          drawTopology();
        }
      });
    }
  } else if (props.ip && dialogVisible.value && !topologyLoaded.value) {
    drawTopology();
  }
});

let throughput = null
let isChartRendered = false;
// Add zoom level tracking
const zoomLevel = ref(1);
// 不再在mounted时立即初始化，而是在对话框打开时初始化
// onMounted(() => {
//   drawTopology();
// });

const tooltipStyle = ref({});
const tooltip = reactive({
  showTooltip: false,
  tooltipName: '',
  tooltipIp: '',
  tooltipStatus: '',
  tooltipDetails: []
})

// 添加缓存对象
const nodeDataCache = reactive({});
// 添加当前悬停的节点IP
const currentHoverIp = ref('');

// 添加工具栏折叠状态变量
const isToolbarCollapsed = ref(false);

// 添加防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 获取节点分析数据
const getNodeAnalysis = debounce((ip) => {
  if (!ip) return;

  // 如果缓存中有数据，直接使用
  if (nodeDataCache[ip]) {
    tooltip.tooltipDetails = nodeDataCache[ip];
    return;
  }

  // 调用接口获取数据
  nginxAnalysis({ ip: ip })
    .then((res) => {
      const nodeData = res.data;
      // 缓存数据
      nodeDataCache[ip] = nodeData;
      tooltip.tooltipDetails = nodeData;
    });
}, 300);

const data = reactive({
  importDialog: false,
  exportDialog: false,
  addDialog: false,
  xml: '',
  exportXml: '',
  iconDialog: false,
  icon: '',
  iconList: '',
  imgTable: [],
  form: [],
  rule: [],
  node: [],
  link: []
})
let graph

const router = useRouter();

function drawTopology() {
  // 确保IP地址可用
  const ipToUse = props.ip || localStorage.getItem('nginx_topology_ip');
  if (!ipToUse) {
    ElMessage.warning('IP地址不能为空，无法加载拓扑图');
    return;
  }

  // 设置标志表示已经开始加载拓扑图
  topologyLoaded.value = true;

  getNginxTopology({ ip: ipToUse })
    .then((res) => {
      data.node = res.data.node
      data.link = res.data.link;

      // 创建一个映射表来存储 node_to_name 到 port 的映射
      const portMap = {};
      
      // 填充映射表
      data.link.forEach(link => {
        if (!portMap[link.node_to_name]) {
          portMap[link.node_to_name] = [link.port];
        } else if (!portMap[link.node_to_name].includes(link.port)) {
          portMap[link.node_to_name].push(link.port);
        }
      });

      const iconMap = {
        'nginx': { 'none': 'nginx-normal', 'success': 'nginx-success', 'danger': 'nginx-warning' },
        'service': { 'none': 'service-normal', 'success': 'service-success', 'danger': 'service-warning' },
      };

      if (!vueMxClient.isBrowserSupported()) {
        vueMxUtils.error("浏览器不支持", 200, false); return;
      }

      const container = document.getElementById('drawioContainer');
      mxEvent.disableContextMenu(container);
      graph = new vueMxGraph(container);

      // Enable panning and zooming
      graph.setPanning(true);
      graph.panningHandler.useLeftButtonForPanning = true;

      // Enable mouse wheel zoom
      graph.zoomFactor = 1.2; // Smoother zoom

      // Customize appearance
      graph.setHtmlLabels(true);

      // Disable folding
      graph.isCellFoldable = function () { return false; };

      // Custom cursor for cells
      graph.getCursorForCell = function (cell) {
        if (cell != null && cell.value != null && cell.vertex == 1) { return 'pointer'; }
      };

      // Setup mouse tracker for tooltips
      const track = new mxCellTracker(graph);
      track.mouseMove = (sender, me) => {
        const cell = track.getCell(me);
        // 检查是否是nginx类型的节点或者nginx组的节点
        const isNginxNode = cell?.userData?.type === 'nginx' || cell?.userData?.group === 'nginx';
        if (cell?.userData?.ip && cell?.userData?.name && !isNginxNode) {
          // 只有当移动到新节点时才更新数据
          if (currentHoverIp.value !== cell.userData.ip) {
            currentHoverIp.value = cell.userData.ip;
            getNodeAnalysis(cell.userData.ip);
          }

          tooltip.showTooltip = true;
          tooltip.tooltipName = cell.userData.name;
          tooltip.tooltipIp = cell.userData.ip;
          tooltip.tooltipStatus = cell.userData.status;
          const mouseX = me.getGraphX();
          const mouseY = me.getGraphY();
          tooltipStyle.value = {
            left: `${mouseX + 10}px`,
            top: `${mouseY + 10}px`,
            position: 'absolute',
          };
        } else {
          tooltip.showTooltip = false;
          currentHoverIp.value = '';
        }
      };
      track.mouseLeave = () => { tooltip.showTooltip = false; };

      // Add click listener
      graph.addListener(mxEvent.CLICK, (sender, evt) => {
        const cell = evt.getProperty('cell');
        if (cell?.userData) {
          // 检查是否是nginx类型的节点或者nginx组的节点
          const isNginxNode = cell.userData.type === 'nginx' || cell.userData.group === 'nginx';
          
          // 处理节点点击 - 有明确的ip和name属性，且不是nginx类型
          if (cell.userData.ip && cell.userData.name && !isNginxNode) {
            console.log(cell.userData);
            // 节点跳转 - type=false表示这是一个节点而不是集群
            router.push({
              path: `/cmdb/nginx_analysis`,
              query: {
                ip: cell.userData.ip,
                name: cell.userData.name,
                status: cell.userData.status,
                ports: cell.userData.ports ? cell.userData.ports.join(',') : '',
                type: 'false' // 标记为节点
              }
            });
          }
          // 处理集群点击 - 集群有group属性
          else if (cell.userData.group && cell.userData.group !== 'nginx') {
            // 获取该集群内的所有节点
            const groupName = cell.userData.group;
            
            // nginx集群本身不可点击
            if (groupName === 'nginx') {
              console.log('nginx集群不可点击');
              return;
            }
            
            const nodesInGroup = data.node.find(g => g.group === groupName)?.children || [];
            
            // 只要有节点就可以点击
            if (nodesInGroup.length > 0) {
              // 获取nginx集群中的节点（统一使用nginx集群的第一个节点IP）
              const nginxClusterNodes = data.node.find(g => g.group === 'nginx')?.children || [];
              const nginxNode = nginxClusterNodes.length > 0 ? nginxClusterNodes[0] : null;
              console.log('nginx集群节点:', nginxNode);
              
              // 如果找到nginx节点使用它，否则使用当前集群的第一个节点
              const targetNode = nginxNode || nodesInGroup[0];
              console.log('最终使用的目标节点:', targetNode);
              
              // 获取该集群的所有端口
              const ports = data.link
                .filter(link => link.node_to_name === groupName || link.node_from_name === groupName)
                .map(link => link.port)
                .filter((port, index, self) => self.indexOf(port) === index); // 去重
              
              console.log(`集群 ${groupName} 点击，选择节点:`, targetNode);
              
              // 集群跳转 - type=true表示这是一个集群
              router.push({
                path: `/cmdb/nginx_analysis`,
                query: {
                  ip: targetNode.ip,
                  name: groupName,
                  status: 'none', // 对于集群，始终将状态设置为none
                  ports: ports.join(','),
                  type: 'true' // 标记为集群
                }
              });
            }
          }
        }
      });

      // Begin model updates
      const model = graph.getModel();
      model.beginUpdate();
      try {
        const vertexMap = {};
        const root = graph.getDefaultParent();

        // Create group style
        const groupStyle = {
          fillColor: '#f5f7fa',
          strokeColor: '#dce3e8',
          strokeWidth: 2,
          rounded: true,
          shadow: true
        };

        // Define custom connection styles
        let edgeStyle = graph.getStylesheet().getDefaultEdgeStyle();
        edgeStyle[mxConstants.STYLE_ROUNDED] = true;
        edgeStyle[mxConstants.STYLE_STROKEWIDTH] = 2;
        edgeStyle[mxConstants.STYLE_STROKECOLOR] = '#1890ff';
        edgeStyle[mxConstants.STYLE_ENDARROW] = mxConstants.ARROW_CLASSIC;

        // 判断组数量是否大于20个，选择不同的布局方式
        const useGridLayout = data.node.length > 20;
        
        // 创建节点组
        if (useGridLayout) {
          // 计算每个组的errorCount总和并按从大到小排序
          const groupsWithErrorCount = data.node.map(groupData => {
            const totalErrorCount = groupData.children.reduce((sum, node) => {
              return sum + (node.errorCount || 0);
            }, 0);
            return {
              ...groupData,
              totalErrorCount
            };
          }).sort((a, b) => b.totalErrorCount - a.totalErrorCount);
          
          // 获取容器宽度以适应屏幕
          const containerWidth = container ? container.clientWidth : window.innerWidth;
          // 计算实际可用宽度 - 不要减去太多，保留最小边距
          const paddingMargin = 40; // 左右各留20px的边距
          const availableWidth = containerWidth - paddingMargin;
          
          // 组之间的间距
          const groupSpacingX = 20;
          const groupSpacingY = 20;
          
          // 预计算所有组的尺寸，以便更好地布局
          const groupsSizes = groupsWithErrorCount.map(groupData => {
            const childCount = groupData.children.length;
            const maxNodesPerRow = 5;
            const rowCount = Math.ceil(childCount / maxNodesPerRow);
            const groupWidth = 20 + 130 * Math.min(maxNodesPerRow, childCount);
            const groupHeight = 30 + 100 * rowCount;
            
            return {
              group: groupData.group,
              data: groupData,
              width: groupWidth,
              height: groupHeight,
              totalErrorCount: groupData.totalErrorCount
            };
          });
          
          // 动态计算每行可放置的组
          let currentX = 20; // 从左侧20px的边距开始
          let currentY = 20;
          let maxHeightInRow = 0;
          
          groupsSizes.forEach((groupSize, index) => {
            // 检查是否需要换行 - 如果当前X + 组宽度 + 组之间的间距 > 可用宽度
            if (currentX + groupSize.width > availableWidth) {
              // 换行
              currentX = 20;
              currentY += maxHeightInRow + groupSpacingY;
              maxHeightInRow = 0;
            }
            
            const groupData = groupSize.data;
            const group = groupData.group;
            const children = groupData.children;
            
            // 创建组容器
            const groupBox = graph.insertVertex(
              root, null, '', currentX, currentY, groupSize.width, groupSize.height,
              `fillColor=${groupStyle.fillColor};strokeColor=${groupStyle.strokeColor};strokeWidth=${groupStyle.strokeWidth};rounded=${groupStyle.rounded ? 1 : 0};shadow=${groupStyle.shadow ? 1 : 0};`
            );
            groupBox.userData = { group: group };
            
            // 添加组标签，包含错误数量信息
            const label = graph.insertVertex(
              groupBox, null, 
              truncateText(`${group} (错误:${groupData.totalErrorCount})`, Math.floor((groupSize.width - 20) / 14)), // 根据宽度动态截断
              10, 5, groupSize.width - 20, 25,
              'fontSize=14;fontStyle=1;align=center;verticalAlign=middle;strokeColor=none;fillColor=#e6f7ff;rounded=1;spacing=10;'
            );
            
            let xOffset = 20;
            let yOffset = 35;
            let rowNodes = 0;
            
            children.forEach((node, index) => {
              if (rowNodes >= 5) { // 每行最多5个节点
                xOffset = 20;
                yOffset += 100;
                rowNodes = 0;
              }
              
              // 获取图标和样式
              const { status, type } = node;
              const iconName = iconMap[type][status];
              
              // 创建样式
              const statusColorMap = {
                'success': '#52c41a',
                'danger': '#faad14',
                'none': '#8c8c8c'
              };
              
              const backgroundColor = status === 'success' ? '#f6ffed' :
                status === 'danger' ? '#fff9e6' : '#f5f5f5';
              
              const fontColor = '#000000';
              
              const style = {
                [mxConstants.STYLE_SHAPE]: mxConstants.SHAPE_LABEL,
                [mxConstants.STYLE_STROKECOLOR]: statusColorMap[status] || '#d9d9d9',
                [mxConstants.STYLE_FILLCOLOR]: backgroundColor,
                [mxConstants.STYLE_FONTCOLOR]: fontColor,
                [mxConstants.STYLE_ALIGN]: mxConstants.ALIGN_CENTER,
                [mxConstants.STYLE_VERTICAL_ALIGN]: mxConstants.ALIGN_BOTTOM,
                [mxConstants.STYLE_FONTSIZE]: 12,
                [mxConstants.STYLE_FONTWEIGHT]: 'bold',
                [mxConstants.STYLE_IMAGE_ALIGN]: mxConstants.ALIGN_CENTER,
                [mxConstants.STYLE_IMAGE_VERTICAL_ALIGN]: mxConstants.ALIGN_TOP,
                [mxConstants.STYLE_IMAGE]: new URL(`../../assets/icons/${iconName}.svg`, import.meta.url).href,
                [mxConstants.STYLE_IMAGE_WIDTH]: 24,
                [mxConstants.STYLE_IMAGE_HEIGHT]: 24,
                [mxConstants.STYLE_SPACING_TOP]: 15,
                [mxConstants.STYLE_ROUNDED]: 1,
                [mxConstants.STYLE_SHADOW]: 1,
                [mxConstants.STYLE_SPACING]: 2,
                [mxConstants.STYLE_OVERFLOW]: 'visible',
                [mxConstants.STYLE_WHITE_SPACE]: 'wrap',
                [mxConstants.STYLE_TEXT_OVERFLOW]: 'ellipsis',
                [mxConstants.STYLE_WIDTH]: 100,
                [mxConstants.STYLE_HEIGHT]: 70
              };
              
              graph.getStylesheet().putCellStyle(`${type}-${status}`, style);
              
              // 创建节点 - 替换❌和✓为带颜色的数字
              const errorCount = node.errorCount || 0;
              const errorCountDisplay = `<span style="color:${errorCount > 0 ? '#f5222d' : '#52c41a'};font-weight:bold;">${errorCount}</span>`;
              
              const vertex = graph.insertVertex(
                groupBox, null,
                `${truncateText(node.name, 7)}\n${errorCountDisplay} ${node.avgTime}`,
                xOffset, yOffset, 100, 70,
                `${type}-${status}`
              );
              
              // 添加ports数据到userData中
              node.ports = portMap[group] || [];
              vertex.userData = node;
              
              vertexMap[group] = groupBox;
              
              xOffset += 130;
              rowNodes++;
            });
            
            // 更新位置信息和行最大高度
            currentX += groupSize.width + groupSpacingX;
            maxHeightInRow = Math.max(maxHeightInRow, groupSize.height);
          });
        } else {
          // 小于等于20个组时使用原来的镭射布局并保留连线
          data.node.forEach((groupData, groupIndex) => {
            const group = groupData.group;
            const children = groupData.children;

            // 计算组尺寸
            const childCount = children.length;
            const maxNodesPerRow = 5;
            const rowCount = Math.ceil(childCount / maxNodesPerRow);
            const groupWidth = 20 + 130 * Math.min(maxNodesPerRow, childCount);
            const groupHeight = 30 + 100 * rowCount;

            // 创建组容器
          const groupBox = graph.insertVertex(
            root, null, '', groupIndex * (groupWidth + 60), 0, groupWidth, groupHeight,
            `fillColor=${groupStyle.fillColor};strokeColor=${groupStyle.strokeColor};strokeWidth=${groupStyle.strokeWidth};rounded=${groupStyle.rounded ? 1 : 0};shadow=${groupStyle.shadow ? 1 : 0};`
          );
          groupBox.userData = { group: group };

            // 添加组标签
          const label = graph.insertVertex(
            groupBox, null, truncateText(group, Math.floor((groupWidth - 20) / 14)), 10, 5, groupWidth - 20, 25, // 根据宽度动态截断
            'fontSize=14;fontStyle=1;align=center;verticalAlign=middle;strokeColor=none;fillColor=#e6f7ff;rounded=1;spacing=10;'
          );

          let xOffset = 20;
          let yOffset = 35;
          let rowNodes = 0;

          children.forEach((node, index) => {
            if (rowNodes >= maxNodesPerRow) {
              xOffset = 20;
              yOffset += 100;
              rowNodes = 0;
            }

              // 获取图标和样式
            const { status, type } = node;
            const iconName = iconMap[type][status];

              // 创建样式
            const statusColorMap = {
              'success': '#52c41a',
              'danger': '#faad14',
              'none': '#8c8c8c'
            };

            const backgroundColor = status === 'success' ? '#f6ffed' :
              status === 'danger' ? '#fff9e6' : '#f5f5f5';

            const fontColor = '#000000';

            const style = {
              [mxConstants.STYLE_SHAPE]: mxConstants.SHAPE_LABEL,
              [mxConstants.STYLE_STROKECOLOR]: statusColorMap[status] || '#d9d9d9',
              [mxConstants.STYLE_FILLCOLOR]: backgroundColor,
              [mxConstants.STYLE_FONTCOLOR]: fontColor,
              [mxConstants.STYLE_ALIGN]: mxConstants.ALIGN_CENTER,
              [mxConstants.STYLE_VERTICAL_ALIGN]: mxConstants.ALIGN_BOTTOM,
              [mxConstants.STYLE_FONTSIZE]: 12,
              [mxConstants.STYLE_FONTWEIGHT]: 'bold',
              [mxConstants.STYLE_IMAGE_ALIGN]: mxConstants.ALIGN_CENTER,
              [mxConstants.STYLE_IMAGE_VERTICAL_ALIGN]: mxConstants.ALIGN_TOP,
              [mxConstants.STYLE_IMAGE]: new URL(`../../assets/icons/${iconName}.svg`, import.meta.url).href,
              [mxConstants.STYLE_IMAGE_WIDTH]: 24,
              [mxConstants.STYLE_IMAGE_HEIGHT]: 24,
              [mxConstants.STYLE_SPACING_TOP]: 15,
              [mxConstants.STYLE_ROUNDED]: 1,
              [mxConstants.STYLE_SHADOW]: 1,
              [mxConstants.STYLE_SPACING]: 2,
              [mxConstants.STYLE_OVERFLOW]: 'visible',
              [mxConstants.STYLE_WHITE_SPACE]: 'wrap',
              [mxConstants.STYLE_TEXT_OVERFLOW]: 'ellipsis',
              [mxConstants.STYLE_WIDTH]: 100,
              [mxConstants.STYLE_HEIGHT]: 70
            };

            graph.getStylesheet().putCellStyle(`${type}-${status}`, style);

              // 创建节点 - 替换❌和✓为带颜色的数字
              const errorCount = node.errorCount || 0;
              const errorCountDisplay = `<span style="color:${errorCount > 0 ? '#f5222d' : '#52c41a'};font-weight:bold;">${errorCount}</span>`;
              
            const vertex = graph.insertVertex(
              groupBox, null,
                `${truncateText(node.name, 7)}\n${errorCountDisplay} ${node.avgTime}`,
              xOffset, yOffset, 100, 70,
              `${type}-${status}`
            );
            
            // 添加ports数据到userData中
            node.ports = portMap[group] || [];
            vertex.userData = node;
            
            vertexMap[group] = groupBox;

            xOffset += 130;
            rowNodes++;
          });
        });

          // 创建连接线
        data.link.forEach(link => {
          const fromGroupBox = vertexMap[link.node_from_name];
          const toGroupBox = vertexMap[link.node_to_name];

          if (fromGroupBox && toGroupBox) {
              // 创建带端口信息的边
            const edge = graph.insertEdge(
              root, null, `端口: ${link.port}`, fromGroupBox, toGroupBox, ''
            );

              // 添加用户数据到边以便可能的交互
            edge.userData = { port: link.port };
          }
        });

          // 使用镭射布局
        const layout = new mxRadialTreeLayout(graph);
        layout.levelDistance = 220;
        layout.nodeDistance = 150;
        layout.execute(graph.getDefaultParent());
        }

        // 不再使用fit函数默认缩放，而是根据布局类型处理
        if (useGridLayout) {
          // 对于网格布局，我们默认不缩放，让用户看到全部内容
          zoomLevel.value = graph.view.getScale();
        } else {
          // 镭射布局时自动居中
          if (container) {
            // 获取图形边界
            const bounds = graph.getGraphBounds();
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;
            const scale = graph.view.scale;

            // 计算中心位置
            const centerX = (containerWidth - bounds.width * scale) / 2;
            const centerY = (containerHeight - bounds.height * scale) / 2;

            // 设置视图以居中图形
            graph.view.setTranslate(centerX / scale, centerY / scale);
          }
          
          // 镭射布局使用fit保持原有行为
          graph.fit();
          zoomLevel.value = graph.view.getScale();
        }
      } finally {
        model.endUpdate();
      }
    })
    .catch((error) => {
      // 如果加载失败，重置标志，以便可以再次尝试
      topologyLoaded.value = false;
    });
}
function getXml() {
  data.exportDialog = true
  const encoder = new mxCodec();
  const node = encoder.encode(graph.getModel());
  data.xml = new XMLSerializer().serializeToString(node);
}
function importIcon() {
  data.iconDialog = true
}
function addIcon() {
  data.addDialog = true
}
// function exportXml() {
//   data.importDialog = true
//   data.exportXml=
// '<mxGraphModel>                                                                                             '+
// '  <root>                                                                                                                                          '+
// '    <mxCell id="0"/>                                                                                                                              '+
// '    <mxCell id="1" parent="0"/>                                                                                                                   '+
// '    <app appId="" appName="" protocol="" ip="" port="" context="" heartBeatUrl="" id="2">                                                         '+
// '      <mxCell style="verticalLabelPosition=top;verticalAlign=bottom;shadow=1;fillColor=#FFFFFF" vertex="1" connectable="0" parent="1" type="app"> '+
// '        <mxGeometry x="100" y="320" width="20" height="40" as="geometry"/>                                                                        '+
// '      </mxCell>                                                                                                                                   '+
// '    </app>                                                                                                                                        '+
// '  </root>                                                                                                                                         '+
// '</mxGraphModel>                                                                                                                                   ';
// }
// function submitXml() {
//   const doc = mxUtils.parseXml(data.exportXml);
//   console.log(data.exportXml)
//   const codec = new mxCodec(doc);
// console.log(codec)
//   codec.decode(doc.documentElement, graph.getModel());
// data.importDialog = false
// }

// Add zoom control functions
function zoomIn() {
  if (graph) {
    graph.zoomIn();
    zoomLevel.value = Math.round(graph.view.getScale() * 100) / 100;
  }
}

function zoomOut() {
  if (graph) {
    graph.zoomOut();
    zoomLevel.value = Math.round(graph.view.getScale() * 100) / 100;
  }
}

function zoomActual() {
  if (graph) {
    // Reset to original size (1:1)
    graph.zoomActual();
    zoomLevel.value = 1;
  }
}

function fitGraph() {
  if (graph) {
    graph.fit();
    zoomLevel.value = Math.round(graph.view.getScale() * 100) / 100;
  }
}

// 切换工具栏折叠状态
function toggleToolbar() {
  isToolbarCollapsed.value = !isToolbarCollapsed.value;
}

// 添加文本截断函数 - 如果已存在则不需要重复添加
function truncateText(text, maxLength) {
  if (text.length > maxLength) {
    return text.substring(0, maxLength) + '...';
  }
  return text;
}

</script>

<template>
  <el-dialog v-model="dialogVisible" fullscreen :show-close="true" :destroy-on-close="true"
    custom-class="nginx-topology-dialog">
    <div class="nginx-topology-container">
      <div class="topology-content">
        <div id="drawioContainer" class="contain"></div>
        <div id="customTooltip" v-show="tooltip.showTooltip" :style="tooltipStyle">
          <div class="tooltip-title">名称：{{ tooltip.tooltipName }}</div>
          <div class="tooltip-ip">IP：{{ tooltip.tooltipIp }}</div>
          <div class="tooltip-status">状态：
            <el-tag size="small" type="success" v-if="tooltip.tooltipStatus === 'success'">正常</el-tag>
            <el-tag size="small" type="danger" v-else-if="tooltip.tooltipStatus === 'danger'">异常</el-tag>
            <el-tag size="small" type="info" v-else>未知</el-tag>
          </div>
          <div class="tooltip-details">
            <el-table class="dark-table" :data="tooltip.tooltipDetails" border size="small">
              <el-table-column prop="time" label="时间" />
              <el-table-column prop="times" label="调用次数" />
              <el-table-column prop="average" label="平均时延" />
              <el-table-column prop="response" label="响应错误次数" />
            </el-table>
          </div>
        </div>
      </div>

      <div class="tools" :class="{'tools-collapsed': isToolbarCollapsed}">
        <div class="tool-header">
        <div class="tool-title">工具栏</div>
          <!-- 移动到工具栏右上角的折叠按钮 -->
          <div class="toolbar-toggle" @click="toggleToolbar">
            <i :class="isToolbarCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'">
              {{ isToolbarCollapsed ? '>' : '<' }}
            </i>
          </div>
        </div>
        
        <!-- Zoom controls -->
        <div class="tool-section" v-show="!isToolbarCollapsed">
          <div class="tool-group-title">缩放控制</div>
          <div class="zoom-controls">
            <button class="tool-item" title="放大" @click="zoomIn">
              <i class="el-icon-plus">+</i>
            </button>
            <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
            <button class="tool-item" title="缩小" @click="zoomOut">
              <i class="el-icon-minus">-</i>
            </button>
            <button class="tool-item" title="适应窗口" @click="fitGraph">
              <i class="el-icon-full-screen">适配</i>
            </button>
          </div>
        </div>
        <!-- 图例说明 -->
        <div class="tool-section" v-show="!isToolbarCollapsed">
          <div class="tool-group-title">图例说明</div>
          <div class="legend-content">
            <div class="legend-item"><span class="legend-icon" style="color:#52c41a;font-weight:bold;">0</span> 服务正常</div>
            <div class="legend-item"><span class="legend-icon" style="color:#f5222d;font-weight:bold;">N</span> 服务出现N次错误</div>
            <div class="legend-item"><span class="legend-icon">ms</span> 平均响应时间</div>
          </div>
        </div>
        <!-- Export controls -->
        <div class="tool-section" v-show="!isToolbarCollapsed">
          <div class="tool-group-title">导出操作</div>
          <button class="tool-item" title="导出xml" @click="getXml">
            导出XML
          </button>
        </div>
      </div>

      <el-dialog v-model="data.exportDialog" title="XML信息" width="800" append-to-body>
        <div class="xml-content">
          <pre>{{ data.xml }}</pre>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="data.exportDialog = false">关闭</el-button>
            <el-button type="primary" @click="data.exportDialog = false">复制</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<style scoped>
/* Base styles */
.nginx-topology-container {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f5f7fa;
}

.topology-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.contain {
  width: 100%;
  height: 100%;
  border-radius: 0;
  border: none;
  box-shadow: none;
  overflow: hidden;
  position: relative;
  background-color: #fff;
  background-image: linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Toolbox styles - 更现代化的工具栏 */
.tools {
  display: flex;
  flex-direction: column;
  position: absolute;
  right: 20px;
  top: 20px;
  /* 调整工具栏位置至顶部 */
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 100;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

/* 折叠状态的工具栏 */
.tools-collapsed {
  min-width: auto;
  width: 40px;
  padding: 10px 5px;
  align-items: center;
}

/* 添加工具栏头部容器 */
.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eaeaea;
}

/* 工具栏标题 */
.tool-title {
  font-size: 16px;
  font-weight: bold;
  color: #1a1a1a;
}

/* 工具栏切换按钮 - 移至右上角 */
.toolbar-toggle {
  width: 24px;
  height: 24px;
  background: rgba(240, 240, 240, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid rgba(200, 200, 200, 0.5);
  transition: all 0.2s ease;
}

.toolbar-toggle:hover {
  background: rgba(230, 230, 230, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.toolbar-toggle i {
  font-size: 14px;
  color: #606266;
  font-weight: bold;
  font-style: normal;
}

.tool-section {
  margin-bottom: 15px;
}

.tool-group-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
  text-align: center;
}

.tool-item {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  color: #606266;
  cursor: pointer;
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
  text-align: center;
  width: 100%;
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.tool-item:hover {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #c6e2ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.tool-item.active {
  color: #409eff;
  background-color: #ecf5ff;
  border-color: #409eff;
}

/* Zoom controls */
.zoom-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.zoom-controls .tool-item {
  padding: 5px 8px;
  margin: 3px;
  width: auto;
  flex: 0 0 auto;
}

.zoom-level {
  padding: 0 8px;
  color: #606266;
  font-weight: 500;
  font-size: 14px;
}

/* Tooltip styles - 更美观的提示框 */
#customTooltip {
  position: absolute;
  background: rgba(33, 33, 33, 0.9);
  color: #ffffff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  font-family: 'Arial', sans-serif;
  z-index: 1000;
  transition: all 0.3s;
  max-width: 650px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tooltip-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-ip,
.tooltip-status {
  font-size: 14px;
  text-align: left;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.tooltip-details {
  margin-top: 10px;
}

/* Table styles - 黑暗主题表格 */
:deep(.el-table) {
  background-color: transparent;
  color: #ffffff;
}

:deep(.el-table th) {
  background-color: #1a237e !important;
  background-size: 100% 100%;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-table .cell) {
  color: #ffffff;
  font-size: 13px;
}

:deep(.el-table tr) {
  background: rgba(26, 35, 126, 0.5);
  transition: background 0.3s;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background: rgba(33, 45, 139, 0.8);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background: rgba(40, 53, 147, 0.6);
}

:deep(.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* XML预览内容 */
.xml-content {
  max-height: 400px;
  overflow: auto;
  background-color: #282c34;
  color: #abb2bf;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Courier New', monospace;
}

.xml-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

/* Dialog 样式重写 */
:deep(.nginx-topology-dialog) {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.nginx-topology-dialog .el-dialog__header) {
  padding: 0;
  margin: 0;
  height: 0;
  /* 隐藏对话框头部 */
}

:deep(.nginx-topology-dialog .el-dialog__title) {
  display: none;
}

:deep(.nginx-topology-dialog .el-dialog__body) {
  flex: 1;
  padding: 0;
  overflow: hidden;
  display: flex;
  align-items: stretch;
  height: 100vh;
  width: 100vw;
}

/* 调整关闭按钮样式，让它更醒目 */
:deep(.nginx-topology-dialog .el-dialog__headerbtn) {
  position: absolute;
  top: 20px;
  left: 20px; /* 改为左上角，避免与工具栏冲突 */
  z-index: 999; /* 增大z-index确保在最上层 */
  font-size: 24px; /* 增大字体 */
  background-color: rgba(0, 0, 0, 0.6); /* 更明显的背景 */
  border-radius: 50%;
  width: 48px; /* 增大尺寸 */
  height: 48px; /* 增大尺寸 */
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3); /* 加强阴影 */
  border: 2px solid rgba(255, 255, 255, 0.8); /* 明显的边框 */
  transition: all 0.3s ease;
  animation: pulseButton 2s infinite; /* 添加脉冲动画使其更醒目 */
}

@keyframes pulseButton {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}

:deep(.nginx-topology-dialog .el-dialog__headerbtn:hover) {
  background-color: rgba(220, 20, 60, 0.8); /* 红色高亮，表示关闭 */
  transform: scale(1.1);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5);
}

:deep(.nginx-topology-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: #ffffff; /* 白色图标 */
  font-weight: bold;
  font-size: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tools {
    right: 10px;
    top: 10px;
    padding: 10px;
    min-width: 150px;
  }
  
  .toolbar-toggle {
    right: 160px;
  }
}

/* 图例样式 */
.legend-content {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #606266;
}

.legend-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-right: 8px;
  font-weight: bold;
}
</style>