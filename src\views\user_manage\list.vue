<route lang="yaml">
meta:
  enabled: false
</route>

<script setup>
import FormMode from "./components/FormMode/index.vue";
import eventBus from "@/utils/eventBus";
import { GetUsers, deleteUser } from "@/api/modules/user_api_management/users";
import { usePagination } from "@/utils/composables";
import { ElMessage } from "element-plus";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
const tableRef = ref();
const data = ref({
  loading: false,
  /**
   * 详情展示模式
   * router 路由跳转
   * dialog 对话框
   * drawer 抽屉
   */
  formMode: "router",
  // 详情
  formModeProps: {
    visible: false,
    id: "",
  },
  // 搜索
  search: {
    account: "",
  },
  searchMore: false,
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },
  // 列表数据
  dataList: [],
  allList: [],
});

onMounted(() => {
  getDataList();
  if (data.value.formMode === "router") {
    eventBus.on("get-data-list", () => {
      getDataList();
    });
  }
});

onBeforeUnmount(() => {
  if (data.value.formMode === "router") eventBus.off("get-data-list");
});

function getDataList() {
  GetUsers().then((res) => {
    let list = res.data.sort((a, b) => {
      return a.id - b.id;
    });
    data.value.dataList = list;
    data.value.allList = list;
    paging(list);
  });
}
function paging(list) {
  pagination.value.total = list.length;
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => getDataList());
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => getDataList());
}

// 字段排序
function sortChange(prop, order) {
  onSortChange(prop, order).then(() => getDataList());
}

function onCreate() {
  if (data.value.formMode === "router") {
    router.push({
      name: "create_user",
    });
  } else {
    data.value.formModeProps.id = "";
    data.value.formModeProps.visible = true;
  }
}

function onEdit(row) {
  if (data.value.formMode === "router") {
    router.push({
      name: "edit_user",
      query: row,
    });
  } else {
    data.value.formModeProps.id = row.id;
    data.value.formModeProps.visible = true;
  }
}

function onDel(row) {
  ElMessageBox.confirm(`确认删除[${row.name}]管理员吗？`, "确认信息").then(() => {
    deleteUser(row.id).then((res) => {
      getDataList();
    });
  });
}
function queryData() {
  if (data.value.search.account === "" || data.value.search.account == null) {
    getDataList();
  }
  //搜索
  let list = [];
  data.value.allList.filter((item) => {
    if (item.name.indexOf(data.value.search.account) !== -1) {
      list.push(item);
    }
  });
  data.value.dataList = list;
  paging(list);
}
</script>

<template>
  <div>
    <page-main title="账号管理">
      <div class="flex justify-between">
        <el-space wrap>
          <el-button v-auth="['admin', 'user_list.create']" type="primary" @click="onCreate">
            <template #icon>
              <el-icon>
                <svg-icon name="i-ep:plus" />
              </el-icon>
            </template>
            新增账户
          </el-button>
        </el-space>
        <el-space wrap>
          <div class="w-220px">
            <el-input v-model="data.search.account" placeholder="请输入账号" clearable @keyup.enter="queryData" />
          </div>
          <el-button type="primary" @click="queryData()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>

      <el-table
        ref="tableRef"
        v-loading="data.loading"
        class="list-table"
        :data="data.dataList"
        border
        stripe
        highlight-current-row
        :default-sort="{ prop: 'id', order: 'descending' }"
        @sort-change="sortChange"
        @selection-change="data.batch.selectionDataList = $event"
      >
        <!-- <el-table-column prop="id" label="ID" /> -->
        <el-table-column prop="name" label="帐号" align="center" />
        <el-table-column prop="notes" label="备注" width="500" align="center" />
        <el-table-column prop="full_name" label="姓名" width="200" align="center" />
        <el-table-column label="职位" width="200" align="center">
          <template #default="scope">
            <el-tag>{{ scope.row.position }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" align="center" fixed="right">
          <template #default="scope">
            <el-button
              v-auth="['admin', 'user_list.edit']"
              type="primary"
              size="small"
              plain
              @click="onEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button v-auth="['admin', 'user_list.edit']" type="danger" size="small" plain @click="onDel(scope.row)">
              删除
            </el-button>
            <!-- <el-dropdown @command="handleMoreOperating($event, scope.row)">
              <el-button size="small">
                更多操作
                <el-icon class="el-icon--right">
                  <svg-icon name="i-ep:arrow-down" />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="resetPassword" @click="onResetPassword">
                    修改密码
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown> -->
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="pagination"
        background
        @current-change="currentChange"
        @size-change="sizeChange"
      />
    </page-main>
    <FormMode
      v-if="['dialog', 'drawer'].includes(data.formMode)"
      :id="data.formModeProps.id"
      v-model="data.formModeProps.visible"
      :mode="data.formMode"
      @success="getDataList"
    />
  </div>
</template>

<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
}
</style>
