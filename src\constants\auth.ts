import useMenuStore from "@/store/modules/menu";
import { deepClone } from "@/utils";
/**
 * 获取权限列表：根据导航结构获取
 * @returns
 */
export function authList() {
  const routes = deepClone(useMenuStore().allMenus[0].children);
  const auth = removeHideSidebar(routes, 0);
  return auth;
}
/**
 * 移除隐藏的导航，详情页，或者新增页面
 * @param menus
 * @param pindex
 * @returns
 */
function removeHideSidebar(menus, pindex) {
  let tree = [];
  for (let index = 0; index < menus.length; index++) {
    const element = menus[index];
    let authItem = { id: pindex + "" + index, menu: element.meta.title, auth: [] };
    authItem.auth.push({ lable: "查看" + element.meta.title, value: element.name + ".browse" });
    if (oprationList[element.name]) {
      const itemopration = oprationList[element.name];
      for (const key in itemopration) {
        authItem.auth.push({ lable: itemopration[key], value: element.name + "." + key });
      }
    }
    if (element.meta.sidebar === false) {
      menus.splice(index, 1);
      index--;
    } else {
      if (element.children) {
        authItem.children = removeHideSidebar(element.children, pindex + "" + index);
      }
      tree.push(authItem);
    }
  }
  pindex++;
  return tree;
}

// 操作列表：除了查看之外的需要控制的操作权限
export const oprationList = {
  user_list: { create: "新增", edit: "编辑", remove: "删除" }, //用户列表的权限
  nginx_list: { archive: "备份配置", restore: "恢复配置" }, //nginx配置的权限
  publish: {
    task_list: "执行记录",
    task_create: "作业执行",
    task_remove: "删除执行记录",
    task_managment: "运维工具管理",
    task_managment_create: "上传运维工具",
    task_managment_remove: "删除",
    task_managment_release: "发布",
    runnerlog: "执行日志",
  }, //功能包管理的权限
  task: { remove: "删除", browse: "作业执行" }, //执行记录的权限
  task_managment: { browse: "运维工具管理", create: "上传运维工具", remove: "删除", release: "发布" },
  runnerlog: { browse: "执行日志" },
  cmdb_server_information: { excute: "执行任务" }, //服务器管理的权限
  cmdb_database_information: { excute: "执行任务" }, //数据库管理的权限
};

//版本信息
export enum licenseInfo {
  flagship = "flagship",
  standard = "standard",
}
export const licenseInfoOption = { [licenseInfo.flagship]: "旗舰版", [licenseInfo.standard]: "基础版" };

// 旗舰版路由 具有全部路由-也就是没有需要排除的
// 标准版路由 具有部分路由-也就是需要排除一部分不能看的,包括路由【*.browse】和功能[*.*]
export const excludingFeatures = {
  [licenseInfoOption[licenseInfo.flagship]]: [],
  [licenseInfoOption[licenseInfo.standard]]: [
    "cmdb_server_information.excute",
    "cmdb_database_information.excute",
    "business_analysis.browse",
    "analysis_apm.browse",
    "publish.task_list",
    "publish.task_remove",
    "publish.task_managment",
    "self_healing.browse",
    "aotumation.browse",
    "publish.browse",
    "kafka.browse",
  ],
};
