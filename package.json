{"name": "fantastic-admin", "version": "0.1.0", "scripts": {"dev": "vite", "dev:test": "vite --mode devtest", "build:test": "vite build --mode test", "build": "vite build", "serve:test": "http-server ./dist-test -o", "serve": "http-server ./dist -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.js", "lint:eslint": "eslint src/**/*.{js,vue} --fix", "lint:stylelint": "stylelint src/**/*.{css,scss,vue} --fix", "prepare": "husky install", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@antv/g2": "^5.1.5", "@antv/g2plot": "2.4.31", "@antv/g6": "^4.8.23", "@antv/x6": "1.34.6", "@bytemd/plugin-gfm": "^1.17.2", "@bytemd/plugin-highlight": "^1.17.2", "@bytemd/vue-next": "^1.17.2", "@dagrejs/graphlib": "^2.2.2", "@imengyu/vue3-context-menu": "^1.1.1", "@tinymce/tinymce-vue": "^5.0.0", "@vueuse/core": "^9.2.0", "@vueuse/integrations": "^9.2.0", "ansi_up": "^6.0.2", "autoprefixer": "10.4.13", "axios": "^0.27.2", "bytemd": "^1.17.2", "d3": "^7.9.0", "dagre": "^0.8.5", "dagre-d3": "^0.6.4", "dayjs": "^1.11.5", "echarts": "^5.6.0", "element-plus": "2.7.2", "encryptlong": "^3.1.4", "hotkeys-js": "^3.9.5", "html-to-markdown": "^1.0.0", "mitt": "^3.0.0", "mockjs": "^1.1.0", "mxgraph": "^4.2.2", "node-waves": "^0.7.6", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.1", "pinia": "^2.0.22", "pnpm": "^7.13.5", "print-js": "^1.6.0", "qs": "^6.11.0", "relation-graph": "^2.0.26", "rollup": "^2.79.0", "sf-relation": "^0.1.5", "sortablejs": "^1.15.0", "spinkit": "^2.0.1", "splitpanes": "^3.1.5", "swiper": "^8.4.3", "tinymce": "^6.1.2", "turndown": "^7.1.2", "vexip-ui": "^2.2.20", "vue": "^3.2.38", "vue-i18n": "^9.2.2", "vue-pdf-embed": "^1.2.1", "vue-router": "^4.1.5", "xlsx": "^0.18.5", "xterm": "^5.2.1", "xterm-addon-attach": "^0.8.0", "xterm-addon-fit": "^0.7.0"}, "devDependencies": {"@iconify/json": "^2.1.104", "@iconify/vue": "^3.2.1", "@intlify/unplugin-vue-i18n": "^0.5.0", "@unocss/preset-icons": "^0.45.18", "@vexip-ui/plugins": "^1.6.0", "@vitejs/plugin-vue": "^3.1.0", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/compiler-sfc": "^3.2.38", "eslint": "^8.23.0", "eslint-plugin-vue": "^9.4.0", "esno": "^0.16.3", "fs-extra": "^10.1.0", "http-server": "^14.1.1", "husky": "^8.0.1", "increase-memory-limit": "^1.0.7", "inquirer": "^9.1.1", "lint-staged": "^13.0.3", "plop": "^3.1.1", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.4", "sass": "1.54.8", "stylelint": "^14.11.0", "stylelint-config-recommended-scss": "^7.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^28.0.0", "stylelint-scss": "^4.3.0", "svgo": "^2.8.0", "unocss": "^0.45.18", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^3.1.0", "vite-plugin-banner": "^0.5.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-mock": "^2.9.6", "vite-plugin-pages": "^0.26.0", "vite-plugin-pwa": "^0.12.7", "vite-plugin-restart": "^0.2.0", "vite-plugin-spritesmith": "^0.1.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-layouts": "^0.6.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.0.3"}, "resolutions": {"esbuild": "0.18.20", "@intlify/bundle-utils": "10.0.1", "@intlify/shared": "10.0.1", "@intlify/message-compiler": "10.0.1", "vue-i18n": "9.2.2", "@intlify/unplugin-vue-i18n": "0.4.0"}}