const Layout = () => import('@/layout/index.vue')
const IframeLayout = () => import('@/layout/iframe.vue')
let children = [
  {
    path: 'screen_player',
    name: 'screen_player',
    component: () => import('@/views/player/playerList.vue'),
    meta: {
      title: '大屏播放',
      copyright: false,
      auth: ['admin', 'screen_player.browse'],
      // cache: true,
    }
  },
  {
    path: 'player_visualization',
    name: 'player_visualization',
    component: () => import('@/views/large_screen/index.vue'),
    meta: {
      title: '大屏可视化配置',
      auth: ['admin', 'player_visualization.browse'],
      // cache: true,
    }
  },
  {
    path: 'player_iframe',
    name: 'player_iframe',
    component: () => import('@/views/large_screen/dashbord.vue'),
    meta: {
      title: '大屏可视化面板',
      copyright: false,
      // cache: true,
      sidebar: false,
      auth: ['admin', 'player_visualization.browse'],
      activeMenu: '/visualization/player_visualization'
    }
  },
  {
    path: 'player_list',
    name: 'player_list',
    component: () => import('@/views/large_screen/playerList.vue'),
    meta: {
      title: '大屏播放列表',
      copyright: false,
      // cache: true,
      sidebar: false,
      auth: ['admin', 'screen_player.browse'],
      activeMenu: '/visualization/player_visualization'
    }
  },

]


export default {
  path: '/visualization',
  redirect: '/visualization/player_visualization',
  component: Layout,
  name: 'visualization',
  meta: {
    title: '大屏管理',
    icon: 'visualization',
    auth: ['admin', 'visualization.browse'],
  },
  children
}
