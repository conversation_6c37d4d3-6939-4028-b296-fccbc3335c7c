import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 脚本管理
 * @param {*} data
 * @returns
 */
export function getScriptList() {
  //获取所有脚本
  return api.get("/script/query_script_list/");
}
export function uploadScript(data) {
  //上传脚本
  return api.post("/script/upload_script_file/", data);
}
export function delScript(data) {
  //根据资源id删除脚本
  return api.post("/script/delete_script_file/", JSON.stringify(data));
}
export function scriptDeployment(data) {
  //脚本部署
  return api.post("/script/deployment_script_file/", JSON.stringify(data));
}
export function searchBackup() {
  //备份查看
  return api.get("/script/query_backup_list/");
}
export function downloadBackup(data) {
  //备份下载
  return api.post("/script/download_backup_file/", JSON.stringify(data));
}
export function delBackup(data) {
  //备份删除
  return api.post("/script/delete_backup_file/", JSON.stringify(data));
}
