import { getEnvViteApiBaseurl } from "@/utils/axios";

//导入资源
export function importExcelOfResource() {
  const fileInput = document.createElement("input");
  fileInput.type = "file";
  fileInput.accept = ".xlsx, .xls";
  fileInput.addEventListener("change", (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();
    reader.onload = (e) => {
      const datas = new Uint8Array(e.target.result);
      const workbook = xlsx.read(datas, { type: "array" });
      const serverSheet = workbook.Sheets[workbook.SheetNames[1]];
      const dbSheet = workbook.Sheets[workbook.SheetNames[2]];
      const serverData = xlsx.utils.sheet_to_json(serverSheet, {
        header: 1,
        blankrows: false,
      });
      const dbData = xlsx.utils.sheet_to_json(dbSheet, {
        header: 1,
        blankrows: false,
      });
      serverData.shift();
      dbData.shift();
    };
    reader.readAsArrayBuffer(file);
  });
  fileInput.click();
}

//导出业务资源
export function exportBusinessResource() {
  let baseURL = getEnvViteApiBaseurl();
  fetch(baseURL + "/business/export_all_resource/")
    .then((response) => {
      if (!response.ok) {
        throw new Error("网络响应存在问题");
      }
      return response.blob(); // 将响应转换为 Blob
    })
    .then((blob) => {
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "业务资源.xlsx"; // 设置下载文件的名称
      document.body.appendChild(a);
      a.click(); // 模拟点击下载
      document.body.removeChild(a);
      URL.revokeObjectURL(url); // 释放对象 URL
    })
    .catch((error) => {
      console.error("fetch请求出现问题:", error);
    });
}
