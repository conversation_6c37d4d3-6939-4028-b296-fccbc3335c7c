<script setup>
import { onMounted, reactive } from "vue";
import { usePagination } from "@/utils/composables";
import {
  getAllTriggerInfo,
  getAllPackageInfo,
  addCureRelation,
  getAllCureRelation,
  deleteCureRelation,
} from "@/api/modules/self_healing/slef_cure";
import { ElMessage, ElMessageBox } from "element-plus";
import { getUTCWithOffset } from "@/utils/dayjs";
import { pageChangeNum } from "@/views/homepage/components/utils";
import { goBack } from "@/utils";
import { ArrowLeftBold } from "@element-plus/icons-vue";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
const formRef = ref();
const data = reactive({
  dialogName: "",
  dataList: [],
  allList: [],
  triggerList: [],
  packageList: [],
  relationForm: {
    trigger_name: "",
    ansible_script_name: "",
    execution_time_period: "",
    timeRange: [],
  },
  // timeRange: [],
  batch: {
    enable: true,
    selectionDataList: [],
  },
  search: {
    searchName: "",
  },
  addRelationDialog: false,
  timeRight: true,
  submitLoading: false,
  formRules: {
    trigger_name: [{ required: true, message: "请选择触发器", trigger: "blur" }],
    ansible_script_name: [{ required: true, message: "请选择对应的功能包", trigger: "blur" }],
    timeRange: [{ required: true, message: "请选择执行周期时间", trigger: "blur" }],
  },
});
onMounted(() => {
  getData();
  getTriggerId();
  getAllPackage();
});
function getData() {
  getAllCureRelation().then((res) => {
    data.dataList = res.data;
    data.allList = res.data;
    changePageNum(data.allList);
  });
}

function changePageNum(list) {
  let res = pageChangeNum(list, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {});
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {});
}

//获取所有触发器信息
function getTriggerId() {
  getAllTriggerInfo().then((res) => {
    data.triggerList = res.data;
  });
}
//获取所有功能包
function getAllPackage() {
  getAllPackageInfo().then((res) => {
    data.packageList = res.data;
  });
}
function createRelation() {
  if (data.relationForm.timeRange.length === 0) {
    return;
  }
  if (!data.timeRight) {
    ElMessage.error("请修改执行周期,起始和结束时间差值,至少为3分钟!");
    return;
  }
  data.submitLoading = true;
  const startTime = getUTCWithOffset(data.relationForm.timeRange[0]);
  const endTime = getUTCWithOffset(data.relationForm.timeRange[1]);
  data.relationForm.execution_time_period = `${startTime.split(" ")[1]}-${endTime.split(" ")[1]}`;
  formRef.value.validate((valid) => {
    if (valid && data.timeRight) {
      delete data.relationForm.timeRange;
      addCureRelation({ data: data.relationForm })
        .then((res) => {
          getData();
        })
        .finally(() => {
          data.submitLoading = false;
          data.addRelationDialog = false;
        });
    } else {
      data.submitLoading = false;
    }
  });
}

//删除自愈关系
function delCureRelation(triggerName) {
  ElMessageBox.confirm("该操作会导致所有处于等待执行状态的自愈任务被清空，是否继续", "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteCureRelation(triggerName).then((res) => {
      getData();
    });
  });
}

//查询
function queryData() {
  if (data.search.searchName == "") {
    getData();
    return;
  }
  let list = [];
  list = data.allList.filter((item) => {
    return item.ansible_script_name.toLowerCase().includes(data.search.searchName.toLowerCase());
  });
  changePageNum(list);
}
//时间校验
function timeVerificate() {
  const startTime = data.relationForm.timeRange[0];
  const endTime = data.relationForm.timeRange[1];
  const timeDiff = Math.abs(endTime - startTime);
  const minutesDiff = Math.floor(timeDiff / 1000 / 60);
  if (minutesDiff > 3) {
    data.timeRight = true;
    return;
  } else {
    data.timeRight = false;
    ElMessage.error("执行周期时间差值，至少3分钟");
  }
}

//关闭清空
function handleClose() {
  data.relationForm = {
    trigger_name: "",
    ansible_script_name: "",
    execution_time_period: "",
    timeRange: [],
  };
}
//重构触发器数据对象
function getTriggerName() {
  const options = data.triggerList.map((item) => ({
    value: item,
    label: item,
  }));
  return options;
}
function editCureRelation(item) {
  ElMessageBox.confirm("该操作会导致所有处于等待执行状态的自愈任务被清空，是否继续", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      data.dialogName = "编辑自愈关系";
      data.addRelationDialog = true;
      data.relationForm.trigger_name = item.trigger_name;
      data.relationForm.ansible_script_name = item.ansible_script_name;
      data.relationForm.execution_time_period = item.execution_time_period;

      // 拆分时间段
      const [startTime, endTime] = item.task_execution_time_period.split("-");

      // 创建时间对象时不使用`Z`，使用本地时间
      const startDate = new Date(`1970-01-01T${startTime}`);
      const endDate = new Date(`1970-01-01T${endTime}`);

      // 将其作为时间范围
      data.relationForm.timeRange = [startDate, endDate];
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消",
      });
    });
}
function addRelation() {
  data.dialogName = "新增自愈关系";
  data.addRelationDialog = true;
}
</script>
<template>
  <div>
    <page-main title="自愈关系">
      <template #title>
        <div class="flex items-center">
          <el-icon class="cursor-pointer" @click="goBack()"><ArrowLeftBold /></el-icon>
          <span class="ml-10px">自愈关系</span>
        </div>
      </template>
      <div class="flex justify-between">
        <el-space wrap>
          <el-button type="primary" @click="addRelation()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            新增
          </el-button>
        </el-space>
        <el-space>
          <div class="w-220px">
            <el-input
              v-model="data.search.searchName"
              placeholder="输入功能包名称"
              clearable
              @keyup.enter="queryData"
            />
          </div>
          <el-button type="primary" @click="queryData()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>
      <el-table :data="data.dataList" border stripe highlight-current-row class="list-table">
        <el-table-column label="自愈规则" prop="trigger_name" />
        <el-table-column label="功能包" prop="ansible_script_name" />
        <el-table-column label="执行周期" prop="task_execution_time_period" />
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scoped">
            <el-button type="primary" @click="editCureRelation(scoped.row)">修改</el-button>
            <el-button type="danger" @click="delCureRelation(scoped.row.trigger_name)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>
    <!-- 创建自愈关系 -->
    <el-dialog v-model="data.addRelationDialog" :title="data.dialogName" :destroy-on-close="true" @close="handleClose">
      <el-form label-suffix="：" label-width="120px" :model="data.relationForm" ref="formRef" :rules="data.formRules">
        <el-form-item label="自愈规则" prop="trigger_name">
          <el-select-v2
            filterable
            v-model="data.relationForm.trigger_name"
            aria-placeholder="可输入关键字筛选"
            :options="getTriggerName()"
          />
          <!-- <el-select filterable v-model="data.relationForm.trigger_name" placeholder="可输入关键字筛选">
            <el-option v-for="(item, index) in data.triggerList" :key="index" :label="item" :value="item" />
          </el-select> -->
        </el-form-item>
        <el-form-item label="自愈功能包" prop="ansible_script_name">
          <el-select filterable v-model="data.relationForm.ansible_script_name" placeholder="可输入关键字筛选">
            <el-option
              v-for="(item, index) in data.packageList"
              :key="index"
              :label="item.packageName"
              :value="item.packageName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="执行周期" prop="timeRange">
          <el-time-picker
            is-range
            placeholder="请选择执行时间"
            v-model="data.relationForm.timeRange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            range-separator="至"
            @change="timeVerificate"
          />
        </el-form-item>
      </el-form>
      <div class="submit_css">
        <el-button type="primary" @click="createRelation" :loading="data.submitLoading">提交</el-button>
        <el-button @click="data.addRelationDialog = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
.submit_css {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
