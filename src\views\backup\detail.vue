<script setup name="BackupDetail">
import { backupOverview, backupDatabase } from "@/api/modules/database_backup_monitoring/dbBackup";
import { onMounted } from "vue";
import { Line } from "@antv/g2plot";
import useDateTimeStore from "@/store/modules/datetime";
import { formatLocalTime, getUTCTimestampWithOffset } from "@/utils/dayjs";
import { goBack } from "@/utils";
import { ArrowLeftBold } from "@element-plus/icons-vue";
const timeStore = useDateTimeStore();
var BackupSpeed;
var ResouceUse;
const route = useRoute();
const data = reactive({
  title: "",
  dataList: [],
  database: "",
  databaseList: [],
  speedTrend: [],
  useTrend: [],
  show: true,
  params: {},
  index: 0,
});
const carouselRef = ref();
const currentIndex = ref(0);
onMounted(() => {
  data.title = route.query.name;
  getDataBaseName();
});
function getData() {
  backupDatabase(data.params).then((res) => {
    data.dataList = res.data;
    nextTick(() => {
      getResouceUse();
      getBackupSpeed();
    });
  });
}
function getDataBaseName() {
  // 实例名称
  if (route.query.item) {
    data.show = false;
    data.database = route.query.item;
    data.params = {
      dbname: route.query.item,
      start_time: timeStore.getUtcUnixTimestamp()[0],
      end_time: timeStore.getUtcUnixTimestamp()[1],
    };
    getData();
  } else {
    let params = {
      start_time: Number(route.query.start_time),
      end_time: Number(route.query.end_time),
    };
    data.databaseList = [];
    // api.post("backup/list", { data: params }, { baseURL: "/mock/" }).then((res) => {
    // 全部备份信息
    backupOverview(params).then((res) => {
      res.data.database_overview.backup_information.forEach((element) => {
        data.databaseList.push(element.name);
      });
      data.databaseList = [...new Set(data.databaseList)];
      data.database = data.databaseList[0];
      data.params = {
        dbname: data.database,
        start_time: timeStore.getUtcUnixTimestamp()[0],
        end_time: timeStore.getUtcUnixTimestamp()[1],
      };
      getData();
    });
  }
}

function getBackupSpeed() {
  if (data.dataList.length > 0) {
    const BackupSpeedId = "BackupSpeed" + data.index;
    const inputSpeedData = data.dataList[data.index].backup_trend.speed_trends.map((item) => ({
      date: item.date,
      value: parseFloat(parseFloat(item.input_speed).toFixed(1)),
      name: "写入速度（MB/S）",
    }));
    const outputSpeedData = data.dataList[data.index].backup_trend.speed_trends.map((item) => ({
      date: item.date,
      value: parseFloat(parseFloat(item.output_speed).toFixed(1)),
      name: "读取速度（MB/S）",
    }));
    const dataList = [...inputSpeedData, ...outputSpeedData];
    BackupSpeed = new Line(
      BackupSpeedId,
      ("container",
      {
        data: dataList,
        padding: "auto",
        xField: "date",
        yField: "value",
        seriesField: "name",
        yAxis: {
          label: {
            formatter: (v) => `${v} MB/s`,
          },
        },
        tooltip: {
          formatter: (datum) => {
            return { name: datum.name, value: datum.value + "MB/s" };
          },
        },
        interactions: [{ type: "marker-active" }, { type: "brush-x" }], // 启用缩放功能
        legend: {
          position: "top",
        },
        smooth: true,
        // @TODO 后续会换一种动画方式
        animation: {
          appear: {
            animation: "path-in",
            duration: 3000,
          },
        },
      })
    );
    // 渲染图表
    BackupSpeed.render();
  }
}
function getResouceUse() {
  if (data.dataList.length > 0) {
    data.dataList[data.index]?.backup_trend?.use_trends.forEach((element) => {
      element.value = parseFloat(element.value.toFixed(1));
    });
    const ResouceUseId = "ResouceUse" + data.index;
    ResouceUse = new Line(ResouceUseId, {
      data: data.dataList[data.index]?.backup_trend?.use_trends || "",
      padding: "auto",
      xField: "time",
      yField: "value",
      xAxis: {
        // type: 'timeCat',
        tickCount: 5,
      },
      tooltip: {
        formatter: (datum) => {
          return { name: "利用率", value: datum.value + "%" };
        },
      },
      yAxis: {
        label: {
          formatter: (v) => `${v} %`,
        },
      },
      interactions: [{ type: "marker-active" }, { type: "brush-x" }], // 启用缩放功能
      annotations: [
        // 低于平均值的颜色变化
        {
          type: "text",
          position: ["min", "mean"],
          content: "平均值",
          offsetY: -4,
          style: {
            textBaseline: "bottom",
          },
        },
        {
          type: "line",
          start: ["min", "mean"],
          end: ["max", "mean"],
          style: {
            stroke: "#F4664A",
            lineDash: [2, 2],
          },
        },
      ],
    });
    // 渲染图表
    ResouceUse.render();
  }
}
function selectDatabase() {
  data.params.dbname = data.database;
  if (ResouceUse) {
    ResouceUse.destroy();
  }
  if (BackupSpeed) {
    BackupSpeed.destroy();
  }
  getData();
}
watch(timeStore.$state, (newValue) => {
  data.params.start_time = getUTCTimestampWithOffset(newValue.begin);
  data.params.end_time = getUTCTimestampWithOffset(newValue.over);
  if (ResouceUse) {
    ResouceUse.destroy();
  }
  if (BackupSpeed) {
    BackupSpeed.destroy();
  }
  getData();
});

function change(current_index, original_index) {
  if (ResouceUse) {
    ResouceUse.destroy();
  }
  if (BackupSpeed) {
    BackupSpeed.destroy();
  }
  data.index = current_index;
  nextTick(() => {
    getResouceUse();
    getBackupSpeed();
  });
}

const prev = () => {
  carouselRef.value.prev();
  currentIndex.value--;
};
const next = () => {
  carouselRef.value.next();
  currentIndex.value++;
};
function getstatus(item) {
  if (item == '"OPEN"') {
    return true;
  } else {
    return false;
  }
}
</script>

<template>
  <div class="example">
    <page-main>
      <el-icon class="cursor-pointer mr-10px mt-5px" @click="goBack()"><ArrowLeftBold /></el-icon>
      <span v-show="data.show">请选择数据库：</span>
      <el-select
        v-show="data.show"
        v-model="data.database"
        style="display: inline-block; width: 150px; padding-right: 20px"
        @change="selectDatabase"
      >
        <el-option v-for="item in data.databaseList" :key="item" :label="item" :value="item" />
      </el-select>
    </page-main>
    <el-carousel arrow="never" ref="carouselRef" :autoplay="false" @change="change" height="706px">
      <el-carousel-item v-for="(item, index) in data.dataList" :key="index" :name="'item' + index">
        <el-row :gutter="20" style="margin: 10px">
          <el-col :span="2" :lg="6" :md="8" :sm="8">
            <el-card>
              <div style="display: flex; justify-content: center; align-items: center">
                <!-- 图标 -->
                <div style="text-align: center">
                  <el-icon size="40px"><svg-icon name="databasename" /></el-icon>
                </div>
                <!-- 图标右边的文字 -->
                <div style="text-align: left; margin-left: 10px">
                  <p>数据库名称</p>
                </div>
              </div>
              <!-- 下半部分：文字描述 -->
              <div style="text-align: center; margin-top: 20px">
                <p>
                  <strong>{{ item?.database_information?.name || "" }}</strong>
                </p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="2" :lg="6" :md="8" :sm="8">
            <el-card>
              <div style="display: flex; justify-content: center; align-items: center">
                <!-- 图标 -->
                <div style="text-align: center">
                  <el-icon size="40px"><svg-icon name="databasebanben" /></el-icon>
                </div>
                <!-- 图标右边的文字 -->
                <div style="text-align: left; margin-left: 10px">
                  <p>数据库版本</p>
                </div>
              </div>
              <!-- 下半部分：文字描述 -->
              <div style="text-align: center; margin-top: 20px">
                <p>
                  <strong>{{ item?.database_information?.version }}</strong>
                </p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="2" :lg="6" :md="8" :sm="8">
            <el-card>
              <div style="display: flex; justify-content: center; align-items: center">
                <!-- 图标 -->
                <div style="text-align: center">
                  <el-icon size="40px"><svg-icon name="databasestatus" /></el-icon>
                </div>
                <!-- 图标右边的文字 -->
                <div style="text-align: left; margin-left: 10px">
                  <p>数据库状态</p>
                </div>
              </div>
              <!-- 下半部分：文字描述 -->
              <div style="text-align: center; margin-top: 20px">
                <p v-if="getstatus(item?.database_information?.status) == true" style="color: #67c23a">
                  <strong>● 正常运行</strong>
                </p>
                <p v-else style="color: #f56c6c"><strong>● 停止运行</strong></p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="2" :lg="6" :md="8" :sm="8">
            <el-card>
              <div style="display: flex; justify-content: center; align-items: center">
                <!-- 图标 -->
                <div style="text-align: center">
                  <el-icon size="40px"><svg-icon name="databasetime" /></el-icon>
                </div>
                <!-- 图标右边的文字 -->
                <div style="text-align: left; margin-left: 10px">
                  <p>数据库启动时间</p>
                </div>
              </div>
              <!-- 下半部分：文字描述 -->
              <div style="text-align: center; margin-top: 20px">
                <p>
                  <strong>{{ item?.dataase_information?.database_startup_time }}</strong>
                </p>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <div class="bottom">
          <LayoutContainer>
            <template #leftSide>
              <div class="right">
                <el-descriptions title="备份总览（最近）" direction="vertical" :column="2" border>
                  <el-descriptions-item label="备份种类">
                    {{ item?.database_information?.type }}
                  </el-descriptions-item>
                  <el-descriptions-item label="备份状态">
                    <el-tag>
                      {{
                        item?.database_information?.recent_status === "COMPLETED"
                          ? "完成"
                          : item?.database_information?.recent_status
                      }}
                    </el-tag>
                  </el-descriptions-item>
                </el-descriptions>
                <h4>历史备份记录</h4>
                <el-table :data="item?.database_information?.backup_history" border style="width: 100%">
                  <el-table-column fixed prop="date" label="日期" width="120" />
                  <el-table-column prop="type" label="备份类型" width="100" />
                  <el-table-column prop="input_size" width="100" label="读取总量（gb）" />
                  <el-table-column prop="output_size" width="100" label="写入总量（gb）" />
                  <el-table-column prop="backup_duration" width="100" label="持续时间（s）" />
                </el-table>
              </div>
            </template>

            <h2>备份速度</h2>
            <div :id="'BackupSpeed' + index" style="width: 100%; height: 200px" />
            <h2>资源利用率</h2>
            <div :id="'ResouceUse' + index" style="width: 100%; height: 200px" />
          </LayoutContainer>
        </div>
      </el-carousel-item>
      <span class="carousel-button carousel-button-left" v-show="currentIndex > 0" @click="prev">&lt;</span>
      <span
        class="carousel-button carousel-button-right"
        v-show="currentIndex < data.dataList.length - 1"
        @click="next"
      >
        &gt;
      </span>
    </el-carousel>
  </div>
</template>

<style lang="scss" scoped>
// scss
.example {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .header {
    margin-bottom: 0;
  }

  .bottom {
    flex: 1;
    position: relative;
    min-height: 561px;
    overflow: auto;

    :deep(.el-radio-group .el-radio-button .el-radio-button__inner) {
      display: flex;
      align-items: center;

      .el-icon {
        margin-right: 10px;
      }
    }
  }
}

h4 {
  font-size: 16px;
  padding: 10px;
}

.carousel-item {
  height: 200px;
  background-color: #f0f0f0;
  text-align: center;
  padding: 20px;
}

.carousel-button {
  position: absolute;
  top: 50%;
  width: 30px;
  height: 30px;
  margin-top: -15px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 20px;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-button:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.carousel-button-left {
  left: 20px;
}

.carousel-button-right {
  right: 20px;
}
</style>
