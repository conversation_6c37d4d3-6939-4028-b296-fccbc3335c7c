import Mock from 'mockjs'

const AllList = []
for (let i = 0; i < 50; i++) {
  AllList.push(Mock.mock({
    id: '@id',
    title: '@ctitle(10, 20)'
  }))
}

export default [
  {
    url: '/mock/player/list',
    method: 'get',
    response: {
      error: '',
      status: 1,
      data: [
        {
          time: "2022.11",
          reason:
            "apm掉线apm掉线apm掉线apm掉线apm掉线apm掉线apm掉线apm掉线apm掉线apm掉线apm掉线",
        },
        {
          time: "2022.11",
          reason: "es掉线",
        },
        {
          time: "2022.11",
          reason: "zabbix掉线",
        },
        {
          time: "2022.11",
          reason: "kibana掉线",
        },
      ]
    }
  },
  {
    url: '/mock/player/detail',
    method: 'get',
    response: option => {
      let info = AllList.filter(item => item.id == option.query.id)
      return {
        error: '',
        status: 1,
        data: info[0]
      }
    }
  },
  {
    url: '/mock/player/create',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: [[{ name: "总耗时", startTime: 0, endTime: 18 },
      { name: "医嘱", startTime: 0, endTime: 2 },
      { name: "开药", startTime: 2, endTime: 3 },
      { name: "微信缴费", startTime: 3, endTime: 17 },
      { name: "完成支付", startTime: 17, endTime: 18 }],
      [{ name: "总耗时", startTime: 0, endTime: 32 },
      { name: "医嘱", startTime: 0, endTime: 5 },
      { name: "开药", startTime: 5, endTime: 13 },
      { name: "微信缴费", startTime: 13, endTime: 17 },
      { name: "完成支付", startTime: 17, endTime: 32 }],
      [{ name: "总耗时", startTime: 0, endTime: 20 },
      { name: "医嘱", startTime: 0, endTime: 2 },
      { name: "开药", startTime: 2, endTime: 3 },
      { name: "微信缴费", startTime: 3, endTime: 17 },
      { name: "完成支付", startTime: 17, endTime: 20 }],]
    }
  },
  {
    url: '/mock/player/edit',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        isSuccess: true
      }
    }
  },
  {
    url: '/mock/player/delete',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        isSuccess: true
      }
    }
  },
  {
    url: '/mock/product_indexes_model/',
    method: "get",
    response: {
      error: "", status: 1,
      data: [
        {
          product_name: "中联专业版电子病历系统", es_index: "zlemr"
        },
        {
          product_name: "中联专业版临生免系统", es_index: "zllab"
        },
        {
          product_name: "中联专业体检系统", es_index: "zlpeis"
        },
        {
          product_name: "一体化护理评分评估", es_index: "zlscore"
        },
      ]
    }
  }
]
// "中联专业版电子病历系统":"zlemr"
// "中联专业版临生免系统":"zllab"
// "中联专业体检系统":"zlpeis"，
// "一体化护理评分评估":"zlscore'
// "中联专业版挂号系统":"zlrgs"，
// "处方开立权限":"zldap"，
// 数据处理平台":"ZLDPPRunService"
// 中联报表系统":"zlrpts"，
// "统一数据源":"zluds"，
