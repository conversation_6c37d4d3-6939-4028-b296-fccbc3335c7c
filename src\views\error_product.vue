<route>
{
    name: 'error_product',
    meta: {
        title: "系统出现异常",
        constant: true,
        layout: false
    }
}
</route>

<script setup>

</script>

<template>
    <div class="notfound">
        <svg-icon name="auth2" class="icon" />
        <div class="content">
            <h1>授权异常</h1>
            <div class="desc">不是针对联壹一体化运维平台的授权文件</div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.notfound {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1000px;
    @include position-center(xy);
    .icon {
        width: 500px;
        height: 500px;
    }
    .content {
        h1 {
            margin: 0;
            font-size: 52px;
            color: var(--el-text-color-primary);
        }
        .desc {
            margin: 20px 0 30px;
            font-size: 20px;
            color: var(--el-text-color-secondary);
        }
    }
}
</style>
