@use "../themes/default.scss" as *;
@use "../themes/green.scss" as *;
@use "../themes/orange.scss" as *;
@use "../themes/pink.scss" as *;
@use "../themes/grey.scss" as *;
@use "../themes/yellow.scss" as *;

$themes: (
    default: $theme-default,
    sys_green: $theme-green,
    sys_orange: $theme-orange,
    sys_pink: $theme-pink,
    sys_grey: $theme-grey,
    sys_yellow: $theme-yellow
);
@mixin set-themes-css-var() {
    @each $theme-name, $theme-map in $themes {
        @each $mode, $map in $theme-map {
            @if ($mode == "") {
                [data-theme=#{$theme-name}] {
                    @each $key, $value in $map {
                        --g-#{$key}: #{$value};
                    }
                }
            }
            @else if ($mode == "dark") {
                &.dark [data-theme=#{$theme-name}] {
                    @each $key, $value in $map {
                        --g-#{$key}: #{$value};
                    }
                }
            }
        }
    }
}
