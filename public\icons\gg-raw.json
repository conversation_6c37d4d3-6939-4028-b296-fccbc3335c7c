{"prefix": "gg", "info": {"name": "css.gg", "total": 704, "version": "2.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/astrit/css.gg"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/astrit/css.gg/blob/master/LICENSE"}, "samples": ["align-left", "server", "overflow"], "height": 24, "category": "General", "palette": false}, "lastModified": **********, "icons": {"abstract": {"body": "<g fill=\"currentColor\"><path d=\"M5 5h14v14h-3V8H5V5Z\"/><path fill-rule=\"evenodd\" d=\"M10 19a5 5 0 1 0 0-10a5 5 0 0 0 0 10Zm0-3a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\" clip-rule=\"evenodd\"/></g>"}, "add": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12Zm10-8a8 8 0 1 0 0 16a8 8 0 0 0 0-16Z\"/><path d=\"M13 7a1 1 0 1 0-2 0v4H7a1 1 0 1 0 0 2h4v4a1 1 0 1 0 2 0v-4h4a1 1 0 1 0 0-2h-4V7Z\"/></g>"}, "add-r": {"body": "<g fill=\"currentColor\"><path d=\"M12 6a1 1 0 0 1 1 1v4h4a1 1 0 1 1 0 2h-4v4a1 1 0 1 1-2 0v-4H7a1 1 0 1 1 0-2h4V7a1 1 0 0 1 1-1Z\"/><path fill-rule=\"evenodd\" d=\"M5 22a3 3 0 0 1-3-3V5a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v14a3 3 0 0 1-3 3H5Zm-1-3a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v14Z\" clip-rule=\"evenodd\"/></g>"}, "adidas": {"body": "<path fill=\"currentColor\" d=\"m1.33 19l-.6-1.036l4.33-2.5L7.103 19H1.329Zm13.856 0H9.412l-3.619-6.268l4.33-2.5L15.187 19Zm8.083 0h-5.774l-6.64-11.5l4.33-2.5l8.084 14Z\"/>"}, "airplane": {"body": "<path fill=\"currentColor\" d=\"M9.01 5.128h2c1.104 0 2.458.769 3.024 1.718L16.509 11h4.5a1 1 0 1 1 0 2h-4.595l-2.476 4.154c-.565.95-1.919 1.718-3.024 1.718h-2l3.5-5.872h-6.99L3.99 15.453h-2L4.01 12v-.033l-2-3.42h2L5.444 11h7.065l-3.5-5.872Z\"/>"}, "alarm": {"body": "<g fill=\"currentColor\"><path d=\"M5.459 2L1 6.015L2.338 7.5l4.46-4.015L5.457 2ZM11 8h2v4h3v2h-5V8Z\"/><path fill-rule=\"evenodd\" d=\"M3 12a9 9 0 1 1 18 0a9 9 0 0 1-18 0Zm2 0a7 7 0 1 1 14 0a7 7 0 0 1-14 0Z\" clip-rule=\"evenodd\"/><path d=\"M18.541 2L23 6.015L21.662 7.5l-4.46-4.015L18.543 2Z\"/></g>"}, "album": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 19a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V5a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v14Zm18 0a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h5v8.011h2.395L14 9.864l1.605 2.147H18V4h1a1 1 0 0 1 1 1v14ZM16 4h-4v5.336l2-2.676l2 2.676V4Z\" clip-rule=\"evenodd\"/>"}, "align-bottom": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M13 10h4v6h-4v-6Z\"/><path d=\"M11 4H7v12h4V4Zm7 14H6v2h12v-2Z\"/></g>"}, "align-center": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M9 13h6v4H9v-4Z\"/><path d=\"M6 7h12v4H6V7Z\"/></g>"}, "align-left": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M8 13h6v4H8v-4Z\"/><path d=\"M6 6H4v12h2V6Zm14 1H8v4h12V7Z\"/></g>"}, "align-middle": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M13 9h4v6h-4V9Z\"/><path d=\"M7 6h4v12H7V6Z\"/></g>"}, "align-right": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path fill-opacity=\".5\" d=\"m16 13.004l-6-.013l-.01 4l6 .013l.01-4Z\"/><path d=\"m19.978 18.002l.026-12l-2-.004l-.026 12l2 .004ZM3.996 10.985l12 .026l.009-4l-12-.026l-.009 4Z\"/></g>"}, "align-top": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path fill-opacity=\".5\" d=\"m13.035 7.988l.002 6l4-.002l-.002-6l-4 .002Z\"/><path d=\"M18 4.012L6 4.018v2l12-.006v-2Zm-6.963 15.974l-.005-12l-4 .002l.005 12l4-.002Z\"/></g>"}, "anchor": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15 6a3.001 3.001 0 0 1-2 2.83v8.044c1.725-.444 3-2.01 3-3.874h2a6.002 6.002 0 0 1-5 5.917V20a1 1 0 1 1-2 0v-1.083A6.002 6.002 0 0 1 6 13h2a4.002 4.002 0 0 0 3 3.874V8.829A3.001 3.001 0 1 1 15 6Zm-3 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\" clip-rule=\"evenodd\"/>"}, "apple-watch": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M16.497 5.03c0 .048-.001.096-.004.143A3.001 3.001 0 0 1 18.5 8.005v1h1v4h-1v3a3 3 0 0 1-2.005 2.83a3 3 0 0 1-2.995 3.17h-4a3 3 0 0 1-2.995-3.17a3.001 3.001 0 0 1-2.005-2.83v-8a3 3 0 0 1 2.003-2.83a3 3 0 0 1 3.01-3.18l4 .02a3 3 0 0 1 2.984 3.015Zm-8-.025h6a1 1 0 0 0-.995-.99l-4-.02a1 1 0 0 0-1.004.995v.015Zm7.208 2.021l-4.22-.021H7.5a1 1 0 0 0-1 1v8a1 1 0 0 0 .999 1H15.5a1 1 0 0 0 .999-1v-8a1 1 0 0 0-.795-.979ZM8.5 19.005a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1h-6Z\" clip-rule=\"evenodd\"/>"}, "arrange-back": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 3h8v4h6v6h4v8h-8v-4H7v-6H3V3Zm12 10h-2v2H9v-4h2V9h4v4Z\" clip-rule=\"evenodd\"/>"}, "arrange-front": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 3h8v4h6v6h4v8h-8v-4H7v-6H3V3Zm12 6H9v6h6V9Z\" clip-rule=\"evenodd\"/>"}, "arrow-align-h": {"body": "<path fill=\"currentColor\" d=\"M13 7h-2v10h2V7Zm-7 .757l1.414 1.415L5.586 11H10v2H5.586l1.828 1.828L6 16.243L1.757 12L6 7.757Zm12 8.486l-1.414-1.414L18.414 13H14v-2h4.414l-1.828-1.828L18 7.757L22.243 12L18 16.243Z\"/>"}, "arrow-align-v": {"body": "<path fill=\"currentColor\" d=\"M7 11v2h10v-2H7Zm.757 7l1.415-1.414L11 18.414V14h2v4.414l1.828-1.828L16.243 18L12 22.243L7.757 18Zm8.486-12l-1.414 1.414L13 5.586V10h-2V5.586L9.172 7.414L7.757 6L12 1.757L16.243 6Z\"/>"}, "arrow-bottom-left": {"body": "<path fill=\"currentColor\" d=\"m18.243 7.172l-1.415-1.415l-9.07 9.071v-4.585h-2v8h8v-2H9.17l9.072-9.071Z\"/>"}, "arrow-bottom-left-o": {"body": "<g fill=\"currentColor\"><path d=\"M10 10.037H8v6h6v-2h-2.586l5.33-5.33l-1.414-1.414l-5.33 5.33v-2.586Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-bottom-left-r": {"body": "<g fill=\"currentColor\"><path d=\"M10 10.037H8v6h6v-2h-2.586l5.33-5.33l-1.414-1.414l-5.33 5.33v-2.586Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-bottom-right": {"body": "<path fill=\"currentColor\" d=\"m5.757 7.172l1.415-1.415l9.07 9.071v-4.585h2v8h-8v-2h4.586l-9.07-9.071Z\"/>"}, "arrow-bottom-right-o": {"body": "<g fill=\"currentColor\"><path d=\"M14.037 10.037h2v6h-6v-2h2.585l-5.329-5.33l1.414-1.414l5.33 5.33v-2.586Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-bottom-right-r": {"body": "<g fill=\"currentColor\"><path d=\"M14.037 10.037h2v6h-6v-2h2.585l-5.329-5.33l1.414-1.414l5.33 5.33v-2.586Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-down": {"body": "<path fill=\"currentColor\" d=\"M11 3.672h2V16.5l3.243-3.243l1.414 1.415L12 20.328l-5.657-5.656l1.414-1.415L11 16.5V3.672Z\"/>"}, "arrow-down-o": {"body": "<g fill=\"currentColor\"><path d=\"m14.829 12.026l1.414 1.414L12 17.683L7.757 13.44l1.415-1.414L11 13.854V6.317h2v7.537l1.829-1.828Z\"/><path fill-rule=\"evenodd\" d=\"M19.778 19.778c-4.296 4.296-11.26 4.296-15.556 0c-4.296-4.296-4.296-11.26 0-15.556c4.296-4.296 11.26-4.296 15.556 0c4.296 4.296 4.296 11.26 0 15.556Zm-1.414-1.414A9 9 0 1 1 5.636 5.636a9 9 0 0 1 12.728 12.728Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-down-r": {"body": "<g fill=\"currentColor\"><path d=\"m14.828 12.026l1.415 1.414L12 17.683L7.757 13.44l1.415-1.414L11 13.854V6.317h2v7.537l1.828-1.828Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-left": {"body": "<path fill=\"currentColor\" d=\"M20.328 11v2H7.5l3.243 3.243l-1.415 1.414L3.672 12l5.656-5.657l1.415 1.414L7.5 11h12.828Z\"/>"}, "arrow-left-o": {"body": "<g fill=\"currentColor\"><path d=\"m11.948 14.829l-1.414 1.414L6.29 12l4.243-4.243l1.414 1.415L10.12 11h7.537v2H10.12l1.828 1.829Z\"/><path fill-rule=\"evenodd\" d=\"M4.222 19.778c-4.296-4.296-4.296-11.26 0-15.556c4.296-4.296 11.26-4.296 15.556 0c4.296 4.296 4.296 11.26 0 15.556c-4.296 4.296-11.26 4.296-15.556 0Zm1.414-1.414A9 9 0 1 1 18.364 5.636A9 9 0 0 1 5.636 18.364Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-left-r": {"body": "<g fill=\"currentColor\"><path d=\"m11.948 14.829l-1.414 1.414L6.29 12l4.243-4.243l1.414 1.415L10.12 11h7.537v2H10.12l1.828 1.829Z\"/><path fill-rule=\"evenodd\" d=\"M23 19a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14Zm-4 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-long-down": {"body": "<path fill=\"currentColor\" d=\"m13.012 19.162l1.813-1.822l1.418 1.41l-4.231 4.255l-4.255-4.231l1.41-1.418l1.846 1.834L10.998.997l2-.002l.014 18.167Z\"/>"}, "arrow-long-down-c": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11 6.85a3.001 3.001 0 1 1 2 0l.012 12.287l1.812-1.822l1.419 1.41l-4.231 4.255l-4.255-4.23l1.41-1.42l1.845 1.835L11 6.85Zm.998-1.83a1 1 0 1 1 0-2a1 1 0 0 1 0 2Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-down-e": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14.998 1.02h-6v6h2.001l.013 12.145l-1.844-1.834l-1.41 1.418l4.254 4.23l4.23-4.254l-1.417-1.41l-1.813 1.823l-.013-12.118h1.999v-6Zm-4 2h2v2h-2v-2Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-down-l": {"body": "<path fill=\"currentColor\" d=\"M8.998.972v2h3l-1 .001l.014 16.24l-1.844-1.834l-1.41 1.418l4.254 4.23l4.23-4.254l-1.417-1.41l-1.813 1.823l-.014-16.214h2v-2h-6Z\"/>"}, "arrow-long-down-r": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M16.242 4.641L11.999.4L7.756 4.64L11 7.886l.013 11.9l-1.845-1.834l-1.41 1.418l4.255 4.231l4.23-4.254l-1.417-1.41l-1.813 1.822L13 7.883l3.242-3.242Zm-5.657 0l1.414-1.414l1.414 1.414L12 6.056L10.585 4.64Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-left": {"body": "<path fill=\"currentColor\" d=\"m1.027 11.993l4.235 4.25L6.68 14.83l-1.821-1.828L22.974 13v-2l-18.12.002L6.69 9.174L5.277 7.757l-4.25 4.236Z\"/>"}, "arrow-long-left-c": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m5.27 7.757l-4.25 4.236l4.236 4.25l1.416-1.412l-1.814-1.82l12.288.042a3.001 3.001 0 0 0 5.834-.975a3 3 0 0 0-5.825-1.025L4.839 11.01l1.843-1.836L5.27 7.757Zm13.71 4.303a1 1 0 1 1 2 .009a1 1 0 0 1-2-.01Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-left-e": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m5.263 7.757l-4.25 4.236l4.236 4.25l1.417-1.412l-1.815-1.82l12.117.04l-.008 2l6 .027l.026-6l-6-.027l-.009 2l-12.144-.04l1.842-1.837l-1.412-1.417Zm15.714 3.312l-.009 2l-2-.01l.01-2l2 .01Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-left-l": {"body": "<path fill=\"currentColor\" d=\"M5.208 7.757L.97 12.003l4.246 4.24l1.413-1.416l-1.819-1.815l16.214.018l-.004 2l2 .004l.012-6l-2-.004l-.006 2.989l.001-.99l-16.24-.018l1.838-1.84l-1.416-1.414Z\"/>"}, "arrow-long-left-r": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m4.649 7.725l-4.25 4.236l4.235 4.25l1.417-1.412l-1.814-1.82l11.866.04l3.255 3.256l4.243-4.243L19.36 7.79l-3.23 3.23l-11.911-.04l1.843-1.837L4.65 7.726Zm13.295 4.307l1.415-1.414l1.414 1.414l-1.415 1.414l-1.414-1.414Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-right": {"body": "<path fill=\"currentColor\" d=\"m23.068 11.993l-4.25-4.236l-1.412 1.417l1.835 1.83L.932 11v2l18.305.002l-1.821 1.828l1.416 1.412l4.236-4.25Z\"/>"}, "arrow-long-right-c": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m18.73 7.757l4.25 4.236l-4.236 4.25l-1.416-1.412l1.814-1.82l-12.288.042a3.001 3.001 0 1 1-.009-2l12.316-.043l-1.843-1.836l1.412-1.417ZM5.02 12.06a1 1 0 1 0-2 .009a1 1 0 0 0 2-.01Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-right-e": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m22.986 11.993l-4.235 4.25l-1.417-1.412l1.815-1.82l-12.118.04l.01 2l-6 .027l-.028-6l6-.027l.01 2l12.144-.04l-1.842-1.837l1.412-1.417l4.25 4.236Zm-19.964-.924l.01 2l2-.01l-.01-2l-2 .01Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-right-l": {"body": "<path fill=\"currentColor\" d=\"m18.916 7.757l4.25 4.236l-4.236 4.25l-1.416-1.412l1.819-1.825l-16.5.022v2.002h-2v-6h2v1.998l16.51-.022l-1.838-1.832l1.411-1.417Z\"/>"}, "arrow-long-right-r": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m19.351 7.725l4.25 4.236l-4.235 4.25l-1.417-1.412l1.814-1.82l-11.866.04l-3.255 3.256l-4.243-4.243L4.642 7.79l3.229 3.23l11.911-.04l-1.843-1.837l1.412-1.417Zm-14.71 5.721l1.415-1.414l-1.414-1.414l-1.415 1.414l1.415 1.414Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-up": {"body": "<path fill=\"currentColor\" d=\"m12.032 1.017l-4.274 4.21L9.16 6.653l1.859-1.83l-.056 18.155l2 .006l.056-18.113l1.798 1.825l1.425-1.403l-4.21-4.275Z\"/>"}, "arrow-long-up-c": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m7.758 5.23l4.274-4.21l4.21 4.275l-1.424 1.403l-1.804-1.83l-.071 12.287a3.001 3.001 0 0 1-1.029 5.825a3 3 0 0 1-.971-5.835l.071-12.315l-1.853 1.826L7.758 5.23Zm4.175 13.75a1 1 0 1 0-.01 2a1 1 0 0 0 .01-2Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-up-e": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m12.032 1.013l4.21 4.275l-1.424 1.403l-1.804-1.83l-.07 12.116l1.998.01l-.028 6l-6-.029l.029-6l2 .01l.071-12.145L9.161 6.65L7.758 5.224l4.274-4.21Zm-1.108 19.955l2 .01l.01-2l-2-.01l-.01 2Z\" clip-rule=\"evenodd\"/>"}, "arrow-long-up-l": {"body": "<path fill=\"currentColor\" d=\"m12.032 1.019l4.21 4.274l-1.424 1.404l-1.799-1.826l-.051 16.11h1.996v2h-6v-2h2.004l.051-16.157l-1.858 1.83l-1.403-1.425l4.274-4.21Z\"/>"}, "arrow-long-up-r": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.793 4.61L12.068.398l4.21 4.275l-1.424 1.403l-1.804-1.831l-.07 11.886l3.227 3.226l-4.243 4.243l-4.242-4.243l3.259-3.26l.07-11.89l-1.854 1.826L7.793 4.61Zm4.171 16.163l1.414-1.415l-1.414-1.414l-1.414 1.414l1.414 1.415Z\" clip-rule=\"evenodd\"/>"}, "arrow-right": {"body": "<path fill=\"currentColor\" d=\"m15.038 6.343l-1.411 1.418l3.27 3.255l-13.605.013l.002 2l13.568-.013l-3.215 3.23l1.417 1.41l5.644-5.67l-5.67-5.643Z\"/>"}, "arrow-right-o": {"body": "<g fill=\"currentColor\"><path d=\"m12.052 14.829l1.414 1.414L17.71 12l-4.243-4.243l-1.414 1.415L13.88 11H6.343v2h7.537l-1.828 1.829Z\"/><path fill-rule=\"evenodd\" d=\"M19.778 19.778c4.296-4.296 4.296-11.26 0-15.556c-4.296-4.296-11.26-4.296-15.556 0c-4.296 4.296-4.296 11.26 0 15.556c4.296 4.296 11.26 4.296 15.556 0Zm-1.414-1.414A9 9 0 1 0 5.636 5.636a9 9 0 0 0 12.728 12.728Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-right-r": {"body": "<g fill=\"currentColor\"><path d=\"m12.052 14.829l1.414 1.414L17.71 12l-4.243-4.243l-1.414 1.415L13.88 11H6.343v2h7.537l-1.828 1.829Z\"/><path fill-rule=\"evenodd\" d=\"M1 19a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14Zm4 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-top-left": {"body": "<path fill=\"currentColor\" d=\"m13.475 5.495l.004 2l-4.557.01l9.603 9.585l-1.412 1.415l-9.63-9.61l.01 4.614l-2 .004l-.018-8l8-.018Z\"/>"}, "arrow-top-left-o": {"body": "<g fill=\"currentColor\"><path d=\"M10 13.963H8v-6h6v2h-2.586l5.33 5.33l-1.414 1.414l-5.33-5.33v2.586Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0-6.075-4.925-11-11-11S1 5.925 1 12s4.925 11 11 11s11-4.925 11-11Zm-2 0a9 9 0 1 0-18 0a9 9 0 0 0 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-top-left-r": {"body": "<g fill=\"currentColor\"><path d=\"M10 13.963H8v-6h6v2h-2.586l5.33 5.33l-1.414 1.414l-5.33-5.33v2.586Z\"/><path fill-rule=\"evenodd\" d=\"M1 19a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14Zm4 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-top-right": {"body": "<path fill=\"currentColor\" d=\"m10.525 5.495l-.004 2l4.557.01l-9.603 9.585l1.413 1.415l9.63-9.61l-.012 4.614l2 .004l.02-8l-8-.018Z\"/>"}, "arrow-top-right-o": {"body": "<g fill=\"currentColor\"><path d=\"M14 13.963h2v-6h-6v2h2.586l-5.33 5.33l1.414 1.414l5.33-5.33v2.586Z\"/><path fill-rule=\"evenodd\" d=\"M1 12C1 5.925 5.925 1 12 1s11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12Zm2 0a9 9 0 1 1 18 0a9 9 0 0 1-18 0Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-top-right-r": {"body": "<g fill=\"currentColor\"><path d=\"M14 13.963h2v-6h-6v2h2.586l-5.33 5.33l1.414 1.414l5.33-5.33v2.586Z\"/><path fill-rule=\"evenodd\" d=\"M23 19a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14Zm-4 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-up": {"body": "<path fill=\"currentColor\" d=\"m17.657 8.962l-1.418 1.411l-3.255-3.27l-.013 13.605l-2-.002l.013-13.568l-3.23 3.215l-1.41-1.417l5.67-5.644l5.643 5.67Z\"/>"}, "arrow-up-o": {"body": "<g fill=\"currentColor\"><path d=\"m14.829 11.948l1.414-1.414L12 6.29l-4.243 4.243l1.415 1.414L11 10.12v7.537h2V10.12l1.829 1.828Z\"/><path fill-rule=\"evenodd\" d=\"M19.778 4.222c-4.296-4.296-11.26-4.296-15.556 0c-4.296 4.296-4.296 11.26 0 15.556c4.296 4.296 11.26 4.296 15.556 0c4.296-4.296 4.296-11.26 0-15.556Zm-1.414 1.414A9 9 0 1 0 5.636 18.364A9 9 0 0 0 18.364 5.636Z\" clip-rule=\"evenodd\"/></g>"}, "arrow-up-r": {"body": "<g fill=\"currentColor\"><path d=\"m14.854 11.974l1.415-1.414l-4.243-4.243l-4.243 4.243l1.414 1.414l1.829-1.828v7.537h2v-7.537l1.828 1.828Z\"/><path fill-rule=\"evenodd\" d=\"M1 19a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14Zm4 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2Z\" clip-rule=\"evenodd\"/></g>"}, "arrows-breake-h": {"body": "<path fill=\"currentColor\" d=\"M9.243 7h2v4h.005v2h-.005v4h-2v-4H4.828l1.829 1.828l-1.414 1.415L1 12l4.243-4.243l1.414 1.415L4.828 11h4.415V7Zm6.01 0h-2v4h-.005v2h.005v4h2v-4h4.414l-1.829 1.829l1.415 1.414L23.495 12l-4.242-4.243l-1.415 1.415L19.668 11h-4.414V7Z\"/>"}, "arrows-breake-v": {"body": "<path fill=\"currentColor\" d=\"m16.243 5.243l-1.414 1.414L13 4.828v4.415h4v2H7v-2h4V4.828L9.172 6.657L7.757 5.243L12 1l4.243 4.243ZM7 15.253v-2h10v2h-4v4.414l1.828-1.829l1.415 1.415L12 23.495l-4.243-4.242l1.415-1.415L11 19.668v-4.414H7Z\"/>"}, "arrows-exchange": {"body": "<path fill=\"currentColor\" d=\"M4.993 12.984a1 1 0 0 0-.531 1.848L7.15 17.52a1 1 0 1 0 1.414-1.415l-1.121-1.12h7.55a1 1 0 0 0 0-2h-10Zm14.014-1.968a1 1 0 0 0 .531-1.848L16.85 6.48a1 1 0 0 0-1.414 1.415l1.121 1.12h-7.55a1 1 0 0 0 0 2h10Z\"/>"}, "arrows-exchange-alt": {"body": "<path fill=\"currentColor\" d=\"M4.993 11.016a1 1 0 0 1-.531-1.848L7.15 6.48a1 1 0 0 1 1.414 1.415l-1.121 1.12h7.55a1 1 0 0 1 0 2h-10Zm14.014 1.968a1 1 0 0 1 .531 1.848L16.85 17.52a1 1 0 1 1-1.414-1.415l1.121-1.12h-7.55a1 1 0 1 1 0-2h10Z\"/>"}, "arrows-exchange-alt-v": {"body": "<path fill=\"currentColor\" d=\"M12.984 4.993a1 1 0 0 1 1.848-.531L17.52 7.15a1 1 0 1 1-1.415 1.414l-1.12-1.121v7.55a1 1 0 0 1-2 0v-10Zm-1.968 14.014a1 1 0 0 1-1.848.531L6.48 16.85a1 1 0 0 1 1.415-1.414l1.12 1.121v-7.55a1 1 0 0 1 2 0v10Z\"/>"}, "arrows-exchange-v": {"body": "<path fill=\"currentColor\" d=\"M12.984 15a1 1 0 0 0 1.848.53l2.688-2.687a1 1 0 0 0-1.415-1.414l-1.12 1.12V5a1 1 0 0 0-2 0v10Zm-1.968-6a1 1 0 0 0-1.848-.53l-2.687 2.687a1 1 0 1 0 1.414 1.414l1.121-1.12V19a1 1 0 1 0 2 0V9Z\"/>"}, "arrows-expand-down-left": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M13 5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V5Zm2 0h4v4h-4V5Z\" clip-rule=\"evenodd\"/><path d=\"M5 13H3v8h8v-2H6.414l5.364-5.364a1 1 0 0 0-1.414-1.414L5 17.586V13Z\"/></g>"}, "arrows-expand-down-right": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M11 5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V5ZM9 5H5v4h4V5Z\" clip-rule=\"evenodd\"/><path d=\"M19 13h2v8h-8v-2h4.586l-5.364-5.364a1 1 0 0 1 1.414-1.414L19 17.586V13Z\"/></g>"}, "arrows-expand-left": {"body": "<path fill=\"currentColor\" d=\"M10.1 4.1v-2h-8v8h2V5.516l5.779 5.778l1.414-1.414L5.515 4.1H10.1Zm9.8 9.8h2v8h-8v-2h4.585l-5.778-5.779l1.414-1.414l5.778 5.778V13.9Z\"/>"}, "arrows-expand-left-alt": {"body": "<path fill=\"currentColor\" d=\"M10.1 2.1v2H5.516l5.778 5.779l-1.414 1.414L4.1 5.515V10.1h-2v-8h8Zm11.8 11.8h-2v4.585l-5.779-5.778l-1.414 1.414l5.778 5.778H13.9v2h8v-8Zm-5.657-4.728l-1.415-1.415l-7.07 7.072l1.414 1.414l7.07-7.071Z\"/>"}, "arrows-expand-right": {"body": "<path fill=\"currentColor\" d=\"M13.9 4.1v-2h8v8h-2V5.516l-5.779 5.778l-1.414-1.414l5.778-5.78H13.9Zm-9.8 9.8h-2v8h8v-2H5.516l5.778-5.779l-1.414-1.414l-5.78 5.778V13.9Z\"/>"}, "arrows-expand-right-alt": {"body": "<path fill=\"currentColor\" d=\"M13.9 2.1v2h4.585l-5.778 5.78l1.414 1.414L19.9 5.515V10.1h2v-8h-8ZM5.515 19.9H10.1v2h-8v-8h2v4.585l5.778-5.778l1.414 1.414L5.515 19.9ZM9.172 7.757L7.757 9.172l7.071 7.07l1.415-1.414l-7.071-7.07Z\"/>"}, "arrows-expand-up-left": {"body": "<g fill=\"currentColor\"><path d=\"M5 11H3V3h8v2H6.414l5.364 5.364a1 1 0 0 1-1.414 1.414L5 6.414V11Z\"/><path fill-rule=\"evenodd\" d=\"M19 13a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h4Zm0 2v4h-4v-4h4Z\" clip-rule=\"evenodd\"/></g>"}, "arrows-expand-up-right": {"body": "<g fill=\"currentColor\"><path d=\"M13 5V3h8v8h-2V6.414l-5.364 5.364a1 1 0 0 1-1.414-1.414L17.586 5H13Z\"/><path fill-rule=\"evenodd\" d=\"M5 13a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2H5Zm0 2v4h4v-4H5Z\" clip-rule=\"evenodd\"/></g>"}, "arrows-h": {"body": "<path fill=\"currentColor\" d=\"M5.657 9.172L4.243 7.757L0 12l4.243 4.243l1.414-1.415L3.829 13H10v-2H3.83l1.828-1.828ZM14 11v2h6.172l-1.829 1.828l1.414 1.415L24 12l-4.243-4.243l-1.414 1.415L20.172 11H14Z\"/>"}, "arrows-h-alt": {"body": "<path fill=\"currentColor\" d=\"m4.243 7.757l1.414 1.415L3.828 11h16.344l-1.829-1.828l1.414-1.415L24 12l-4.243 4.243l-1.414-1.415L20.171 13H3.828l1.829 1.828l-1.414 1.415L0 12l4.243-4.243Z\"/>"}, "arrows-merge-alt-h": {"body": "<path fill=\"currentColor\" d=\"M1.503 6h2v5h4.172L5.846 9.172l1.415-1.415L11.503 12l-4.242 4.243l-1.415-1.415L7.675 13H3.503v5h-2V6Zm18.994 0h2v12h-2v-5h-4.172l1.829 1.829l-1.415 1.414L12.497 12l4.242-4.243l1.415 1.415L16.325 11h4.172V6Z\"/>"}, "arrows-merge-alt-v": {"body": "<path fill=\"currentColor\" d=\"M18 1.503v2h-5v4.172l1.829-1.829l1.414 1.415L12 11.503L7.757 7.261l1.415-1.415L11 7.675V3.503H6v-2h12Zm0 18.994v2H6v-2h5v-4.172l-1.828 1.829l-1.415-1.415L12 12.497l4.243 4.242l-1.415 1.415L13 16.325v4.172h5Z\"/>"}, "arrows-scroll-h": {"body": "<path fill=\"currentColor\" d=\"m15.305 12l2.825-2.825l-1.414-1.414l-2.825 2.825l-.004-.004l-1.414 1.414l.004.004l-.004.004l1.414 1.414l.004-.004l2.825 2.825l1.414-1.414L15.305 12Zm-5.195-1.414l.003-.004l1.414 1.414l-.004.004l.004.004l-1.414 1.414l-.004-.004l-2.825 2.825l-1.414-1.414L8.695 12L5.87 9.175l1.414-1.414l2.825 2.825Z\"/>"}, "arrows-scroll-v": {"body": "<path fill=\"currentColor\" d=\"m13.414 10.11l.004.003l-1.414 1.414l-.004-.004l-.004.004l-1.414-1.414l.004-.004L7.76 7.284L9.175 5.87L12 8.695l2.825-2.825l1.414 1.414l-2.825 2.825ZM12 15.305l2.825 2.825l1.414-1.414l-2.825-2.825l.004-.004l-1.414-1.414l-.004.004l-.004-.004l-1.414 1.414l.004.004l-2.825 2.825l1.414 1.414L12 15.305Z\"/>"}, "arrows-shrink-h": {"body": "<path fill=\"currentColor\" d=\"M1 7h2v10H1V7Zm7.448.757l1.414 1.415L8.033 11h7.933l-1.828-1.828l1.414-1.415L19.795 12l-4.243 4.243l-1.414-1.415L15.966 13H8.034l1.828 1.828l-1.414 1.415L4.205 12l4.243-4.243ZM23 7h-2v10h2V7Z\"/>"}, "arrows-shrink-v": {"body": "<path fill=\"currentColor\" d=\"M17 1v2H7V1h10Zm-.757 7.448l-1.414 1.414L13 8.033v7.934l1.829-1.829l1.414 1.414L12 19.795l-4.243-4.243l1.415-1.414L11 15.966V8.034L9.172 9.862L7.757 8.448L12 4.205l4.243 4.243ZM17 23v-2H7v2h10Z\"/>"}, "arrows-v": {"body": "<path fill=\"currentColor\" d=\"m7.757 5.04l1.415 1.415L11 4.627V10h2V4.627l1.828 1.828l1.415-1.414L12 .798L7.757 5.041Zm8.486 13.92l-1.415-1.415L13 19.373V14h-2v5.373l-1.828-1.828l-1.415 1.414L12 23.202l4.243-4.243Z\"/>"}, "arrows-v-alt": {"body": "<path fill=\"currentColor\" d=\"M9.172 6.455L7.757 5.041L12 .798l4.243 4.243l-1.415 1.414L13 4.627v14.746l1.828-1.828l1.415 1.414L12 23.202l-4.243-4.243l1.415-1.414L11 19.373V4.627L9.172 6.455Z\"/>"}, "assign": {"body": "<path fill=\"currentColor\" d=\"M6 6h4V4H4v6h2V6Zm4 12H6v-4H4v6h6v-2Zm4-12h4v4h2V4h-6v2Zm0 12h4v-4h2v6h-6v-2Zm-2-9.5a3.5 3.5 0 1 0 0 7a3.5 3.5 0 0 0 0-7Z\"/>"}, "asterisk": {"body": "<path fill=\"currentColor\" d=\"M11 6h2v4.079l3.341-2.34l1.147 1.639L13.743 12l3.745 2.622l-1.147 1.639L13 13.92V18h-2v-4.079l-3.341 2.34l-1.148-1.639L10.257 12L6.51 9.378l1.15-1.639L11 10.08V6Z\"/>"}, "atlasian": {"body": "<g fill=\"currentColor\"><path d=\"M8.507 11.556c-.317-.452-.725-.397-.911.122L5 18.908h5.178c.52-2.058.07-4.865-1.097-6.533l-.574-.819Z\" opacity=\".8\"/><path d=\"M12.874 7.126c-1.267 1.81-1.675 4.958-.911 7.03l1.75 4.751h5.251l-4.597-12.48c-.19-.519-.602-.572-.919-.12l-.574.82Z\"/></g>"}, "attachment": {"body": "<path fill=\"currentColor\" d=\"M14 0a5 5 0 0 1 5 5v12a7 7 0 1 1-14 0V9h2v8a5 5 0 0 0 10 0V5a3 3 0 1 0-6 0v12a1 1 0 1 0 2 0V6h2v11a3 3 0 1 1-6 0V5a5 5 0 0 1 5-5Z\"/>"}, "attribution": {"body": "<path fill=\"currentColor\" d=\"M6 8a2 2 0 0 0 1.732-1H14a2 2 0 1 1 0 4h-4a4 4 0 0 0 0 8h6.268A2 2 0 0 0 20 18a2 2 0 0 0-3.732-1H10a2 2 0 1 1 0-4h4a4 4 0 0 0 0-8H7.732A2 2 0 1 0 6 8Z\"/>"}, "awards": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M19 9a6.992 6.992 0 0 1-3 5.745V22h-2.586L12 20.586L10.586 22H8v-7.255A7 7 0 1 1 19 9Zm-2 0A5 5 0 1 1 7 9a5 5 0 0 1 10 0Zm-7 10.757l2-2l2 2V15.71a7.001 7.001 0 0 1-2 .29a7.001 7.001 0 0 1-2-.29v4.047Z\" clip-rule=\"evenodd\"/>"}, "backspace": {"body": "<g fill=\"currentColor\"><path d=\"m17.743 8.464l1.414 1.415L17.036 12l2.121 2.121l-1.414 1.415l-2.122-2.122l-2.121 2.122l-1.414-1.415L14.207 12l-2.121-2.121L13.5 8.465l2.121 2.12l2.122-2.12Z\"/><path fill-rule=\"evenodd\" d=\"m8.586 19l-6.293-6.293a1 1 0 0 1 0-1.414L8.586 5h14v14h-14Zm.828-12l-5 5l5 5h11.172V7H9.414Z\" clip-rule=\"evenodd\"/></g>"}, "band-aid": {"body": "<g fill=\"currentColor\"><path d=\"M11.939 9.765a1 1 0 1 1-1.813-.845a1 1 0 0 1 1.813.845ZM8.92 13.874a1 1 0 1 0 .845-1.813a1 1 0 0 0-.846 1.813Zm4.954 1.206a1 1 0 1 1-1.813-.845a1 1 0 0 1 1.813.846Zm.361-3.141a1 1 0 1 0 .845-1.813a1 1 0 0 0-.845 1.813Z\"/><path fill-rule=\"evenodd\" d=\"M17.071 1.124a6 6 0 0 0-7.973 2.902L4.026 14.902a6 6 0 0 0 10.876 5.072l5.072-10.876a6 6 0 0 0-2.903-7.974Zm-3.136 16.192l3.38-7.25l-7.25-3.382l-3.38 7.25l7.25 3.382Zm-.846 1.812l-7.25-3.38a4 4 0 1 0 7.25 3.38Zm3.137-16.191a4 4 0 0 1 1.935 5.316l-7.25-3.381a4 4 0 0 1 5.315-1.935Z\" clip-rule=\"evenodd\"/></g>"}, "battery": {"body": "<g fill=\"currentColor\"><path d=\"M6 15a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h6v6H6Z\"/><path fill-rule=\"evenodd\" d=\"M18 6H5a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h13a3 3 0 0 0 3-3a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1a3 3 0 0 0-3-3Zm0 2H5a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h13a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "battery-empty": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M18 6H5a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h13a3 3 0 0 0 3-3a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1a3 3 0 0 0-3-3Zm0 2H5a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h13a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/>"}, "battery-full": {"body": "<g fill=\"currentColor\"><path d=\"M6 15a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H6Z\"/><path fill-rule=\"evenodd\" d=\"M18 6H5a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h13a3 3 0 0 0 3-3a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1a3 3 0 0 0-3-3Zm0 2H5a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h13a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "bee": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.951 15.571a5.993 5.993 0 0 1-2.27 4.064a4.016 4.016 0 0 1-1.756 1.96a2 2 0 0 1-3.874 0a4.016 4.016 0 0 1-1.756-1.96a5.993 5.993 0 0 1-2.269-4.047a3.001 3.001 0 0 1-4.11-4.32L6.01 6.39a6 6 0 0 1 11.953-.033l4.12 4.91a3 3 0 0 1-4.132 4.304Zm-2.326-2.665l-1.678-2h-3.894l-1.678 2h7.25Zm2.363-.296l1.032 1.229a1 1 0 1 0 1.532-1.286l-2.564-3.055v3.112Zm-2-3.704v-2a4 4 0 0 0-8 0v2h8ZM4.98 13.839l1.007-1.2V9.527l-2.54 3.027a1 1 0 1 0 1.533 1.285Zm7.007 5.067a4 4 0 0 1-4-4h8a4 4 0 0 1-4 4Z\" clip-rule=\"evenodd\"/>"}, "bell": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14 3v.29c2.892.86 5 3.539 5 6.71v7h1v2H4v-2h1v-7a7.003 7.003 0 0 1 5-6.71V3a2 2 0 1 1 4 0ZM7 17h10v-7a5 5 0 0 0-10 0v7Zm7 4v-1h-4v1a2 2 0 1 0 4 0Z\" clip-rule=\"evenodd\"/>"}, "bitbucket": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4.583 4.635c-.552 0-.915.44-.811.982l2.456 12.766c.104.542.637.982 1.189.982h9.166c.552 0 1.085-.44 1.189-.982l2.456-12.766c.104-.542-.259-.982-.811-.982H4.583Zm8.962 9.73l.91-4.73h-4.91l.91 4.73h3.09Z\" clip-rule=\"evenodd\"/>"}, "block": {"body": "<g fill=\"currentColor\"><path d=\"M8.465 14.121a1 1 0 1 0 1.414 1.415l5.657-5.657a1 1 0 1 0-1.415-1.415l-5.656 5.657Z\"/><path fill-rule=\"evenodd\" d=\"M6.343 17.657A8 8 0 1 0 17.657 6.343A8 8 0 0 0 6.343 17.657Zm9.9-1.414a6 6 0 1 1-8.486-8.485a6 6 0 0 1 8.486 8.485Z\" clip-rule=\"evenodd\"/></g>"}, "bmw": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M21 12a9 9 0 1 1-18 0a9 9 0 0 1 18 0ZM5 12a7 7 0 0 0 7 7v-7h7a7 7 0 0 0-7-7v7H5Z\" clip-rule=\"evenodd\"/>"}, "board": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6 4a4 4 0 0 0-4 4v8a4 4 0 0 0 4 4h12a4 4 0 0 0 4-4V8a4 4 0 0 0-4-4H6Zm8 2h-4v12h4V6Zm2 0v12h2a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-2ZM6 18h2V6H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2Z\" clip-rule=\"evenodd\"/>"}, "bolt": {"body": "<path fill=\"currentColor\" d=\"m9 21.5l8.5-8.5l-4.5-3l2-7.5L6.5 11l4.5 3l-2 7.5Z\"/>"}, "bookmark": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M19 20h-1.828l-4.465-4.465a1 1 0 0 0-1.414 0L6.828 20H5V7a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3v13ZM17 7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v10l2.879-2.879a3 3 0 0 1 4.242 0L17 17V7Z\" clip-rule=\"evenodd\"/>"}, "border-all": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\" d=\"M6.5 6.5h11v11h-11v-11Z\"/>"}, "border-bottom": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".3\" d=\"M8 8h8v7h3V5H5v10h3V8Z\"/><path d=\"M5 17h14v3H5v-3Z\"/></g>"}, "border-left": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".3\" d=\"M16 8v8H9v3h10V5H9v3h7Z\"/><path d=\"M7 5v14H4V5h3Z\"/></g>"}, "border-right": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".3\" d=\"M8 16V8h7V5H5v14h10v-3H8Z\"/><path d=\"M17 19V5h3v14h-3Z\"/></g>"}, "border-style-dashed": {"body": "<path fill=\"currentColor\" d=\"M4 11h4v2H4v-2Zm6 0h4v2h-4v-2Zm10 0h-4v2h4v-2Z\"/>"}, "border-style-dotted": {"body": "<path fill=\"currentColor\" d=\"M3 11H1v2h2v-2Zm4 0H5v2h2v-2Zm2 0h2v2H9v-2Zm6 0h-2v2h2v-2Zm2 0h2v2h-2v-2Zm6 0h-2v2h2v-2Z\"/>"}, "border-style-solid": {"body": "<path fill=\"currentColor\" d=\"M2 11h20v2H2v-2Z\"/>"}, "border-top": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".3\" d=\"M8 16h8V9h3v10H5V9h3v7Z\"/><path d=\"M5 7h14V4H5v3Z\"/></g>"}, "bot": {"body": "<g fill=\"currentColor\"><path d=\"M14.125 13h-4v2h4v-2Z\"/><path fill-rule=\"evenodd\" d=\"M8.125 13a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm0-1.5a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1Zm10-.5a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm-1.5 0a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0Z\" clip-rule=\"evenodd\"/><path fill-rule=\"evenodd\" d=\"M2.749 14.666A6 6 0 0 0 8.125 18h8c2.44 0 4.54-1.456 5.478-3.547A2.997 2.997 0 0 0 22.875 12c0-1.013-.503-1.91-1.272-2.452A6.001 6.001 0 0 0 16.125 6h-8A6 6 0 0 0 2.75 9.334a3 3 0 0 0 0 5.332ZM8.125 8h8c1.384 0 2.603.702 3.322 1.77c.276.69.428 1.442.428 2.23s-.152 1.54-.428 2.23A3.996 3.996 0 0 1 16.125 16h-8a4 4 0 0 1 0-8Z\" clip-rule=\"evenodd\"/></g>"}, "bowl": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M20.547 3.672a1 1 0 0 0-1.414 0l-5.364 5.364H2v2h.008c.218 5.33 4.608 9.585 9.992 9.585c5.384 0 9.774-4.255 9.992-9.585H22v-2h-5.403l3.95-3.95a1 1 0 0 0 0-1.414Zm-6.37 7.364h5.813a8 8 0 0 1-15.98 0h10.166Z\" clip-rule=\"evenodd\"/>"}, "box": {"body": "<g fill=\"currentColor\"><path d=\"M10 12a1 1 0 1 0 0 2h4a1 1 0 0 0 0-2h-4Z\"/><path fill-rule=\"evenodd\" d=\"M4 2a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V5a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v3h18V5a1 1 0 0 0-1-1ZM3 19v-9h18v9a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "boy": {"body": "<g fill=\"currentColor\"><path d=\"M9 14a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm7-1a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path fill-rule=\"evenodd\" d=\"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10Zm0-2a8 8 0 0 0 7.634-10.4c-.835.226-1.713.346-2.619.346a9.996 9.996 0 0 1-8.692-5.053A8 8 0 0 0 12 20Z\" clip-rule=\"evenodd\"/></g>"}, "brackets": {"body": "<g fill=\"currentColor\"><path d=\"M11 7v2H9v6h2v2H7V7h4Zm4 8h-2v2h4V7h-4v2h2v6Z\"/><path fill-rule=\"evenodd\" d=\"M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm3-1h12a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "briefcase": {"body": "<g fill=\"currentColor\"><path d=\"M14 11h-4v2h4v-2Z\"/><path fill-rule=\"evenodd\" d=\"M7 5V4a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1h3a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V8a3 3 0 0 1 3-3h3Zm2-1h6v1H9V4ZM4 7a1 1 0 0 0-1 1v6h18V8a1 1 0 0 0-1-1H4ZM3 18v-2h18v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "browse": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M14.364 13.121c.924.924 1.12 2.3.586 3.415l1.535 1.535a1 1 0 0 1-1.414 1.414l-1.535-1.535a3.001 3.001 0 0 1-3.415-4.829a3 3 0 0 1 4.243 0ZM12.95 15.95a1 1 0 1 0-1.414-1.414a1 1 0 0 0 1.414 1.414Z\" clip-rule=\"evenodd\"/><path d=\"M8 5h8v2H8V5Zm8 4H8v2h8V9Z\"/><path fill-rule=\"evenodd\" d=\"M4 4a3 3 0 0 1 3-3h10a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H7a3 3 0 0 1-3-3V4Zm3-1h10a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "browser": {"body": "<g fill=\"currentColor\"><path d=\"M4 8a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm4-1a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm2 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M3 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h18a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H3Zm18 2H3a1 1 0 0 0-1 1v3h20V6a1 1 0 0 0-1-1ZM2 18v-7h20v7a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "brush": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15 11h3a1 1 0 0 1 1 1v6a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3v-6a1 1 0 0 1 1-1h3V6a3 3 0 1 1 6 0v5Zm-2-5a1 1 0 1 0-2 0v7H7v5a1 1 0 0 0 1 1h1v-3h2v3h2v-3h2v3h1a1 1 0 0 0 1-1v-5h-4V6Z\" clip-rule=\"evenodd\"/>"}, "bulb": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M4 9a7.997 7.997 0 0 0 4 6.93V16a4 4 0 1 0 8 0v-.07A8 8 0 1 0 4 9Zm12 4.472a6 6 0 1 0-8 0h2V16a2 2 0 1 0 4 0v-2.53l2 .001Z\" clip-rule=\"evenodd\"/><path d=\"M10 21.006V21c.588.34 1.271.535 2 .535c.729 0 1.412-.195 2-.535v.006a2 2 0 1 1-4 0Z\"/></g>"}, "c-plus-plus": {"body": "<g fill=\"currentColor\"><path d=\"M12.207 16.278a6 6 0 1 1 .071-8.485l1.414-1.414a8 8 0 1 0-.071 11.314l-1.414-1.415Z\"/><path d=\"M15 9h-2v2h-2v2h2v2h2v-2h2v-2h-2V9Zm5 0h2v2h2v2h-2v2h-2v-2h-2v-2h2V9Z\"/></g>"}, "calculator": {"body": "<g fill=\"currentColor\"><path d=\"M17 5H7v2h10V5ZM7 9h2v2H7V9Zm2 4H7v2h2v-2Zm-2 4h2v2H7v-2Zm6-8h-2v2h2V9Zm-2 4h2v2h-2v-2Zm2 4h-2v2h2v-2Zm2-8h2v2h-2V9Zm2 4h-2v6h2v-6Z\"/><path fill-rule=\"evenodd\" d=\"M3 3a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v18a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3Zm2 0h14v18H5V3Z\" clip-rule=\"evenodd\"/></g>"}, "calendar": {"body": "<g fill=\"currentColor\"><path d=\"M8 9a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H8Z\"/><path fill-rule=\"evenodd\" d=\"M6 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H6ZM5 18V7h14v11a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "calendar-dates": {"body": "<g fill=\"currentColor\"><path d=\"M8 13a1 1 0 1 1 0-2a1 1 0 0 1 0 2Zm0 4a1 1 0 1 1 0-2a1 1 0 0 1 0 2Zm3-1a1 1 0 1 0 2 0a1 1 0 0 0-2 0Zm5 1a1 1 0 1 1 0-2a1 1 0 0 1 0 2Zm-5-5a1 1 0 1 0 2 0a1 1 0 0 0-2 0Zm5 1a1 1 0 1 1 0-2a1 1 0 0 1 0 2ZM8 7a1 1 0 0 0 0 2h8a1 1 0 1 0 0-2H8Z\"/><path fill-rule=\"evenodd\" d=\"M6 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H6Zm12 2H6a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "calendar-due": {"body": "<g fill=\"currentColor\"><path d=\"M7 8a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1Zm5 8a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M6 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H6Zm12 2H6a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "calendar-next": {"body": "<g fill=\"currentColor\"><path d=\"m11.725 16.546l4.5-2.598l-4.5-2.598v1.598h-3.95v2h3.95v1.598ZM8 7a1 1 0 0 0 0 2h8a1 1 0 1 0 0-2H8Z\"/><path fill-rule=\"evenodd\" d=\"M6 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H6Zm12 2H6a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "calendar-today": {"body": "<g fill=\"currentColor\"><path d=\"M15 17a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M6 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H6ZM5 18V7h14v11a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "calendar-two": {"body": "<g fill=\"currentColor\"><path d=\"M8 7a1 1 0 0 0 0 2h8a1 1 0 1 0 0-2H8Z\"/><path fill-rule=\"evenodd\" d=\"M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm3-1h12a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "calibrate": {"body": "<path fill=\"currentColor\" d=\"M12.05 5a8.97 8.97 0 0 1 6.314 2.586l-4.243 4.243A2.99 2.99 0 0 0 12.05 11c-.855 0-1.625.357-2.172.93L5.636 7.687A8.973 8.973 0 0 1 12.05 5Zm0 14a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\"/>"}, "camera": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4.5v2h8v1H3a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h12a3 3 0 0 0 2.99-2.751L24 17.5v-8l-6.01.751A3 3 0 0 0 15 7.5h-1v-2a1 1 0 0 0-1-1H4Zm14 7.766v2.468l4 .5v-3.468l-4 .5ZM16 10.5a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-6Z\" clip-rule=\"evenodd\"/>"}, "cap": {"body": "<g fill=\"currentColor\"><path d=\"M8 18v2h8v-2H8Z\"/><path fill-rule=\"evenodd\" d=\"M13.988 3.22a2 2 0 1 0-3.976 0a9.003 9.003 0 0 0-6.94 9.926A3.001 3.001 0 0 0 1 16v4a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3v-4c0-1.333-.87-2.463-2.072-2.854a9.003 9.003 0 0 0-6.94-9.926ZM12 5a7 7 0 0 0-6.93 8h13.86A7 7 0 0 0 12 5ZM3 16a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-4Z\" clip-rule=\"evenodd\"/></g>"}, "captions": {"body": "<g fill=\"currentColor\"><path d=\"M11 8v2H8v4h3v2H6V8h5Zm7 0v2h-3v4h3v2h-5V8h5Z\"/><path fill-rule=\"evenodd\" d=\"M2 5a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5Zm2 13V6h16v12H4Z\" clip-rule=\"evenodd\"/></g>"}, "card-clubs": {"body": "<g fill=\"currentColor\"><path d=\"M12 11a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm-1 2a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm4 2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M3 4a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V4Zm3-1h12a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "card-diamonds": {"body": "<g fill=\"currentColor\"><path d=\"M12 7.757L7.757 12L12 16.243L16.243 12L12 7.757Z\"/><path fill-rule=\"evenodd\" d=\"M3 4a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V4Zm3-1h12a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "card-hearts": {"body": "<g fill=\"currentColor\"><path d=\"M9.146 12.293a2 2 0 0 1 2.829-2.829L12 9.49l.025-.026a2 2 0 1 1 2.829 2.829l-2.829 2.828l-.025-.025l-.025.025l-2.829-2.828Z\"/><path fill-rule=\"evenodd\" d=\"M3 4a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V4Zm3-1h12a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "card-spades": {"body": "<g fill=\"currentColor\"><path d=\"M9.146 11.707a2 2 0 0 0 2.829 2.829L12 14.51l.025.026a2 2 0 1 0 2.829-2.829l-2.83-2.827l-.024.024l-.025-.025l-2.829 2.828Z\"/><path fill-rule=\"evenodd\" d=\"M3 20a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H6a3 3 0 0 0-3 3v16Zm3 1h12a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1Z\" clip-rule=\"evenodd\"/></g>"}, "carousel": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M4 3a3 3 0 0 0-3 3v4a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/><path d=\"M7 20a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1Zm-2-5a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2H5Z\"/></g>"}, "cast": {"body": "<g fill=\"currentColor\"><path d=\"M20 6H4v2H2V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-5v-2h5V6ZM2 13a7 7 0 0 1 7 7H7a5 5 0 0 0-5-5v-2Zm0 4a3 3 0 0 1 3 3H2v-3Z\"/><path d=\"M2 9c6.075 0 11 4.925 11 11h-2a9 9 0 0 0-9-9V9Z\"/></g>"}, "chanel": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.072 3.173a9 9 0 0 0-4.608 2.463l2.13 2.13a5.989 5.989 0 0 1 5.701-1.571a9.002 9.002 0 0 0 0 11.61a5.987 5.987 0 0 1-5.702-1.57l-2.13 2.129A9 9 0 0 0 12 19.974a9.003 9.003 0 0 0 10.536-1.61l-2.13-2.13a5.988 5.988 0 0 1-5.701 1.571A9.012 9.012 0 0 0 16.828 12a9 9 0 0 0-2.123-5.805a5.988 5.988 0 0 1 5.702 1.57l2.13-2.129A9 9 0 0 0 12 4.026a9 9 0 0 0-5.928-.853ZM12 7.705a5.99 5.99 0 0 0-.806 7.622c.235.352.505.676.806.968a5.987 5.987 0 0 0 0-8.59Z\" clip-rule=\"evenodd\"/>"}, "chart": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M22.775 8A9 9 0 0 1 23 10h-9V1a9 9 0 0 1 8.775 7Zm-2.067 0A6.999 6.999 0 0 0 16 3.292V8h4.708Z\"/><path d=\"M1 14a9 9 0 0 1 11-8.775V12h6.775A9 9 0 1 1 1 14Zm15.803 0H10V7.196A6.804 6.804 0 1 0 16.803 14Z\"/></g>"}, "check": {"body": "<path fill=\"currentColor\" d=\"m10.586 13.414l-2.829-2.828L6.343 12l4.243 4.243l7.07-7.071l-1.413-1.415l-5.657 5.657Z\"/>"}, "check-o": {"body": "<g fill=\"currentColor\"><path d=\"M10.243 16.314L6 12.07l1.414-1.414l2.829 2.828l5.656-5.657l1.415 1.415l-7.071 7.07Z\"/><path fill-rule=\"evenodd\" d=\"M1 12C1 5.925 5.925 1 12 1s11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12Zm11 9a9 9 0 1 1 0-18a9 9 0 0 1 0 18Z\" clip-rule=\"evenodd\"/></g>"}, "check-r": {"body": "<g fill=\"currentColor\"><path d=\"M10.243 16.314L6 12.07l1.414-1.414l2.829 2.828l5.656-5.657l1.415 1.415l-7.071 7.07Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-double-down": {"body": "<g fill=\"currentColor\"><path d=\"M7.757 5.636L6.343 7.05L12 12.707l5.657-5.657l-1.414-1.414L12 9.88L7.757 5.636Z\"/><path d=\"m6.343 12.707l1.414-1.414L12 15.536l4.243-4.243l1.414 1.414L12 18.364l-5.657-5.657Z\"/></g>"}, "chevron-double-down-o": {"body": "<g fill=\"currentColor\"><path d=\"M7.757 8.464L9.172 7.05L12 9.88l2.828-2.829l1.415 1.415L12 12.707L7.757 8.464Z\"/><path d=\"m9.172 11.293l-1.415 1.414L12 16.95l4.243-4.243l-1.415-1.414L12 14.12l-2.828-2.828Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-double-down-r": {"body": "<g fill=\"currentColor\"><path d=\"M7.757 8.464L9.172 7.05L12 9.88l2.828-2.829l1.415 1.415L12 12.707L7.757 8.464Z\"/><path d=\"m9.172 11.293l-1.415 1.414L12 16.95l4.243-4.243l-1.415-1.414L12 14.12l-2.828-2.828Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-double-left": {"body": "<g fill=\"currentColor\"><path d=\"M18.364 7.757L16.95 6.343L11.293 12l5.657 5.657l1.414-1.414L14.12 12l4.243-4.243Z\"/><path d=\"m11.293 6.343l1.414 1.414L8.464 12l4.243 4.243l-1.414 1.414L5.636 12l5.657-5.657Z\"/></g>"}, "chevron-double-left-o": {"body": "<g fill=\"currentColor\"><path d=\"m12.707 9.172l-1.414-1.415L7.05 12l4.243 4.243l1.414-1.415L9.88 12l2.828-2.828Z\"/><path d=\"m15.536 7.757l1.414 1.415L14.12 12l2.829 2.828l-1.414 1.415L11.293 12l4.243-4.243Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-double-left-r": {"body": "<g fill=\"currentColor\"><path d=\"m12.707 9.172l-1.414-1.415L7.05 12l4.243 4.243l1.414-1.415L9.88 12l2.828-2.828Z\"/><path d=\"m15.536 7.757l1.414 1.415L14.12 12l2.829 2.828l-1.414 1.415L11.293 12l4.243-4.243Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-double-right": {"body": "<g fill=\"currentColor\"><path d=\"M5.636 7.757L7.05 6.343L12.707 12L7.05 17.657l-1.414-1.414L9.88 12L5.636 7.757Z\"/><path d=\"m12.707 6.343l-1.414 1.414L15.536 12l-4.243 4.243l1.414 1.414L18.364 12l-5.657-5.657Z\"/></g>"}, "chevron-double-right-o": {"body": "<g fill=\"currentColor\"><path d=\"M8.464 7.757L7.05 9.172L9.88 12l-2.83 2.828l1.415 1.415L12.707 12L8.464 7.757Z\"/><path d=\"m11.293 9.172l1.414-1.415L16.95 12l-4.243 4.243l-1.414-1.415L14.12 12l-2.828-2.828Z\"/><path fill-rule=\"evenodd\" d=\"M1 12c0 6.075 4.925 11 11 11s11-4.925 11-11S18.075 1 12 1S1 5.925 1 12Zm2 0a9 9 0 1 0 18 0a9 9 0 0 0-18 0Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-double-right-r": {"body": "<g fill=\"currentColor\"><path d=\"M8.464 7.757L7.05 9.172L9.88 12l-2.83 2.828l1.415 1.415L12.707 12L8.464 7.757Z\"/><path d=\"m11.293 9.172l1.414-1.415L16.95 12l-4.243 4.243l-1.414-1.415L14.12 12l-2.828-2.828Z\"/><path fill-rule=\"evenodd\" d=\"M23 5a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V5Zm-4-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-double-up": {"body": "<g fill=\"currentColor\"><path d=\"m17.657 11.293l-1.414 1.414L12 8.464l-4.243 4.243l-1.414-1.414L12 5.636l5.657 5.657Z\"/><path d=\"m17.657 16.95l-1.414 1.414L12 14.12l-4.243 4.243l-1.414-1.414L12 11.293l5.657 5.657Z\"/></g>"}, "chevron-double-up-o": {"body": "<g fill=\"currentColor\"><path d=\"m14.828 12.707l1.415-1.414L12 7.05l-4.243 4.243l1.415 1.414L12 9.88l2.828 2.828Z\"/><path d=\"m14.828 16.95l1.415-1.414L12 11.293l-4.243 4.243l1.415 1.414L12 14.12l2.828 2.829Z\"/><path fill-rule=\"evenodd\" d=\"M1 12c0 6.075 4.925 11 11 11s11-4.925 11-11S18.075 1 12 1S1 5.925 1 12Zm2 0a9 9 0 1 0 18 0a9 9 0 0 0-18 0Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-double-up-r": {"body": "<g fill=\"currentColor\"><path d=\"m14.828 12.481l1.415-1.414L12 6.824l-4.243 4.243l1.415 1.414L12 9.653l2.828 2.828Z\"/><path d=\"m14.828 16.724l1.415-1.414L12 11.067L7.757 15.31l1.415 1.414L12 13.895l2.828 2.829Z\"/><path fill-rule=\"evenodd\" d=\"M23 4.774a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4v-14Zm-4-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-14a2 2 0 0 0-2-2Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-down": {"body": "<path fill=\"currentColor\" d=\"M6.343 7.757L4.93 9.172l7.07 7.07l7.071-7.07l-1.414-1.415L12 13.414L6.343 7.757Z\"/>"}, "chevron-down-o": {"body": "<g fill=\"currentColor\"><path d=\"m7.757 10.586l1.415-1.414L12 12l2.829-2.828l1.414 1.414L12 14.828l-4.243-4.242Z\"/><path fill-rule=\"evenodd\" d=\"M1 12C1 5.925 5.925 1 12 1s11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12Zm11 9a9 9 0 1 1 0-18a9 9 0 0 1 0 18Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-down-r": {"body": "<g fill=\"currentColor\"><path d=\"m7.757 10.586l1.415-1.414L12 12l2.828-2.828l1.415 1.414L12 14.828l-4.243-4.242Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-left": {"body": "<path fill=\"currentColor\" d=\"M16.243 6.343L14.828 4.93L7.758 12l7.07 7.071l1.415-1.414L10.586 12l5.657-5.657Z\"/>"}, "chevron-left-o": {"body": "<g fill=\"currentColor\"><path d=\"m12 7.757l1.414 1.415L10.586 12l2.828 2.829L12 16.242L7.757 12L12 7.757Z\"/><path fill-rule=\"evenodd\" d=\"M12 1c6.075 0 11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1ZM3 12a9 9 0 1 1 18 0a9 9 0 0 1-18 0Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-left-r": {"body": "<g fill=\"currentColor\"><path d=\"m13 7.757l1.414 1.415L11.586 12l2.828 2.828L13 16.243L8.757 12L13 7.757Z\"/><path fill-rule=\"evenodd\" d=\"M19 1a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5a4 4 0 0 1 4-4h14Zm2 4v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-right": {"body": "<path fill=\"currentColor\" d=\"M10.586 6.343L12 4.93L19.071 12L12 19.071l-1.414-1.414L16.243 12l-5.657-5.657Z\"/>"}, "chevron-right-o": {"body": "<g fill=\"currentColor\"><path d=\"M11.086 7.757L15.328 12l-4.242 4.243l-1.414-1.414L12.5 12L9.672 9.172l1.414-1.415Z\"/><path fill-rule=\"evenodd\" d=\"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11s11-4.925 11-11S18.075 1 12 1Zm9 11a9 9 0 1 0-18 0a9 9 0 0 0 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-right-r": {"body": "<g fill=\"currentColor\"><path d=\"m16.485 12.045l-4.242-4.243l-1.415 1.415l2.829 2.828l-2.829 2.829l1.415 1.414l4.242-4.243Z\"/><path fill-rule=\"evenodd\" d=\"M1 4a3 3 0 0 1 3-3h16a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V4Zm3-1h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-up": {"body": "<path fill=\"currentColor\" d=\"m17.657 16.243l1.414-1.414l-7.07-7.072l-7.072 7.072l1.414 1.414L12 10.586l5.657 5.657Z\"/>"}, "chevron-up-o": {"body": "<g fill=\"currentColor\"><path d=\"m14.829 14.828l1.414-1.414L12 9.172l-4.243 4.242l1.415 1.415L12 12l2.829 2.828Z\"/><path fill-rule=\"evenodd\" d=\"M1 12c0 6.075 4.925 11 11 11s11-4.925 11-11S18.075 1 12 1S1 5.925 1 12Zm11-9a9 9 0 1 0 0 18a9 9 0 0 0 0-18Z\" clip-rule=\"evenodd\"/></g>"}, "chevron-up-r": {"body": "<g fill=\"currentColor\"><path d=\"m14.829 14.828l1.414-1.414L12 9.172l-4.243 4.242l1.415 1.415L12 12l2.829 2.828Z\"/><path fill-rule=\"evenodd\" d=\"M1 19a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14Zm4 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2Z\" clip-rule=\"evenodd\"/></g>"}, "circleci": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12s12-5.373 12-12S18.627 0 12 0ZM4.144 13.517a8 8 0 1 0-.006-3l2.59-.01a5.478 5.478 0 1 1 .004 3l-2.588.01ZM9.522 12a2.478 2.478 0 1 0 4.956 0a2.478 2.478 0 0 0-4.956 0Z\" clip-rule=\"evenodd\"/>"}, "clapper-board": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m20.17 3l-.004.005A3 3 0 0 1 23 6v12a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V6a3 3 0 0 1 3-3h16.17Zm-9.694 2h6L13.09 9h-6l3.387-4ZM5.09 9l3.387-4H4a1 1 0 0 0-1 1v3h2.089ZM3 11v7a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-7H3Zm18-2V6a1 1 0 0 0-1-1h-1.524L15.09 9H21Z\" clip-rule=\"evenodd\"/>"}, "clipboard": {"body": "<g fill=\"currentColor\"><path d=\"M8 11a1 1 0 1 0 0 2h7.96a1 1 0 1 0 0-2H8Zm.04 4.066a1 1 0 1 0 0 2H16a1 1 0 1 0 0-2H8.04Z\"/><path fill-rule=\"evenodd\" d=\"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H5Zm2 2H5v14h14V5h-2v1a3 3 0 0 1-3 3h-4a3 3 0 0 1-3-3V5Zm2 0v1a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V5H9Z\" clip-rule=\"evenodd\"/></g>"}, "close": {"body": "<path fill=\"currentColor\" d=\"M6.225 4.811a1 1 0 0 0-1.414 1.414L10.586 12L4.81 17.775a1 1 0 1 0 1.414 1.414L12 13.414l5.775 5.775a1 1 0 0 0 1.414-1.414L13.414 12l5.775-5.775a1 1 0 0 0-1.414-1.414L12 10.586L6.225 4.81Z\"/>"}, "close-o": {"body": "<g fill=\"currentColor\"><path d=\"M16.34 9.322a1 1 0 1 0-1.364-1.463l-2.926 2.728L9.322 7.66A1 1 0 0 0 7.86 9.024l2.728 2.926l-2.927 2.728a1 1 0 1 0 1.364 1.462l2.926-2.727l2.728 2.926a1 1 0 1 0 1.462-1.363l-2.727-2.926l2.926-2.728Z\"/><path fill-rule=\"evenodd\" d=\"M1 12C1 5.925 5.925 1 12 1s11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12Zm11 9a9 9 0 1 1 0-18a9 9 0 0 1 0 18Z\" clip-rule=\"evenodd\"/></g>"}, "close-r": {"body": "<g fill=\"currentColor\"><path d=\"M16.396 7.757a1 1 0 0 1 0 1.415l-2.982 2.981l2.676 2.675a1 1 0 1 1-1.415 1.415L12 13.567l-2.675 2.676a1 1 0 0 1-1.415-1.415l2.676-2.675l-2.982-2.981A1 1 0 1 1 9.02 7.757L12 10.74l2.981-2.982a1 1 0 0 1 1.415 0Z\"/><path fill-rule=\"evenodd\" d=\"M4 1a3 3 0 0 0-3 3v16a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "cloud": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14.738 19.996A8 8 0 1 0 8.735 7H7.52a6.5 6.5 0 0 0 0 13h7a5.3 5.3 0 0 0 .219-.004Zm1.953-2.275c2.35-.769 4.29-3.04 4.29-5.721a6 6 0 0 0-12 0h-2c0-1.06.206-2.074.581-3H7.52a4.5 4.5 0 1 0 0 9h7c.55 0 1.385-.099 2.172-.279Z\" clip-rule=\"evenodd\"/>"}, "code": {"body": "<path fill=\"currentColor\" d=\"m9.953 16.912l-1.36 1.449l-6.562-6.16L8.19 5.64l1.458 1.369l-4.79 5.104l5.094 4.781v.02Zm4.094 0l1.36 1.449l6.562-6.16L15.81 5.64l-1.458 1.369l4.79 5.104l-5.094 4.781v.02Z\"/>"}, "code-climate": {"body": "<path fill=\"currentColor\" d=\"m9.495 8.11l-6.364 6.365l1.414 1.414l4.95-4.95l4.95 4.95l1.414-1.414L9.495 8.11Zm5.01 0l-1.973 1.974l1.418 1.41l.555-.555l4.95 4.95l1.414-1.414l-6.364-6.364Z\"/>"}, "code-slash": {"body": "<path fill=\"currentColor\" d=\"M13.325 3.05L8.667 20.432l1.932.518l4.658-17.382l-1.932-.518ZM7.612 18.36l1.36-1.448l-.001-.019l-5.094-4.78l4.79-5.105l-1.458-1.369l-6.16 6.563l6.563 6.159Zm8.776 0l-1.36-1.448l.001-.019l5.094-4.78l-4.79-5.105l1.458-1.369l6.16 6.563l-6.563 6.159Z\"/>"}, "coffee": {"body": "<g fill=\"currentColor\"><path d=\"M6 2.5a1 1 0 0 0-1 1v2a1 1 0 0 0 2 0v-2a1 1 0 0 0-1-1Z\"/><path fill-rule=\"evenodd\" d=\"M13 21.5a6.002 6.002 0 0 0 5.917-5H19a4 4 0 0 0 0-8v-1H1v8a6 6 0 0 0 6 6h6ZM3 9.5v6a4 4 0 0 0 4 4h6a4 4 0 0 0 4-4v-6H3Zm18 3a2 2 0 0 1-2 2v-4a2 2 0 0 1 2 2Z\" clip-rule=\"evenodd\"/><path d=\"M9 3.5a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0v-2Zm5-1a1 1 0 0 0-1 1v2a1 1 0 1 0 2 0v-2a1 1 0 0 0-1-1Z\"/></g>"}, "collage": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 5a3 3 0 0 1 3-3h10a3 3 0 0 1 3 3v14a3 3 0 0 1-3 3H7a3 3 0 0 1-3-3V5Zm9-1h4a1 1 0 0 1 1 1v8h-5V4Zm0 11v5h4a1 1 0 0 0 1-1v-4h-5ZM11 4H7a1 1 0 0 0-1 1v3h5V4ZM6 19v-9h5v10H7a1 1 0 0 1-1-1Z\" clip-rule=\"evenodd\"/>"}, "color-bucket": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M8.203 2.004c1.261 0 2.304 1.103 2.476 2.538l8.483 8.484l-7.778 7.778a3 3 0 0 1-4.243 0L2.9 16.562a3 3 0 0 1 0-4.243l2.804-2.805V4.961c0-1.633 1.12-2.957 2.5-2.957Zm.5 2.957v1.553l-1 1V4.961c0-.327.224-.591.5-.591c.277 0 .5.264.5.591Zm0 5.914V9.342l-4.39 4.391a1 1 0 0 0 0 1.414l4.243 4.243a1 1 0 0 0 1.414 0l6.364-6.364l-5.63-5.63v3.48l-.003.128h-2.01a.698.698 0 0 0 .012-.129Z\" clip-rule=\"evenodd\"/><path d=\"M16.859 16.875a3 3 0 1 0 4.242 0l-2.121-2.121l-2.121 2.12Z\"/></g>"}, "color-picker": {"body": "<g fill=\"currentColor\"><path d=\"M20.385 2.879a3 3 0 0 0-4.243 0L14.02 5l-.707-.708a1 1 0 1 0-1.414 1.415l5.657 5.656A1 1 0 0 0 18.97 9.95l-.707-.707l2.122-2.122a3 3 0 0 0 0-4.242Z\"/><path fill-rule=\"evenodd\" d=\"M11.93 7.091L4.152 14.87a3.001 3.001 0 0 0-.587 3.415L2 19.85l1.414 1.415l1.565-1.566a3.001 3.001 0 0 0 3.415-.586l7.778-7.778L11.93 7.09Zm1.414 4.243L11.93 9.92l-6.364 6.364a1 1 0 0 0 1.414 1.414l6.364-6.364Z\" clip-rule=\"evenodd\"/></g>"}, "comedy-central": {"body": "<g fill=\"currentColor\"><path d=\"M10.544 19a7 7 0 1 0-4.95-11.95L3.475 4.93l-.019.018A9.969 9.969 0 0 1 10.545 2c5.522 0 10 4.477 10 10s-4.478 10-10 10a9.969 9.969 0 0 1-7.072-2.929l2.122-2.121a6.978 6.978 0 0 0 4.95 2.05Z\"/><path d=\"M10.544 14c.594 0 1.126-.258 1.493-.668l2.122 2.122a5 5 0 1 1 0-6.909l-2.122 2.123A2 2 0 1 0 10.545 14Z\"/></g>"}, "comment": {"body": "<g fill=\"currentColor\"><path d=\"M17 9H7V7h10v2ZM7 13h10v-2H7v2Z\"/><path fill-rule=\"evenodd\" d=\"M2 18V2h20v16h-6v4h-2a4 4 0 0 1-4-4H2Zm10-2v2a2 2 0 0 0 2 2v-4h6V4H4v12h8Z\" clip-rule=\"evenodd\"/></g>"}, "community": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 17.456a6 6 0 1 1 0-10.912a6 6 0 1 1 0 10.912Zm-2-1.487a4 4 0 1 1 0-7.938A5.977 5.977 0 0 0 8.5 12a5.98 5.98 0 0 0 1.5 3.969Zm4-7.938a4 4 0 1 1 0 7.938A5.978 5.978 0 0 0 15.5 12A5.978 5.978 0 0 0 14 8.031Zm-2 .846c.915.733 1.5 1.86 1.5 3.123c0 1.263-.585 2.39-1.5 3.123A3.993 3.993 0 0 1 10.5 12c0-1.263.585-2.39 1.5-3.123Z\" clip-rule=\"evenodd\"/>"}, "components": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.757 6.343L12 2.1l4.242 4.243L12 10.586L7.757 6.343Zm2.829 0L12 4.93l1.414 1.414L12 7.757l-1.414-1.414ZM2.1 12l4.243-4.243L10.586 12l-4.243 4.242L2.1 12Zm2.829 0l1.414-1.414L7.757 12l-1.414 1.414L4.93 12Zm8.485 0l4.243 4.242L21.899 12l-4.242-4.243L13.414 12Zm4.243-1.414L16.243 12l1.414 1.414L19.07 12l-1.414-1.414Zm-9.9 7.071L12 13.414l4.242 4.243L12 21.899l-4.243-4.242Zm2.829 0L12 16.243l1.414 1.414L12 19.07l-1.414-1.414Z\" clip-rule=\"evenodd\"/>"}, "compress": {"body": "<path fill=\"currentColor\" d=\"m19.095 8.43l-1.424-1.404l-4.914 4.985l4.985 4.914l1.404-1.424l-2.502-2.467l6.497.05l.016-2l-6.628-.05l2.566-2.604ZM5.467 15.562l1.416 1.412l4.944-4.956l-4.956-4.943L5.459 8.49l2.591 2.585l-7.206.024l.006 2l7.097-.024l-2.48 2.486Z\"/>"}, "compress-left": {"body": "<path fill=\"currentColor\" d=\"m7.979 9.457l-3.57-.003l-.002 2l7 .006l.006-7l-2-.002L9.41 8.06L3.096 1.77L1.685 3.185l6.294 6.271Zm11.582 5.095l-.008-2l-7 .028l.028 7l2-.008l-.014-3.601l6.343 6.26l1.405-1.424l-6.324-6.24l3.57-.015Z\"/>"}, "compress-right": {"body": "<path fill=\"currentColor\" d=\"m14.567 8.03l6.343-6.26l1.405 1.423l-6.323 6.24l3.57.015l-.009 2l-7-.028l.028-7l2 .008l-.014 3.601Zm-6.588 6.513l-3.57.003l-.002-2l7-.006l.006 7l-2 .002l-.003-3.602l-6.314 6.29l-1.411-1.416l6.294-6.271Z\"/>"}, "compress-v": {"body": "<path fill=\"currentColor\" d=\"M13.034 7.356L15.5 4.854l1.424 1.404l-4.913 4.985L7.025 6.33L8.43 4.905l2.604 2.566l.05-6.627l2 .015l-.05 6.497Zm2.529 11.176l1.412-1.416l-4.957-4.943l-4.943 4.957l1.417 1.412l2.584-2.592l.026 7.207l2-.008l-.026-7.096l2.487 2.479Z\"/>"}, "controller": {"body": "<g fill=\"currentColor\"><path d=\"m14.828 6.343l1.415-1.414L12 .686L7.757 4.93l1.415 1.414L12 3.515l2.828 2.828Zm-9.899 9.9l1.414-1.415L3.515 12l2.828-2.828L4.93 7.757L.686 12l4.243 4.243Zm2.828 2.828L12 23.314l4.243-4.243l-1.415-1.414L12 20.485l-2.828-2.828l-1.415 1.414Zm9.9-9.899L20.485 12l-2.828 2.828l1.414 1.415L23.314 12L19.07 7.757l-1.414 1.415Z\"/><path fill-rule=\"evenodd\" d=\"M12 8a4 4 0 1 1 0 8a4 4 0 0 1 0-8Zm0 2a2 2 0 1 1 0 4a2 2 0 0 1 0-4Z\" clip-rule=\"evenodd\"/></g>"}, "copy": {"body": "<g fill=\"currentColor\"><path d=\"M13 7H7V5h6v2Zm0 4H7V9h6v2Zm-6 4h6v-2H7v2Z\"/><path fill-rule=\"evenodd\" d=\"M3 19V1h14v4h4v18H7v-4H3Zm12-2V3H5v14h10Zm2-10v12H9v2h10V7h-2Z\" clip-rule=\"evenodd\"/></g>"}, "copyright": {"body": "<g fill=\"currentColor\"><path d=\"m13.392 10.436l1.419-1.418a4 4 0 1 0 .176 5.798l-1.313-1.313h-.21a2 2 0 1 1-.073-3.067Z\"/><path fill-rule=\"evenodd\" d=\"M12 3a9 9 0 1 1 0 18a9 9 0 0 1 0-18Zm0 2a7 7 0 1 1 0 14a7 7 0 0 1 0-14Z\" clip-rule=\"evenodd\"/></g>"}, "corner-double-down-left": {"body": "<path fill=\"currentColor\" d=\"m11.295 7.694l4.84-5.056l5.057 4.84l-1.383 1.445l-2.462-2.357l.162 6.034a4.8 4.8 0 0 1-4.67 4.927l-5.925.16l2.294 2.246l-1.4 1.43l-5-4.9l4.898-5l1.429 1.4l-2.377 2.427l6.017-.162a2.4 2.4 0 0 0 2.335-2.463l-.158-5.898l-2.212 2.31l-1.445-1.383Z\"/>"}, "corner-double-down-right": {"body": "<path fill=\"currentColor\" d=\"M12.6 7.68L7.638 2.741L2.701 7.704l1.418 1.41L6.522 6.7l-.014 6.036a4.8 4.8 0 0 0 4.788 4.812l5.928.014l-2.239 2.303l1.434 1.394l4.88-5.019l-5.019-4.88l-1.394 1.434l2.436 2.369l-6.02-.015a2.4 2.4 0 0 1-2.394-2.406l.014-5.9l2.268 2.256L12.6 7.68Z\"/>"}, "corner-double-left-down": {"body": "<path fill=\"currentColor\" d=\"m21.299 7.76l-5.019 4.88l-1.394-1.434l2.436-2.368l-6.02.015a2.4 2.4 0 0 0-2.394 2.406l.014 5.9l2.268-2.256l1.41 1.418l-4.962 4.937l-4.937-4.962l1.418-1.41L6.522 17.3l-.014-6.036a4.8 4.8 0 0 1 4.788-4.812l5.928-.014l-2.239-2.303l1.434-1.394L21.3 7.76Z\"/>"}, "corner-double-left-up": {"body": "<g fill=\"currentColor\"><path d=\"M7.784 9.25L6.37 7.836l4.242-4.242l4.243 4.242L13.44 9.25l-2.829-2.828L7.784 9.25Z\"/><path d=\"m13.44 13.493l1.415-1.414l-4.243-4.243L6.37 12.08l1.414 1.414l1.847-1.847v4.76a4 4 0 0 0 4 4h4v-2h-4a2 2 0 0 1-2-2v-4.723l1.81 1.81Z\"/></g>"}, "corner-double-right-down": {"body": "<path fill=\"currentColor\" d=\"m7.694 12.705l-5.056-4.84l4.84-5.057L8.923 4.19L6.566 6.653L12.6 6.49a4.8 4.8 0 0 1 4.927 4.669l.16 5.926l2.246-2.294l1.43 1.4l-4.9 5l-5-4.898l1.4-1.429l2.427 2.378l-.162-6.018a2.4 2.4 0 0 0-2.463-2.335l-5.898.158l2.31 2.212l-1.383 1.445Z\"/>"}, "corner-double-right-up": {"body": "<g fill=\"currentColor\"><path d=\"m16.216 9.25l1.415-1.414l-4.243-4.242l-4.243 4.242L10.56 9.25l2.828-2.828l2.828 2.828Z\"/><path d=\"M10.56 13.493L9.145 12.08l4.243-4.243l4.243 4.243l-1.415 1.414l-1.847-1.847v4.76a4 4 0 0 1-4 4h-4v-2h4a2 2 0 0 0 2-2v-4.723l-1.81 1.81Z\"/></g>"}, "corner-double-up-left": {"body": "<g fill=\"currentColor\"><path d=\"M9.25 7.784L7.836 6.369l-4.242 4.243l4.242 4.243L9.25 13.44l-2.828-2.828L9.25 7.784Z\"/><path d=\"m13.493 13.44l-1.414 1.415l-4.243-4.243L12.08 6.37l1.414 1.415l-1.847 1.846h4.76a4 4 0 0 1 4 4v4h-2v-4a2 2 0 0 0-2-2h-4.723l1.81 1.81Z\"/></g>"}, "corner-double-up-right": {"body": "<g fill=\"currentColor\"><path d=\"m14.75 7.784l1.414-1.415l4.242 4.243l-4.242 4.243l-1.415-1.415l2.829-2.828l-2.829-2.828Z\"/><path d=\"m10.507 13.44l1.414 1.415l4.243-4.243L11.92 6.37l-1.414 1.415l1.847 1.846h-4.76a4 4 0 0 0-4 4v4h2v-4a2 2 0 0 1 2-2h4.723l-1.81 1.81Z\"/></g>"}, "corner-down-left": {"body": "<path fill=\"currentColor\" d=\"M17.15 13.4a2 2 0 0 0 2-2v-8h2v8a4 4 0 0 1-4 4H6.844l3.785 3.785L9.214 20.6L2.85 14.235l6.364-6.364l1.415 1.415L6.514 13.4H17.15Z\"/>"}, "corner-down-right": {"body": "<path fill=\"currentColor\" d=\"M6.85 13.4a2 2 0 0 1-2-2v-8h-2v8a4 4 0 0 0 4 4h10.306l-3.785 3.785l1.415 1.414l6.364-6.364l-6.364-6.364l-1.415 1.415l4.115 4.114H6.85Z\"/>"}, "corner-left-down": {"body": "<path fill=\"currentColor\" d=\"M10.601 6.85a2 2 0 0 1 2.002-1.998l8 .007l.002-2l-8-.007a4 4 0 0 0-4.004 3.996l-.01 10.306l-3.78-3.788l-1.416 1.412l6.358 6.37l6.37-6.358l-1.413-1.415l-4.119 4.11l.01-10.635Z\"/>"}, "corner-left-up": {"body": "<path fill=\"currentColor\" d=\"m14.71 10.625l1.413-1.415l-6.37-6.358l-6.358 6.37l1.416 1.413l3.78-3.789l.01 10.306a4 4 0 0 0 4.004 3.996l8-.007l-.002-2l-8 .007a2 2 0 0 1-2.002-1.998l-.01-10.636l4.119 4.111Z\"/>"}, "corner-right-down": {"body": "<path fill=\"currentColor\" d=\"M13.399 6.85a2 2 0 0 0-2.002-1.998l-8 .007l-.002-2l8-.007a4 4 0 0 1 4.004 3.996l.01 10.306l3.78-3.788l1.416 1.412l-6.358 6.37l-6.37-6.358l1.413-1.415l4.119 4.11l-.01-10.635Z\"/>"}, "corner-right-up": {"body": "<path fill=\"currentColor\" d=\"M9.29 10.625L7.877 9.21l6.37-6.358l6.358 6.37l-1.416 1.413l-3.78-3.789l-.01 10.306a4 4 0 0 1-4.004 3.996l-8-.007l.002-2l8 .007a2 2 0 0 0 2.002-1.998l.01-10.636l-4.119 4.111Z\"/>"}, "corner-up-left": {"body": "<path fill=\"currentColor\" d=\"m10.628 14.722l-1.412 1.417L2.84 9.79l6.35-6.378l1.417 1.411L6.83 8.615l10.305-.022a4 4 0 0 1 4.009 3.991l.017 8l-2 .005l-.017-8a2 2 0 0 0-2.004-1.996l-10.636.023l4.124 4.106Z\"/>"}, "corner-up-right": {"body": "<path fill=\"currentColor\" d=\"m13.372 14.722l1.412 1.417l6.377-6.35l-6.35-6.378l-1.417 1.411l3.776 3.793l-10.305-.022a4 4 0 0 0-4.009 3.991l-.017 8l2 .005l.017-8a2 2 0 0 1 2.004-1.996l10.636.023l-4.124 4.106Z\"/>"}, "credit-card": {"body": "<g fill=\"currentColor\"><path d=\"M4 9a1 1 0 0 1 1-1h4a1 1 0 0 1 0 2H5a1 1 0 0 1-1-1Z\"/><path fill-rule=\"evenodd\" d=\"M4 3a4 4 0 0 0-4 4v10a4 4 0 0 0 4 4h16a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4H4Zm16 2H4a2 2 0 0 0-2 2v7h20V7a2 2 0 0 0-2-2Zm2 11H2v1a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-1Z\" clip-rule=\"evenodd\"/></g>"}, "crop": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.932 9.009V16H15v4.009h2V16h3.932v-2H17V7.009H9.932V3h-2v4.009H4v2h3.932Zm2 0V14H15V9.009H9.932Z\" clip-rule=\"evenodd\"/>"}, "cross": {"body": "<path fill=\"currentColor\" d=\"M13 6a1 1 0 1 0-2 0v3H7a1 1 0 0 0 0 2h4v7a1 1 0 1 0 2 0v-7h4a1 1 0 1 0 0-2h-4V6Z\"/>"}, "crowdfire": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2v2h8v8h2c0 5.523-4.477 10-10 10S2 17.523 2 12Zm16 0h-2V8h-4V6a6 6 0 1 0 6 6Z\" clip-rule=\"evenodd\"/>"}, "crown": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m2.5 6.091l4.72 4.72L12 6.031l4.781 4.78L21.5 6.092v8.877a3 3 0 0 1-3 3h-13a3 3 0 0 1-3-3V6.091Zm17 4.818v4.06a1 1 0 0 1-1 1h-13a1 1 0 0 1-1-1v-4.061l2.72 2.72L12 8.848l4.781 4.78l2.719-2.72Z\" clip-rule=\"evenodd\"/>"}, "danger": {"body": "<g fill=\"currentColor\"><path d=\"M12 6a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0V7a1 1 0 0 1 1-1Zm0 10a1 1 0 1 0 0 2a1 1 0 0 0 0-2Z\"/><path fill-rule=\"evenodd\" d=\"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10s10-4.477 10-10S17.523 2 12 2ZM4 12a8 8 0 1 0 16 0a8 8 0 0 0-16 0Z\" clip-rule=\"evenodd\"/></g>"}, "dark-mode": {"body": "<g fill=\"currentColor\"><path d=\"M12 16a4 4 0 0 0 0-8v8Z\"/><path fill-rule=\"evenodd\" d=\"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10s10-4.477 10-10S17.523 2 12 2Zm0 2v4a4 4 0 1 0 0 8v4a8 8 0 1 0 0-16Z\" clip-rule=\"evenodd\"/></g>"}, "data": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12 8.89a3 3 0 0 1 1 5.829v5.17h-2v-5.17a3.001 3.001 0 0 1 1-5.83Zm0 2a1 1 0 1 1 0 2a1 1 0 0 1 0-2Z\" clip-rule=\"evenodd\"/><path d=\"M7.05 6.94A6.978 6.978 0 0 0 5 11.89c0 2.177.994 4.122 2.554 5.406l1.423-1.424A4.992 4.992 0 0 1 7 11.89c0-1.38.56-2.63 1.464-3.535L7.05 6.939Zm8.486 1.413A4.985 4.985 0 0 1 17 11.89c0 1.626-.776 3.07-1.977 3.983l1.423 1.424A6.986 6.986 0 0 0 19 11.889a6.978 6.978 0 0 0-2.05-4.95l-1.414 1.414Z\"/><path d=\"M1 11.89a10.97 10.97 0 0 1 3.222-7.78l1.414 1.415A8.972 8.972 0 0 0 3 11.89a8.972 8.972 0 0 0 2.636 6.364l-1.414 1.414A10.966 10.966 0 0 1 1 11.89Zm18.778 7.777A10.965 10.965 0 0 0 23 11.89c0-3.038-1.231-5.788-3.222-7.778l-1.414 1.414A8.972 8.972 0 0 1 21 11.89a8.972 8.972 0 0 1-2.636 6.364l1.414 1.414Z\"/></g>"}, "database": {"body": "<g fill=\"currentColor\"><path d=\"M5 9V7h2v2H5Zm4 0h10V7H9v2Zm-4 6v2h2v-2H5Zm14 2H9v-2h10v2Z\"/><path fill-rule=\"evenodd\" d=\"M1 6a3 3 0 0 1 3-3h16a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V6Zm3-1h16a1 1 0 0 1 1 1v5H3V6a1 1 0 0 1 1-1Zm-1 8v5a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-5H3Z\" clip-rule=\"evenodd\"/></g>"}, "debug": {"body": "<g fill=\"currentColor\"><path d=\"M10 11a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1Zm1 3a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2h-2Z\"/><path fill-rule=\"evenodd\" d=\"M9.094 4.75A3.986 3.986 0 0 1 8 2h2a2 2 0 1 0 4 0h2a3.986 3.986 0 0 1-1.095 2.75A6.02 6.02 0 0 1 17.66 8H19a1 1 0 1 1 0 2h-1v2h1a1 1 0 1 1 0 2h-1v2h1a1 1 0 1 1 0 2h-1.341A6.003 6.003 0 0 1 6.34 18H5a1 1 0 1 1 0-2h1v-2H5a1 1 0 1 1 0-2h1v-2H5a1 1 0 1 1 0-2h1.341a6.02 6.02 0 0 1 2.753-3.25ZM8 16v-6a4 4 0 1 1 8 0v6a4 4 0 0 1-8 0Z\" clip-rule=\"evenodd\"/></g>"}, "designmodo": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M7 4.054a5 5 0 1 0 0 10a5 5 0 0 0 0-10Zm-2 5a2 2 0 1 0 4 0a2 2 0 0 0-4 0Z\" clip-rule=\"evenodd\"/><path d=\"M22 10.554h-5v-3h5v3Zm-10.45 6.392a3.988 3.988 0 0 0 2.829-1.172l2.121 2.121a6.978 6.978 0 0 1-4.95 2.05a6.978 6.978 0 0 1-4.95-2.05l2.122-2.12a3.987 3.987 0 0 0 2.828 1.17Z\"/></g>"}, "desktop": {"body": "<g fill=\"currentColor\"><path d=\"M8 15a1 1 0 1 0 0 2a1 1 0 0 0 0-2Zm3 1a1 1 0 1 1 2 0a1 1 0 0 1-2 0Zm5-1a1 1 0 1 0 0 2a1 1 0 0 0 0-2Z\"/><path fill-rule=\"evenodd\" d=\"M4 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v1h18V6a1 1 0 0 0-1-1ZM3 18V9h18v9a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "details-less": {"body": "<path fill=\"currentColor\" d=\"M3 9a1 1 0 0 0 0 2h18a1 1 0 1 0 0-2H3Zm0 4a1 1 0 1 0 0 2h12a1 1 0 1 0 0-2H3Z\"/>"}, "details-more": {"body": "<path fill=\"currentColor\" d=\"M2 8a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm0 4a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm1 3a1 1 0 1 0 0 2h12a1 1 0 1 0 0-2H3Z\"/>"}, "dialpad": {"body": "<path fill=\"currentColor\" d=\"M5.5 3h3v3h-3V3Zm0 5h3v3h-3V8Zm0 5v3h3v-3h-3Zm5-10h3v3h-3V3Zm0 5v3h3V8h-3Zm0 5h3v3h-3v-3Zm0 5v3h3v-3h-3Zm5-15h3v3h-3V3Zm0 5v3h3V8h-3Zm0 5h3v3h-3v-3Z\"/>"}, "dice-1": {"body": "<g fill=\"currentColor\"><path d=\"M10 12a2 2 0 1 1 4 0a2 2 0 0 1-4 0Z\"/><path fill-rule=\"evenodd\" d=\"M1 4a3 3 0 0 1 3-3h16a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V4Zm3-1h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "dice-2": {"body": "<g fill=\"currentColor\"><path d=\"M17.2 14.943a2 2 0 1 0 0 4a2 2 0 0 0 0-4ZM5.055 7.055a2 2 0 1 1 4 0a2 2 0 0 1-4 0Z\"/><path fill-rule=\"evenodd\" d=\"M4 1a3 3 0 0 0-3 3v16a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "dice-3": {"body": "<g fill=\"currentColor\"><path d=\"M10 12a2 2 0 1 1 4 0a2 2 0 0 1-4 0Zm6.945 2.892a2 2 0 1 0 0 4a2 2 0 0 0 0-4ZM5.055 7.055a2 2 0 1 1 4 0a2 2 0 0 1-4 0Z\"/><path fill-rule=\"evenodd\" d=\"M1 4a3 3 0 0 1 3-3h16a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V4Zm3-1h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "dice-4": {"body": "<g fill=\"currentColor\"><path d=\"M16.945 5.055a2 2 0 1 0 0 4a2 2 0 0 0 0-4Zm-2 11.837a2 2 0 1 1 4 0a2 2 0 0 1-4 0Zm-7.89-2a2 2 0 1 0 0 4a2 2 0 0 0 0-4Zm-2-7.837a2 2 0 1 1 4 0a2 2 0 0 1-4 0Z\"/><path fill-rule=\"evenodd\" d=\"M4 1a3 3 0 0 0-3 3v16a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "dice-5": {"body": "<g fill=\"currentColor\"><path d=\"M14.945 7.055a2 2 0 1 1 4 0a2 2 0 0 1-4 0Zm2 7.837a2 2 0 1 0 0 4a2 2 0 0 0 0-4Zm-11.89 2a2 2 0 1 1 4 0a2 2 0 0 1-4 0Zm2-11.837a2 2 0 1 0 0 4a2 2 0 0 0 0-4ZM10 12a2 2 0 1 1 4 0a2 2 0 0 1-4 0Z\"/><path fill-rule=\"evenodd\" d=\"M1 4a3 3 0 0 1 3-3h16a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V4Zm3-1h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "dice-6": {"body": "<g fill=\"currentColor\"><path d=\"M16.945 5.055a2 2 0 1 0 0 4a2 2 0 0 0 0-4Zm-2 11.837a2 2 0 1 1 4 0a2 2 0 0 1-4 0Zm-7.89-2a2 2 0 1 0 0 4a2 2 0 0 0 0-4Zm-2-7.837a2 2 0 1 1 4 0a2 2 0 0 1-4 0Zm11.89 2.919a2 2 0 1 0 0 4a2 2 0 0 0 0-4Zm-11.89 2a2 2 0 1 1 4 0a2 2 0 0 1-4 0Z\"/><path fill-rule=\"evenodd\" d=\"M4 1a3 3 0 0 0-3 3v16a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "digitalocean": {"body": "<g fill=\"currentColor\"><path d=\"M12 6a6 6 0 0 0-6 6H1C1 5.925 5.925 1 12 1s11 4.925 11 11s-4.925 11-11 11v-5a6 6 0 0 0 0-12Z\"/><path d=\"M7 18v-5h5v5H7Zm-4 0v4h4v-4H3Zm0 0H1v-2h2v2Z\"/></g>"}, "disc": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12 9a3 3 0 1 0 0 6a3 3 0 0 0 0-6Zm-1 3a1 1 0 1 0 2 0a1 1 0 0 0-2 0Z\" clip-rule=\"evenodd\"/><path d=\"M5 12a7 7 0 0 1 7-7v2a5 5 0 0 0-5 5H5Zm7 5a5 5 0 0 0 5-5h2a7 7 0 0 1-7 7v-2Z\"/><path fill-rule=\"evenodd\" d=\"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11s11-4.925 11-11S18.075 1 12 1ZM3 12a9 9 0 1 0 18 0a9 9 0 0 0-18 0Z\" clip-rule=\"evenodd\"/></g>"}, "display-flex": {"body": "<g fill=\"currentColor\"><path d=\"M6 17V7h2v10H6ZM16 7v10h2V7h-2Z\"/><path fill-rule=\"evenodd\" d=\"M2 3h20v18H2V3Zm2 2v14h16V5H4Z\" clip-rule=\"evenodd\"/></g>"}, "display-fullwidth": {"body": "<g fill=\"currentColor\"><path d=\"M2 5h20V3H2v2Zm0 16h20v-2H2v2Z\"/><path fill-rule=\"evenodd\" d=\"M2 7v10h20V7H2Zm2 2h16v6H4V9Z\" clip-rule=\"evenodd\"/></g>"}, "display-grid": {"body": "<g fill=\"currentColor\"><path d=\"M7 7v4h4V7H7Zm6 0h4v4h-4V7Zm0 6v4h4v-4h-4Zm-6 0h4v4H7v-4Z\"/><path fill-rule=\"evenodd\" d=\"M3 3h18v18H3V3Zm2 2v14h14V5H5Z\" clip-rule=\"evenodd\"/></g>"}, "display-spacing": {"body": "<g fill=\"currentColor\"><path d=\"M3 21V3h2v18H3Z\"/><path fill-rule=\"evenodd\" d=\"M7 3h10v18H7V3Zm2 2v14h6V5H9Z\" clip-rule=\"evenodd\"/><path d=\"M19 3v18h2V3h-2Z\"/></g>"}, "distribute-horizontal": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-opacity=\".5\" stroke-width=\"2\" d=\"M11 9h2v6h-2V9Z\"/><path fill=\"currentColor\" d=\"M5 5v14h2V5H5Zm12 0v14h2V5h-2Z\"/></g>"}, "distribute-vertical": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-opacity=\".5\" stroke-width=\"2\" d=\"M9 11h6v2H9v-2Z\"/><path fill=\"currentColor\" d=\"M19 7H5V5h14v2Zm0 12H5v-2h14v2Z\"/></g>"}, "dock-bottom": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 20V4h20v16H2ZM4 6h16v8H4V6Z\" clip-rule=\"evenodd\"/>"}, "dock-left": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 4h20v16H2V4Zm6 2h12v12H8V6Z\" clip-rule=\"evenodd\"/>"}, "dock-right": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 4h20v16H2V4Zm14 14V6H4v12h12Z\" clip-rule=\"evenodd\"/>"}, "dock-window": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 20V4h20v16H2ZM20 6H6v10h14V6Z\" clip-rule=\"evenodd\"/>"}, "dolby": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M0 4v16h24V4H0Zm10 8a4 4 0 0 0-4-4H4v8h2a4 4 0 0 0 4-4Zm8 4h2V8h-2a4 4 0 0 0 0 8Z\" clip-rule=\"evenodd\"/>"}, "dollar": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11 19v3h2v-3h1a4 4 0 0 0 0-8h-1V7h2v2h2V5h-4V2h-2v3h-1a4 4 0 1 0 0 8h1v4H9v-2H7v4h4Zm2-2h1a2 2 0 1 0 0-4h-1v4Zm-2-6V7h-1a2 2 0 1 0 0 4h1Z\" clip-rule=\"evenodd\"/>"}, "dribbble": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M1 11.955v.09c.01 2.685.984 5.144 2.592 7.048a11.066 11.066 0 0 0 3.378 2.692A10.922 10.922 0 0 0 12 23c3.69 0 6.955-1.816 8.95-4.604A10.96 10.96 0 0 0 23 12c0-3.26-1.418-6.19-3.672-8.203a10.949 10.949 0 0 0-7.663-2.792A10.944 10.944 0 0 0 4.43 4.019a11.05 11.05 0 0 0-2.76 4.188A10.976 10.976 0 0 0 1 11.955Zm19.481 3.064c.336-.944.519-1.96.519-3.019a8.971 8.971 0 0 0-2.581-6.309a10.93 10.93 0 0 1-3.152 3.356a11.04 11.04 0 0 1 .738 3.83a11.075 11.075 0 0 1 4.476 2.142Zm-4.64-.124a9.048 9.048 0 0 1 3.731 1.971a8.995 8.995 0 0 1-6.993 4.116a10.97 10.97 0 0 0 2.393-3.33c.419-.899.706-1.825.87-2.757Zm-1.845-2.273a9.025 9.025 0 0 0-.495-2.581A10.975 10.975 0 0 1 3.366 9.45A9.002 9.002 0 0 0 3 12a8.96 8.96 0 0 0 1.668 5.22a11.017 11.017 0 0 1 9.328-4.598ZM6.047 18.75a9.01 9.01 0 0 1 7.811-4.13a9.018 9.018 0 0 1-.699 2.186a8.958 8.958 0 0 1-3.485 3.89a8.98 8.98 0 0 1-3.627-1.946ZM12 3c1.785 0 3.448.52 4.847 1.415a8.944 8.944 0 0 1-2.479 2.816a10.941 10.941 0 0 0-4.341-4.014A9.031 9.031 0 0 1 12 3ZM8.806 4.846a8.958 8.958 0 0 1 3.832 3.39a8.979 8.979 0 0 1-7.439-.077a9.084 9.084 0 0 1-1.044-.573a9.044 9.044 0 0 1 3.172-3.28c.5.135.994.314 1.48.54Z\" clip-rule=\"evenodd\"/>"}, "drive": {"body": "<g fill=\"currentColor\"><path d=\"M19 11a1 1 0 1 0 0 2a1 1 0 0 0 0-2Zm-5 1a1 1 0 1 1 2 0a1 1 0 0 1-2 0Z\"/><path fill-rule=\"evenodd\" d=\"M2 8a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2H2Zm20 2H2v4h20v-4Z\" clip-rule=\"evenodd\"/></g>"}, "drop": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M6.343 19.52a8 8 0 0 1 0-11.313L12 2.55l5.657 5.657A8 8 0 0 1 6.343 19.521Z\"/>"}, "drop-invert": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 1.136L5.636 7.5a9 9 0 0 0 7.227 15.323A9 9 0 0 0 18.364 7.5L12 1.136ZM7.05 8.914L12 3.964v16.9a7 7 0 0 1-4.95-11.95Z\" clip-rule=\"evenodd\"/>"}, "drop-opacity": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15.945 21.956A9 9 0 0 1 5.635 7.5L12 1.136L18.364 7.5a8.97 8.97 0 0 1 1.991 3.012a9.002 9.002 0 0 1-1.991 9.716a8.987 8.987 0 0 1-2.419 1.728ZM7.05 8.914L12 3.964l4.95 4.95a6.977 6.977 0 0 1 2.048 4.783H5.002A6.976 6.976 0 0 1 7.05 8.914Z\" clip-rule=\"evenodd\"/>"}, "duplicate": {"body": "<g fill=\"currentColor\"><path d=\"M19 5H7V3h14v14h-2V5ZM9 13v-2h2v2h2v2h-2v2H9v-2H7v-2h2Z\"/><path fill-rule=\"evenodd\" d=\"M3 7h14v14H3V7Zm2 2h10v10H5V9Z\" clip-rule=\"evenodd\"/></g>"}, "edit-black-point": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M8 12a4 4 0 1 1 8 0a4 4 0 0 1-8 0Zm4 1a1 1 0 1 1 0-2a1 1 0 0 1 0 2Z\"/><path d=\"M3 12a9 9 0 1 1 18 0a9 9 0 0 1-18 0Zm9 7a7 7 0 1 1 0-14a7 7 0 0 1 0 14Z\"/></g>"}, "edit-contrast": {"body": "<g fill=\"currentColor\"><path d=\"M12 18a5.978 5.978 0 0 1-4-1.528A5.985 5.985 0 0 1 6 12c0-1.777.772-3.374 2-4.472A5.978 5.978 0 0 1 12 6v12Z\"/><path fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12Zm10 8a8 8 0 1 1 0-16a8 8 0 0 1 0 16Z\" clip-rule=\"evenodd\"/></g>"}, "edit-exposure": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 20a8 8 0 1 1 0-16a8 8 0 0 1 0 16ZM2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12Zm9-5v2H9v2h2v2h2v-2h2V9h-2V7h-2Zm-2 8v2h6v-2H9Z\" clip-rule=\"evenodd\"/>"}, "edit-fade": {"body": "<g fill=\"none\" stroke=\"currentColor\"><path stroke-opacity=\".3\" stroke-width=\"4\" d=\"M8 12c0-1.48.804-2.773 2-3.465v6.93A3.998 3.998 0 0 1 8 12Z\"/><path stroke-opacity=\".6\" stroke-width=\"4\" d=\"M14 15.465v-6.93c1.196.692 2 1.984 2 3.465c0 1.48-.804 2.773-2 3.465Z\"/><path stroke-width=\"2\" d=\"M3 12a9 9 0 1 1 18 0a9 9 0 0 1-18 0Z\"/></g>"}, "edit-flip-h": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M18 7a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1h-3v2h3a3 3 0 0 0 3-3V8a3 3 0 0 0-3-3h-3v2h3Z\"/><path d=\"M13 3h-2v18h2V3ZM5 8a1 1 0 0 1 1-1h3V5H6a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h3v-2H6a1 1 0 0 1-1-1V8Z\"/></g>"}, "edit-flip-v": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M17 18a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1v-3H5v3a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3v-3h-2v3Z\"/><path d=\"M16 5a1 1 0 0 1 1 1v3h2V6a3 3 0 0 0-3-3H8a3 3 0 0 0-3 3v3h2V6a1 1 0 0 1 1-1h8Zm5 8v-2H3v2h18Z\"/></g>"}, "edit-highlight": {"body": "<g fill=\"currentColor\"><path d=\"M12 6a5.972 5.972 0 0 0-3.306.992H12v1H7.535a5.996 5.996 0 0 0-1.203 2.034H12v1H6.079a6.042 6.042 0 0 0 .003 1.966H12v1H6.339c.263.748.67 1.429 1.189 2.008H12v1H8.682A6 6 0 1 0 12 6Z\"/><path fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12Zm10 8a8 8 0 1 1 0-16a8 8 0 0 1 0 16Z\" clip-rule=\"evenodd\"/></g>"}, "edit-markup": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 24c6.627 0 12-5.373 12-12S18.627 0 12 0S0 5.373 0 12s5.373 12 12 12Zm6.58-4.469A9.976 9.976 0 0 0 22 12c0-5.523-4.477-10-10-10S2 6.477 2 12a9.975 9.975 0 0 0 3.333 7.453L7 10.973h2.17l2.83-4.9l2.83 4.9H17l1.58 8.558Zm-2.488 1.596l-.886-8.153H8.794l-.886 8.153A9.964 9.964 0 0 0 12 22a9.965 9.965 0 0 0 4.092-.873Z\" clip-rule=\"evenodd\"/>"}, "edit-mask": {"body": "<g fill=\"currentColor\"><path d=\"M12 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10Zm-2.97-2.57a3 3 0 1 1 5.939 0a8.026 8.026 0 0 0 4.462-4.46a3 3 0 1 1 0-5.938a8.026 8.026 0 0 0-4.462-4.463a3 3 0 1 1-5.939 0a8.026 8.026 0 0 0-4.46 4.462A3.015 3.015 0 0 1 5 9a3 3 0 1 1-.43 5.97a8.026 8.026 0 0 0 4.46 4.46Z\" clip-rule=\"evenodd\"/></g>"}, "edit-noise": {"body": "<g fill=\"currentColor\"><path d=\"M10.404 17.766a.59.59 0 0 1 .042.045a5.946 5.946 0 0 1-.733-.248a.75.75 0 0 1 .691.203Zm-1.968-1.061a.758.758 0 0 1-.086.074a6.035 6.035 0 0 1-1.056-1.038a.75.75 0 1 1 1.142.964Zm-2.065-3.057a.75.75 0 0 1 .155.835a5.953 5.953 0 0 1-.342-.972a.75.75 0 0 1 .187.137Zm0-2.973a.748.748 0 0 1-.262.17a5.88 5.88 0 0 1 .344-1.134a.75.75 0 0 1-.082.964Zm1.004-1.968a.747.747 0 0 1-.181-.293c.265-.353.568-.676.903-.963a.75.75 0 1 1-.722 1.255Zm1.969-1.94a.754.754 0 0 1-.082-.097a5.958 5.958 0 0 1 1.36-.5a.749.749 0 0 1-1.278.597Zm3.943 11.106c.31-.068.61-.16.9-.273a.75.75 0 0 0-.9.273Zm2.244-1.013c.404-.294.77-.638 1.088-1.023a.75.75 0 1 0-1.088 1.024Zm1.834-2.168a5.93 5.93 0 0 0 .461-1.26a.75.75 0 0 0-.461 1.26Zm.526-3.799a5.95 5.95 0 0 0-.419-1.334a.75.75 0 0 0 .419 1.335ZM16.756 8.36a6.04 6.04 0 0 0-.875-.92a.75.75 0 1 0 .875.919Zm-2.319-1.593a.75.75 0 0 0 .131-.176a5.957 5.957 0 0 0-1.403-.464a.75.75 0 0 0 1.272.64Zm-1.911.879a.75.75 0 1 1-1.06 1.06a.75.75 0 0 1 1.06-1.06Zm-2.122 1.969a.75.75 0 1 1-1.06 1.06a.75.75 0 0 1 1.06-1.06Zm-1.968 3.182a.75.75 0 1 0-1.061-1.061a.75.75 0 0 0 1.06 1.06Zm1.968.851a.75.75 0 1 1-1.06 1.06a.75.75 0 0 1 1.06-1.06Zm2.122-.851a.75.75 0 1 0-1.061-1.061a.75.75 0 0 0 1.06 1.06Zm1.911-3.182a.75.75 0 1 1-1.06 1.06a.75.75 0 0 1 1.06-1.06Zm2.122 3.153a.75.75 0 1 0-1.061-1.06a.75.75 0 0 0 1.06 1.06Zm-2.136.922a.75.75 0 1 1-1.06 1.06a.75.75 0 0 1 1.06-1.06Zm-2.107 3.015a.75.75 0 1 0-1.06-1.06a.75.75 0 0 0 1.06 1.06Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 1-16 0a8 8 0 0 1 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "edit-shadows": {"body": "<g fill=\"currentColor\"><path d=\"M15.306 6.992A6 6 0 1 0 15.318 17H12v-1h4.472a6.01 6.01 0 0 0 1.19-2.008H12v-1h5.918a6.038 6.038 0 0 0 .003-1.966H12v-1h5.668a5.996 5.996 0 0 0-1.203-2.034H12v-1h3.306Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10Zm-10 8a8 8 0 1 0 0-16a8 8 0 0 0 0 16Z\" clip-rule=\"evenodd\"/></g>"}, "edit-straight": {"body": "<path fill=\"currentColor\" d=\"M12 4a7 7 0 0 1 7 7H5a7 7 0 0 1 7-7Zm-7 9H1v-2h4v2Zm14 0a7 7 0 1 1-14 0h14Zm0 0v-2h4v2h-4Z\"/>"}, "edit-unmask": {"body": "<path fill=\"currentColor\" d=\"M15 5a3 3 0 1 1-6 0a3 3 0 0 1 6 0Zm-1 7a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm-2 10a3 3 0 1 0 0-6a3 3 0 0 0 0 6Zm10-10a3 3 0 1 1-6 0a3 3 0 0 1 6 0ZM5 15a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\"/>"}, "eject": {"body": "<path fill=\"currentColor\" d=\"m16.95 14.395l1.414-1.415L12 6.617L5.636 12.98l1.414 1.415L12 9.445l4.95 4.95ZM6 17.384h12v-2H6v2Z\"/>"}, "enter": {"body": "<g fill=\"currentColor\"><path d=\"M20 5H8v4H6V3h16v18H6v-6h2v4h12V5Z\"/><path d=\"m13.074 16.95l-1.414-1.414L14.196 13H2v-2h12.196L11.66 8.465l1.414-1.415l4.95 4.95l-4.95 4.95Z\"/></g>"}, "erase": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3.5 12.9a2 2 0 0 0 0 2.828l3.858 3.858H4.086a1 1 0 1 0 0 2h16a1 1 0 0 0 0-2h-9.13l9.515-9.515a2 2 0 0 0 0-2.828L16.227 3a2 2 0 0 0-2.829 0L3.5 12.9Zm4.326-1.498l-2.912 2.912l4.243 4.242l2.911-2.911l-4.242-4.243ZM9.24 9.988l4.243 4.242l5.573-5.573l-4.242-4.243L9.24 9.988Z\" clip-rule=\"evenodd\"/>"}, "ereader": {"body": "<g fill=\"currentColor\"><path d=\"M16 7a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2h-3Zm-1 5a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1Zm1 3a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2h-3Z\"/><path fill-rule=\"evenodd\" d=\"M3 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h18a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H3Zm18 2h-8v14h8a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1ZM3 5h8v14H3a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "ericsson": {"body": "<path fill=\"currentColor\" d=\"M7.717 5.723a2 2 0 1 0 1.69 3.625l10.876-5.071a2 2 0 0 0-1.69-3.625L7.717 5.723ZM4.75 15.381a2 2 0 0 1 .967-2.658l10.876-5.071a2 2 0 1 1 1.69 3.625L7.407 16.348a2 2 0 0 1-2.657-.967Zm-2 7a2 2 0 0 1 .967-2.658l10.876-5.071a2 2 0 1 1 1.69 3.625L5.407 23.348a2 2 0 0 1-2.657-.967Z\"/>"}, "ethernet": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 .5v20h7v3h2v-3h7V.5H4Zm14 2H6v6h4v6h4v-6h4v-6Zm-12 16v-8h2v6h8v-6h2v8H6Z\" clip-rule=\"evenodd\"/>"}, "euro": {"body": "<path fill=\"currentColor\" d=\"M18.553 15.536a4.999 4.999 0 0 1-7.902-1.098h2.381l.696-1.876H10.05a5.047 5.047 0 0 1 0-1.125h4.287l.696-1.874h-4.38a4.998 4.998 0 0 1 7.902-1.099l1.414-1.414A7.003 7.003 0 0 0 8.454 9.562H6.032l-.696 1.875H8.04a7.095 7.095 0 0 0 0 1.126H4.728l-.696 1.874h4.422a7.003 7.003 0 0 0 11.514 2.513l-1.415-1.414Z\"/>"}, "eventbrite": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14.673 17.438a6.002 6.002 0 0 1-6.819-1.234l14.01-6.533a10.047 10.047 0 0 0-.663-1.897C18.867 2.768 12.917.603 7.91 2.937C2.907 5.27.742 11.22 3.076 16.227c2.334 5.005 8.284 7.17 13.289 4.836a9.974 9.974 0 0 0 5.317-6.077h-4.339a5.972 5.972 0 0 1-2.669 2.452ZM9.602 6.562a6.002 6.002 0 0 0-3.438 6.017l10.257-4.783a6.002 6.002 0 0 0-6.819-1.234Z\" clip-rule=\"evenodd\"/>"}, "expand": {"body": "<path fill=\"currentColor\" d=\"m12.306 16.593l-.035 2l-6.999-.122l.123-7l2 .036l-.063 3.585l7.894-7.624l-3.532-.061l.035-2l6.999.122l-.123 7l-2-.036l.064-3.638l-7.948 7.676l3.585.062Z\"/>"}, "export": {"body": "<g fill=\"currentColor\"><path d=\"m16.95 5.968l-1.414 1.414L13 4.846v12.196h-2V4.847L8.465 7.382L7.05 5.968L12 1.018l4.95 4.95Z\"/><path d=\"M5 20.982v-10h4v-2H3v14h18v-14h-6v2h4v10H5Z\"/></g>"}, "extension": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M13 3h8v8h-8V3Zm2 2h4v4h-4V5Z\"/><path d=\"M17 21v-8h-6V7H3v14h14ZM9 9H5v4h4V9ZM5 19v-4h4v4H5Zm6 0v-4h4v4h-4Z\"/></g>"}, "extension-add": {"body": "<g fill=\"currentColor\"><path d=\"M16 4h2v2h2v2h-2v2h-2V8h-2V6h2V4Z\"/><path fill-rule=\"evenodd\" d=\"M12 12V6H4v14h14v-8h-6ZM6 8h4v4H6V8Zm4 6v4H6v-4h4Zm6 0v4h-4v-4h4Z\" clip-rule=\"evenodd\"/></g>"}, "extension-alt": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5v14h8v-6h6V5H5Zm6 2H7v4h4V7Zm0 6H7v4h4v-4Zm2-2h4V7h-4v4Z\" clip-rule=\"evenodd\"/>"}, "extension-remove": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12 11V5H4v14h14v-8h-6ZM6 7h4v4H6V7Zm4 6v4H6v-4h4Zm6 0v4h-4v-4h4Z\" clip-rule=\"evenodd\"/><path d=\"M20 7h-6v2h6V7Z\"/></g>"}, "external": {"body": "<g fill=\"currentColor\"><path d=\"M15.64 7.025h-3.622v-2h7v7h-2v-3.55l-4.914 4.914l-1.414-1.414l4.95-4.95Z\"/><path d=\"M10.982 6.975h-6v12h12v-6h-2v4h-8v-8h4v-2Z\"/></g>"}, "eye": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M16 12a4 4 0 1 1-8 0a4 4 0 0 1 8 0Zm-2 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/><path d=\"M12 3c5.591 0 10.29 3.824 11.622 9c-1.332 5.176-6.03 9-11.622 9S1.71 17.176.378 12C1.71 6.824 6.408 3 12 3Zm0 16c-4.476 0-8.269-2.942-9.543-7C3.731 7.942 7.524 5 12 5c4.476 0 8.269 2.942 9.543 7c-1.274 4.058-5.067 7-9.543 7Z\"/></g>"}, "eye-alt": {"body": "<g fill=\"currentColor\"><path d=\"M14 12a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/><path fill-rule=\"evenodd\" d=\"M12 3C6.408 3 1.71 6.824.378 12C1.71 17.176 6.408 21 12 21c5.591 0 10.29-3.824 11.622-9C22.29 6.824 17.592 3 12 3Zm4 9a4 4 0 1 1-8 0a4 4 0 0 1 8 0Z\" clip-rule=\"evenodd\"/></g>"}, "facebook": {"body": "<path fill=\"currentColor\" d=\"M9.198 21.5h4v-8.01h3.604l.396-3.98h-4V7.5a1 1 0 0 1 1-1h3v-4h-3a5 5 0 0 0-5 5v2.01h-2l-.396 3.98h2.396v8.01Z\"/>"}, "feed": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M12.552 8a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2h-4Zm0 9a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2h-4Z\"/><path fill-opacity=\".8\" d=\"M12.552 5a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2h-8Zm0 9a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2h-8Z\"/><path d=\"M3.448 4.002a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1h-5Zm0 8.996a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1h-5Z\"/></g>"}, "figma": {"body": "<path fill=\"currentColor\" d=\"M8.5 2a3 3 0 0 0 0 6h7a3 3 0 1 0 0-6h-7Zm7 7a3 3 0 1 0 0 6a3 3 0 0 0 0-6Zm-10 3a3 3 0 0 1 3-3h3v6h-3a3 3 0 0 1-3-3Zm3 4a3 3 0 1 0 3 3v-3h-3Z\"/>"}, "file": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 5a3 3 0 0 1 3-3h8a7 7 0 0 1 7 7v10a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V5Zm10-1H6a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V9h-6V4Zm5.584 3A5.009 5.009 0 0 0 15 4.1V7h3.584Z\" clip-rule=\"evenodd\"/>"}, "file-add": {"body": "<g fill=\"currentColor\"><path d=\"M10 18v-2H8v-2h2v-2h2v2h2v2h-2v2h-2Z\"/><path fill-rule=\"evenodd\" d=\"M6 2a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V9a7 7 0 0 0-7-7H6Zm0 2h7v5h6v10a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1Zm9 .1A5.009 5.009 0 0 1 18.584 7H15V4.1Z\" clip-rule=\"evenodd\"/></g>"}, "file-document": {"body": "<g fill=\"currentColor\"><path d=\"M7 18h10v-2H7v2Zm10-4H7v-2h10v2ZM7 10h4V8H7v2Z\"/><path fill-rule=\"evenodd\" d=\"M6 2a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V9a7 7 0 0 0-7-7H6Zm0 2h7v5h6v10a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1Zm9 .1A5.009 5.009 0 0 1 18.584 7H15V4.1Z\" clip-rule=\"evenodd\"/></g>"}, "file-remove": {"body": "<g fill=\"currentColor\"><path d=\"M9 15h6v-2H9v2Z\"/><path fill-rule=\"evenodd\" d=\"M6 2a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V9a7 7 0 0 0-7-7H6Zm0 2h7v5h6v10a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1Zm9 .1A5.009 5.009 0 0 1 18.584 7H15V4.1Z\" clip-rule=\"evenodd\"/></g>"}, "film": {"body": "<g fill=\"currentColor\"><path d=\"M6 7a1 1 0 1 0 0 2a1 1 0 0 0 0-2Zm11 1a1 1 0 1 1 2 0a1 1 0 0 1-2 0ZM6 11a1 1 0 1 0 0 2a1 1 0 0 0 0-2Zm11 1a1 1 0 1 1 2 0a1 1 0 0 1-2 0ZM6 15a1 1 0 1 0 0 2a1 1 0 0 0 0-2Zm11 1a1 1 0 1 1 2 0a1 1 0 0 1-2 0Z\"/><path fill-rule=\"evenodd\" d=\"M4 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "filters": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4.708 15.44a6.968 6.968 0 0 0 3.997 1.266a7 7 0 1 0 6.59-9.413A7 7 0 1 0 4.708 15.44Zm1.147-1.64a4.976 4.976 0 0 0 2.432.886a6.97 6.97 0 0 1 1.256-4.408a6.97 6.97 0 0 1 3.713-2.687a5 5 0 1 0-7.4 6.21Zm12.29-3.603a4.977 4.977 0 0 0-2.432-.885a6.97 6.97 0 0 1-1.256 4.408a6.97 6.97 0 0 1-3.713 2.687a5 5 0 1 0 7.4-6.21Z\" clip-rule=\"evenodd\"/>"}, "flag": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 21h2V11h6v2h8V5h-7V3H4v18Zm8-16H6v4h7v2h5V7h-6V5Z\" clip-rule=\"evenodd\"/>"}, "flag-alt": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m12.439 7l2.4-3H7v6h7.839l-2.4-3ZM19 12H7v10H5V2h14l-4 5l4 5Z\" clip-rule=\"evenodd\"/>"}, "folder": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 1.5a2 2 0 0 0-2 2v1c0 .***************.168A3.001 3.001 0 0 0 0 7.5v12a3 3 0 0 0 3 3h18a3 3 0 0 0 3-3v-12a3 3 0 0 0-3-3h-9.126A4.002 4.002 0 0 0 8 1.5H4Zm5.732 3A2 2 0 0 0 8 3.5H4v1h5.732ZM3 6.5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1v-12a1 1 0 0 0-1-1H3Z\" clip-rule=\"evenodd\"/>"}, "folder-add": {"body": "<g fill=\"currentColor\"><path d=\"M11 14.5v2h2v-2h2v-2h-2v-2h-2v2H9v2h2Z\"/><path fill-rule=\"evenodd\" d=\"M4 1.5a2 2 0 0 0-2 2v1c0 .***************.168A3.001 3.001 0 0 0 0 7.5v12a3 3 0 0 0 3 3h18a3 3 0 0 0 3-3v-12a3 3 0 0 0-3-3h-9.126A4.002 4.002 0 0 0 8 1.5H4Zm5.732 3A2 2 0 0 0 8 3.5H4v1h5.732ZM3 6.5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1v-12a1 1 0 0 0-1-1H3Z\" clip-rule=\"evenodd\"/></g>"}, "folder-remove": {"body": "<g fill=\"currentColor\"><path d=\"M9 14.5v-2h6v2H9Z\"/><path fill-rule=\"evenodd\" d=\"M4 1.5a2 2 0 0 0-2 2v1c0 .***************.168A3.001 3.001 0 0 0 0 7.5v12a3 3 0 0 0 3 3h18a3 3 0 0 0 3-3v-12a3 3 0 0 0-3-3h-9.126A4.002 4.002 0 0 0 8 1.5H4Zm5.732 3A2 2 0 0 0 8 3.5H4v1h5.732ZM3 6.5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1v-12a1 1 0 0 0-1-1H3Z\" clip-rule=\"evenodd\"/></g>"}, "font-height": {"body": "<g fill=\"currentColor\"><path d=\"M21 5V3H3v2h18Zm0 14v2H3v-2h18Z\"/><path fill-rule=\"evenodd\" d=\"M12 7.376a1 1 0 0 0-.967.576l-3.381 7.25a1 1 0 1 0 1.812.846L9.953 15h4.094l.489 1.048a1 1 0 1 0 1.813-.845l-3.381-7.251A1 1 0 0 0 12 7.376ZM13.115 13h-2.23L12 10.61L13.115 13Z\" clip-rule=\"evenodd\"/></g>"}, "font-spacing": {"body": "<g fill=\"currentColor\"><path d=\"M19 21h2V3h-2v18ZM5 21H3V3h2v18Z\"/><path fill-rule=\"evenodd\" d=\"M9.464 16.048L9.953 15h4.094l.489 1.048a1 1 0 1 0 1.813-.845l-3.381-7.25A1 1 0 0 0 12 7.375a1 1 0 0 0-.967.576l-3.381 7.25a1 1 0 0 0 1.812.846ZM12 10.61L10.885 13h2.23L12 10.61Z\" clip-rule=\"evenodd\"/></g>"}, "format-bold": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"m12.946 10.783l-1.67 1.426l2.172.324A3.001 3.001 0 0 1 13 12.5H8v-1h3a3 3 0 0 0 1.946-.717Z\"/>"}, "format-center": {"body": "<path fill=\"currentColor\" d=\"M4 5a1 1 0 0 0 0 2h16a1 1 0 1 0 0-2H4Zm0 8a1 1 0 1 0 0 2h16a1 1 0 1 0 0-2H4Zm2-3a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1Zm1 7a1 1 0 1 0 0 2h10a1 1 0 1 0 0-2H7Z\"/>"}, "format-color": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12.948 3.21A1 1 0 0 0 12 2.632a1 1 0 0 0-.948.576l-5.917 12.69a1 1 0 1 0 1.813.845l1.45-3.111h7.203l1.451 3.111a1 1 0 0 0 1.813-.845L12.948 3.209Zm1.72 8.422L12 5.909l-2.669 5.723h5.338Z\" clip-rule=\"evenodd\"/><path d=\"M6 19.368a1 1 0 0 0 0 2h12a1 1 0 1 0 0-2H6Z\"/></g>"}, "format-heading": {"body": "<path fill=\"currentColor\" d=\"M6 19V5h2v6h8V5h2v14h-2v-6H8v6H6Z\"/>"}, "format-indent-decrease": {"body": "<path fill=\"currentColor\" d=\"M20 7H4V5h16v2Zm0 4h-8V9h8v2Zm-8 4h8v-2h-8v2ZM9 9l-5 3l5 3V9Zm-5 8v2h16v-2H4Z\"/>"}, "format-indent-increase": {"body": "<path fill=\"currentColor\" d=\"M20 7H4V5h16v2Zm0 4h-8V9h8v2Zm-8 4h8v-2h-8v2Zm-8 0l5-3l-5-3v6Zm0 2v2h16v-2H4Z\"/>"}, "format-italic": {"body": "<path fill=\"currentColor\" d=\"M11.49 5.458h6l-.711 1.87h-2l-3.558 9.345h2l-.711 1.87h-6l.711-1.87h2l3.558-9.346h-2l.711-1.869Z\"/>"}, "format-justify": {"body": "<path fill=\"currentColor\" d=\"M4 5a1 1 0 0 0 0 2h16a1 1 0 1 0 0-2H4Zm0 4a1 1 0 0 0 0 2h16a1 1 0 1 0 0-2H4Zm-1 5a1 1 0 0 1 1-1h16a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Zm1 3a1 1 0 1 0 0 2h16a1 1 0 1 0 0-2H4Z\"/>"}, "format-left": {"body": "<path fill=\"currentColor\" d=\"M4 5a1 1 0 0 0 0 2h16a1 1 0 1 0 0-2H4Zm0 4a1 1 0 0 0 0 2h8a1 1 0 1 0 0-2H4Zm-1 5a1 1 0 0 1 1-1h16a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Zm1 3a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H4Z\"/>"}, "format-line-height": {"body": "<path fill=\"currentColor\" d=\"M5.097 6.997h2.077l-3-3l-3 3h1.923v10.006H1.159l3 3l3-3H5.097V6.997ZM22.841 7h-14V5h14v2Zm0 4h-14V9h14v2Zm-14 4h14v-2h-14v2Zm14 4h-14v-2h14v2Z\"/>"}, "format-right": {"body": "<path fill=\"currentColor\" d=\"M20 5a1 1 0 1 1 0 2H4a1 1 0 0 1 0-2h16Zm0 4a1 1 0 1 1 0 2h-8a1 1 0 1 1 0-2h8Zm1 5a1 1 0 0 0-1-1H4a1 1 0 1 0 0 2h16a1 1 0 0 0 1-1Zm-1 3a1 1 0 1 1 0 2h-8a1 1 0 1 1 0-2h8Z\"/>"}, "format-separator": {"body": "<g fill=\"currentColor\"><path d=\"M16 5a1 1 0 1 0 0-2H8a1 1 0 1 0 0 2h8Zm0 2a1 1 0 1 1 0 2H8a1 1 0 1 1 0-2h8Zm1 5a1 1 0 0 1-1 1H8a1 1 0 1 1 0-2h8a1 1 0 0 1 1 1Zm-1 9a1 1 0 1 0 0-2H8a1 1 0 1 0 0 2h8Z\" opacity=\".5\"/><path fill-rule=\"evenodd\" d=\"M21 16a1 1 0 0 1-1 1H4a1 1 0 1 1 0-2h16a1 1 0 0 1 1 1Z\" clip-rule=\"evenodd\"/></g>"}, "format-slash": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14.526 6.106c.5.233.74.777.537 1.215L9.884 18.424c-.204.438-.775.604-1.276.37c-.5-.233-.74-.778-.536-1.216L13.25 6.476c.204-.438.775-.604 1.276-.37Z\" clip-rule=\"evenodd\"/>"}, "format-strike": {"body": "<path fill=\"currentColor\" d=\"M13 7h4V5H7v2h4v3h2V7Zm-2 12v-5h2v5h-2Zm-6-6h14v-2H5v2Z\"/>"}, "format-text": {"body": "<path fill=\"currentColor\" d=\"M6.5 3H3v3.5h1V4h2.5V3Zm2 1V3H11v1H8.5ZM13 4h2.5V3H13v1Zm4.5-1v1H20v2.5h1V3h-3.5ZM21 8.5h-1V11h1V8.5Zm0 4.5h-1v2.5h1V13Zm0 4.5h-1V20h-2.5v1H21v-3.5ZM15.5 21v-1H13v1h2.5ZM11 21v-1H8.5v1H11Zm-4.5 0v-1H4v-2.5H3V21h3.5ZM3 15.5h1V13H3v2.5ZM3 11h1V8.5H3V11Zm8-1.5H7v-2h10v2h-4v7h-2v-7Z\"/>"}, "format-underline": {"body": "<path fill=\"currentColor\" d=\"M6 10V4h2v6a4 4 0 0 0 8 0V4h2v6a6 6 0 0 1-12 0Zm1 8a1 1 0 1 0 0 2h10a1 1 0 1 0 0-2H7Z\"/>"}, "format-uppercase": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M13 9h-3v8H8V9H5V7h8v2Zm5 4h-2v4h-2v-4h-2v-2h6v2Z\" clip-rule=\"evenodd\"/>"}, "framer": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M12 21V9H6v6l6 6Z\"/><path d=\"M18 9V3H6l6 6H6v6h12l-6-6h6Z\"/></g>"}, "games": {"body": "<g fill=\"currentColor\"><path d=\"M15.47 11.293a1 1 0 1 0-1.415 1.414a1 1 0 0 0 1.415-1.414Zm.707-2.121a1 1 0 1 1 1.414 1.414a1 1 0 0 1-1.414-1.414Zm3.535 2.121a1 1 0 1 0-1.414 1.414a1 1 0 0 0 1.414-1.414Zm-3.535 2.121a1 1 0 1 1 1.414 1.415a1 1 0 0 1-1.414-1.415ZM6 13H4v-2h2V9h2v2h2v2H8v2H6v-2Z\"/><path fill-rule=\"evenodd\" d=\"M7 5a7 7 0 0 0 0 14h10a7 7 0 1 0 0-14H7Zm10 2H7a5 5 0 0 0 0 10h10a5 5 0 0 0 0-10Z\" clip-rule=\"evenodd\"/></g>"}, "gender-female": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 3a5 5 0 0 0-1 9.9V15H8v2h3v4h2v-4h3v-2h-3v-2.1A5.002 5.002 0 0 0 12 3ZM9 8a3 3 0 1 0 6 0a3 3 0 0 0-6 0Z\" clip-rule=\"evenodd\"/>"}, "gender-male": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m12.189 7l.002-2l7 .007l-.008 7l-2-.002l.004-3.588l-3.044 3.044a5.002 5.002 0 0 1-7.679 6.336a5 5 0 0 1 6.25-7.736l3.058-3.057L12.189 7Zm-4.31 5.14a3 3 0 1 1 4.242 4.244A3 3 0 0 1 7.88 12.14Z\" clip-rule=\"evenodd\"/>"}, "ghost": {"body": "<path fill=\"currentColor\" d=\"M3 4h12v4H3V4Zm18 4h-4V4h4v4ZM3 10h18v4H3v-4Zm8 6H3v4h8v-4Zm2 0v4h8v-4h-8Z\"/>"}, "ghost-character": {"body": "<g fill=\"currentColor\"><path d=\"M10.976 9a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm2.995 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M19 21V10a7 7 0 1 0-14 0v11h2.828l1.415-1.414L10.657 21h2.686l1.414-1.414L16.172 21H19Zm-2-11a5 5 0 0 0-10 0v9l2.243-2.243L12 19.515l2.757-2.758L17 19v-9Z\" clip-rule=\"evenodd\"/></g>"}, "gift": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.535 2.879a3 3 0 0 0-4.242 0l-1.414 1.414c-.06.06-.118.122-.172.186a3.01 3.01 0 0 0-.171-.186L10.12 2.879A3 3 0 1 0 5.88 7.12l.877.88H1v6h2v8h18v-8h2V8h-6.343l.878-.879a3 3 0 0 0 0-4.242ZM14.707 7.12l1.414-1.414a1 1 0 0 0-1.414-1.414l-1.414 1.414a1 1 0 0 0 1.414 1.414Zm-4.586-1.414L8.707 4.293a1 1 0 1 0-1.414 1.414l1.414 1.414a1 1 0 1 0 1.414-1.414ZM21 10v2H3v-2h18Zm-8.083 4H19v6h-6.083v-6Zm-1.834 0v6H5v-6h6.083Z\" clip-rule=\"evenodd\"/>"}, "girl": {"body": "<g fill=\"currentColor\"><path d=\"M10 12a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm5 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M12.024 2H12C6.477 2 2 6.477 2 12s4.477 10 10 10s10-4.477 10-10c0-5.258-4.058-9.568-9.212-9.97v-.002A9.94 9.94 0 0 0 12.025 2ZM12 20a8 8 0 0 0 7.742-10.022a10.016 10.016 0 0 1-8.982-4.376a7.976 7.976 0 0 1-5.691 2.4A8 8 0 0 0 12 20Zm-.021-16h.045h-.045Z\" clip-rule=\"evenodd\"/></g>"}, "git-branch": {"body": "<path fill=\"currentColor\" d=\"M9 3a2 2 0 0 0-1 3.732v10.536A2 2 0 1 0 10.732 20h1.227a4 4 0 0 0 4-4v-1.268a2 2 0 0 0-1-3.732a2 2 0 0 0-1 3.732V16a2 2 0 0 1-2 2h-1.227a2.01 2.01 0 0 0-.732-.732V6.732A2 2 0 0 0 9 3Z\"/>"}, "git-commit": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11 4a1 1 0 1 1 2 0v5.17a3.001 3.001 0 0 1 0 5.66V20a1 1 0 1 1-2 0v-5.17a3.001 3.001 0 0 1 0-5.66V4Zm1 9a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\" clip-rule=\"evenodd\"/>"}, "git-fork": {"body": "<path fill=\"currentColor\" d=\"M7 5a2 2 0 1 1 3 1.732v7.308h1.791a2 2 0 0 0 2-2v-1.256a2 2 0 1 1 2-.024v1.28a4 4 0 0 1-4 4H10v1.228A2 2 0 0 1 9 21a2 2 0 0 1-1-3.732V6.732A2 2 0 0 1 7 5Z\"/>"}, "git-pull": {"body": "<path fill=\"currentColor\" d=\"M7 5a2 2 0 1 1 3.763.945h.58a4 4 0 0 1 4 4v1.28a2 2 0 0 1-1.02 3.72a2 2 0 0 1-.98-3.745V9.945a2 2 0 0 0-2-2H10v9.323A2 2 0 0 1 9 21a2 2 0 0 1-1-3.732V6.732A2 2 0 0 1 7 5Z\"/>"}, "gitter": {"body": "<path fill=\"currentColor\" d=\"M5 1.5h2v13H5v-13Zm4 3h2v18H9v-18Zm6 0h-2v18h2v-18Zm2 0h2v10h-2v-10Z\"/>"}, "glass": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17 10a5.002 5.002 0 0 1-4 4.9V17h2v2H9v-2h2v-2.1A5.002 5.002 0 0 1 7 10V5h10v5Zm-2-3H9v3a3 3 0 1 0 6 0V7Z\" clip-rule=\"evenodd\"/>"}, "glass-alt": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 2h14l-1.64 16.398A4 4 0 0 1 13.38 22h-2.76a4 4 0 0 1-3.98-3.602L5 2Zm2.51 5l-.3-3h9.58l-.3 3H7.51Zm.2 2l.92 9.199A2 2 0 0 0 10.62 20h2.76a2 2 0 0 0 1.99-1.801L16.29 9H7.71Z\" clip-rule=\"evenodd\"/>"}, "globe": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M6.853 8a5 5 0 1 1 10 0a5 5 0 0 1-10 0Zm5 3a3 3 0 1 1 0-6a3 3 0 0 1 0 6Z\" clip-rule=\"evenodd\"/><path d=\"M5 12.13a8.001 8.001 0 0 0 5.941 3.819V18H8.853v2h6v-2h-1.912v-2.073A8.002 8.002 0 0 0 18.63 3.745l-1.704 1.048a6 6 0 1 1-10.221 6.288L5 12.13Z\"/></g>"}, "globe-alt": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 21a9 9 0 1 0 0-18a9 9 0 0 0 0 18Zm2.806-2.585a7.004 7.004 0 0 0 4.175-5.89c-.823.449-1.861.817-3.044 1.073c-.149 1.87-.554 3.54-1.131 4.817ZM9.195 5.585a7.016 7.016 0 0 0-3.973 4.659c.232.22.626.49 1.226.757c.45.2.973.379 1.557.529c.054-2.324.498-4.415 1.19-5.945Zm.906 8.326c.156 1.457.484 2.71.898 3.64c.294.662.593 1.074.823 1.293c.*************.178.14a.983.983 0 0 0 .178-.14c.23-.22.529-.63.823-1.292c.414-.932.742-2.184.898-3.641a20.034 20.034 0 0 1-3.798 0Zm-2.038-.313c.149 1.87.554 3.54 1.131 4.817a7.004 7.004 0 0 1-4.175-5.89c.823.449 1.861.817 3.044 1.073ZM14 11.89a18.153 18.153 0 0 1-4 0c.014-2.226.423-4.145 1-5.442c.293-.661.592-1.073.822-1.292a.988.988 0 0 1 .178-.14a.988.988 0 0 1 .178.14c.23.22.529.63.823 1.292c.576 1.297.986 3.216.999 5.442Zm1.995-.36c-.053-2.324-.498-4.415-1.19-5.945a7.016 7.016 0 0 1 3.973 4.659c-.232.22-.626.49-1.226.757c-.45.2-.973.379-1.557.529Z\" clip-rule=\"evenodd\"/>"}, "google": {"body": "<path fill=\"currentColor\" d=\"M6 12a6 6 0 0 0 11.659 2H12v-4h9.805v4H21.8c-.927 4.564-4.962 8-9.8 8c-5.523 0-10-4.477-10-10S6.477 2 12 2a9.99 9.99 0 0 1 8.282 4.393l-3.278 2.295A6 6 0 0 0 6 12Z\"/>"}, "google-tasks": {"body": "<path fill=\"currentColor\" d=\"M16.768 5.714a2 2 0 0 1 3.064 2.572L10.833 19.01a2 2 0 1 1-3.064-2.57l8.999-10.726ZM3 12.74a2 2 0 1 1 4 0a2 2 0 0 1-4 0Z\"/>"}, "gym": {"body": "<path fill=\"currentColor\" d=\"m20.274 9.869l-3.442-4.915l1.639-1.147l3.441 4.915l-1.638 1.147Zm-1.884 2.54L16.67 9.95l-8.192 5.736l1.72 2.457l-1.638 1.148l-4.588-6.554L5.61 11.59l1.72 2.458l8.192-5.736l-1.72-2.458l1.638-1.147l4.588 6.553l-1.638 1.148Zm2.375-5.326l1.638-1.147l-1.147-1.638l-1.638 1.147l1.147 1.638ZM7.168 19.046l-3.442-4.915l-1.638 1.147l3.441 4.915l1.639-1.147Zm-2.786-.491l-1.638 1.147l-1.147-1.638l1.638-1.147l1.147 1.638Z\"/>"}, "hashtag": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 4v4H4v2h4v4H4v2h4v4h2v-4h4v4h2v-4h4v-2h-4v-4h4V8h-4V4h-2v4h-4V4H8Zm6 10v-4h-4v4h4Z\" clip-rule=\"evenodd\"/>"}, "headset": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17 21a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h2v-1a7 7 0 1 0-14 0v1h2a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H3v-9a9 9 0 0 1 18 0v9h-4Zm2-6h-2v4h2v-4ZM7 15H5v4h2v-4Z\" clip-rule=\"evenodd\"/>"}, "heart": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m12.012 5.572l-1.087-1.087a5.5 5.5 0 1 0-7.778 7.778l8.839 8.839l.002-.002l.026.026l8.839-8.839a5.5 5.5 0 1 0-7.778-7.778l-1.063 1.063Zm-.024 12.7l4.936-4.937l1.45-1.4h.002l1.063-1.062a3.5 3.5 0 1 0-4.95-4.95L12.013 8.4l-.007-.007h-.001L9.511 5.9a3.5 3.5 0 1 0-4.95 4.95l2.54 2.54l.001-.003l4.886 4.886Z\" clip-rule=\"evenodd\"/>"}, "hello": {"body": "<g fill=\"currentColor\"><path d=\"M17.5 12a5.485 5.485 0 0 1-1.725 4A5.481 5.481 0 0 1 12 17.5c-1.461 0-2.79-.57-3.775-1.5A5.485 5.485 0 0 1 6.5 12h11Z\"/><path fill-rule=\"evenodd\" d=\"M1 7a6 6 0 0 1 6-6h10a6 6 0 0 1 6 6v10a6 6 0 0 1-6 6H1V7Zm2.75 5a8.25 8.25 0 1 1 16.5 0a8.25 8.25 0 0 1-16.5 0Z\" clip-rule=\"evenodd\"/></g>"}, "home": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m21 8.772l-6.98-6.979a3 3 0 0 0-4.242 0L3 8.571v14.515h7v-6a2 2 0 1 1 4 0v6h7V8.772Zm-9.808-5.565L5 9.4v11.686h3v-4a4 4 0 0 1 8 0v4h3V9.6l-6.393-6.394a1 1 0 0 0-1.415 0Z\" clip-rule=\"evenodd\"/>"}, "home-alt": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6 22.879a3 3 0 0 1-3-3v-10c0-.034.002-.068.005-.1H3c0-.577.229-1.13.636-1.536L9.88 2a3 3 0 0 1 4.242 0l6.243 6.243c.407.407.636.96.636 1.535h-.005c.***************.005.1v10a3 3 0 0 1-3 3H6Zm6.707-19.465L19 9.707V19.88a1 1 0 0 1-1 1h-3v-5a3 3 0 1 0-6 0v5H6a1 1 0 0 1-1-1V9.707l6.293-6.293a1 1 0 0 1 1.414 0Z\" clip-rule=\"evenodd\"/>"}, "home-screen": {"body": "<g fill=\"currentColor\"><path d=\"M10 5a1 1 0 1 1-2 0a1 1 0 0 1 2 0ZM9 9a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm1 10a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm-1-7a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm4-7a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm-1 4a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm1 10a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm2-13a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm1 2a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm-1 12a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M8 1a3 3 0 0 0-3 3v16a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H8Zm8 2H8a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "icecream": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15 17h4V8A7 7 0 1 0 5 8v9h4v3a3 3 0 1 0 6 0v-3Zm2-2V8A5 5 0 0 0 7 8v7h4v5a1 1 0 1 0 2 0v-5h4Z\" clip-rule=\"evenodd\"/>"}, "if-design": {"body": "<path fill=\"currentColor\" d=\"M10 5h4v14h-4V5ZM5 19v-9h4v9H5ZM7 5a2 2 0 1 0 0 4a2 2 0 0 0 0-4Zm8 0h4v4h-4V5Zm4 5h-4v4h4v-4Z\"/>"}, "image": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M7 7a3 3 0 1 0 0 6a3 3 0 0 0 0-6Zm-1 3a1 1 0 1 1 2 0a1 1 0 0 1-2 0Z\"/><path d=\"M3 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h18a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H3Zm18 2H3a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h4.314l6.878-6.879a3 3 0 0 1 4.243 0L22 15.686V6a1 1 0 0 0-1-1Zm0 14H10.142l5.465-5.464a1 1 0 0 1 1.414 0l4.886 4.886A1 1 0 0 1 21 19Z\"/></g>"}, "import": {"body": "<g fill=\"currentColor\"><path d=\"M5 9.982v10h14v-10h-4v-2h6v14H3v-14h6v2H5Z\"/><path d=\"M13 2h-2v12.053l-2.535-2.536l-1.415 1.415l4.95 4.95l4.95-4.95l-1.414-1.415L13 14.053V2Z\"/></g>"}, "inbox": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 5a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v15a2 2 0 0 1-2 2H8.148a2.006 2.006 0 0 1-.148.005H4a2 2 0 0 1-2-2V5Zm3-1h14a1 1 0 0 1 1 1v9h-4a2 2 0 0 0-2 2v1h-4v-.995a2 2 0 0 0-2-2H4V5a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/>"}, "indie-hackers": {"body": "<path fill=\"currentColor\" d=\"M4 6h3v12H4V6Zm6 0h3v4.5h4V6h3v12h-3v-4.5h-4V18h-3V6Z\"/>"}, "infinity": {"body": "<path fill=\"currentColor\" d=\"m8.121 9.879l2.083 2.083l.007-.006l1.452 1.452l.006.006l2.122 2.122a5 5 0 1 0 0-7.072l-.714.714l1.415 1.414l.713-.713a3 3 0 1 1 0 4.242l-2.072-2.072l-.007.006l-3.59-3.59a5 5 0 1 0 0 7.07l.713-.713l-1.414-1.414l-.714.713a3 3 0 1 1 0-4.242Z\"/>"}, "info": {"body": "<g fill=\"currentColor\"><path d=\"M11 10.98a1 1 0 1 1 2 0v6a1 1 0 1 1-2 0v-6Zm1-4.929a1 1 0 1 0 0 2a1 1 0 0 0 0-2Z\"/><path fill-rule=\"evenodd\" d=\"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10s10-4.477 10-10S17.523 2 12 2ZM4 12a8 8 0 1 0 16 0a8 8 0 0 0-16 0Z\" clip-rule=\"evenodd\"/></g>"}, "inpicture": {"body": "<g fill=\"currentColor\"><path d=\"M20 12h-6v5h6v-5Z\"/><path fill-rule=\"evenodd\" d=\"M1 6a2 2 0 0 1 2-2h18a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V6Zm2 0h18v12H3V6Z\" clip-rule=\"evenodd\"/></g>"}, "insert-after": {"body": "<path fill=\"currentColor\" d=\"M12 4a1 1 0 0 1 1 1v3h3a1 1 0 1 1 0 2h-3v3a1 1 0 1 1-2 0v-3H8a1 1 0 0 1 0-2h3V5a1 1 0 0 1 1-1ZM3 19a1 1 0 0 1 1-1h16a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Z\"/>"}, "insert-after-o": {"body": "<g fill=\"currentColor\"><path d=\"M9 8a1 1 0 0 0 0 2h2v2a1 1 0 1 0 2 0v-2h2a1 1 0 1 0 0-2h-2V6a1 1 0 1 0-2 0v2H9Z\"/><path fill-rule=\"evenodd\" d=\"M4 9a8 8 0 1 1 16 0A8 8 0 0 1 4 9Zm8 6a6 6 0 1 1 0-12a6 6 0 0 1 0 12Z\" clip-rule=\"evenodd\"/><path d=\"M5 20a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2H5Z\"/></g>"}, "insert-after-r": {"body": "<g fill=\"currentColor\"><path d=\"M9 8a1 1 0 0 0 0 2h2v2a1 1 0 1 0 2 0v-2h2a1 1 0 1 0 0-2h-2V6a1 1 0 1 0-2 0v2H9Z\"/><path fill-rule=\"evenodd\" d=\"M4 4a3 3 0 0 1 3-3h10a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3H7a3 3 0 0 1-3-3V4Zm3-1h10a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/><path d=\"M5 20a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2H5Z\"/></g>"}, "insert-before": {"body": "<path fill=\"currentColor\" d=\"M3 5a1 1 0 0 0 1 1h16a1 1 0 1 0 0-2H4a1 1 0 0 0-1 1Zm9 15a1 1 0 0 0 1-1v-3h3a1 1 0 1 0 0-2h-3v-3a1 1 0 1 0-2 0v3H8a1 1 0 1 0 0 2h3v3a1 1 0 0 0 1 1Z\"/>"}, "insert-before-o": {"body": "<g fill=\"currentColor\"><path d=\"M5 3a1 1 0 0 1 0-2h14a1 1 0 1 1 0 2H5Zm4 12a1 1 0 1 1 0-2h2v-2a1 1 0 1 1 2 0v2h2a1 1 0 1 1 0 2h-2v2a1 1 0 1 1-2 0v-2H9Z\"/><path fill-rule=\"evenodd\" d=\"M4 14a8 8 0 1 0 16 0a8 8 0 0 0-16 0Zm8-6a6 6 0 1 0 0 12a6 6 0 0 0 0-12Z\" clip-rule=\"evenodd\"/></g>"}, "insert-before-r": {"body": "<g fill=\"currentColor\"><path d=\"M5 3a1 1 0 0 1 0-2h14a1 1 0 1 1 0 2H5Zm4 12a1 1 0 1 1 0-2h2v-2a1 1 0 1 1 2 0v2h2a1 1 0 1 1 0 2h-2v2a1 1 0 1 1-2 0v-2H9Z\"/><path fill-rule=\"evenodd\" d=\"M4 19a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3H7a3 3 0 0 0-3 3v10Zm13 1a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10Z\" clip-rule=\"evenodd\"/></g>"}, "insights": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15 8h4v12H5V10h4V4h6v4Zm-2-2h-2v12h2V6Zm2 4v8h2v-8h-2Zm-6 2v6H7v-6h2Z\" clip-rule=\"evenodd\"/>"}, "instagram": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12 7a5 5 0 1 0 0 10a5 5 0 0 0 0-10Zm-3 5a3 3 0 1 0 6 0a3 3 0 0 0-6 0Z\" clip-rule=\"evenodd\"/><path d=\"M18 5a1 1 0 1 0 0 2a1 1 0 0 0 0-2Z\"/><path fill-rule=\"evenodd\" d=\"M5 1a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4H5Zm14 2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2Z\" clip-rule=\"evenodd\"/></g>"}, "internal": {"body": "<g fill=\"currentColor\"><path d=\"m20.708 4.412l-10.25 10.287h3.59v2h-7v-7h2v3.58L19.293 3l1.416 1.412Z\"/><path d=\"M11 4.706v2H5v12h12v-6h2v8H3v-16h8Z\"/></g>"}, "key": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6 8a3 3 0 0 0-3 3v2a3 3 0 1 0 6 0h6v2h2v-2h1v2h2v-4H9a3 3 0 0 0-3-3Zm1 5v-2a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0Z\" clip-rule=\"evenodd\"/>"}, "keyboard": {"body": "<g fill=\"currentColor\"><path d=\"M8 9a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm1 6a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2H9Zm0-3a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3-3a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm1 3a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3-3a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm1 3a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path fill-rule=\"evenodd\" d=\"M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm3-1h12a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "keyhole": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M13 12.83a3.001 3.001 0 1 0-2 0V16a1 1 0 1 0 2 0v-3.17ZM11 10a1 1 0 1 0 2 0a1 1 0 0 0-2 0Z\"/><path d=\"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10s10-4.477 10-10S17.523 2 12 2ZM4 12a8 8 0 1 0 16 0a8 8 0 0 0-16 0Z\"/></g>"}, "laptop": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M3 6a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6Zm2 0h14v8H5V6Z\" clip-rule=\"evenodd\"/><path d=\"M2 18a1 1 0 1 0 0 2h20a1 1 0 1 0 0-2H2Z\"/></g>"}, "lastpass": {"body": "<path fill=\"currentColor\" d=\"M21 6a1 1 0 0 0-1 1v10a1 1 0 1 0 2 0V7a1 1 0 0 0-1-1ZM4 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm8-2a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm4 2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/>"}, "layout-grid": {"body": "<path fill=\"currentColor\" d=\"M11 7H7v4h4V7Zm0 6H7v4h4v-4Zm2 0h4v4h-4v-4Zm4-6h-4v4h4V7Z\"/>"}, "layout-grid-small": {"body": "<path fill=\"currentColor\" d=\"M7 7h2v2H7V7Zm4 0h2v2h-2V7Zm6 0h-2v2h2V7ZM7 11h2v2H7v-2Zm6 0h-2v2h2v-2Zm2 0h2v2h-2v-2Zm-6 4H7v2h2v-2Zm2 0h2v2h-2v-2Zm6 0h-2v2h2v-2Z\"/>"}, "layout-list": {"body": "<path fill=\"currentColor\" d=\"M9 7H7v2h2V7Zm-2 6v-2h2v2H7Zm0 2v2h2v-2H7Zm4 0v2h6v-2h-6Zm6-2v-2h-6v2h6Zm0-6v2h-6V7h6Z\"/>"}, "layout-pin": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M16.93 4.016h-2.165a3.001 3.001 0 0 0-5.67 0H6.932a3 3 0 0 0-3 3v2.196a3.001 3.001 0 0 0 0 5.608v2.196a3 3 0 0 0 3 3h2.154a3.001 3.001 0 0 0 5.692 0h2.154a3 3 0 0 0 3-3v-2.171a3.001 3.001 0 0 0 0-5.658V7.016a3 3 0 0 0-3-3Zm-11 10.853v2.147a1 1 0 0 0 1 1h2.19a3.001 3.001 0 0 1 5.623 0h2.189a1 1 0 0 0 1-1v-2.17a3.001 3.001 0 0 1 0-5.66v-2.17a1 1 0 0 0-1-1h-2.177a3.001 3.001 0 0 1-5.647 0H6.931a1 1 0 0 0-1 1v2.147a3.001 3.001 0 0 1 0 5.706Z\" clip-rule=\"evenodd\"/>"}, "linear": {"body": "<path fill=\"currentColor\" d=\"M3.035 12.943a8.963 8.963 0 0 0 2.587 5.421a8.963 8.963 0 0 0 5.42 2.587l-8.007-8.008ZM3 11.494l9.492 9.492a9.016 9.016 0 0 0 2.378-.459L3.46 9.115A9.016 9.016 0 0 0 3 11.494Zm.867-3.384l12.009 12.009a8.948 8.948 0 0 0 1.773-1.123L4.99 6.336A8.95 8.95 0 0 0 3.867 8.11Zm1.796-2.515a9 9 0 0 1 12.728 12.728L5.663 5.595Z\"/>"}, "link": {"body": "<g fill=\"currentColor\"><path d=\"m14.828 12l1.415 1.414l2.828-2.828a4 4 0 0 0-5.657-5.657l-2.828 2.828L12 9.172l2.828-2.829a2 2 0 1 1 2.829 2.829L14.828 12ZM12 14.829l1.414 1.414l-2.828 2.828a4 4 0 0 1-5.657-5.657l2.828-2.828L9.172 12l-2.829 2.829a2 2 0 1 0 2.829 2.828L12 14.828Z\"/><path d=\"M14.829 10.586a1 1 0 0 0-1.415-1.415l-4.242 4.243a1 1 0 1 0 1.414 1.414l4.242-4.242Z\"/></g>"}, "list": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M20 4H4a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1ZM4 2a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V5a3 3 0 0 0-3-3H4Zm2 5h2v2H6V7Zm5 0a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2h-6Zm-3 4H6v2h2v-2Zm2 1a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2h-6a1 1 0 0 1-1-1Zm-2 3H6v2h2v-2Zm2 1a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2h-6a1 1 0 0 1-1-1Z\" clip-rule=\"evenodd\"/>"}, "list-tree": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 1H1v8h8V6h2v14h4v3h8v-8h-8v3h-2V6h2v3h8V1h-8v3H9V1Zm12 2h-4v4h4V3Zm-4 14h4v4h-4v-4Z\" clip-rule=\"evenodd\"/>"}, "live-photo": {"body": "<g fill=\"currentColor\"><path d=\"M12.98 21.953a10.12 10.12 0 0 1-1.96 0l.193-1.991a8.1 8.1 0 0 0 1.574 0l.193 1.99Zm-3.884-.381l.58-1.914a7.947 7.947 0 0 1-1.446-.6l-.945 1.763c.573.307 1.179.56 1.811.75Zm-3.44-1.842l1.27-1.545a8.062 8.062 0 0 1-1.111-1.11L4.27 18.343c.415.506.88.97 1.386 1.386Zm-2.477-3.015l1.763-.944a7.938 7.938 0 0 1-.6-1.447l-1.914.58a9.93 9.93 0 0 0 .751 1.81ZM2.047 12.98l1.991-.193a8.12 8.12 0 0 1 0-1.574l-1.99-.193a10.123 10.123 0 0 0 0 1.96Zm.381-3.884l1.914.58c.153-.505.355-.989.6-1.446l-1.763-.945a9.938 9.938 0 0 0-.75 1.811Zm1.842-3.44l1.545 1.27a8.06 8.06 0 0 1 1.11-1.111L5.657 4.27c-.506.415-.97.88-1.386 1.386Zm3.015-2.477l.945 1.763a7.938 7.938 0 0 1 1.446-.6l-.58-1.914a9.938 9.938 0 0 0-1.81.751Zm3.735-1.132a10.123 10.123 0 0 1 1.96 0l-.193 1.991a8.12 8.12 0 0 0-1.574 0l-.193-1.99Zm3.884.381l-.58 1.914c.505.153.989.355 1.447.6l.944-1.763a9.936 9.936 0 0 0-1.811-.75Zm3.44 1.842l-1.27 1.545c.406.333.778.705 1.111 1.11l1.545-1.269a10.06 10.06 0 0 0-1.386-1.386Zm2.477 3.015l-1.763.945c.245.457.447.941.6 1.446l1.914-.58a9.937 9.937 0 0 0-.751-1.81Zm1.132 3.735l-1.991.193a8.1 8.1 0 0 1 0 1.574l1.99.194a10.123 10.123 0 0 0 0-1.961Zm-.381 3.884l-1.914-.58a7.947 7.947 0 0 1-.6 1.447l1.763.944c.307-.573.56-1.179.75-1.811Zm-1.842 3.44l-1.545-1.27a8.063 8.063 0 0 1-1.11 1.111l1.269 1.545c.506-.415.97-.88 1.386-1.386Zm-3.015 2.477l-.944-1.763a7.947 7.947 0 0 1-1.447.6l.58 1.914a9.935 9.935 0 0 0 1.81-.751Z\"/><path fill-rule=\"evenodd\" d=\"M9 12a3 3 0 1 1 6 0a3 3 0 0 1-6 0Zm3 1a1 1 0 1 1 0-2a1 1 0 0 1 0 2Z\" clip-rule=\"evenodd\"/><path fill-rule=\"evenodd\" d=\"M12 6a6 6 0 1 0 0 12a6 6 0 0 0 0-12Zm-4 6a4 4 0 1 0 8 0a4 4 0 0 0-8 0Z\" clip-rule=\"evenodd\"/></g>"}, "loadbar": {"body": "<rect width=\"18\" height=\"4\" x=\"3\" y=\"10\" fill=\"currentColor\" rx=\"2\"/>"}, "loadbar-alt": {"body": "<g fill=\"currentColor\"><rect width=\"18\" height=\"4\" x=\"3\" y=\"10\" opacity=\".3\" rx=\"2\"/><rect width=\"10\" height=\"4\" x=\"7\" y=\"10\" rx=\"2\"/></g>"}, "loadbar-doc": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M17 5H7a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1ZM7 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H7Z\" clip-rule=\"evenodd\"/><path d=\"M8 7h8v2H8V7Zm0 4h8v2H8v-2Zm0 4h5v2H8v-2Z\"/></g>"}, "loadbar-sound": {"body": "<path fill=\"currentColor\" d=\"M11 6h2v12h-2V6Zm-4 7h2v5H7v-5Zm8-4h2v9h-2V9Z\"/>"}, "lock": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M18 10.5a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-6a3 3 0 0 1 3-3v-3a6 6 0 1 1 12 0v3Zm-6-7a4 4 0 0 1 4 4v3H8v-3a4 4 0 0 1 4-4Zm6 9H6a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/>"}, "lock-unlock": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M19 7h-2a4 4 0 0 0-8 0v3h9a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-6a3 3 0 0 1 3-3h1V7a6 6 0 1 1 12 0Zm-1 5H6a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/>"}, "log-in": {"body": "<g fill=\"currentColor\"><path d=\"M15.486 20h4a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-4v2h4v12h-4v2Z\"/><path d=\"m10.158 17.385l-1.42-1.408l3.92-3.953H3.513a1 1 0 1 1 0-2h9.163l-3.98-3.947l1.408-1.42l6.391 6.337l-6.337 6.391Z\"/></g>"}, "log-off": {"body": "<g fill=\"currentColor\"><path d=\"M13 4.009a1 1 0 1 0-2 0l-.003 8.003a1 1 0 0 0 2 0L13 4.01Z\"/><path d=\"M4 12.992c0-2.21.895-4.21 2.343-5.657l1.414 1.414a6 6 0 1 0 8.485 0l1.415-1.414A8 8 0 1 1 4 12.992Z\"/></g>"}, "log-out": {"body": "<g fill=\"currentColor\"><path d=\"M8.514 20h-4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h4v2h-4v12h4v2Z\"/><path d=\"m13.842 17.385l1.42-1.408l-3.92-3.953h9.144a1 1 0 1 0 0-2h-9.162l3.98-3.947l-1.408-1.42l-6.391 6.337l6.337 6.391Z\"/></g>"}, "loupe": {"body": "<g fill=\"currentColor\"><path d=\"M11 11V8h2v3h3v2h-3v3h-2v-3H8v-2h3Z\"/><path fill-rule=\"evenodd\" d=\"M3 12a9 9 0 0 0 9 9h6a3 3 0 0 0 3-3v-6a9 9 0 1 0-18 0Zm9-7a7 7 0 1 1 0 14a7 7 0 0 1 0-14Z\" clip-rule=\"evenodd\"/></g>"}, "magnet": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M8 2.5H4v3h4v-3Zm12 0h-4v3h4v-3Z\"/><path d=\"M8 7.5H4v6a8 8 0 1 0 16 0v-6h-4v6a4 4 0 0 1-8 0v-6Z\"/></g>"}, "mail": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3.01 5.838a1 1 0 0 1 1-1H20a1 1 0 0 1 1 1v11.324a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-11c0-.048.003-.094.01-.14v-.184ZM5 8.062v9.1h14v-9.1l-4.879 4.879a3 3 0 0 1-4.242 0L5 8.06Zm1.572-1.256h10.856l-4.72 4.72a1 1 0 0 1-1.415 0l-4.72-4.72Z\" clip-rule=\"evenodd\"/>"}, "mail-forward": {"body": "<path fill=\"currentColor\" d=\"m13.638 15.529l1.414 1.414l4.95-4.95l-4.95-4.95l-1.414 1.415l2.498 2.498H7.998a4 4 0 0 0-4 4v2h2v-2a2 2 0 0 1 2-2h8.212l-2.572 2.573Z\"/>"}, "mail-open": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3.05 10.015a2 2 0 0 1 .465-2.1L9.879 1.55a3 3 0 0 1 4.242 0l6.364 6.364a2 2 0 0 1 .465 2.101c.**************.05.313v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-11a1 1 0 0 1 .05-.313Zm1.879-.687l6.364-6.363a1 1 0 0 1 1.414 0l6.364 6.363h-.03v.03l-6.334 6.334a1 1 0 0 1-1.414 0L4.929 9.328Zm14.07 2.9l-4.878 4.879a3 3 0 0 1-4.242 0l-4.88-4.88v9.101h14v-9.1Z\" clip-rule=\"evenodd\"/>"}, "mail-reply": {"body": "<path fill=\"currentColor\" d=\"m10.362 15.529l-1.414 1.414l-4.95-4.95l4.95-4.95l1.414 1.415l-2.498 2.498h8.138a4 4 0 0 1 4 4v2h-2v-2a2 2 0 0 0-2-2H7.79l2.572 2.573Z\"/>"}, "math-divide": {"body": "<path fill=\"currentColor\" d=\"M14 7a2 2 0 1 1-4 0a2 2 0 0 1 4 0ZM3 12a1 1 0 0 1 1-1h16a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Zm9 7a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/>"}, "math-equal": {"body": "<path fill=\"currentColor\" d=\"M5 9a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2H5Zm0 4a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2H5Z\"/>"}, "math-minus": {"body": "<path fill=\"currentColor\" d=\"M4 12a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1Z\"/>"}, "math-percent": {"body": "<path fill=\"currentColor\" d=\"M16.243 6.343a1 1 0 1 1 1.414 1.414l-9.9 9.9a1 1 0 0 1-1.414-1.414l9.9-9.9ZM9.879 9.879A2 2 0 1 1 7.05 7.05a2 2 0 0 1 2.83 2.83Zm4.242 7.071a2 2 0 1 0 2.829-2.829a2 2 0 0 0-2.829 2.829Z\"/>"}, "math-plus": {"body": "<path fill=\"currentColor\" d=\"M12 4a1 1 0 0 0-1 1v6H5a1 1 0 1 0 0 2h6v6a1 1 0 1 0 2 0v-6h6a1 1 0 1 0 0-2h-6V5a1 1 0 0 0-1-1Z\"/>"}, "maximize": {"body": "<path fill=\"currentColor\" d=\"M3 3h6v2H5v4H3V3Zm0 18h6v-2H5v-4H3v6Zm12 0h6v-6h-2v4h-4v2Zm6-18h-6v2h4v4h2V3Z\"/>"}, "maximize-alt": {"body": "<path fill=\"currentColor\" d=\"M3 3h6v2H6.462l4.843 4.843l-1.415 1.414L5 6.367V9H3V3Zm0 18h6v-2H6.376l4.929-4.928l-1.415-1.414L5 17.548V15H3v6Zm12 0h6v-6h-2v2.524l-4.867-4.866l-1.414 1.414L17.647 19H15v2Zm6-18h-6v2h2.562l-4.843 4.843l1.414 1.414L19 6.39V9h2V3Z\"/>"}, "maze": {"body": "<path fill=\"currentColor\" d=\"M11.37 9.593L8.779 7L1 14.778l2.593 2.593l7.778-7.778ZM15.222 7L23 14.778l-2.576 2.576l-5.202-5.202l-5.202 5.202l-2.576-2.576L15.222 7Z\"/>"}, "media-live": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M15 12a3 3 0 1 1-6 0a3 3 0 0 1 6 0Zm-2 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path d=\"M12 19a7 7 0 1 0 0-14a7 7 0 0 0 0 14Zm0-2a5 5 0 1 0 0-10a5 5 0 0 0 0 10Z\"/><path d=\"M12 23c6.075 0 11-4.925 11-11S18.075 1 12 1S1 5.925 1 12s4.925 11 11 11Zm0-2a9 9 0 1 0 0-18a9 9 0 0 0 0 18Z\"/></g>"}, "media-podcast": {"body": "<g fill=\"currentColor\"><path d=\"M5.636 20.364a9 9 0 1 1 12.728 0l1.414 1.414A10.966 10.966 0 0 0 23 14c0-6.075-4.925-11-11-11S1 7.925 1 14c0 3.038 1.231 5.788 3.222 7.778l1.414-1.414Z\"/><path d=\"M16.95 18.95a7 7 0 1 0-9.9 0l1.415-1.414a5 5 0 1 1 7.071 0l1.414 1.414Z\"/><path d=\"M14.121 16.121a3 3 0 1 0-4.243 0l1.415-1.414a1 1 0 1 1 1.414 0l1.414 1.414Z\"/></g>"}, "menu": {"body": "<path fill=\"currentColor\" d=\"M2 6a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm0 6.032a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm1 5.032a1 1 0 1 0 0 2h18a1 1 0 0 0 0-2H3Z\"/>"}, "menu-boxed": {"body": "<g fill=\"currentColor\"><path d=\"M8.016 6.982a1.003 1.003 0 0 0 0 2.006h7.95a1.003 1.003 0 0 0 0-2.006h-7.95Zm-1 5.018c0-.552.447-1.003 1-1.003h7.95a1.003 1.003 0 0 1 0 2.006h-7.95c-.553 0-1-.45-1-1.003Zm1.009 3.012a1.003 1.003 0 0 0 0 2.007h7.95a1.003 1.003 0 0 0 0-2.007h-7.95Z\"/><path fill-rule=\"evenodd\" d=\"M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm3-1h12a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "menu-cake": {"body": "<path fill=\"currentColor\" d=\"M12 8a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm-7 2a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2H5Zm-1 5a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1Zm1 3a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2H5Z\"/>"}, "menu-cheese": {"body": "<path fill=\"currentColor\" d=\"M3 6a1 1 0 0 1 1-1h16a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Zm0 12a1 1 0 0 1 1-1h16a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Zm0-6a1 1 0 0 1 1-1h16a1 1 0 1 1 0 2h-9.738l-2.647 2.648L4.967 13H4a1 1 0 0 1-1-1Z\"/>"}, "menu-grid-o": {"body": "<path fill=\"currentColor\" d=\"M8 6a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm0 6a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm-2 8a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm8-14a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm-2 8a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm2 4a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm4-10a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm2 4a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm-2 8a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/>"}, "menu-grid-r": {"body": "<path fill=\"currentColor\" d=\"M4 4h4v4H4V4Zm0 6h4v4H4v-4Zm4 6H4v4h4v-4Zm2-12h4v4h-4V4Zm4 6h-4v4h4v-4Zm-4 6h4v4h-4v-4ZM20 4h-4v4h4V4Zm-4 6h4v4h-4v-4Zm4 6h-4v4h4v-4Z\"/>"}, "menu-hotdog": {"body": "<path fill=\"currentColor\" d=\"M7 6a3 3 0 0 0-3 3h16a3 3 0 0 0-3-3H7Zm0 12a3 3 0 0 1-3-3h16a3 3 0 0 1-3 3H7Zm-4-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3Z\"/>"}, "menu-left": {"body": "<path fill=\"currentColor\" d=\"M2 5.995c0-.55.446-.995.995-.995h8.01a.995.995 0 0 1 0 1.99h-8.01A.995.995 0 0 1 2 5.995ZM2 12c0-.55.446-.995.995-.995h18.01a.995.995 0 1 1 0 1.99H2.995A.995.995 0 0 1 2 12Zm.995 5.01a.995.995 0 0 0 0 1.99h12.01a.995.995 0 0 0 0-1.99H2.995Z\"/>"}, "menu-left-alt": {"body": "<path fill=\"currentColor\" d=\"M4 6a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1Zm0 12a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1Zm1-7a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H5Z\"/>"}, "menu-motion": {"body": "<path fill=\"currentColor\" d=\"M12 5a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2h-8Zm-5 7a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1Zm-4 6a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Z\"/>"}, "menu-oreos": {"body": "<path fill=\"currentColor\" d=\"M7 3a3 3 0 0 0-3 3h16a3 3 0 0 0-3-3H7Zm0 8a3 3 0 0 1-3-3h16a3 3 0 0 1-3 3H7Zm0 2a3 3 0 0 0-3 3h16a3 3 0 0 0-3-3H7Zm0 8a3 3 0 0 1-3-3h16a3 3 0 0 1-3 3H7Z\"/>"}, "menu-right": {"body": "<path fill=\"currentColor\" d=\"M22 18.005c0 .55-.446.995-.995.995h-8.01a.995.995 0 0 1 0-1.99h8.01c.55 0 .995.445.995.995ZM22 12c0 .55-.446.995-.995.995H2.995a.995.995 0 1 1 0-1.99h18.01c.55 0 .995.446.995.995Zm-.995-5.01a.995.995 0 0 0 0-1.99H8.995a.995.995 0 1 0 0 1.99h12.01Z\"/>"}, "menu-right-alt": {"body": "<path fill=\"currentColor\" d=\"M4 6a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1Zm0 12a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1Zm7-7a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2h-8Z\"/>"}, "menu-round": {"body": "<g fill=\"currentColor\"><path d=\"M8 6.983a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H8ZM7 12a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1Zm1 3.017a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H8Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 1-16 0a8 8 0 0 1 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "merge-horizontal": {"body": "<path fill=\"currentColor\" d=\"m12 8.976l4.243-4.243l-1.415-1.414L12 6.147L9.172 3.32L7.757 4.733L12 8.976ZM5 12a1 1 0 0 1 1-1h12a1 1 0 0 1 0 2H6a1 1 0 0 1-1-1Zm7 3.024l-4.243 4.243l1.415 1.414L12 17.853l2.828 2.828l1.415-1.414L12 15.024Z\"/>"}, "merge-vertical": {"body": "<path fill=\"currentColor\" d=\"M8.976 12L4.733 7.757L3.32 9.172L6.147 12L3.32 14.828l1.414 1.415L8.976 12ZM12 19a1 1 0 0 1-1-1V6a1 1 0 1 1 2 0v12a1 1 0 0 1-1 1Zm3.024-7l4.243 4.243l1.414-1.415L17.853 12l2.828-2.828l-1.414-1.415L15.024 12Z\"/>"}, "mic": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M9 4a3 3 0 1 1 6 0v8a3 3 0 1 1-6 0V4Zm4 0v8a1 1 0 1 1-2 0V4a1 1 0 1 1 2 0Z\" clip-rule=\"evenodd\"/><path d=\"M18 12a6.002 6.002 0 0 1-5 5.917V21h4v2H7v-2h4v-3.083A6.002 6.002 0 0 1 6 12V9h2v3a4 4 0 0 0 8 0V9h2v3Z\"/></g>"}, "microbit": {"body": "<g fill=\"currentColor\"><path d=\"M7 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm12-2a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/><path fill-rule=\"evenodd\" d=\"M7 5a7 7 0 0 0 0 14h10a7 7 0 1 0 0-14H7Zm10 3H7a4 4 0 1 0 0 8h10a4 4 0 0 0 0-8Z\" clip-rule=\"evenodd\"/></g>"}, "microsoft": {"body": "<path fill=\"currentColor\" d=\"M3 3h8v8H3V3Zm0 10h8v8H3v-8ZM13 3h8v8h-8V3Zm0 10h8v8h-8v-8Z\"/>"}, "mini-player": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm3-1h12a1 1 0 0 1 1 1v6.268A1.99 1.99 0 0 0 18 12h-4a2 2 0 0 0-2 2v4c0 .364.097.706.268 1H6a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/>"}, "minimize": {"body": "<path fill=\"currentColor\" d=\"M9 9H3V7h4V3h2v6Zm0 6H3v2h4v4h2v-6Zm12 0h-6v6h2v-4h4v-2Zm-6-6h6V7h-4V3h-2v6Z\"/>"}, "minimize-alt": {"body": "<path fill=\"currentColor\" d=\"m20.073 2l1.415 1.414l-5.85 5.85h2.544v2h-6v-6h2v2.627L20.073 2Zm-8.891 10.264v6h-2v-2.422L3.414 21.61L2 20.196l5.932-5.932h-2.75v-2h6Z\"/>"}, "modem": {"body": "<g fill=\"currentColor\"><path d=\"M18 16.634a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path fill-rule=\"evenodd\" d=\"M5.866 3.134a1 1 0 1 0-1 1.732l13.454 7.768H2v4a4 4 0 0 0 4 4h12a4 4 0 0 0 4-4v-4l-16.134-9.5ZM20 14.634H4v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2Z\" clip-rule=\"evenodd\"/></g>"}, "monday": {"body": "<path fill=\"currentColor\" d=\"M6.779 6.14a3 3 0 0 1 4.915 3.44l-5.736 8.192a3 3 0 0 1-4.915-3.441l5.736-8.192Zm8.489.088a3 3 0 0 1 4.915 3.442l-5.736 8.191a3 3 0 0 1-4.915-3.441l5.736-8.192ZM20.5 18.86a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\"/>"}, "moon": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.226 2.003a9.971 9.971 0 0 0-7.297 2.926c-3.905 3.905-3.905 10.237 0 14.142c3.905 3.905 10.237 3.905 14.142 0a9.972 9.972 0 0 0 2.926-7.297a10.037 10.037 0 0 0-.337-2.368a14.87 14.87 0 0 1-1.744 1.436c-1.351.949-2.733 1.563-3.986 1.842c-1.906.423-3.214.032-3.93-.684c-.716-.716-1.107-2.024-.684-3.93c.279-1.253.893-2.635 1.841-3.986c.415-.592.894-1.177 1.437-1.744a10.017 10.017 0 0 0-2.368-.337Zm5.43 15.654a7.964 7.964 0 0 0 2.251-4.438c-3.546 2.045-7.269 2.247-9.321.195c-2.052-2.052-1.85-5.775.195-9.321a8 8 0 1 0 6.876 13.564Z\" clip-rule=\"evenodd\"/>"}, "more": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 15a3 3 0 1 0 0-6a3 3 0 0 0 0 6Zm0-2a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm7 2a3 3 0 1 0 0-6a3 3 0 0 0 0 6Zm0-2a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm10-1a3 3 0 1 1-6 0a3 3 0 0 1 6 0Zm-2 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\" clip-rule=\"evenodd\"/>"}, "more-alt": {"body": "<path fill=\"currentColor\" d=\"M8 12a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm6 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm4 2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/>"}, "more-o": {"body": "<g fill=\"currentColor\"><path d=\"M7 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm7-2a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm3 2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M24 12c0 6.627-5.373 12-12 12S0 18.627 0 12S5.373 0 12 0s12 5.373 12 12Zm-2 0c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Z\" clip-rule=\"evenodd\"/></g>"}, "more-r": {"body": "<g fill=\"currentColor\"><path d=\"M7 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm7-2a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm3 2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M0 5a3 3 0 0 1 3-3h18a3 3 0 0 1 3 3v14a3 3 0 0 1-3 3H3a3 3 0 0 1-3-3V5Zm3-1h18a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "more-vertical": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 5a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm0 8a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm0 8a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm3-17a3 3 0 1 1-6 0a3 3 0 0 1 6 0Zm0 8a3 3 0 1 1-6 0a3 3 0 0 1 6 0Zm-3 11a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\" clip-rule=\"evenodd\"/>"}, "more-vertical-alt": {"body": "<path fill=\"currentColor\" d=\"M14 6a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm0 6a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm0 6a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/>"}, "more-vertical-o": {"body": "<g fill=\"currentColor\"><path d=\"M12 9a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm2 3a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm-2 7a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M24 12c0 6.627-5.373 12-12 12S0 18.627 0 12S5.373 0 12 0s12 5.373 12 12Zm-2 0c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Z\" clip-rule=\"evenodd\"/></g>"}, "more-vertical-r": {"body": "<g fill=\"currentColor\"><path d=\"M12 9a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm2 3a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm-2 7a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M2 3a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v18a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V3Zm3-1h14a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "mouse": {"body": "<g fill=\"currentColor\"><path d=\"M12 5a1 1 0 0 0-1 1v4a1 1 0 1 0 2 0V6a1 1 0 0 0-1-1Z\"/><path fill-rule=\"evenodd\" d=\"M4 8a8 8 0 1 1 16 0v8a8 8 0 1 1-16 0V8Zm14 0v8a6 6 0 0 1-12 0V8a6 6 0 1 1 12 0Z\" clip-rule=\"evenodd\"/></g>"}, "move-down": {"body": "<g fill=\"currentColor\"><path d=\"M7 5h2v8H7V5Zm8 0h2v8h-2V5Z\"/><path d=\"M11 5h2v10h3.035l-4 4.071l-4-4.071H11V5Z\"/></g>"}, "move-left": {"body": "<g fill=\"currentColor\"><path d=\"M19.071 17v-2h-8v2h8Zm0-8V7h-8v2h8Z\"/><path d=\"M19.071 13v-2h-10V7.965l-4.071 4l4.071 4V13h10Z\"/></g>"}, "move-right": {"body": "<g fill=\"currentColor\"><path d=\"M5 17v-2h8v2H5Zm0-8V7h8v2H5Z\"/><path d=\"M5 13v-2h10V7.965l4.071 4l-4.071 4V13H5Z\"/></g>"}, "move-task": {"body": "<path fill=\"currentColor\" d=\"M18.964 7h-8v2h8V7ZM6 8.829v6.342L9.964 12L6 8.829ZM18.964 11h-8v2h8v-2Zm-8 4h8v2h-8v-2Z\"/>"}, "move-up": {"body": "<g fill=\"currentColor\"><path d=\"M17 19.071h-2v-8h2v8Zm-8 0H7v-8h2v8Z\"/><path d=\"M13 19.071h-2v-10H7.965l4-4.071l4 4.071H13v10Z\"/></g>"}, "music": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M22 6a4 4 0 0 0-4.608-3.953l-7 1.077A4 4 0 0 0 7 7.078v8.763a3.5 3.5 0 1 0 2 3.163V7.078A2 2 0 0 1 10.696 5.1l7-1.077A2 2 0 0 1 20 6.001v6.84a3.5 3.5 0 1 0 2 3.163V6.001Zm-2 10.004a1.5 1.5 0 1 0-3 0a1.5 1.5 0 0 0 3 0Zm-13 3a1.5 1.5 0 1 0-3 0a1.5 1.5 0 0 0 3 0Z\" clip-rule=\"evenodd\"/>"}, "music-note": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m12 8.954l5.635-1.127a2.942 2.942 0 0 0-1.154-5.769l-4.07.814A3 3 0 0 0 10 5.814v8.076a4 4 0 1 0 2 3.465v-8.4Zm4.874-4.935l-4.07.814a1 1 0 0 0-.804.98v1.102l5.243-1.049a.942.942 0 0 0-.37-1.847ZM10 17.354a2 2 0 1 0-4 0a2 2 0 0 0 4 0Z\" clip-rule=\"evenodd\"/>"}, "music-speaker": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12 18.939a4 4 0 1 0 0-8a4 4 0 0 0 0 8Zm0-2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\" clip-rule=\"evenodd\"/><path d=\"M12 9.044a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M7 1a3 3 0 0 0-3 3v16a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H7Zm10 2H7a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "nametag": {"body": "<g fill=\"currentColor\"><path d=\"M4 14v6h6v-2H6v-4H4Z\"/><path fill-rule=\"evenodd\" d=\"M9 9v6h6V9H9Zm4 2h-2v2h2v-2Z\" clip-rule=\"evenodd\"/><path d=\"M4 10V4h6v2H6v4H4Zm16 0V4h-6v2h4v4h2Zm0 4v6h-6v-2h4v-4h2Z\"/></g>"}, "notes": {"body": "<g fill=\"currentColor\"><path d=\"M6 6a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1Zm0 4a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1Zm1 3a1 1 0 1 0 0 2h10a1 1 0 1 0 0-2H7Zm-1 5a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1Z\"/><path fill-rule=\"evenodd\" d=\"M2 4a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V4Zm3-1h14a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "notifications": {"body": "<g fill=\"currentColor\"><path d=\"M20 7a3 3 0 1 1-6 0a3 3 0 0 1 6 0Z\"/><path d=\"M12 6H4v14h14v-8h-2v6H6V8h6V6Z\"/></g>"}, "npm": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 21a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5Zm1-3V6h12v12h-3V9h-3v9H6Z\" clip-rule=\"evenodd\"/>"}, "oculus": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M16 10H8a2 2 0 1 0 0 4h8a2 2 0 1 0 0-4ZM8 6a6 6 0 1 0 0 12h8a6 6 0 0 0 0-12H8Z\" clip-rule=\"evenodd\"/>"}, "open-collective": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"m16.682 15.753l2.13 2.13A8.965 8.965 0 0 0 21 12a8.964 8.964 0 0 0-2.123-5.806l-2.133 2.133A5.974 5.974 0 0 1 18 12c0 1.42-.493 2.725-1.318 3.753Z\"/><path d=\"M15.673 16.744a6 6 0 1 1 .08-9.426l2.13-2.13a9 9 0 1 0-.077 13.689l-2.133-2.133Z\"/></g>"}, "options": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7 3a4.002 4.002 0 0 1 3.874 3H19v2h-8.126A4.002 4.002 0 0 1 3 7a4 4 0 0 1 4-4Zm0 6a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm10 11a4.002 4.002 0 0 1-3.874-3H5v-2h8.126A4.002 4.002 0 0 1 21 16a4 4 0 0 1-4 4Zm0-2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\" clip-rule=\"evenodd\"/>"}, "organisation": {"body": "<g fill=\"currentColor\"><path d=\"M17 15h2v2h-2v-2Zm2-4h-2v2h2v-2Z\"/><path fill-rule=\"evenodd\" d=\"M13 7h10v14H1V3h12v4ZM8 5h3v2H8V5Zm3 14v-2H8v2h3Zm0-4v-2H8v2h3Zm0-4V9H8v2h3Zm10 8V9h-8v2h2v2h-2v2h2v2h-2v2h8ZM3 19v-2h3v2H3Zm0-4h3v-2H3v2Zm3-4V9H3v2h3ZM3 7h3V5H3v2Z\" clip-rule=\"evenodd\"/></g>"}, "overflow": {"body": "<g fill=\"currentColor\"><path d=\"M22 11a10 10 0 0 1-20 0h20Z\" opacity=\".2\"/><path d=\"M20 11a8 8 0 0 1-16 0h16Z\" opacity=\".3\"/><path d=\"M20 11a8 8 0 0 0-16 0h16Z\"/></g>"}, "pacman": {"body": "<g fill=\"currentColor\"><path d=\"M14.064 8a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M13 3c2.152 0 4.128.756 5.677 2.016l1.447 1.447l-1.295 1.295h-.001L14.585 12l3.639 3.638l-.002.002l1.905 1.904l-1.413 1.413l-.002-.002A9 9 0 1 1 13 3Zm-1.243 9l5.532 5.532a7 7 0 1 1 0-11.065L11.757 12Z\" clip-rule=\"evenodd\"/></g>"}, "password": {"body": "<g fill=\"currentColor\"><path d=\"M6 12a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm5-1a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm6-1h-4v2h4v-2Z\"/><path fill-rule=\"evenodd\" d=\"M2 6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H2Zm20 2H2v8h20V8Z\" clip-rule=\"evenodd\"/></g>"}, "path-back": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 14H4V4h10v5h5v10H9v-5ZM6 6h6v6H6V6Z\" clip-rule=\"evenodd\"/>"}, "path-crop": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M6 6h8v8H6z\" opacity=\".5\"/><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 9h10v10H9V9Zm6 2h2v6h-6v-2h4v-4Z\" clip-rule=\"evenodd\"/></g>"}, "path-divide": {"body": "<g fill=\"currentColor\"><path d=\"M5 5h10v4H9v6H5V5Z\"/><path d=\"M9 15v4h10V9h-4v6H9Z\"/><path d=\"M10 10h4v4h-4v-4Z\"/></g>"}, "path-exclude": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5h10v4H9v6H5V5Zm4 10v4h10V9h-4v6H9Z\" clip-rule=\"evenodd\"/>"}, "path-front": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5h10v4h4v10H9v-4H5V5Zm6 6v6h6v-6h-6Z\" clip-rule=\"evenodd\"/>"}, "path-intersect": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15 5H5v10h4v4h10V9h-4V5Zm-2 2H7v6h2V9h4V7Zm4 10h-6v-2h4v-4h2v6Z\" clip-rule=\"evenodd\"/>"}, "path-outline": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5h10v4h4v10H9v-4H5V5Zm2 2h6v2H9v4H7V7Zm4 10h6v-6h-2v4h-4v2Zm2-6h-2v2h2v-2Z\" clip-rule=\"evenodd\"/>"}, "path-trim": {"body": "<g fill=\"currentColor\"><path d=\"M5 5h10v3H8v7H5V5Z\"/><path d=\"M19 9H9v10h10V9Z\"/></g>"}, "path-unite": {"body": "<path fill=\"currentColor\" d=\"M15 5H5v10h4v4h10V9h-4V5Z\"/>"}, "patreon": {"body": "<g fill=\"currentColor\"><path d=\"M21 10a6 6 0 1 1-12 0a6 6 0 0 1 12 0Z\" opacity=\".5\"/><path d=\"M3 4h4v16H3V4Z\"/></g>"}, "paypal": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.47 3.544h8c1.639 0 2.945.775 3.626 1.971c1.22.872 1.847 2.4 1.512 4.138c-.521 2.712-3.183 4.91-5.944 4.91h-2l-1.134 5.892H6.398l.23-1.199h-3.18L6.47 3.544Zm1.622 1.964h6c1.657 0 2.746 1.32 2.433 2.946c-.313 1.627-1.91 2.946-3.566 2.946h-4l-1.134 5.892h-2L8.092 5.508Z\" clip-rule=\"evenodd\"/>"}, "pen": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M21.264 2.293a1 1 0 0 0-1.415 0l-.872.872a3.001 3.001 0 0 0-3.415.587L4.955 14.358l5.657 5.657L21.22 9.408a2.999 2.999 0 0 0 .586-3.414l.873-.873a1 1 0 0 0 0-1.414l-1.415-1.414Zm-4.268 8.51l-6.384 6.384l-2.828-2.829l6.383-6.383l2.829 2.829Zm1.818-1.818l.99-.99a1 1 0 0 0 0-1.415l-1.413-1.414a1 1 0 0 0-1.415 0l-.99.99l2.828 2.83Z\" clip-rule=\"evenodd\"/><path d=\"m2 22.95l2.122-7.778l5.656 5.657L2 22.95Z\"/></g>"}, "pentagon-bottom-left": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M13.33 9.232L10 5l-5 8.66l3.33 4.232l5.33.768l5-8.66l-5.33-.768Zm2.121 2.326l-3.198-.46l-1.998-2.54l-2.846 4.93l1.998 2.539l3.198.46l2.846-4.929Z\" clip-rule=\"evenodd\"/>"}, "pentagon-bottom-right": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10.33 8.232L13.66 4l5 8.66l-3.33 4.232l-5.33.768L5 9l5.33-.768Zm-2.12 2.326l3.197-.46l1.998-2.54l2.846 4.93l-1.998 2.539l-3.198.46l-2.846-4.929Z\" clip-rule=\"evenodd\"/>"}, "pentagon-down": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 8L7 6v10l5 2.5l5-2.5V6l-5 2Zm3 .954l-3 1.2l-3-1.2v5.81l3 1.5l3-1.5v-5.81Z\" clip-rule=\"evenodd\"/>"}, "pentagon-left": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m16 12l2-5H8l-2 5l2 5h10l-2-5Zm-.954 3l-1.2-3l1.2-3H9.354l-1.2 3l1.2 3h5.692Z\" clip-rule=\"evenodd\"/>"}, "pentagon-right": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 12L6 7h10l2 5l-2 5H6l2-5Zm.954 3l1.2-3l-1.2-3h5.692l1.2 3l-1.2 3H8.954Z\" clip-rule=\"evenodd\"/>"}, "pentagon-top-left": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14.33 15.16L11 19.392l-5-8.66L9.33 6.5l5.33-.768l5 8.66l-5.33.768Zm2.121-2.326l-3.198.46l-1.998 2.54l-2.846-4.93l1.998-2.539l3.198-.46l2.846 4.929Z\" clip-rule=\"evenodd\"/>"}, "pentagon-top-right": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10.33 15.16L5 14.392l5-8.66l5.33.768l3.33 4.232l-5 8.66l-3.33-4.232Zm3.075.674l-1.998-2.54l-3.198-.46l2.846-4.93l3.198.461l1.998 2.54l-2.846 4.929Z\" clip-rule=\"evenodd\"/>"}, "pentagon-up": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m12 16l5 2V8l-5-2l-5 2v10l5-2Zm-3-.954l3-1.2l3 1.2V9.354l-3-1.2l-3 1.2v5.692Z\" clip-rule=\"evenodd\"/>"}, "performance": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M11 9v4.17a3.001 3.001 0 1 0 2 0V9h-2Zm0 7a1 1 0 1 1 2 0a1 1 0 0 1-2 0Z\" clip-rule=\"evenodd\"/><path d=\"M12 5a7 7 0 0 1 7 7v1h-2v-1a5 5 0 0 0-10 0v1H5v-1a7 7 0 0 1 7-7Z\"/><path fill-rule=\"evenodd\" d=\"M12 23c6.075 0 11-4.925 11-11S18.075 1 12 1S1 5.925 1 12s4.925 11 11 11Zm0-2a9 9 0 1 0 0-18a9 9 0 0 0 0 18Z\" clip-rule=\"evenodd\"/></g>"}, "pexels": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 5a5.001 5.001 0 0 1 0 10v4H6V5h6ZM8 7v10h2v-4h2l.003-.001a2.993 2.993 0 0 0 3.041-3.044l-.007-.39a2.61 2.61 0 0 0-2.711-2.562l-.306.005L12 7H8Z\" clip-rule=\"evenodd\"/>"}, "phone": {"body": "<g fill=\"currentColor\"><path d=\"M22 12A10.002 10.002 0 0 0 12 2v2a8.003 8.003 0 0 1 7.391 4.938A8 8 0 0 1 20 12h2ZM2 10V5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H6a8 8 0 0 0 8 8v-2a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1h-5C7.373 22 2 16.627 2 10Z\"/><path d=\"M17.543 9.704A5.99 5.99 0 0 1 18 12h-1.8A4.199 4.199 0 0 0 12 7.8V6a6 6 0 0 1 5.543 3.704Z\"/></g>"}, "photoscan": {"body": "<g fill=\"currentColor\"><path d=\"M9 8a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M17 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h10Zm-4.535 2H17v11H7v-5.535A4 4 0 0 0 12.465 5ZM9 5a2 2 0 1 0 0 4a2 2 0 0 0 0-4Z\" clip-rule=\"evenodd\"/></g>"}, "piano": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M22 21a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h19ZM11 5H8.985v8h-1v6H12v-6h-1V5Zm7.015 14H22V5h-2.985v8h-1v6Zm-1-6h-1V5H14v8h-1v6h4.015v-6Zm-10.03 6v-6h-1V5H3v14h3.985Z\" clip-rule=\"evenodd\"/>"}, "pill": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.657 2.757a6 6 0 1 1 8.485 8.486l-9.9 9.9a6 6 0 1 1-8.485-8.486l9.9-9.9Zm7.07 7.071l-4.242 4.243l-5.657-5.657l4.243-4.242a4 4 0 1 1 5.657 5.656Z\" clip-rule=\"evenodd\"/>"}, "pin": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M16.272 10.272a4 4 0 1 1-8 0a4 4 0 0 1 8 0Zm-2 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/><path d=\"M5.794 16.518a9 9 0 1 1 12.724-.312l-6.206 6.518l-6.518-6.206Zm11.276-1.691l-4.827 5.07l-5.07-4.827a7 7 0 1 1 9.897-.243Z\"/></g>"}, "pin-alt": {"body": "<g fill=\"currentColor\"><path d=\"M12 11a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M18 9a6.002 6.002 0 0 1-5 5.917V20a1 1 0 1 1-2 0v-5.083A6.002 6.002 0 0 1 12 3a6 6 0 0 1 6 6Zm-6 4a4 4 0 1 0 0-8a4 4 0 0 0 0 8Z\" clip-rule=\"evenodd\"/></g>"}, "pin-bottom": {"body": "<g fill=\"currentColor\"><path d=\"M12 11a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\"/><path fill-rule=\"evenodd\" d=\"M18 9a6.002 6.002 0 0 1-5 5.917V20h3a1 1 0 1 1 0 2H8a1 1 0 1 1 0-2h3v-5.083A6.002 6.002 0 0 1 12 3a6 6 0 0 1 6 6Zm-6 4a4 4 0 1 0 0-8a4 4 0 0 0 0 8Z\" clip-rule=\"evenodd\"/></g>"}, "pin-top": {"body": "<g fill=\"currentColor\"><path d=\"M12 14a2 2 0 1 1 0 4a2 2 0 0 1 0-4Z\"/><path fill-rule=\"evenodd\" d=\"M8 5a1 1 0 0 1 0-2h8a1 1 0 1 1 0 2h-3v5.083A6.002 6.002 0 0 1 12 22a6 6 0 0 1-1-11.917V5H8Zm4 7a4 4 0 1 1 0 8a4 4 0 0 1 0-8Z\" clip-rule=\"evenodd\"/></g>"}, "play-backwards": {"body": "<path fill=\"currentColor\" d=\"M2 7h3v10H2V7Zm4 5l7.002-5v10L6 12Zm15.002-5L14 12l7.002 5V7Z\"/>"}, "play-button": {"body": "<path fill=\"currentColor\" d=\"m15 12.33l-6 4.33V8l6 4.33Z\"/>"}, "play-button-o": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12 21a9 9 0 1 0 0-18a9 9 0 0 0 0 18Zm0 2c6.075 0 11-4.925 11-11S18.075 1 12 1S1 5.925 1 12s4.925 11 11 11Z\" clip-rule=\"evenodd\"/><path d=\"m16 12l-6 4.33V7.67L16 12Z\"/></g>"}, "play-button-r": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2ZM5 1a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4H5Z\" clip-rule=\"evenodd\"/><path d=\"m16 12l-6 4.33V7.67L16 12Z\"/></g>"}, "play-forwards": {"body": "<path fill=\"currentColor\" d=\"M21.002 17h-3V7h3v10Zm-4-5L10 17V7l7.002 5ZM2 17l7.002-5L2 7v10Z\"/>"}, "play-list": {"body": "<path fill=\"currentColor\" d=\"M16 5H4v2h12V5Zm0 4H4v2h12V9ZM4 13h8v2H4v-2Zm16 3l-6-3v6l6-3Z\"/>"}, "play-list-add": {"body": "<g fill=\"currentColor\"><path d=\"M2 5h12v2H2V5Zm0 4h12v2H2V9Zm8 4H2v2h8v-2Z\"/><path d=\"M16 9h2v4h4v2h-4v4h-2v-4h-4v-2h4V9Z\"/></g>"}, "play-list-check": {"body": "<path fill=\"currentColor\" d=\"M15 6H3v2h12V6Zm0 4H3v2h12v-2ZM3 14h8v2H3v-2Zm8.99 1.025l1.415-1.414l2.121 2.121l4.243-4.242l1.414 1.414l-5.657 5.657l-3.535-3.536Z\"/>"}, "play-list-remove": {"body": "<path fill=\"currentColor\" d=\"M15.964 4.634h-12v2h12v-2Zm0 4h-12v2h12v-2Zm-12 4h8v2h-8v-2Zm9 1.076l1.415-1.415l2.121 2.121l2.121-2.12l1.415 1.413l-2.122 2.122l2.122 2.12l-1.415 1.415l-2.121-2.121l-2.121 2.121l-1.415-1.414l2.122-2.122l-2.122-2.12Z\"/>"}, "play-list-search": {"body": "<g fill=\"currentColor\"><path d=\"M15.879 4.879h-12v2h12v-2Zm0 4h-12v2h12v-2Zm-12 4h8v2h-8v-2Z\"/><path fill-rule=\"evenodd\" d=\"M13.757 12.757a3 3 0 0 0 3.415 4.83l1.535 1.534l1.414-1.414l-1.535-1.535a3.001 3.001 0 0 0-4.829-3.415Zm1.415 2.829a1 1 0 1 0 1.414-1.415a1 1 0 0 0-1.414 1.415Z\" clip-rule=\"evenodd\"/></g>"}, "play-pause": {"body": "<path fill=\"currentColor\" d=\"M11 7H8v10h3V7Zm2 10h3V7h-3v10Z\"/>"}, "play-pause-o": {"body": "<g fill=\"currentColor\"><path d=\"M9 9h2v6H9V9Zm6 6h-2V9h2v6Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "play-pause-r": {"body": "<g fill=\"currentColor\"><path d=\"M9 9h2v6H9V9Zm6 6h-2V9h2v6Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "play-stop": {"body": "<path fill=\"currentColor\" d=\"M7 7h10v10H7V7Z\"/>"}, "play-stop-o": {"body": "<g fill=\"currentColor\"><path d=\"M15 9H9v6h6V9Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "play-stop-r": {"body": "<g fill=\"currentColor\"><path d=\"M15 9H9v6h6V9Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "play-track-next": {"body": "<path fill=\"currentColor\" d=\"m6 17l8-5l-8-5v10ZM18 7h-3v10h3V7Z\"/>"}, "play-track-next-o": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 21a9 9 0 1 1 0-18a9 9 0 0 1 0 18ZM1 12C1 5.925 5.925 1 12 1s11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12Zm13-3a1 1 0 1 1 2 0v6a1 1 0 1 1-2 0V9Zm-1 3l-6 3.464V8.536L13 12Z\" clip-rule=\"evenodd\"/>"}, "play-track-next-r": {"body": "<g fill=\"currentColor\"><path d=\"M15 9a1 1 0 1 1 2 0v6a1 1 0 1 1-2 0V9Zm-1 3l-6 3.464V8.536L14 12Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "play-track-prev": {"body": "<path fill=\"currentColor\" d=\"m18 17l-8-5l8-5v10ZM6 7h3v10H6V7Z\"/>"}, "play-track-prev-o": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M8 8a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0V9a1 1 0 0 1 1-1Zm8 7.464L10 12l6-3.464v6.928Z\"/><path d=\"M3 12a9 9 0 1 0 18 0a9 9 0 0 0-18 0Zm9-11C5.925 1 1 5.925 1 12s4.925 11 11 11s11-4.925 11-11S18.075 1 12 1Z\"/></g>"}, "play-track-prev-r": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M8 8a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0V9a1 1 0 0 1 1-1Zm8 7.464L10 12l6-3.464v6.928Z\"/><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2ZM5 1a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4H5Z\"/></g>"}, "plug": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2a1 1 0 0 0-1 1v2a1 1 0 1 0 2 0V3a1 1 0 0 0-1-1ZM8 9h8v2a4 4 0 0 1-8 0V9Zm5 7.917A6.002 6.002 0 0 0 18 11V7H6v4a6.002 6.002 0 0 0 5 5.917V22a1 1 0 1 0 2 0v-5.083ZM14 3a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0V3Z\" clip-rule=\"evenodd\"/>"}, "pocket": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 4h18v7a9 9 0 1 1-18 0V4ZM1 2h22v9c0 6.075-4.925 11-11 11S1 17.075 1 11V2Zm10.293 12.694a1 1 0 0 0 1.414 0l4.243-4.243a1 1 0 1 0-1.414-1.414L12 12.572L8.464 9.037A1 1 0 0 0 7.05 10.45l4.243 4.242Z\" clip-rule=\"evenodd\"/>"}, "pokemon": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 12a9 9 0 1 1 18 0a9 9 0 0 1-18 0Zm2.07 1a7.002 7.002 0 0 0 13.86 0h-4.1a3.001 3.001 0 0 1-5.66 0h-4.1Zm13.86-2a7.001 7.001 0 0 0-13.86 0h4.1a3.001 3.001 0 0 1 5.66 0h4.1ZM12 13a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\" clip-rule=\"evenodd\"/>"}, "polaroid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 4a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4Zm2 0h14v11H5V4Z\" clip-rule=\"evenodd\"/>"}, "poll": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M19 4H5a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1ZM5 2a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V5a3 3 0 0 0-3-3H5Z\" clip-rule=\"evenodd\"/><path d=\"M11 7h2v10h-2V7Zm4 6h2v4h-2v-4Zm-8-3h2v7H7v-7Z\"/></g>"}, "presentation": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10Zm8-10h-2a6 6 0 0 0-12 0H4a8 8 0 1 1 16 0ZM4.252 14h15.496a8.003 8.003 0 0 1-15.496 0ZM8 12a4 4 0 1 1 8 0H8Z\" clip-rule=\"evenodd\"/>"}, "printer": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 4h8v2H8V4Zm10 2h4v12h-4v4H6v-4H2V6h4V2h12v4Zm2 10h-2v-2H6v2H4V8h16v8ZM8 16h8v4H8v-4Zm0-6H6v2h2v-2Z\" clip-rule=\"evenodd\"/>"}, "product-hunt": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 19a7 7 0 1 1 0-14a7 7 0 0 1 0 14Zm-9-7a9 9 0 1 1 18 0a9 9 0 0 1-18 0Zm6 4V8h4a3 3 0 1 1 0 6h-2v2H9Zm5-5a1 1 0 0 1-1 1h-2v-2h2a1 1 0 0 1 1 1Z\" clip-rule=\"evenodd\"/>"}, "profile": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M16 9a4 4 0 1 1-8 0a4 4 0 0 1 8 0Zm-2 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/><path d=\"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11s11-4.925 11-11S18.075 1 12 1ZM3 12c0 2.09.713 4.014 1.908 5.542A8.986 8.986 0 0 1 12.065 14a8.984 8.984 0 0 1 7.092 3.458A9 9 0 1 0 3 12Zm9 9a8.963 8.963 0 0 1-5.672-2.012A6.992 6.992 0 0 1 12.065 16a6.991 6.991 0 0 1 5.689 2.92A8.964 8.964 0 0 1 12 21Z\"/></g>"}, "pull-clear": {"body": "<g fill=\"currentColor\"><path d=\"M4 6H2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6h-2v10H4V6Z\"/><path d=\"M6 12h12v2H6v-2Zm12-4H6v2h12V8Z\"/></g>"}, "push-chevron-down": {"body": "<path fill=\"currentColor\" d=\"M5 7.414L6.414 6l5.657 5.657L17.728 6l1.414 1.414l-7.07 7.071L5 7.415Zm14 8.929H5v2h14v-2Z\"/>"}, "push-chevron-down-o": {"body": "<g fill=\"currentColor\"><path d=\"M16 14v2H8v-2h8ZM7.757 8.703l1.415-1.415L12 10.117l2.828-2.829l1.415 1.415L12 12.945L7.757 8.703Z\"/><path fill-rule=\"evenodd\" d=\"M12 23c6.075 0 11-4.925 11-11S18.075 1 12 1S1 5.925 1 12s4.925 11 11 11Zm0-2a9 9 0 1 0 0-18a9 9 0 0 0 0 18Z\" clip-rule=\"evenodd\"/></g>"}, "push-chevron-down-r": {"body": "<g fill=\"currentColor\"><path d=\"M9.172 7.288L7.757 8.703L12 12.945l4.243-4.242l-1.415-1.415L12 10.117L9.172 7.288ZM8 14h8v2H8v-2Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "push-chevron-left": {"body": "<path fill=\"currentColor\" d=\"m16.929 5l1.414 1.414l-5.657 5.657l5.657 5.657l-1.414 1.414l-7.071-7.07L16.929 5ZM8 19V5H6v14h2Z\"/>"}, "push-chevron-left-o": {"body": "<g fill=\"currentColor\"><path d=\"M10 16H8V8h2v8Zm5.297-8.243l1.415 1.415L13.883 12l2.829 2.828l-1.415 1.415L11.055 12l4.242-4.243Z\"/><path fill-rule=\"evenodd\" d=\"M1 12c0 6.075 4.925 11 11 11s11-4.925 11-11S18.075 1 12 1S1 5.925 1 12Zm2 0a9 9 0 1 0 18 0a9 9 0 0 0-18 0Z\" clip-rule=\"evenodd\"/></g>"}, "push-chevron-left-r": {"body": "<g fill=\"currentColor\"><path d=\"M16.674 9.172L15.26 7.757L11.017 12l4.243 4.243l1.414-1.415L13.846 12l2.828-2.828ZM9.963 8v8h-2V8h2Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "push-chevron-right": {"body": "<path fill=\"currentColor\" d=\"M7.414 5L6 6.414l5.657 5.657L6 17.728l1.414 1.414l7.071-7.07L7.415 5Zm8.929 14V5h2v14h-2Z\"/>"}, "push-chevron-right-o": {"body": "<g fill=\"currentColor\"><path d=\"M14 8h2v8h-2V8Zm-5.297 8.243l-1.415-1.414L10.117 12L7.288 9.172l1.415-1.415L12.945 12l-4.242 4.243Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0-6.075-4.925-11-11-11S1 5.925 1 12s4.925 11 11 11s11-4.925 11-11Zm-2 0a9 9 0 1 0-18 0a9 9 0 0 0 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "push-chevron-right-r": {"body": "<g fill=\"currentColor\"><path d=\"m7.644 14.828l1.415 1.415L13.3 12L9.06 7.757L7.644 9.172L10.473 12l-2.829 2.828ZM14.356 16V8h2v8h-2Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "push-chevron-up": {"body": "<path fill=\"currentColor\" d=\"m5 16.929l1.414 1.414l5.657-5.657l5.657 5.657l1.414-1.414l-7.07-7.071L5 16.929ZM19 8H5V6h14v2Z\"/>"}, "push-chevron-up-o": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 3a9 9 0 1 0 0 18a9 9 0 0 0 0-18Zm0-2C5.925 1 1 5.925 1 12s4.925 11 11 11s11-4.925 11-11S18.075 1 12 1Zm-4 9V8h8v2H8Zm8.243 5.297l-1.414 1.415L12 13.883l-2.828 2.829l-1.415-1.415L12 11.055l4.243 4.242Z\" clip-rule=\"evenodd\"/>"}, "push-chevron-up-r": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 21h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2Zm-4-2a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14Zm8.172-2.288l-1.415-1.415L12 11.055l4.243 4.242l-1.415 1.415L12 13.883l-2.828 2.829ZM8 10h8V8H8v2Z\" clip-rule=\"evenodd\"/>"}, "push-down": {"body": "<path fill=\"currentColor\" d=\"M11 1h2v14.485l3.243-3.242l1.414 1.414L12 19.314l-5.657-5.657l1.414-1.414L11 15.485V1Zm7 19.288H6v2h12v-2Z\"/>"}, "push-left": {"body": "<path fill=\"currentColor\" d=\"M22.288 11v2H7.802l3.243 3.243l-1.414 1.414L3.974 12L9.63 6.343l1.414 1.414L7.802 11h14.486ZM3 18V6H1v12h2Z\"/>"}, "push-right": {"body": "<path fill=\"currentColor\" d=\"M1 13v-2h14.485l-3.242-3.243l1.414-1.414L19.314 12l-5.657 5.657l-1.414-1.415L15.485 13H1Zm19.288-7v12h2V6h-2Z\"/>"}, "push-up": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11 22.288h2V7.802l3.243 3.243l1.414-1.414L12 3.974L6.343 9.63l1.414 1.414L11 7.802v14.486ZM18 3H6V1h12v2Z\" clip-rule=\"evenodd\"/>"}, "qr": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 3H3v6h2V5h4V3ZM3 21v-6h2v4h4v2H3ZM15 3v2h4v4h2V3h-6Zm4 12h2v6h-6v-2h4v-4ZM7 7h4v4H7V7Zm0 6h4v4H7v-4Zm10-6h-4v4h4V7Zm-4 6h4v4h-4v-4Z\" clip-rule=\"evenodd\"/>"}, "quote": {"body": "<path fill=\"currentColor\" d=\"M9.135 9h3L10 14.607H7L9.135 9Zm5 0h3L15 14.607h-3L14.135 9Z\"/>"}, "quote-o": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M20 5H4v14h16V5ZM4 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H4Z\" clip-rule=\"evenodd\"/><path d=\"M9.067 9.196h3l-2.134 5.608h-3l2.134-5.608Zm5 0h3l-2.134 5.608h-3l2.134-5.608Z\"/></g>"}, "radio-check": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 20a8 8 0 1 0 0-16a8 8 0 0 0 0 16Zm0 2c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10Z\" clip-rule=\"evenodd\"/>"}, "radio-checked": {"body": "<g fill=\"currentColor\"><path d=\"M12 16a4 4 0 1 0 0-8a4 4 0 0 0 0 8Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 1-16 0a8 8 0 0 1 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "ratio": {"body": "<g fill=\"currentColor\"><path d=\"M4 6v6h2V8h4V6H4Zm16 12h-6v-2h4v-4h2v6Z\"/><path fill-rule=\"evenodd\" d=\"M4 2a4 4 0 0 0-4 4v12a4 4 0 0 0 4 4h16a4 4 0 0 0 4-4V6a4 4 0 0 0-4-4H4Zm16 2H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Z\" clip-rule=\"evenodd\"/></g>"}, "read": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm3.465-4a4.002 4.002 0 0 0-7.339 1H2a1 1 0 1 0 0 2h1.126A4.002 4.002 0 0 0 11 12h2a4 4 0 0 0 7.874 1H22a1 1 0 1 0 0-2h-1.126a4.002 4.002 0 0 0-7.339-1h-3.07ZM15 12a2 2 0 1 0 4 0a2 2 0 0 0-4 0Z\" clip-rule=\"evenodd\"/>"}, "readme": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 5.5h5a2 2 0 0 1 2 2v9a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-10a1 1 0 0 1 1-1Zm10 14c-.35 0-.687-.06-1-.17v.17a1 1 0 1 1-2 0v-.17c-.313.11-.65.17-1 .17H4a3 3 0 0 1-3-3v-10a3 3 0 0 1 3-3h5a3.99 3.99 0 0 1 3 1.354A3.99 3.99 0 0 1 15 3.5h5a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3h-6Zm-1-12v9a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-10a1 1 0 0 0-1-1h-5a2 2 0 0 0-2 2Zm-8 0h4v2H5v-2Zm10 0h4v2h-4v-2Zm4 3h-4v2h4v-2Zm-14 0h4v2H5v-2Zm14 3h-4v2h4v-2Zm-14 0h4v2H5v-2Z\" clip-rule=\"evenodd\"/>"}, "record": {"body": "<g fill=\"currentColor\"><path d=\"M12 15a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 1-16 0a8 8 0 0 1 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "redo": {"body": "<path fill=\"currentColor\" d=\"m13.146 11.05l-.174-1.992l2.374-.208a5 5 0 1 0 .82 6.173l2.002.5a7 7 0 1 1-1.315-7.996l-.245-2.803L18.6 4.55l.523 5.977l-5.977.523Z\"/>"}, "remote": {"body": "<path fill=\"currentColor\" d=\"m17.051 4.322l1.415 1.414l-4.243 4.243l4.243 4.242l-1.415 1.415l-5.656-5.657l5.656-5.657ZM6.949 19.678l-1.415-1.414l4.243-4.242l-4.243-4.243L6.95 8.365l5.656 5.657l-5.656 5.656Z\"/>"}, "remove": {"body": "<g fill=\"currentColor\"><path d=\"M8 11a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H8Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "remove-r": {"body": "<g fill=\"currentColor\"><path d=\"M8 11a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H8Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "rename": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M10 4H8v2H5a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h3v2h2V4ZM8 8v8H5a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h3Z\" clip-rule=\"evenodd\"/><path d=\"M19 16h-7v2h7a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-7v2h7a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1Z\"/></g>"}, "reorder": {"body": "<g fill=\"currentColor\"><path fill-opacity=\".5\" d=\"M3 4a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Zm0 8a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Zm0 4a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Zm0 4a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1Z\"/><path fill-rule=\"evenodd\" d=\"M15.17 9a3.001 3.001 0 1 0 0-2H4a1 1 0 0 0 0 2h11.17ZM19 8a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\" clip-rule=\"evenodd\"/></g>"}, "repeat": {"body": "<path fill=\"currentColor\" d=\"m18.37 8l-4.5 2.598V9H6.89v4h-2V7h8.98V5.402L18.37 8Zm-8.24 9h8.98v-6h-2v4h-6.98v-1.598L5.63 16l4.5 2.598V17Z\"/>"}, "ring": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M6.343 3.686A8.015 8.015 0 0 1 7.936 2.45a8.014 8.014 0 0 1 8.128 0a8.016 8.016 0 0 1 1.593 1.236L12 9.343L6.343 3.686ZM12 6.514L9.413 3.927a6.017 6.017 0 0 1 5.174 0L12 6.514Z\" clip-rule=\"evenodd\"/><path d=\"M2 12.658a9.98 9.98 0 0 1 3.692-7.76l1.423 1.424a8 8 0 1 0 9.77 0l1.423-1.424A9.98 9.98 0 0 1 22 12.658c0 5.522-4.477 10-10 10s-10-4.478-10-10Z\"/></g>"}, "row-first": {"body": "<g fill=\"currentColor\"><path d=\"M6 11a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H6Zm0 4a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H6Z\" opacity=\".5\"/><path d=\"M5 8a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1Z\"/></g>"}, "row-last": {"body": "<g fill=\"currentColor\"><path d=\"M6 13a1 1 0 1 1 0-2h8a1 1 0 1 1 0 2H6Zm0-4a1 1 0 0 1 0-2h8a1 1 0 1 1 0 2H6Z\" opacity=\".5\"/><path d=\"M5 16a1 1 0 0 0 1 1h12a1 1 0 1 0 0-2H6a1 1 0 0 0-1 1Z\"/></g>"}, "ruler": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 6a3 3 0 0 0-3 3v7a3 3 0 0 0 3 3h18a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3H3Zm6 2H7v5a1 1 0 1 1-2 0V8H3a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1h-2v3a1 1 0 1 1-2 0V8h-2v5a1 1 0 1 1-2 0V8h-2v3a1 1 0 1 1-2 0V8Z\" clip-rule=\"evenodd\"/>"}, "sand-clock": {"body": "<g fill=\"currentColor\"><path d=\"M13 6h-2v1a1 1 0 1 0 2 0V6Z\"/><path fill-rule=\"evenodd\" d=\"M6 2v2h1v3a5 5 0 0 0 5 5a5 5 0 0 0-5 5v3H6v2h12v-2h-1v-3a5 5 0 0 0-5-5a5 5 0 0 0 5-5V4h1V2H6Zm3 2h6v3a3 3 0 1 1-6 0V4Zm0 13v3h6v-3a3 3 0 1 0-6 0Z\" clip-rule=\"evenodd\"/></g>"}, "scan": {"body": "<path fill=\"currentColor\" d=\"M11 3h2v18h-2V3ZM5 8a1 1 0 0 1 1-1h3V5H6a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h3v-2H6a1 1 0 0 1-1-1V8Zm14 0a1 1 0 0 0-1-1h-3V5h3a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3h-3v-2h3a1 1 0 0 0 1-1V8Z\"/>"}, "screen": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11 17H4a3 3 0 0 1-3-3V6a3 3 0 0 1 3-3h16a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3h-7v2h3a1 1 0 1 1 0 2H8a1 1 0 1 1 0-2h3v-2ZM4 5h16a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/>"}, "screen-mirror": {"body": "<g fill=\"currentColor\"><path d=\"M5 8h14v6h-3v2h5V6H3v10h5v-2H5V8Z\"/><path d=\"M16.33 19L12 13l-4.33 6h8.66Z\"/></g>"}, "screen-shot": {"body": "<g fill=\"currentColor\"><path d=\"M12 8V6H8v4h2V8h2Zm2 6h2v4h-4v-2h2v-2Z\"/><path fill-rule=\"evenodd\" d=\"M4 3a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V3Zm2 17V4h12v16H6Z\" clip-rule=\"evenodd\"/></g>"}, "screen-wide": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11 16H3a3 3 0 0 1-3-3V8a3 3 0 0 1 3-3h18a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-8v1h2a1 1 0 1 1 0 2H9a1 1 0 1 1 0-2h2v-1ZM3 7h18a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/>"}, "scroll-h": {"body": "<g fill=\"currentColor\"><path d=\"M7.182 9.172L5.768 7.757L1.525 12l4.243 4.243l1.414-1.415L4.353 12l2.829-2.828Zm9.636 5.656l1.414 1.415L22.475 12l-4.243-4.243l-1.414 1.415L19.646 12l-2.828 2.828Z\"/><path fill-rule=\"evenodd\" d=\"M15 12a3 3 0 1 1-6 0a3 3 0 0 1 6 0Zm-2 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\" clip-rule=\"evenodd\"/></g>"}, "scroll-v": {"body": "<g fill=\"currentColor\"><path d=\"m9.172 16.818l-1.415 1.414L12 22.475l4.243-4.243l-1.415-1.414L12 19.647l-2.828-2.829Zm5.656-9.636l1.415-1.414L12 1.525L7.757 5.768l1.415 1.414L12 4.354l2.828 2.828Z\"/><path fill-rule=\"evenodd\" d=\"M12 9a3 3 0 1 1 0 6a3 3 0 0 1 0-6Zm0 2a1 1 0 1 1 0 2a1 1 0 0 1 0-2Z\" clip-rule=\"evenodd\"/></g>"}, "search": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M18.319 14.433A8.001 8.001 0 0 0 6.343 3.868a8 8 0 0 0 10.564 11.976l.043.045l4.242 4.243a1 1 0 1 0 1.415-1.415l-4.243-4.242a1.116 1.116 0 0 0-.045-.042Zm-2.076-9.15a6 6 0 1 1-8.485 8.485a6 6 0 0 1 8.485-8.485Z\" clip-rule=\"evenodd\"/>"}, "search-found": {"body": "<g fill=\"currentColor\"><path d=\"M7.665 10.237L9.198 8.95l1.285 1.532l3.064-2.571l1.286 1.532l-4.596 3.857l-2.572-3.064Z\"/><path fill-rule=\"evenodd\" d=\"M16.207 4.893a8.001 8.001 0 0 1 .662 10.565c.**************.045.042l4.243 4.243a1 1 0 0 1-1.414 1.414L15.5 16.914a1.046 1.046 0 0 1-.042-.045A8.001 8.001 0 0 1 4.893 4.893a8 8 0 0 1 11.314 0Zm-1.414 9.9a6 6 0 1 0-8.485-8.485a6 6 0 0 0 8.485 8.485Z\" clip-rule=\"evenodd\"/></g>"}, "search-loading": {"body": "<g fill=\"currentColor\"><path d=\"M8.55 10.55a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm2 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm3 0a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M16.207 4.893a8.001 8.001 0 0 1 .662 10.565c.**************.045.042l4.243 4.243a1 1 0 0 1-1.414 1.414L15.5 16.914a.933.933 0 0 1-.042-.045A8.001 8.001 0 0 1 4.893 4.893a8 8 0 0 1 11.314 0Zm-9.9 9.9a6 6 0 1 0 8.486-8.485a6 6 0 0 0-8.485 8.485Z\" clip-rule=\"evenodd\"/></g>"}, "select": {"body": "<path fill=\"currentColor\" d=\"m6 9.657l1.414 1.414l4.243-4.243l4.242 4.243l1.415-1.414L11.657 4L6 9.657Zm0 4.786l1.414-1.414l4.243 4.243l4.242-4.243l1.415 1.414l-5.657 5.657L6 14.443Z\"/>"}, "select-o": {"body": "<g fill=\"currentColor\"><path d=\"m9.172 11.508l-1.415-1.414L12 5.85l4.243 4.243l-1.415 1.414L12 8.68l-2.828 2.828Zm0 .984l-1.415 1.414L12 18.15l4.243-4.243l-1.415-1.414L12 15.32l-2.828-2.828Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "select-r": {"body": "<g fill=\"currentColor\"><path d=\"m9.172 11.508l-1.415-1.414L12 5.85l4.243 4.243l-1.415 1.414L12 8.68l-2.828 2.828Zm0 .984l-1.415 1.414L12 18.15l4.243-4.243l-1.415-1.414L12 15.32l-2.828-2.828Z\"/><path fill-rule=\"evenodd\" d=\"M1 5a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H5a4 4 0 0 1-4-4V5Zm4-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/></g>"}, "server": {"body": "<g fill=\"currentColor\"><path d=\"M9 6a1 1 0 0 0 0 2h6a1 1 0 1 0 0-2H9Zm0 4a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2H9Zm4 7a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path fill-rule=\"evenodd\" d=\"M4 5a3 3 0 0 1 3-3h10a3 3 0 0 1 3 3v14a3 3 0 0 1-3 3H7a3 3 0 0 1-3-3V5Zm3-1h10a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "serverless": {"body": "<path fill=\"currentColor\" d=\"M11.787 6H5v3h5.695l1.092-3Zm-1.82 5H5v3h3.875l1.092-3Zm1.037 3l1.092-3H20v3h-8.996Zm-2.856 2H5v3h2.056l1.092-3Zm1.036 3l1.092-3H20v3H9.184Zm3.64-10l1.092-3H20v3h-7.176Z\"/>"}, "shape-circle": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 17a5 5 0 1 0 0-10a5 5 0 0 0 0 10Zm0 3a8 8 0 1 0 0-16a8 8 0 0 0 0 16Z\" clip-rule=\"evenodd\"/>"}, "shape-half-circle": {"body": "<path fill=\"currentColor\" d=\"M16 4a8 8 0 1 0 0 16v-3a5 5 0 0 1 0-10V4Z\"/>"}, "shape-hexagon": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m6 15.235l6 3.333l6-3.333v-6.47l-6-3.333l-6 3.333v6.47ZM12 2L3 7v10l9 5l9-5V7l-9-5Z\" clip-rule=\"evenodd\"/>"}, "shape-rhombus": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 6.343L6.343 12L12 17.657L17.657 12L12 6.343ZM2.1 12l9.9 9.9l9.9-9.9L12 2.1L2.1 12Z\" clip-rule=\"evenodd\"/>"}, "shape-square": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17 7H7v10h10V7ZM4 4v16h16V4H4Z\" clip-rule=\"evenodd\"/>"}, "shape-triangle": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11.66 5L3 20h17.32L11.66 5Zm0 6l-3.464 6h6.928l-3.464-6Z\" clip-rule=\"evenodd\"/>"}, "shape-zigzag": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2.312 9L1 10.51l3.774 3.28l1.509 1.312l1.312-1.51l1.54-1.77l2.264 1.968l1.51 1.312l1.311-1.51l1.538-1.769l2.263 1.967l1.51 1.312l1.311-1.51l1.969-2.264l-1.51-1.312l-1.968 2.264L15.559 9l-1.312 1.51h.002l-1.538 1.77L8.937 9l-.883 1.016l-1.968 2.264L2.312 9Z\" clip-rule=\"evenodd\"/>"}, "share": {"body": "<path fill=\"currentColor\" d=\"M18 9a3 3 0 1 0-2.977-2.63l-6.94 3.47a3 3 0 1 0 0 4.319l6.94 3.47a3 3 0 1 0 .895-1.789l-6.94-3.47a3.03 3.03 0 0 0 0-.74l6.94-3.47C16.456 8.68 17.19 9 18 9Z\"/>"}, "shield": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7 8v5a5 5 0 0 0 10 0V8H7ZM5 4h14v9a7 7 0 1 1-14 0V4Z\" clip-rule=\"evenodd\"/>"}, "shopping-bag": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 4h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1ZM2 5a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v14a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V5Zm10 7c-2.761 0-5-2.686-5-6h2c0 2.566 1.67 4 3 4s3-1.434 3-4h2c0 3.314-2.239 6-5 6Z\" clip-rule=\"evenodd\"/>"}, "shopping-cart": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M5.792 2H1v2h3.218l2.77 12.678H7V17h13v-.248l2.193-9.661L22.531 6H6.655l-.57-2.611L5.792 2Zm14.195 6H7.092l1.529 7h9.777l1.589-7Z\" clip-rule=\"evenodd\"/><path d=\"M10 22a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm9-2a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/></g>"}, "shortcut": {"body": "<g fill=\"currentColor\"><path d=\"M16.192 7.707a1 1 0 0 0-1.414 0l-7.07 7.071a1 1 0 1 0 1.413 1.414l7.071-7.07a1 1 0 0 0 0-1.415Z\"/><path fill-rule=\"evenodd\" d=\"M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm3-1h12a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "shutterstock": {"body": "<g fill=\"currentColor\"><path d=\"M12 17a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-1v4h-4v1ZM11 6a1 1 0 0 1 1 1v1H8v4H7a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h4Z\"/><path fill-rule=\"evenodd\" d=\"M5 2a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V5a3 3 0 0 0-3-3H5Zm14 2H5a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "sidebar": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M21 20H7V4h14v16Zm-2-2H9V6h10v12Z\" clip-rule=\"evenodd\"/><path d=\"M3 20h2V4H3v16Z\"/></g>"}, "sidebar-open": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 4h18v16H3V4Zm6 2h10v12H9V6Z\" clip-rule=\"evenodd\"/>"}, "sidebar-right": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M3 4h14v16H3V4Zm2 2h10v12H5V6Z\" clip-rule=\"evenodd\"/><path d=\"M21 4h-2v16h2V4Z\"/></g>"}, "signal": {"body": "<path fill=\"currentColor\" d=\"M15 7a1 1 0 1 1 2 0v10a1 1 0 1 1-2 0V7Zm-8 8a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0v-2Zm5-5a1 1 0 0 0-1 1v6a1 1 0 1 0 2 0v-6a1 1 0 0 0-1-1Z\"/>"}, "size": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 6V4h8v16h-8v-2H8v-2H4V8h4V6h4Zm2 0h4v12h-4V6Zm-2 2h-2v8h2V8Zm-4 2v4H6v-4h2Z\" clip-rule=\"evenodd\"/>"}, "sketch": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M5.209 3h13.694l1.209 7.253l-8.056 10.933L4 10.253L5.209 3Zm1.694 2l-.791 4.747l5.944 8.067L18 9.747L17.209 5H6.903Z\" clip-rule=\"evenodd\"/><path d=\"M8.056 8h8v2h-8V8Z\"/></g>"}, "slack": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M13 10a2 2 0 1 0 4 0V5a2 2 0 1 0-4 0v5ZM5 8a2 2 0 1 0 0 4h5a2 2 0 1 0 0-4H5Zm10 5a2 2 0 1 0 0 4h5a2 2 0 1 0 0-4h-5Zm-5 9a2 2 0 0 1-2-2v-5a2 2 0 1 1 4 0v5a2 2 0 0 1-2 2ZM8 5a2 2 0 1 1 4 0v2h-2a2 2 0 0 1-2-2ZM3 15a2 2 0 1 0 4 0v-2H5a2 2 0 0 0-2 2Zm14 5a2 2 0 1 1-4 0v-2h2a2 2 0 0 1 2 2Zm5-10a2 2 0 1 0-4 0v2h2a2 2 0 0 0 2-2Z\" clip-rule=\"evenodd\"/>"}, "sleep": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M21 12a9 9 0 1 1-18 0a9 9 0 0 1 18 0Zm-4.101 5A6.977 6.977 0 0 1 12 19a6.977 6.977 0 0 1-4.899-2h9.798Zm1.427-2a7 7 0 1 0-12.653 0h12.653Z\" clip-rule=\"evenodd\"/>"}, "smart-home-boiler": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5a4 4 0 0 1 4-4h6a4 4 0 0 1 4 4v16h-3.856l.742 2h-2l-.742-2h-2l.742 2h-2l-.742-2H5V5Zm4-2h6a2 2 0 0 1 2 2v2H7V5a2 2 0 0 1 2-2ZM7 9h10v10H7V9Z\" clip-rule=\"evenodd\"/>"}, "smart-home-cooker": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M15 16a3 3 0 1 1-6 0a3 3 0 0 1 6 0Zm-2 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path d=\"M15 1H9v2h2v2H7a4 4 0 0 0-4 4v10a4 4 0 0 0 4 4h10a4 4 0 0 0 4-4V9a4 4 0 0 0-4-4h-4V3h2V1Zm2 6H7a2 2 0 0 0-2 2h14a2 2 0 0 0-2-2Zm2 4H5v8a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-8Z\"/></g>"}, "smart-home-heat": {"body": "<g fill=\"currentColor\"><path d=\"M17 11H7a1 1 0 1 0 0 2h3v2H7a3 3 0 1 1 0-6h10a3 3 0 1 1 0 6h-3v-2h3a1 1 0 1 0 0-2Z\"/><path fill-rule=\"evenodd\" d=\"M0 12a7 7 0 0 1 7-7h10a7 7 0 1 1 0 14H7a7 7 0 0 1-7-7Zm7-5h10a5 5 0 0 1 0 10H7A5 5 0 0 1 7 7Z\" clip-rule=\"evenodd\"/></g>"}, "smart-home-light": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M7.034 6.5a5 5 0 0 1 10 0v4a5 5 0 0 1-10 0v-4Zm8 0v4a3 3 0 0 1-6 0v-4a3 3 0 0 1 6 0Z\" clip-rule=\"evenodd\"/><path d=\"M12.034 16.5a1 1 0 0 0-1 1v4a1 1 0 1 0 2 0v-4a1 1 0 0 0-1-1Zm-4.29-.06a1 1 0 1 1 1.88.684l-1.368 3.759a1 1 0 1 1-1.88-.684l1.368-3.76Zm7.23-.598a1 1 0 0 0-.598 1.282l1.369 3.759a1 1 0 1 0 1.879-.684l-1.368-3.76a1 1 0 0 0-1.282-.597Z\"/></g>"}, "smart-home-refrigerator": {"body": "<g fill=\"currentColor\"><path d=\"M9 6a1 1 0 0 1 2 0v2a1 1 0 1 1-2 0V6Zm1 7a1 1 0 0 0-1 1v2a1 1 0 1 0 2 0v-2a1 1 0 0 0-1-1Z\"/><path fill-rule=\"evenodd\" d=\"M5 4a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3v16a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V4Zm3-1h8a1 1 0 0 1 1 1v6H7V4a1 1 0 0 1 1-1Zm-1 9h10v8a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1v-8Z\" clip-rule=\"evenodd\"/></g>"}, "smart-home-wash-machine": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6 4h12a1 1 0 0 1 1 1v3H5V5a1 1 0 0 1 1-1Zm13 15v-9H5v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1ZM3 5a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v14a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V5Zm4 0a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2H7Zm7 2a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm4-1a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm-4 9a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm2 0a4 4 0 1 1-8 0a4 4 0 0 1 8 0Z\" clip-rule=\"evenodd\"/>"}, "smartphone": {"body": "<g fill=\"currentColor\"><path d=\"M13 16h-2v2h2v-2Z\"/><path fill-rule=\"evenodd\" d=\"M5 4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V4Zm2 0h10v16H7V4Z\" clip-rule=\"evenodd\"/></g>"}, "smartphone-chip": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M9 22a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm4 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path d=\"M9 2a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm4 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2ZM9 22a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm4 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm7-7a1 1 0 1 0-2 0a1 1 0 0 0 2 0Zm0-4a1 1 0 1 0-2 0a1 1 0 0 0 2 0Zm-1-5a1 1 0 1 1 0 2a1 1 0 0 1 0-2ZM2 15a1 1 0 1 1 0 2a1 1 0 0 1 0-2Zm0-4a1 1 0 1 1 0 2a1 1 0 0 1 0-2Zm1-3a1 1 0 1 0-2 0a1 1 0 0 0 2 0Zm14-2H7a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1ZM7 4a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3H7Zm7 6h-4v4h4v-4ZM8 8v8h8V8H8Z\"/></g>"}, "smartphone-ram": {"body": "<g fill=\"currentColor\"><path d=\"M5 4a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm4 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm5-1a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2ZM5 20a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm4 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm5-1a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2ZM5 12a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm15 1a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path fill-rule=\"evenodd\" d=\"M0 9a3 3 0 0 1 3-3h18a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3H3a3 3 0 0 1-3-3V9Zm3-1h18a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "smartphone-shake": {"body": "<g fill=\"currentColor\"><path d=\"M13 14h-2v2h2v-2Z\"/><path fill-rule=\"evenodd\" d=\"M8 7a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V7Zm2 0h4v10h-4V7Z\" clip-rule=\"evenodd\"/><path d=\"M18 9h2v6h-2V9ZM0 14h2v-4H0v4Zm6 1H4V9h2v6Zm18-5h-2v4h2v-4Z\"/></g>"}, "smile": {"body": "<g fill=\"currentColor\"><path d=\"M16 13h-2a2 2 0 1 1-4 0H8a4 4 0 0 0 8 0Zm-6-3a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm5 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 1-16 0a8 8 0 0 1 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "smile-mouth-open": {"body": "<g fill=\"currentColor\"><path d=\"M12 17a4 4 0 0 0 4-4H8a4 4 0 0 0 4 4Zm-2-7a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm5 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 1-16 0a8 8 0 0 1 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "smile-neutral": {"body": "<g fill=\"currentColor\"><path d=\"M9 11a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm0 4a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2H9Zm7-5a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 1-16 0a8 8 0 0 1 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "smile-no-mouth": {"body": "<g fill=\"currentColor\"><path d=\"M10 10a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm5 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10Zm0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16Z\" clip-rule=\"evenodd\"/></g>"}, "smile-none": {"body": "<g fill=\"currentColor\"><path d=\"M8 9a1 1 0 0 0 0 2h1a1 1 0 1 0 0-2H8Zm7 0a1 1 0 1 0 0 2h1a1 1 0 1 0 0-2h-1Zm-6 6a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2H9Z\"/><path fill-rule=\"evenodd\" d=\"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10Zm0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16Z\" clip-rule=\"evenodd\"/></g>"}, "smile-sad": {"body": "<g fill=\"currentColor\"><path d=\"M9 11a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm5 6a2 2 0 1 0-4 0H8a4 4 0 0 1 8 0h-2Zm2-7a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 1-16 0a8 8 0 0 1 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "smile-upside": {"body": "<g fill=\"currentColor\"><path d=\"M16 11h-2a2 2 0 1 0-4 0H8a4 4 0 1 1 8 0Zm-6 3a1 1 0 1 0-2 0a1 1 0 0 0 2 0Zm5-1a1 1 0 1 1 0 2a1 1 0 0 1 0-2Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10Zm-2 0a8 8 0 1 0-16 0a8 8 0 0 0 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "software-download": {"body": "<g fill=\"currentColor\"><path d=\"M11 5a1 1 0 1 1 2 0v7.158l3.243-3.243l1.414 1.414L12 15.986L6.343 10.33l1.414-1.414L11 12.158V5Z\"/><path d=\"M4 14h2v4h12v-4h2v4a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-4Z\"/></g>"}, "software-upload": {"body": "<g fill=\"currentColor\"><path d=\"M11 14.986a1 1 0 1 0 2 0V7.828l3.243 3.243l1.414-1.414L12 4L6.343 9.657l1.414 1.414L11 7.83v7.157Z\"/><path d=\"M4 14h2v4h12v-4h2v4a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-4Z\"/></g>"}, "sort-az": {"body": "<path fill=\"currentColor\" d=\"M6 8a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1Zm2 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1Zm3 3a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2h-2Z\"/>"}, "sort-za": {"body": "<path fill=\"currentColor\" d=\"M6 16a1 1 0 0 0 1 1h10a1 1 0 1 0 0-2H7a1 1 0 0 0-1 1Zm2-4a1 1 0 0 0 1 1h6a1 1 0 1 0 0-2H9a1 1 0 0 0-1 1Zm3-3a1 1 0 1 1 0-2h2a1 1 0 1 1 0 2h-2Z\"/>"}, "space-between": {"body": "<path fill=\"currentColor\" d=\"M19 5h-4v14h4v-2h-2V7h2V5ZM5 5h4v14H5v-2h2V7H5V5Zm8 2v10h-2V7h2Z\"/>"}, "space-between-v": {"body": "<path fill=\"currentColor\" d=\"M5 5v4h14V5h-2v2H7V5H5Zm0 14v-4h14v4h-2v-2H7v2H5Zm2-8h10v2H7v-2Z\"/>"}, "spectrum": {"body": "<path fill=\"currentColor\" d=\"M12 16h4a8 8 0 0 0-8-8v4a4 4 0 0 1 4 4Z\"/>"}, "spinner": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12 19a7 7 0 1 0 0-14a7 7 0 0 0 0 14Zm0 3c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10Z\" clip-rule=\"evenodd\" opacity=\".2\"/><path d=\"M2 12C2 6.477 6.477 2 12 2v3a7 7 0 0 0-7 7H2Z\"/></g>"}, "spinner-alt": {"body": "<path fill=\"currentColor\" d=\"M2 12C2 6.477 6.477 2 12 2v3a7 7 0 0 0-7 7H2Z\"/>"}, "spinner-two": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12 19a7 7 0 1 0 0-14a7 7 0 0 0 0 14Zm0 3c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10Z\" clip-rule=\"evenodd\" opacity=\".2\"/><path d=\"M12 22c5.523 0 10-4.477 10-10h-3a7 7 0 0 1-7 7v3ZM2 12C2 6.477 6.477 2 12 2v3a7 7 0 0 0-7 7H2Z\"/></g>"}, "spinner-two-alt": {"body": "<path fill=\"currentColor\" d=\"M12 22c5.523 0 10-4.477 10-10h-3a7 7 0 0 1-7 7v3ZM2 12C2 6.477 6.477 2 12 2v3a7 7 0 0 0-7 7H2Z\"/>"}, "square": {"body": "<g fill=\"currentColor\"><path d=\"M14 10h-4v4h4v-4Z\"/><path fill-rule=\"evenodd\" d=\"M5 9a4 4 0 0 1 4-4h6a4 4 0 0 1 4 4v6a4 4 0 0 1-4 4H9a4 4 0 0 1-4-4V9Zm4-1h6a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "stack": {"body": "<g fill=\"currentColor\"><path d=\"M20 4v12h2V2H8v2h12Z\"/><path fill-rule=\"evenodd\" d=\"M2 8v14h14V8H2Zm12 2H4v10h10V10Z\" clip-rule=\"evenodd\"/><path d=\"M17 7H5V5h14v14h-2V7Z\"/></g>"}, "stark": {"body": "<path fill=\"currentColor\" d=\"M7.172 18.025a8 8 0 0 1 4.935-14.948l-.437 3.126a4.844 4.844 0 0 0-2.988 9.05l6.146-11.278l2.634 1.436a8 8 0 0 1-4.934 14.948l.436-3.126a4.844 4.844 0 0 0 2.988-9.05L9.806 19.46l-2.634-1.435Z\"/>"}, "stopwatch": {"body": "<g fill=\"currentColor\"><path d=\"m18.621 2.55l2.829 2.83l-1.414 1.414l-2.829-2.828l1.414-1.415ZM12.822 8.6h-2v4h2v-4Z\"/><path fill-rule=\"evenodd\" d=\"M5.186 18.814A9 9 0 1 0 17.914 6.086A9 9 0 0 0 5.186 18.814Zm1.415-1.415A7 7 0 1 0 16.5 7.5a7 7 0 0 0-9.9 9.9Z\" clip-rule=\"evenodd\"/></g>"}, "stories": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M15 6H9a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1ZM9 4a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h6a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3H9Z\" clip-rule=\"evenodd\"/><path d=\"M2 6a1 1 0 0 1 2 0v12a1 1 0 1 1-2 0V6Zm18 0a1 1 0 1 1 2 0v12a1 1 0 1 1-2 0V6Z\"/></g>"}, "studio": {"body": "<g fill=\"currentColor\"><path d=\"M17 13h-4v4h4v-4Z\"/><path fill-rule=\"evenodd\" d=\"M3 3h18v18H3V3Zm2 2h14v14H5V5Z\" clip-rule=\"evenodd\"/></g>"}, "style": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M13 21v-8h8v8h-8Zm2-6h4v4h-4v-4ZM3 11V3h8v8H3Zm2-6h4v4H5V5Z\" clip-rule=\"evenodd\"/><path d=\"M18 6v6h-2V8h-4V6h6Zm-6 12H6v-6h2v4h4v2Z\"/></g>"}, "sun": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 16a4 4 0 1 0 0-8a4 4 0 0 0 0 8Zm0 2a6 6 0 1 0 0-12a6 6 0 0 0 0 12ZM11 0h2v4.062a8.079 8.079 0 0 0-2 0V0ZM7.094 5.68L4.222 2.808L2.808 4.222L5.68 7.094A8.048 8.048 0 0 1 7.094 5.68ZM4.062 11H0v2h4.062a8.079 8.079 0 0 1 0-2Zm1.618 5.906l-2.872 2.872l1.414 1.414l2.872-2.872a8.048 8.048 0 0 1-1.414-1.414ZM11 19.938V24h2v-4.062a8.069 8.069 0 0 1-2 0Zm5.906-1.618l2.872 2.872l1.414-1.414l-2.872-2.872a8.048 8.048 0 0 1-1.414 1.414ZM19.938 13H24v-2h-4.062a8.069 8.069 0 0 1 0 2ZM18.32 7.094l2.872-2.872l-1.414-1.414l-2.872 2.872c.528.41 1.003.886 1.414 1.414Z\" clip-rule=\"evenodd\"/>"}, "support": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.26 21.997a10.276 10.276 0 0 1-.52 0a10.004 10.004 0 0 1-8.983-6.173a10.034 10.034 0 0 1 .017-7.69A10.015 10.015 0 0 1 4.908 4.95l.042-.042a10.015 10.015 0 0 1 3.167-2.126a10.034 10.034 0 0 1 7.753-.006a10.015 10.015 0 0 1 3.186 2.138l.03.03c.913.917 1.65 2.01 2.153 3.223a10.012 10.012 0 0 1 .76 3.985a10.004 10.004 0 0 1-6.226 9.112a10.013 10.013 0 0 1-3.512.733Zm1.772-6.55l2.874 2.873a8.004 8.004 0 0 1-9.812 0l2.874-2.874a4.007 4.007 0 0 0 4.064 0Zm-5.478-1.415L5.68 16.906a8.004 8.004 0 0 1 0-9.812l2.874 2.874a4.007 4.007 0 0 0 0 4.064Zm1.528-1.463a2.01 2.01 0 0 1-.014-1.087a1.99 1.99 0 0 1 .518-.896a1.99 1.99 0 0 1 1.932-.518c.328.088.639.26.896.518a1.99 1.99 0 0 1 .518 1.932c-.088.328-.26.639-.518.896a1.99 1.99 0 0 1-1.932.518a1.991 1.991 0 0 1-.896-.518a1.99 1.99 0 0 1-.504-.845Zm3.95-4.015a4.007 4.007 0 0 0-4.064 0L7.094 5.68a8.004 8.004 0 0 1 9.812 0l-2.874 2.874Zm4.288 8.352a8.004 8.004 0 0 0 0-9.812l-2.874 2.874a4.007 4.007 0 0 1 0 4.064l2.874 2.874Z\" clip-rule=\"evenodd\"/>"}, "swap": {"body": "<path fill=\"currentColor\" d=\"M16 13v-1.5h-6v-2h6V8l3 2.5l-3 2.5Zm-8 4v-1.5h6v-2H8V12l-3 2.5L8 17Z\"/>"}, "swap-vertical": {"body": "<path fill=\"currentColor\" d=\"M12 16h1.5v-6h2v6H17l-2.5 3l-2.5-3ZM8 8h1.5v6h2V8H13l-2.5-3L8 8Z\"/>"}, "sweden": {"body": "<path fill=\"currentColor\" d=\"M23 4H10v7h13V4Zm0 9v7H10v-7h13ZM8 13v7H1v-7h7Zm-7-2V4h7v7H1Z\"/>"}, "swiss": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 3v18h18V3H3Zm11 4h-4v3H7v4h3v3h4v-3h3v-4h-3V7Z\" clip-rule=\"evenodd\"/>"}, "sync": {"body": "<path fill=\"currentColor\" d=\"M4.56 10.642L6.355 3.95l1.9 1.9a9.004 9.004 0 0 1 11.156 1.256l-1.414 1.415a7.003 7.003 0 0 0-8.28-1.21l1.537 1.538l-6.692 1.793Zm14.88 2.716l-1.794 6.692l-1.9-1.9A9.003 9.003 0 0 1 4.59 16.894l1.414-1.415a7.003 7.003 0 0 0 8.28 1.21l-1.537-1.538l6.692-1.793Z\"/>"}, "tab": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M19 4a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V7a3 3 0 0 1 3-3h14Zm1 5.625h-7c-.552 0-1.156-.42-1.348-.938L10.654 6H5a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V9.625Z\" clip-rule=\"evenodd\"/>"}, "tag": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M2 8v8a1 1 0 0 0 1 1h13.62a1 1 0 0 0 .76-.35l3.428-4a1 1 0 0 0 0-1.3l-3.428-4a1 1 0 0 0-.76-.35H3a1 1 0 0 0-1 1ZM0 8v8a3 3 0 0 0 3 3h13.62a3 3 0 0 0 2.278-1.048l3.428-4a3 3 0 0 0 0-3.904l-3.428-4A3 3 0 0 0 16.62 5H3a3 3 0 0 0-3 3Z\"/><path d=\"M15 13a1 1 0 1 0 0-2a1 1 0 0 0 0 2Zm0 2a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\"/></g>"}, "tally": {"body": "<path fill=\"currentColor\" d=\"M3.661 2.671a.63.63 0 0 1 .595-.49h2c.276 0 .457.219.405.49L5.34 9.545a.63.63 0 0 1-.595.491h-2a.397.397 0 0 1-.405-.49L3.66 2.67Zm5 0a.63.63 0 0 1 .595-.49h2c.276 0 .457.219.405.49L8.071 21.33a.63.63 0 0 1-.594.491h-2a.397.397 0 0 1-.405-.491l3.59-18.658Zm5 0a.63.63 0 0 1 .595-.49h2c.276 0 .457.219.405.49l-3.59 18.658a.63.63 0 0 1-.594.491h-2a.397.397 0 0 1-.405-.491l3.59-18.658Zm5 0a.63.63 0 0 1 .595-.49h2c.276 0 .457.219.405.49L20.34 9.545a.63.63 0 0 1-.595.491h-2a.397.397 0 0 1-.405-.49l1.32-6.876Z\"/>"}, "tap-double": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M14.924 18v-4a3 3 0 0 0-6 0v4a3 3 0 1 0 6 0Zm-3-9a5 5 0 0 0-5 5v4a5 5 0 0 0 10 0v-4a5 5 0 0 0-5-5Z\" clip-rule=\"evenodd\"/><path d=\"M10.924 14a1 1 0 1 1 2 0v3h-2v-3Zm1-13a9.97 9.97 0 0 1 7.105 2.963l-1.415 1.414A7.976 7.976 0 0 0 11.924 3c-2.15 0-4.1.847-5.538 2.227L4.97 3.812A9.967 9.967 0 0 1 11.924 1Z\"/><path fill-rule=\"evenodd\" d=\"M11.923 5a6.97 6.97 0 0 1 4.38 1.539l-1.426 1.426A4.978 4.978 0 0 0 11.923 7c-1.075 0-2.071.34-2.886.917l-1.43-1.429A6.97 6.97 0 0 1 11.924 5Z\" clip-rule=\"evenodd\"/></g>"}, "tap-single": {"body": "<g fill=\"currentColor\"><path d=\"M12.05 3.114c2.143 0 4.09.843 5.526 2.216L16.16 6.744a5.98 5.98 0 0 0-4.112-1.63a5.98 5.98 0 0 0-4.21 1.725L6.424 5.425a7.974 7.974 0 0 1 5.625-2.311Zm-1.073 8.772a1 1 0 1 1 2 0v2h-2v-2Z\"/><path fill-rule=\"evenodd\" d=\"M11.977 6.886a5 5 0 0 0-5 5v4a5 5 0 0 0 10 0v-4a5 5 0 0 0-5-5Zm3 9v-4a3 3 0 0 0-6 0v4a3 3 0 0 0 6 0Z\" clip-rule=\"evenodd\"/></g>"}, "template": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M3 3v6h18V3H3Zm16 2H5v2h14V5ZM3 11v10h8V11H3Zm6 2H5v6h4v-6Z\" clip-rule=\"evenodd\"/><path d=\"M21 11h-8v2h8v-2Zm-8 4h8v2h-8v-2Zm8 4h-8v2h8v-2Z\"/></g>"}, "tennis": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M19.071 19.071c3.905-3.905 3.905-10.237 0-14.142c-3.905-3.905-10.237-3.905-14.142 0c-3.905 3.905-3.905 10.237 0 14.142c3.905 3.905 10.237 3.905 14.142 0Zm.872-8.03a7.966 7.966 0 0 0-2.286-4.698a7.966 7.966 0 0 0-4.717-2.288l-.01.056a11.011 11.011 0 0 1-8.819 8.819l-.056.01a7.966 7.966 0 0 0 2.288 4.717a7.966 7.966 0 0 0 4.698 2.286l.012-.07a11.011 11.011 0 0 1 8.819-8.82l.07-.012Zm-.071 2.388v-.334a9.013 9.013 0 0 0-6.777 6.777h.334a7.964 7.964 0 0 0 4.228-2.215a7.963 7.963 0 0 0 2.215-4.228Zm-15.76-2.54v-.223a7.963 7.963 0 0 1 2.231-4.323a7.964 7.964 0 0 1 4.323-2.232h.222a9.013 9.013 0 0 1-6.777 6.777Z\" clip-rule=\"evenodd\"/>"}, "terminal": {"body": "<g fill=\"currentColor\"><path d=\"m5.033 14.828l1.415 1.415L10.69 12L6.448 7.757L5.033 9.172L7.862 12l-2.829 2.828ZM15 14h-4v2h4v-2Z\"/><path fill-rule=\"evenodd\" d=\"M2 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2Zm20 2H2v16h20V4Z\" clip-rule=\"evenodd\"/></g>"}, "terrain": {"body": "<path fill=\"currentColor\" d=\"m8 10l-5 8h10l-5-8Zm2.529.754L13.5 6L21 18h-5.943l-4.528-7.246Z\"/>"}, "thermometer": {"body": "<g fill=\"currentColor\"><path d=\"M16.95 5.636a1 1 0 1 1 1.414 1.414l-7.071 7.071a1 1 0 1 1-1.414-1.414l7.07-7.07Z\"/><path fill-rule=\"evenodd\" d=\"M7.828 17.586a5.002 5.002 0 0 0 6.293-.636l7.071-7.071a5 5 0 1 0-7.07-7.071L7.05 9.878a5.002 5.002 0 0 0-.636 6.294l-3.606 3.606a1 1 0 1 0 1.414 1.415l3.606-3.607Zm4.88-2.05l7.07-7.071a3 3 0 1 0-4.242-4.243l-7.071 7.071a3 3 0 1 0 4.242 4.243Z\" clip-rule=\"evenodd\"/></g>"}, "thermostat": {"body": "<g fill=\"currentColor\"><path d=\"M12 19a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M15 14a5 5 0 1 1-6 0V4a3 3 0 1 1 6 0v10ZM13 4v11.17A3.001 3.001 0 0 1 12 21a3 3 0 0 1-1-5.83V4a1 1 0 1 1 2 0Z\" clip-rule=\"evenodd\"/></g>"}, "tikcode": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M9 5H5v4h4V5ZM3 3v8h8V3H3Zm16 2h-4v4h4V5Zm-6-2v8h8V3h-8ZM9 15H5v4h4v-4Zm-6-2v8h8v-8H3Z\" clip-rule=\"evenodd\"/><path d=\"M13 13h2v8h-2v-8Zm3 0h2v8h-2v-8Zm3 0h2v8h-2v-8Z\"/></g>"}, "time": {"body": "<g fill=\"currentColor\"><path d=\"M9 7h2v5h5v2H9V7Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 1-16 0a8 8 0 0 1 16 0Z\" clip-rule=\"evenodd\"/></g>"}, "timelapse": {"body": "<g fill=\"currentColor\"><path d=\"M12 19a6.978 6.978 0 0 1-4.95-2.05L12 12V5a7 7 0 1 1 0 14Z\"/><path fill-rule=\"evenodd\" d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "timer": {"body": "<g fill=\"currentColor\"><path d=\"M13 5.07A7.002 7.002 0 0 1 12 19A7 7 0 0 1 7.262 6.847L5.847 5.432A9 9 0 1 0 11 3.055v6.03h2V5.072Z\"/><path d=\"M7.707 8.707a1 1 0 0 0 0 1.414l2.829 2.829a1 1 0 0 0 1.414-1.414L9.12 8.707a1 1 0 0 0-1.414 0Z\"/></g>"}, "today": {"body": "<g fill=\"currentColor\"><rect width=\"10\" height=\"10\" x=\"7\" y=\"9\" opacity=\".5\" rx=\"1\"/><path fill-rule=\"evenodd\" d=\"M18 3H6a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1ZM6 1a3 3 0 0 0-3 3v16a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H6Z\" clip-rule=\"evenodd\"/><path d=\"M7 6a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1Z\"/></g>"}, "toggle-off": {"body": "<g fill=\"currentColor\"><path d=\"M17 15a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\"/><path fill-rule=\"evenodd\" d=\"M0 12a7 7 0 0 1 7-7h10a7 7 0 1 1 0 14H7a7 7 0 0 1-7-7Zm7-5h10a5 5 0 0 1 0 10H7A5 5 0 0 1 7 7Z\" clip-rule=\"evenodd\"/></g>"}, "toggle-on": {"body": "<g fill=\"currentColor\"><path d=\"M7 15a3 3 0 1 1 0-6a3 3 0 0 1 0 6Z\"/><path fill-rule=\"evenodd\" d=\"M24 12a7 7 0 0 0-7-7H7a7 7 0 0 0 0 14h10a7 7 0 0 0 7-7Zm-7-5H7a5 5 0 0 0 0 10h10a5 5 0 0 0 0-10Z\" clip-rule=\"evenodd\"/></g>"}, "toggle-square": {"body": "<g fill=\"currentColor\"><path d=\"M9 9a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h4Z\"/><path fill-rule=\"evenodd\" d=\"M24 7a2 2 0 0 0-2-2H2a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V7Zm-2 0H2v10h20V7Z\" clip-rule=\"evenodd\"/></g>"}, "toggle-square-off": {"body": "<g fill=\"currentColor\"><path d=\"M15 9a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-4Z\"/><path fill-rule=\"evenodd\" d=\"M0 7a2 2 0 0 1 2-2h20a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V7Zm2 0h20v10H2V7Z\" clip-rule=\"evenodd\"/></g>"}, "toolbar-bottom": {"body": "<g fill=\"currentColor\"><path d=\"M18 13H6v2h12v-2Z\"/><path fill-rule=\"evenodd\" d=\"M2 8a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8Zm3-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "toolbar-left": {"body": "<g fill=\"currentColor\"><path d=\"M8 9H6v6h2V9Z\"/><path fill-rule=\"evenodd\" d=\"M2 8a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8Zm3-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/></g>"}, "toolbar-right": {"body": "<g fill=\"currentColor\"><path d=\"M16 9h2v6h-2V9Z\"/><path fill-rule=\"evenodd\" d=\"M22 8a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V8Zm-3-1H5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "toolbar-top": {"body": "<g fill=\"currentColor\"><path d=\"M18 11H6V9h12v2Z\"/><path fill-rule=\"evenodd\" d=\"M2 16a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V8a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v8Zm3 1h14a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1Z\" clip-rule=\"evenodd\"/></g>"}, "toolbox": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17 5.5h3a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-12a2 2 0 0 1 2-2h3a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3Zm-3-1h-4a1 1 0 0 0-1 1h6a1 1 0 0 0-1-1Zm6 3H4v2h16v-2Zm-16 12v-8h3v2h4v-2h2v2h4v-2h3v8H4Z\" clip-rule=\"evenodd\"/>"}, "touchpad": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M20 21a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H4a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h16ZM4 5h16a1 1 0 0 1 1 1v8H3V6a1 1 0 0 1 1-1ZM3 16v2a1 1 0 0 0 1 1h7v-3H3Zm10 3v-3h8v2a1 1 0 0 1-1 1h-7Z\" clip-rule=\"evenodd\"/>"}, "track": {"body": "<g fill=\"currentColor\"><path d=\"M12 15a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\"/><path fill-rule=\"evenodd\" d=\"M12 3a1 1 0 0 1 1 1v1.07A7.004 7.004 0 0 1 18.93 11H20a1 1 0 1 1 0 2h-1.07A7.004 7.004 0 0 1 13 18.93V20a1 1 0 1 1-2 0v-1.07A7.004 7.004 0 0 1 5.07 13H4a1 1 0 1 1 0-2h1.07A7.005 7.005 0 0 1 11 5.07V4a1 1 0 0 1 1-1Zm-5 9a5 5 0 1 1 10 0a5 5 0 0 1-10 0Z\" clip-rule=\"evenodd\"/></g>"}, "transcript": {"body": "<g fill=\"currentColor\"><path d=\"M5 16a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1Zm13-5a1 1 0 1 1 0 2h-8a1 1 0 1 1 0-2h8Zm-2 5a1 1 0 0 1 1-1h1a1 1 0 1 1 0 2h-1a1 1 0 0 1-1-1Zm-9-5a1 1 0 1 1 0 2H6a1 1 0 1 1 0-2h1Z\"/><path fill-rule=\"evenodd\" d=\"M4 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "trash": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M17 5V4a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2v1H4a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V7h1a1 1 0 1 0 0-2h-3Zm-2-1H9v1h6V4Zm2 3H7v11a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V7Z\" clip-rule=\"evenodd\"/><path d=\"M9 9h2v8H9V9Zm4 0h2v8h-2V9Z\"/></g>"}, "trash-empty": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17 6V5a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2v1H4a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V8h1a1 1 0 1 0 0-2h-3Zm-2-1H9v1h6V5Zm2 3H7v11a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V8Z\" clip-rule=\"evenodd\"/>"}, "tree": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11 17.9A5.002 5.002 0 0 1 7 13V7a5 5 0 0 1 10 0v6a5.002 5.002 0 0 1-4 4.9V21a1 1 0 1 1-2 0v-3.1ZM12 4a3 3 0 0 1 3 3v6a3.001 3.001 0 0 1-2 2.83V11a1 1 0 1 0-2 0v4.83A3.001 3.001 0 0 1 9 13V7a3 3 0 0 1 3-3Z\" clip-rule=\"evenodd\"/>"}, "trees": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.74 16.319A4.995 4.995 0 0 1 10 17.9V21a1 1 0 1 1-2 0v-3.1A5.002 5.002 0 0 1 4 13V7a5 5 0 0 1 9.98-.453A4 4 0 0 1 20 10v4a4.002 4.002 0 0 1-3 3.874V21a1 1 0 1 1-2 0v-3.126a4.005 4.005 0 0 1-2.26-1.555ZM12 7v6a3.001 3.001 0 0 1-2 2.83V13a1 1 0 1 0-2 0v2.83A3.001 3.001 0 0 1 6 13V7a3 3 0 0 1 6 0Zm5 8.732V13a1 1 0 1 0-2 0v2.732A2 2 0 0 1 14 14v-4a2 2 0 1 1 4 0v4a2 2 0 0 1-1 1.732Z\" clip-rule=\"evenodd\"/>"}, "trello": {"body": "<g fill=\"currentColor\"><path d=\"M6 7a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Zm7 0a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1V7Z\"/><path fill-rule=\"evenodd\" d=\"M2 4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4Zm2 0h16v16H4V4Z\" clip-rule=\"evenodd\"/></g>"}, "trending": {"body": "<path fill=\"currentColor\" d=\"M1.414 16.432L0 15.018l7.071-7.071l6.364 6.364l4.243-4.243l-1.743-1.742l6.692-1.793l-1.793 6.692l-1.742-1.742l-5.657 5.656l-6.364-6.364l-5.657 5.657Z\"/>"}, "trending-down": {"body": "<path fill=\"currentColor\" d=\"M1.851 8.106L.437 9.52l7.07 7.072l6.365-6.364l4.242 4.242l-1.742 1.743l6.692 1.793l-1.793-6.692l-1.742 1.742l-5.657-5.657l-6.364 6.364L1.85 8.106Z\"/>"}, "trophy": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M13 15.9a5.002 5.002 0 0 0 4-4.9V4H7v7a5.002 5.002 0 0 0 4 4.9V18H9v2h6v-2h-2v-2.1ZM9 6h6v5a3 3 0 1 1-6 0V6Z\" clip-rule=\"evenodd\"/><path d=\"M18 6h2v5h-2V6ZM6 6H4v5h2V6Z\"/></g>"}, "tv": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"m8 6.119l1.413-1.413l2.124 2.124L14.367 4l1.411 1.412l-2.464 2.464H18a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3.757L8 6.119Zm10 3.757H6v7h12v-7Z\" clip-rule=\"evenodd\"/><path d=\"M8 19.876h8v1H8v-1Z\"/></g>"}, "twilio": {"body": "<g fill=\"currentColor\"><path d=\"M11 9a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm6 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm-2 8a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm-4-2a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/><path fill-rule=\"evenodd\" d=\"M24 12c0 6.627-5.373 12-12 12S0 18.627 0 12S5.373 0 12 0s12 5.373 12 12Zm-3 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\" clip-rule=\"evenodd\"/></g>"}, "twitter": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 3a2 2 0 0 1 2 2v3h6a2 2 0 1 1 0 4h-6v2a3 3 0 0 0 3 3h3a2 2 0 1 1 0 4h-3a7 7 0 0 1-7-7V5a2 2 0 0 1 2-2Z\" clip-rule=\"evenodd\"/>"}, "ui-kit": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M14 6h-4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1Zm-4-2a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h4a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3h-4Z\" clip-rule=\"evenodd\"/><path d=\"m6 7.46l-2.507-.418A3 3 0 0 0 0 10v4.917a3 3 0 0 0 3.493 2.96L6 17.458v-2.027l-2.836.472A1 1 0 0 1 2 14.918v-4.917a1 1 0 0 1 1.164-.987L6 9.487V7.459Zm12 0l2.507-.418A3 3 0 0 1 24 10v4.917a3 3 0 0 1-3.493 2.96L18 17.458v-2.027l2.836.472A1 1 0 0 0 22 14.918v-4.917a1 1 0 0 0-1.164-.987L18 9.487V7.459Z\"/></g>"}, "umbrella": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 9a8 8 0 1 1 16 0v2h-6.981v9.5a2.5 2.5 0 0 1-5 0v-2.643h2V20.5a.5.5 0 1 0 1 0V11H4V9Zm8-6a6 6 0 0 1 6 6H6a6 6 0 0 1 6-6Z\" clip-rule=\"evenodd\"/>"}, "unavailable": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M18.364 5.636A9 9 0 1 1 5.636 18.364A9 9 0 0 1 18.364 5.636Zm-2.172 11.97L6.393 7.808a7.001 7.001 0 0 0 9.8 9.8ZM16.95 7.05a7.002 7.002 0 0 1 .657 9.142l-9.8-9.799a7.001 7.001 0 0 1 9.143.657Z\" clip-rule=\"evenodd\"/>"}, "unblock": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.636 18.364A9 9 0 1 0 18.364 5.636A9 9 0 0 0 5.636 18.364Zm2.171-.757a7.001 7.001 0 0 0 9.8-9.8l-2.779 2.779a1 1 0 0 1-1.414-1.414l2.778-2.779a7.002 7.002 0 0 0-9.799 9.8l2.779-2.779a1 1 0 0 1 1.414 1.414l-2.779 2.779Z\" clip-rule=\"evenodd\"/>"}, "undo": {"body": "<path fill=\"currentColor\" d=\"M5.34 4.468h2v2.557a7 7 0 1 1-1.037 10.011l1.619-1.185a5 5 0 1 0 .826-7.384h2.591v2h-6v-6Z\"/>"}, "unfold": {"body": "<path fill=\"currentColor\" d=\"M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10H2Z\"/>"}, "unsplash": {"body": "<path fill=\"currentColor\" d=\"M15 4.5H9v4h6v-4Zm-11 6h5v4h6v-4h5v9H4v-9Z\"/>"}, "usb": {"body": "<g fill=\"currentColor\"><path d=\"M10 4.5h1v2h-1v-2Zm4 0h-1v2h1v-2Z\"/><path fill-rule=\"evenodd\" d=\"M7 8.5v-7h10v7h2v11a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3v-11h2Zm2-5h6v5H9v-5Zm8 7H7v9a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-9Z\" clip-rule=\"evenodd\"/></g>"}, "usb-c": {"body": "<g fill=\"currentColor\"><path d=\"M8 11a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H8Z\"/><path fill-rule=\"evenodd\" d=\"M3 12a5 5 0 0 1 5-5h8a5 5 0 0 1 0 10H8a5 5 0 0 1-5-5Zm5-3h8a3 3 0 1 1 0 6H8a3 3 0 1 1 0-6Z\" clip-rule=\"evenodd\"/></g>"}, "user": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M16 7a4 4 0 1 1-8 0a4 4 0 0 1 8 0Zm-2 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\" clip-rule=\"evenodd\"/><path d=\"M16 15a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v6H6v-6a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3v6h-2v-6Z\"/></g>"}, "user-add": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M8 11a4 4 0 1 0 0-8a4 4 0 0 0 0 8Zm0-2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\" clip-rule=\"evenodd\"/><path d=\"M11 14a1 1 0 0 1 1 1v6h2v-6a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v6h2v-6a1 1 0 0 1 1-1h6Zm7-7h2v2h2v2h-2v2h-2v-2h-2V9h2V7Z\"/></g>"}, "user-list": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M8 11a4 4 0 1 0 0-8a4 4 0 0 0 0 8Zm0-2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\" clip-rule=\"evenodd\"/><path d=\"M11 14a1 1 0 0 1 1 1v6h2v-6a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v6h2v-6a1 1 0 0 1 1-1h6Zm11-3h-6v2h6v-2Zm-6 4h6v2h-6v-2Zm6-8h-6v2h6V7Z\"/></g>"}, "user-remove": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M8 11a4 4 0 1 0 0-8a4 4 0 0 0 0 8Zm0-2a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z\" clip-rule=\"evenodd\"/><path d=\"M11 14a1 1 0 0 1 1 1v6h2v-6a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v6h2v-6a1 1 0 0 1 1-1h6Zm11-5h-6v2h6V9Z\"/></g>"}, "userlane": {"body": "<path fill=\"currentColor\" d=\"M15 4h6v6h-6V4ZM3 12a9 9 0 1 0 18 0h-4a5 5 0 0 1-10 0H3Zm3-2a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\"/>"}, "view-cols": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 8a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8Zm14-1h3a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1h-3V7Zm-2 0h-4v10h4V7ZM8 17V7H5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h3Z\" clip-rule=\"evenodd\"/>"}, "view-comfortable": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 8a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8Zm3-1h14a1 1 0 0 1 1 1v3H4V8a1 1 0 0 1 1-1Zm-1 6v3a1 1 0 0 0 1 1h3v-4H4Zm6 4h9a1 1 0 0 0 1-1v-3H10v4Z\" clip-rule=\"evenodd\"/>"}, "view-day": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 8a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8Zm11-1h6a1 1 0 0 1 1 1v3h-7V7Zm-2 0H5a1 1 0 0 0-1 1v3h7V7Zm-7 6v3a1 1 0 0 0 1 1h6v-4H4Zm9 4h6a1 1 0 0 0 1-1v-3h-7v4Z\" clip-rule=\"evenodd\"/>"}, "view-grid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V8a3 3 0 0 0-3-3H5Zm3 2H5a1 1 0 0 0-1 1v1h4V7Zm2 0v2h4V7h-4Zm6 0v2h4V8a1 1 0 0 0-1-1h-3Zm-2 4h-4v2h4v-2Zm2 2v-2h4v2h-4Zm-2 2h-4v2h4v-2Zm2 2v-2h4v1a1 1 0 0 1-1 1h-3Zm-8 0v-2H4v1a1 1 0 0 0 1 1h3Zm0-4v-2H4v2h4Z\" clip-rule=\"evenodd\"/>"}, "view-list": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V8a3 3 0 0 0-3-3H5Zm2 2H5a1 1 0 0 0-1 1v1h3V7Zm2 0v2h11V8a1 1 0 0 0-1-1H9Zm-2 4H4v2h3v-2Zm2 2v-2h11v2H9Zm-2 2H4v1a1 1 0 0 0 1 1h2v-2Zm2 2v-2h11v1a1 1 0 0 1-1 1H9Z\" clip-rule=\"evenodd\"/>"}, "view-month": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 8a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8Zm15-1h2a1 1 0 0 1 1 1v1h-3V7Zm-2 0h-2v2h2V7Zm-4 0H9v2h2V7ZM7 7H5a1 1 0 0 0-1 1v1h3V7Zm-3 4v2h3v-2H4Zm0 4v1a1 1 0 0 0 1 1h2v-2H4Zm5 2h2v-2H9v2Zm4 0h2v-2h-2v2Zm4 0h2a1 1 0 0 0 1-1v-1h-3v2Zm3-4v-2h-3v2h3Zm-9 0H9v-2h2v2Zm4 0h-2v-2h2v2Z\" clip-rule=\"evenodd\"/>"}, "view-split": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 8a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8Zm11-1h6a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1h-6V7Zm-2 0H5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h6V7Z\" clip-rule=\"evenodd\"/>"}, "vinyl": {"body": "<g fill=\"currentColor\"><path d=\"M12 13a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z\"/><path fill-rule=\"evenodd\" d=\"M20 12a8 8 0 1 1-16 0a8 8 0 0 1 16 0Zm-4 0a4 4 0 1 1-8 0a4 4 0 0 1 8 0Z\" clip-rule=\"evenodd\"/></g>"}, "voicemail": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10 15a5 5 0 1 0-4 2h12a5 5 0 1 0-4-2h-4Zm-4 0a3 3 0 1 0 0-6a3 3 0 0 0 0 6Zm12 0a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\" clip-rule=\"evenodd\"/>"}, "voicemail-o": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M11 12c0 .35-.06.687-.17 1h2.34A3 3 0 1 1 16 15H8a3 3 0 1 1 3-3Zm-2 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm8 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path d=\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12S5.925 1 12 1s11 4.925 11 11Zm-2 0a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\"/></g>"}, "voicemail-r": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M11.5 12c0 .35-.06.687-.17 1h2.34a3 3 0 1 1 2.83 2h-8a3 3 0 1 1 3-3Zm-2 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Zm8 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path d=\"M1.5 8a3 3 0 0 1 3-3h15a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3h-15a3 3 0 0 1-3-3V8Zm3-1h15a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1h-15a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1Z\"/></g>"}, "volume": {"body": "<g fill=\"currentColor\"><path d=\"M24 12a8 8 0 0 1-8 8v-2a6 6 0 0 0 0-12V4a8 8 0 0 1 8 8Z\"/><path d=\"M20 12a4 4 0 0 1-4 4v-2a2 2 0 1 0 0-4V8a4 4 0 0 1 4 4Z\"/><path fill-rule=\"evenodd\" d=\"m9 16l6 4V4L9 8H5a4 4 0 1 0 0 8h4Zm-4-6h4l4-2.5v9L9 14H5a2 2 0 1 1 0-4Z\" clip-rule=\"evenodd\"/></g>"}, "webcam": {"body": "<g fill=\"currentColor\"><path d=\"M13 10a1 1 0 1 1-2 0a1 1 0 0 1 2 0Z\"/><path fill-rule=\"evenodd\" d=\"M13 14.9A5.002 5.002 0 0 0 12 5a5 5 0 0 0-1 9.9V17H7v2h10v-2h-4v-2.1ZM12 13a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z\" clip-rule=\"evenodd\"/></g>"}, "website": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M14 7a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1h-4Zm3 2h-2v6h2V9Z\" clip-rule=\"evenodd\"/><path d=\"M6 7a1 1 0 0 0 0 2h4a1 1 0 1 0 0-2H6Zm0 4a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2H6Zm-1 5a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1Z\"/><path fill-rule=\"evenodd\" d=\"M4 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H4Zm16 2H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1Z\" clip-rule=\"evenodd\"/></g>"}, "windows": {"body": "<path fill=\"currentColor\" d=\"m3 5.548l7.195-.966v7.029l-7.188.054L3 5.55Zm7.195 6.843v7.105l-7.19-.985v-6.12h7.19Zm.918-7.935L20.998 3v8.533l-9.885.078V4.456ZM21 12.505L20.998 21l-9.885-1.353v-7.142H21Z\"/>"}, "work-alt": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17 7a3 3 0 0 0-3-3h-4a3 3 0 0 0-3 3H6a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v-8a3 3 0 0 0-3-3h-1Zm-3-1h-4a1 1 0 0 0-1 1h6a1 1 0 0 0-1-1ZM6 9h12a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/>"}, "yinyang": {"body": "<g fill=\"currentColor\"><path d=\"M14 16a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10Zm-10 0a4 4 0 0 1 0-8a8 8 0 1 0 0 16a4 4 0 0 0 0-8Zm2-4a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\" clip-rule=\"evenodd\"/></g>"}, "youtube": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 7h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1ZM2 8a3 3 0 0 1 3-3h14a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8Zm8 1l4 3l-4 3V9Z\" clip-rule=\"evenodd\"/>"}, "zeit": {"body": "<path fill=\"currentColor\" d=\"M17.992 17.023L11.981 6.977L6.008 17.023h11.984Z\"/>"}, "zoom-in": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15.343 15.243a6 6 0 1 0-8.485-8.486a6 6 0 0 0 8.485 8.486Zm1.414-9.9a8.001 8.001 0 0 1 .662 10.565c.**************.046.042l4.242 4.242a1 1 0 0 1-1.414 1.415l-4.243-4.243a.99.99 0 0 1-.042-.045A8.001 8.001 0 0 1 5.444 5.343a8 8 0 0 1 11.313 0ZM10.1 7h2v3h3v2h-3v3h-2v-3h-3v-2h3V7Z\" clip-rule=\"evenodd\"/>"}, "zoom-out": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15.343 15.243a6 6 0 1 0-8.485-8.486a6 6 0 0 0 8.485 8.486Zm1.414-9.9a8.001 8.001 0 0 1 .662 10.565c.**************.046.042l4.242 4.242a1 1 0 0 1-1.414 1.415l-4.243-4.243a.99.99 0 0 1-.042-.045A8.001 8.001 0 0 1 5.444 5.343a8 8 0 0 1 11.313 0ZM7.101 10v2h8v-2h-8Z\" clip-rule=\"evenodd\"/>"}}, "width": 24, "height": 24}