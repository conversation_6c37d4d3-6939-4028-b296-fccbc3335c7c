const Layout = () => import("@/layout/index.vue");

let children = [
  {
    path: "problem_list",
    name: "problem_list",
    component: () => import("@/views/problem_list/index.vue"),
    meta: {
      title: "问题清单",
      auth: ["admin", "problem_list.browse"],
    },
  },
  {
    path: "latest",
    name: "latest",
    component: () => import("@/views/problem_list/lastData.vue"),
    meta: {
      title: "最新数据",
      auth: ["admin", "latest.browse"],
    },
  },
  {
    path: "rule",
    name: "rule",
    redirect: "/monitor_center/rule/ruleHostList",
    meta: {
      title: "告警规则",
      auth: ["admin", "rule.browse"],
    },
    children: [
      {
        path: "ruleHostList",
        name: "ruleHostList",
        component: () => import("@/views/rule/ruleHostList.vue"),
        meta: {
          sidebar: false,
          title: "主机列表",
          auth: ["admin", "rule.browse"],
          activeMenu: "/monitor_center/rule",
        },
      },
      {
        path: "ruleItemsList/:id",
        name: "ruleItemsList",
        component: () => import("@/views/rule/ruleItemsList.vue"),
        meta: {
          sidebar: false,
          title: "监控项列表",
          auth: ["admin", "rule.browse"],
          activeMenu: "/monitor_center/rule",
        },
      },
      {
        path: "ruleTriggersList/:id",
        name: "ruleTriggersList",
        component: () => import("@/views/rule/ruleTriggersList.vue"),
        meta: {
          sidebar: false,
          title: "触发器列表",
          auth: ["admin", "rule.browse"],
          activeMenu: "/monitor_center/rule",
        },
      },
    ],
  },
  {
    path: "WarningMonitior",
    name: "WarningMonitior",
    component: () => import("@/views/business_monitor/warning.vue"),
    meta: {
      title: "业务详情",
      sidebar: false,
      // auth: ['admin', "monitor_plat.browse"],
      activeMenu: "/cmdb/biz_hub",
    },
  },
];
export default {
  path: "/monitor_center",
  component: Layout,
  redirect: "/monitor_center/problem_list",
  name: "monitor_center",
  meta: {
    auth: ["admin", "monitor_center.browse"],
    title: "告警中心",
    icon: "monitor",
  },
  children,
};
