<script setup name="HomepageInformation">
import { onMounted, reactive, ref } from "vue";
import { usePagination } from "@/utils/composables";
import { cmdbGetResourceList, cmdbDeleteResource } from "@/api/modules/cmdb/resource";
import { ElMessageBox } from "element-plus";
import SocketUtil from "@/utils/cmdb_utils";
import { goToPageAssetDetail } from "@/views/homepage/components/utils";
import { ResourceTypeEnum } from "@/views/homepage/cmdb_asset/components/constants";
import { showNotification } from "@/plugins/element-ui";
import SoftAsset from "@/views/model_configuration/cmdb_soft/components/detail.vue";
import CreateBS from "@/views/model_configuration/cmdb_business_soft/createBusinessSoft.vue";
import { pageChangeNum, deleteResource } from "../../homepage/components/utils";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
const data = reactive({
  title: "",
  assetTitle: "",
  softId: 0,
  showAsset: false,
  dataList: [],
  allList: [],

  search: {
    searchName: "",
    isSearch: false,
  },

  ip_list: [],
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },
  drawer: false,
  router_id: 0,
});
onMounted(() => {
  data.title = ResourceTypeEnum.businessApp;
  getData();
});

SocketUtil.socket.onmessage = (recv) => {
  var data = JSON.parse(recv.data);
  if (data.status == 0) {
    showNotification("连接成功", data.message, "success");
  } else if (data.status == 1) {
    if (data.message.status == "python_script_error") {
      showNotification("python脚本执行失败", data.message.error_message, "error");
    } else {
      showNotification("ansible脚本执行失败", data.message.error_message, "error");
    }
  } else {
    showNotification("采集成功", data.message, "success");
  }
};

//加载初始化数据
function getData() {
  cmdbGetResourceList(data.title).then((res) => {
    data.dataList = res.data;
    data.allList = data.dataList;
    changePageNum(data.allList);
  });
}
//分页
function changePageNum(list) {
  let res = pageChangeNum(list, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
}

const drawerTitle = computed(() => {
  return data.router_id === 0 ? "新增应用服务" : "编辑应用服务";
});

//添加页面跳转

function create() {
  data.router_id = 0;
  data.drawer = true;
}

// 每页数量切换

function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）

function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

//筛选函数

function queryData() {
  if (data.search.searchName === "") {
    getData();
    data.search.isSearch = false;
  } else {
    searchByName();
    data.search.isSearch = true;
  }
}

function searchByName() {
  let list = [];
  const ipRegex = /^[\d.]+$/;
  if (ipRegex.test(data.search.searchName.trim())) {
    list = data.allList.filter((item) => {
      return item.ip.trim().indexOf(data.search.searchName.trim()) != -1;
    });
  } else {
    list = data.allList.filter((item) => {
      return item.databaseName.trim().toLowerCase().indexOf(data.search.searchName.trim().toLowerCase()) != -1;
    });
  }
  changePageNum(list);
}

// 跳转详情
function jumpAssetDetail(row, item = data.title) {
  const queryParams = { item, id: row.zabbix_id, ip: row.ip, relation_id: row.relation_id };
  if (item == ResourceTypeEnum.server) {
    queryParams.id = row.server_zabbix_id;
  }
  goToPageAssetDetail(queryParams);
}

function editDatabase(row) {
  data.router_id = row.relation_id;
  data.drawer = true;
}

//批量删除

async function batchDel() {
  let list = [];
  data.batch.selectionDataList.forEach((item) => {
    list.push({ relation_id: item.relation_id, zabbix_id: item.zabbix_id });
  });
  const result = await deleteResource(ResourceTypeEnum.businessApp, list);
  if (!result) {
    return;
  }
  getData();
}
</script>

<template>
  <div>
    <page-main title="应用服务">
      <div class="flex justify-between">
        <el-space wrap>
          <el-button type="primary" @click="create">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            新增
          </el-button>
          <el-button-group v-if="data.batch.enable">
            <el-button type="danger" :disabled="!data.batch.selectionDataList.length" @click="batchDel()">
              删除
            </el-button>
          </el-button-group>
        </el-space>
        <el-space wrap>
          <div class="w-220px">
            <el-input
              v-model="data.search.searchName"
              placeholder="请输入业务名称和IP查询"
              clearable
              @keyup.enter="queryData"
            />
          </div>
          <el-button type="primary" @click="queryData()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>

      <el-table
        class="list-table"
        :data="data.dataList"
        border
        stripe
        highlight-current-row
        @selection-change="data.batch.selectionDataList = $event"
      >
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column label="IP" prop="ip" />
        <el-table-column label="业务名称" prop="databaseName">
          <template #default="scope">
            <el-link @click="jumpAssetDetail(scope.row)" type="primary">{{ scope.row.databaseName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="databaseSoft" label="安装软件"></el-table-column>
        <el-table-column prop="type" label="类型">
          <template #default="scope">
            <el-tag type="warning">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="服务器" prop="databaseServer">
          <template #default="scope">
            <el-link @click="jumpAssetDetail(scope.row, ResourceTypeEnum.server)" type="primary">
              {{ scope.row.databaseServer }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scoped">
            <el-button type="primary" plain @click="editDatabase(scoped.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页插件 -->
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>

    <el-drawer v-model="data.drawer" size="50%" :destroy-on-close="true" :close-on-click-modal="false">
      <template #header>
        <h4>{{ drawerTitle }}</h4>
      </template>
      <el-divider class="cancel_top" />
      <CreateBS v-model="data.router_id" @closeDialog="data.drawer = false" @getResource="getData()"></CreateBS>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
// scss
.cancel_top {
  margin-top: 0px;
}
</style>
