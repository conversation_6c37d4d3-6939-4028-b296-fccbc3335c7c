import path from "path-browserify";
import router from "@/router";
// 深拷贝
export function deepClone(target) {
  // 定义一个变量
  let result;
  // 如果当前需要深拷贝的是一个对象的话
  if (typeof target === "object") {
    // 如果是一个数组的话
    if (Array.isArray(target)) {
      result = []; // 将result赋值为一个数组，并且执行遍历
      for (let i in target) {
        // 递归克隆数组中的每一项
        result.push(deepClone(target[i]));
      }
      // 判断如果当前的值是null的话；直接赋值为null
    } else if (target === null) {
      result = null;
      // 判断如果当前的值是一个RegExp对象的话，直接赋值
    } else if (target.constructor === RegExp) {
      result = target;
    } else {
      // 否则是普通对象，直接for in循环，递归赋值对象的所有值
      result = {};
      for (let i in target) {
        result[i] = deepClone(target[i]);
      }
    }
    // 如果不是对象的话，就是基本数据类型，那么直接赋值
  } else {
    result = target;
  }
  // 返回最终结果
  return result;
}

// 防抖
export const debounce = (fn, delay = 300, immediate = true) => {
  let timer = null; // 借助闭包
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    if (immediate) {
      let callNow = !timer;
      timer = setTimeout(() => {
        timer = null;
      }, delay);
      if (callNow) {
        fn.apply(this, arguments);
      }
    } else {
      timer = setTimeout(() => {
        fn.apply(this, arguments);
      }, delay);
    }
  };
};

// 节流
export function throttle(fn, delay = 300) {
  let valid = true;
  return function () {
    if (!valid) {
      return false;
    }
    // 工作时间，执行函数并且在间隔期内把状态位设为无效
    valid = false;
    setTimeout(() => {
      fn.apply(this, arguments);
      valid = true;
    }, delay);
  };
}

export function isExternalLink(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

export function resolveRoutePath(basePath, routePath) {
  if (isExternalLink(routePath)) {
    return routePath;
  }
  if (isExternalLink(basePath)) {
    return basePath;
  }
  return basePath ? path.resolve(basePath, routePath) : routePath;
}

/**
 * 返回上一页
 */
export function goBack() {
  router.go(-1);
}
