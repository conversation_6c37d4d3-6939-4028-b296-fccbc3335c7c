<template>
  <div class="h-100% absolute w-100%">
    <page-main class="h-[calc(100%-40px)]">
      <div class="flex h-full">
        <!-- left -->
        <div class="w-240px shrink-0 b-r-coolGray b-r-1px b-r-solid h-100% flex-shrink-0">
          <!-- head -->
          <div class="h-60px flex justify-between b-b-coolGray b-b-solid b-b-1px items-center p-x-20px">
            <div>机构管理</div>
            <el-icon :size="20" class="cursor-pointer" @click="handleOpenAddDialog()">
              <Plus />
            </el-icon>
          </div>

          <!-- line -->
          <div class="py-10px h-[calc(100%-60px)]">
            <el-scrollbar height="100%">
              <div
                v-for="item in campurList"
                :key="item"
                :class="[
                  'h-48px flex items-center justify-between p-10px campurItem ',
                  item.id == activeCampur.id ? 'active' : '',
                ]"
                @click="changeActive(item)"
              >
                <div class="flex flex-1">
                  <el-icon :size="20"><OfficeBuilding /></el-icon>
                  <div class="p-l-10px">{{ item.campur_name }}</div>
                </div>
                <el-icon class="icon-icon"><Edit /></el-icon>
              </div>
            </el-scrollbar>
          </div>
        </div>
        <!-- right -->
        <div class="flex-1 w-[calc(100%-240px)]">
          <!-- head -->
          <div class="h-60px flex items-center justify-between px-20px b-b-coolGray b-b-solid b-b-1px">
            <div class="flex items-center">
              <el-icon :size="20"><OfficeBuilding /></el-icon>
              <div class="pl-10px">{{ activeCampur.campur_name }}</div>
            </div>
            <div>
              <el-button type="primary">增加成员</el-button>
            </div>
          </div>

          <!-- content -->
          <div class="p-10px">
            <el-table :data="activeCampur.personList" style="width: 100%">
              <el-table-column prop="name" label="名称"></el-table-column>
              <el-table-column prop="opration" label="操作">
                <template #default>
                  <div class="opration">
                    <!-- <div class="opration-icon">
                      <el-icon :size="16"><Edit /></el-icon>
                    </div> -->
                    <div class="opration-icon" title="移除院区" @click="handleDeleteCampur()">
                      <el-icon :size="16"><Delete /></el-icon>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <AddCampurDialog v-model="openAddCampurDialog"></AddCampurDialog>
    </page-main>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { Plus, OfficeBuilding, Edit, Delete, Eleme } from "@element-plus/icons-vue";
import AddCampurDialog from "./add_campus_dialog.vue";
const campurList = ref([]);

const activeCampur = ref({});
const openAddCampurDialog = ref(false);
onMounted(() => {
  getCampurList();
});

function getCampurList() {
  for (let i = 0; i < 5; i++) {
    campurList.value.push({
      id: i,
      campur_name: `第${i}医院`,
      personList: [
        {
          id: i + 1,
          name: `王思${i + 1}`,
        },
      ],
    });
  }

  activeCampur.value = campurList.value[0];
}

/**
 * 将指定的项设置为当前激活项
 * 此函数通过更改 activeCampur.value 来更新当前激活项
 * @param {any} item - 要设置为激活项的项
 */
const changeActive = (item) => {
  activeCampur.value = item;
};

/**
 *
 * 打开添加院区的对话框
 */
function handleOpenAddDialog() {
  openAddCampurDialog.value = true;
}

/**
 *
 * 处理删除院区的操作
 */
function handleDeleteCampur() {
  ElMessageBox.confirm("确定删除该院区吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 删除院区逻辑
      console.log("删除院区");
    })
    .catch(() => {});
  console.log("删除院区");
}
</script>

<style lang="scss" scoped>
.scrollbar-demo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  margin: 10px;
  text-align: center;
  border-radius: 4px;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}
.campurItem {
  cursor: pointer;
  margin: 5px 5px;
  .icon-icon {
    display: none;
  }
  &:hover {
    background: #dedede;
    border: 1px solid #dedede;
    border-radius: 10px;
    > .icon-icon {
      display: block;
    }
  }
}
.active {
  background: #dedede;
  border: 1px solid #dedede;
  border-radius: 10px;
}

.opration {
  display: flex;
  .opration-icon {
    display: flex;
    align-items: center;
    margin: 0 5px 0 0;
    padding: 5px;

    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background: #dedede;
    }
  }
}
</style>
