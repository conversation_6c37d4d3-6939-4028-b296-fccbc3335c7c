<script setup name="BackupList">
import { backupOverview } from "@/api/modules/database_backup_monitoring/dbBackup";
import { usePagination } from "@/utils/composables";
import { reactive } from "vue";
import { Column } from "@antv/g2plot";
import useDateTimeStore from "@/store/modules/datetime";
import { getUTCTimestampWithOffset } from "@/utils/dayjs";
const timeStore = useDateTimeStore();
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
// const route = useRoute()
var trendInputG2;
var trendOutputG2;
// var timeTrendChart
const data = reactive({
  loading: false,
  /**
   * 详情展示模式
   * router 路由跳转
   * dialog 对话框
   * drawer 抽屉
   */
  formMode: "router",
  // 详情
  formModeProps: {
    visible: false,
    id: "",
  },
  // 搜索
  search: {
    title: "",
  },
  test: 0,
  // 批量操作
  batch: {
    enable: false,
    selectionDataList: [],
  },
  // 列表数据
  gdpStatus: [],
  trendData: [],
  time: [],
  sortData: [],
  database_overview: {
    information: [],
    instance: "",
    dataList: [],
    lineChart: [],
    barChart: [],
    alldataList: [],
  },
  color: "",
});

const problemList = ref(null);
onMounted(() => {
  data.time = timeStore.getUtcUnixTimestamp();
  getDataList();
  // timer=setInterval(getDataList, timerInterval);
});

onBeforeUnmount(() => {
  //    clearInterval(timer)
  if (trendInputG2) {
    trendInputG2.destroy();
  }
  if (trendOutputG2) {
    trendOutputG2.destroy();
  }
  //    timeTrendChart.destroy();
});

function getDataList() {
  let params = {
    start_time: data.time[0],
    end_time: data.time[1],
  };
  // api.post('backup/list',{data:params},{baseURL:'/mock/',}).then((res)=>{
  backupOverview(params).then((res) => {
    data.database_overview.information = res.data.database_overview.backup_information;
    data.database_overview.information.forEach((item) => {
      // const takeTimeInSeconds = getSecondsFromTimeString(item.backup_take)
      item.backup_input_total = (item.backup_input_total / 1024 ** 3).toFixed(1);
      item.backup_output_total = (item.backup_output_total / 1024 ** 3).toFixed(1);
      item.backup_input_io = (item.backup_input_io / 1024 / 1024).toFixed(0);
      item.backup_output_io = (item.backup_output_io / 1024 / 1024).toFixed(0);
    });
    data.database_overview.instance = res.data.database_overview.backup_instance;
    data.database_overview.alldataList = res.data.database_overview.warning_list;
    data.database_overview.lineChart = res.data.database_overview.capacity_trends;
    data.database_overview.lineChart.forEach((item) => {
      item.backup_input_total = parseInt((item.backup_input_total / 1024 ** 3).toFixed(1));
      item.backup_output_total = parseInt((item.backup_output_total / 1024 ** 3).toFixed(1));
    });
    data.database_overview.barChart = res.data.database_overview.time_period;
    data.sortData = sortData(data.database_overview.lineChart);
    getInputChart();
    getOutputChart();
    getWarningList();
  });
}
function getWarningList() {
  data.loading = true;
  let param = getParams();
  data.database_overview.dataList = dataFilter(data.database_overview.alldataList, param).list;
  pagination.value.total = dataFilter(data.database_overview.alldataList, param).total;
}
function dataFilter(dataList, params) {
  let list = dataList;
  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });

  return {
    list: pageList,
    total: list.length,
  };
}

const problemScroll = () => {
  if (problemList.value) {
    problemList.value.scrollIntoView({
      behavior: "smooth", // 可以使滚动平滑
    });
  }
};
function sortData(list) {
  const sortedData = list
    .sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA - dateB;
    })
    .map((obj) => {
      const date = new Date(obj.date);
      const month = date.getMonth() + 1; // 月份从0开始，需要加1
      const day = date.getDate();
      const time = date.toTimeString().split(" ")[0];
      const newDate = `${month}-${day} ${time}`;
      return { ...obj, date: newDate };
    });
  return sortedData;
}
function getInputChart() {
  trendInputG2 = new Column(
    "trendInputRef",
    ("container",
    {
      data: data.sortData,
      isGroup: true,
      xField: "date",
      yField: "backup_input_total",
      seriesField: "database_name",
      columnStyle: {
        radius: [20, 20, 0, 0],
      },
      legend: {
        position: "top",
      },
      tooltip: {
        formatter: (datum) => {
          return {
            name: datum.database_name,
            value: datum.backup_input_total + "GB",
          };
        },
      },
    })
  );
  trendInputG2.render();
}
function getOutputChart() {
  trendOutputG2 = new Column(
    "trendOutputRef",
    ("container",
    {
      data: data.sortData,
      isGroup: true,
      xField: "date",
      yField: "backup_output_total",
      seriesField: "database_name",
      columnStyle: {
        radius: [20, 20, 0, 0],
      },
      legend: {
        position: "top",
      },
      tooltip: {
        formatter: (datum) => {
          return {
            name: datum.database_name,
            value: datum.backup_output_total + "GB",
          };
        },
      },
    })
  );
  trendOutputG2.render();
}
// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => getWarningList());
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => getWarningList());
}

function look(item = null) {
  let query = {
    name: "备份策略监控",
    start_time: data.time[0],
    end_time: data.time[1],
  };
  if (item != null) {
    query.item = item.dbname;
  }
  router.push({
    name: "backup_information",
    query,
  });
}
function calculateInputBackupTotal(data) {
  const total = data.reduce((acc, cur) => {
    return acc + parseFloat(cur.backup_input_total);
  }, 0);
  return total.toFixed(0);
}
function calculateOutputBackupTotal(data) {
  const total = data.reduce((acc, cur) => {
    return acc + parseFloat(cur.backup_output_total);
  }, 0);
  return total.toFixed(0);
}
function calculateBackupTime(data) {
  const total = data.reduce((acc, cur) => {
    let time = convertTimeToHours(cur.backup_take);
    return acc + time;
  }, 0);
  return total.toFixed(1);
}
function getRandomColor() {
  // 生成随机的RGB值
  var r = Math.floor(Math.random() * 256);
  var g = Math.floor(Math.random() * 256);
  var b = Math.floor(Math.random() * 256);

  // 将RGB值转换为十六进制表示
  var hex = "#" + r.toString(16) + g.toString(16) + b.toString(16);

  return hex;
}
function getSuccess(list) {
  let filteredData = list.filter((item) => item.backup_status == "COMPLETED");
  return filteredData.length;
}
function getInputBackupSpeed(list) {
  const sum = list.reduce((acc, cur) => {
    return acc + parseFloat(cur.backup_input_io);
  }, 0);
  const average = sum || 0 / list.length || 0;
  return average.toFixed(0);
}
function getOutputBackupSpeed(list) {
  const sum = list.reduce((acc, cur) => {
    return acc + parseFloat(cur.backup_output_io);
  }, 0);
  const average = sum || 0 / list.length || 0;
  return average.toFixed(0);
}
watch(timeStore.$state, (newValue, oldValue) => {
  trendInputG2 && trendInputG2.destroy();
  trendOutputG2 && trendOutputG2.destroy();
  data.time = [getUTCTimestampWithOffset(newValue.begin), getUTCTimestampWithOffset(newValue.over)];
  getDataList();
});

function convertTimeToHours(timeString) {
  const [hours, minutes, seconds] = timeString.split(":").map(Number);
  const totalHours = hours + minutes / 60 + seconds / 3600;
  return totalHours;
}
</script>

<template>
  <div>
    <el-row :gutter="20" style="margin: 20px 10px">
      <el-col :span="4">
        <el-card shadow="hover" class="goods-card" @click="problemScroll">
          <div style="display: flex">
            <div style="padding-top: 25px; padding-left: 40px">
              <el-icon size="40px"><svg-icon name="backupwarning" /></el-icon>
            </div>
            <div style="flex: 2; padding: 16px">
              <div class="cardtitle">
                <span class="large-text">{{ data.database_overview.alldataList.length }}</span>
                <span class="small-text">条告警</span>
              </div>
              <div class="sub-title">备份告警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-popover :width="400">
          <template #reference>
            <el-card shadow="hover" class="goods-card">
              <div style="display: flex">
                <div style="padding-top: 25px; padding-left: 30px">
                  <el-icon size="40px"><svg-icon name="total" /></el-icon>
                </div>
                <div style="flex: 1; padding: 16px">
                  <div class="cardtitle">
                    <span class="large-text">
                      {{
                        calculateInputBackupTotal(data.database_overview.information) +
                        "/" +
                        calculateOutputBackupTotal(data.database_overview.information)
                      }}
                    </span>
                    <span class="small-text">GB</span>
                  </div>
                  <div class="sub-title">备份总量(读取/写入)</div>
                </div>
              </div>
            </el-card>
          </template>
          <el-table :data="data.database_overview.information">
            <el-table-column width="100" property="name" label="名称" />
            <el-table-column width="150" property="backup_input_total" label="写入量">
              <template #default="scoped">
                <trend style="display: inline-block" :value="scoped.row.backup_input_total + 'GB'" />
              </template>
            </el-table-column>
            <el-table-column width="150" property="backup_output_total" label="输出量">
              <template #default="scoped">
                <trend style="display: inline-block" type="down" :value="scoped.row.backup_output_total + 'GB'" />
              </template>
            </el-table-column>
          </el-table>
        </el-popover>
      </el-col>
      <el-col :span="4">
        <el-popover :width="250">
          <template #reference>
            <el-card shadow="hover" class="goods-card">
              <div style="display: flex">
                <div style="padding-top: 25px; padding-left: 40px">
                  <el-icon size="40px"><svg-icon name="time" /></el-icon>
                </div>
                <div style="flex: 2; padding: 16px; white-space: nowrap">
                  <div class="cardtitle">
                    <span class="large-text">{{ calculateBackupTime(data.database_overview.information) }}</span>
                    <span class="small-text">小时</span>
                  </div>
                  <div class="sub-title">备份耗时</div>
                </div>
              </div>
            </el-card>
          </template>
          <div style="display: flex" v-for="(item, index) in data.database_overview.information" :key="index">
            <div>
              <span
                :style="{
                  color: getRandomColor(),
                  fontSize: '20px',
                  display: 'inline-block',
                }"
              >
                ●
              </span>
              {{ item.name }}：
              <trend style="display: inline-block" :value="item.backup_take" />
            </div>
          </div>
        </el-popover>
      </el-col>
      <el-col :span="4">
        <el-popover :width="300">
          <template #reference>
            <el-card shadow="hover" class="goods-card">
              <div style="display: flex">
                <div style="padding-top: 25px; padding-left: 40px">
                  <el-icon size="40px"><svg-icon name="status" /></el-icon>
                </div>
                <div style="flex: 2; padding: 16px">
                  <div class="cardtitle">
                    <span class="large-text">{{ getSuccess(data.database_overview.information) }}</span>
                    <span class="small-text">个成功</span>
                  </div>
                  <div class="sub-title">备份状态</div>
                </div>
              </div>
            </el-card>
          </template>
          <el-table :data="data.database_overview.information">
            <el-table-column width="175" property="name" label="名称" />
            <el-table-column width="125" property="backup_status" label="状态">
              <template #default="scope">
                <div v-if="scope.row.backup_status == 'COMPLETED'">
                  <el-tag type="success">备份成功</el-tag>
                </div>
                <div v-else>
                  <el-tag type="danger">备份失败</el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-popover>
      </el-col>
      <el-col :span="4">
        <el-popover :width="450">
          <template #reference>
            <el-card shadow="hover" class="goods-card">
              <div style="display: flex">
                <div style="padding-top: 25px; padding-left: 40px">
                  <el-icon size="40px"><svg-icon name="inOutputModue" /></el-icon>
                </div>
                <div style="flex: 2; padding: 16px">
                  <div class="cardtitle">
                    <span class="large-text">
                      {{ getInputBackupSpeed(data.database_overview.information || []) }}/{{
                        getOutputBackupSpeed(data.database_overview.information || [])
                      }}
                    </span>
                    <span class="small-text">mb/s</span>
                  </div>
                  <div class="sub-title">IO消耗(写入/输出)</div>
                </div>
              </div>
            </el-card>
          </template>
          <el-table :data="data.database_overview.information">
            <el-table-column width="150" property="name" label="名称" />
            <el-table-column width="125" label="写入IO">
              <template #default="scope">{{ scope.row.backup_input_io + "mb/s" }}</template>
            </el-table-column>
            <el-table-column width="125" label="读取IO">
              <template #default="scope">{{ scope.row.backup_output_io + "mb/s" }}</template>
            </el-table-column>
          </el-table>
        </el-popover>
      </el-col>
      <el-col :span="4">
        <el-card @click="look(null)" shadow="hover" class="goods-card">
          <div style="display: flex">
            <div style="padding-top: 25px; padding-left: 40px">
              <el-icon size="40px"><svg-icon name="instance" /></el-icon>
            </div>
            <div style="flex: 2; padding: 16px">
              <div class="cardtitle">
                <span class="large-text">{{ data.database_overview.instance || 0 }}</span>
                <span class="small-text">个实例</span>
              </div>
              <div class="sub-title">数据库实例总个数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <page-main style="margin-top: 0px">
      <el-divider style="border-width: 3px" content-position="left">
        <el-icon size="50px"><svg-icon name="tend" /></el-icon>
        <span style="padding-left: 10px">备份容量趋势（读取数据）</span>
      </el-divider>
      <div style="padding-bottom: 10px; font-size: 12px; padding-top: 20px">单位：GB</div>
      <div id="trendInputRef" style="width: 100%; height: 100%; padding-bottom: 30px" />
      <el-divider style="border-width: 3px" content-position="left">
        <el-icon size="50px"><svg-icon name="tend" /></el-icon>
        <span style="padding-left: 10px">备份容量趋势（写入数据）</span>
      </el-divider>
      <div style="padding-bottom: 10px; font-size: 12px; padding-top: 20px">单位：GB</div>
      <div id="trendOutputRef" style="width: 100%; height: 100%; padding-bottom: 30px" />
    </page-main>
    <page-main>
      <div ref="problemList" style="text-align: center; padding-bottom: 10px">
        <h3>备份告警</h3>
      </div>
      <div style="display: flex; justify-content: center">
        <el-table :data="data.database_overview.dataList" border style="width: 50%">
          <el-table-column prop="backup_time" label="日期" width="180" />
          <el-table-column prop="dbname" label="名称" width="180" />
          <el-table-column prop="status" label="状态">
            <template #default="scoped">
              <el-tag type="danger" v-if="scoped.row.status == 'FAIL'">备份失败</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100px">
            <template #default="scope"><el-button @click="look(scope.row)">查看</el-button></template>
          </el-table-column>
        </el-table>
      </div>
      <div style="display: flex; justify-content: center">
        <el-pagination
          :current-page="pagination.page"
          :total="pagination.total"
          :page-size="pagination.size"
          :page-sizes="pagination.sizes"
          :layout="pagination.layout"
          :hide-on-single-page="false"
          class="pagination"
          background
          @current-change="currentChange"
          @size-change="sizeChange"
        />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
  width: 50%;
}

.block {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px;
  width: 300px;
  height: 300px;
  color: #fff;
  background: #409eff;
  border-radius: 10px;
}

img {
  display: block;
  width: 300px;
  height: 300px;
}

.arrow {
  width: 200px;
  height: 2px;
  background-color: #333;
  position: relative;
}

.arrow::after {
  content: "";
  width: 20px;
  height: 20px;
  background-color: #333;
  position: absolute;
  top: -10px;
  right: -10px;
  transform: rotate(45deg);
}

.el-card ::v-deep .el-card__body {
  padding: 0px;
}

.large-text {
  font-size: 22px;
  /* 大文本的字体大小 */
  letter-spacing: 1px;
}

.small-text {
  font-size: 16px;
  /* 小文本的字体大小 */
  font-weight: bold;
}

.sub-title {
  padding-top: 5px;
  font-size: 16px;
}
</style>
