export enum Severity {
  Unspecified = 0, //未分类
  Info = 1, //信息
  Warning = 2, //警告
  Moderate = 3, //一般严重
  Severe = 4, //严重问题：
  Disaster = 5, //灾难问题：
}
export enum SeverityDescriptions {
  未分类 = Severity.Unspecified,
  信息 = Severity.Info,
  警告 = Severity.Warning,
  一般严重 = Severity.Moderate,
  严重 = Severity.Severe,
  灾难 = Severity.Disaster,
}

export const SeverityDescriptionsNames = Object.entries(SeverityDescriptions)
  .filter(([key, value]) => isNaN(Number(value)))
  .map(([key, value]) => ({
    label: key,
    value: value as Severity, // 类型断言
  }));
// Not classified：未分类故障，显示为灰色。
// Information：一般信息，显示为亮蓝色。
// Warning：警告信息，显示为×××。
// Average：一般故障，显示为橙色。
// High：严重故障，显示为亮红色。
// Disaster：灾难，显示为红色。
// 辅助对象，用于映射严重性级别到中文描述和CSS颜色
export const severityDescriptionsAndColors = {
  [Severity.Unspecified]: {
    description: "未分类",
    class: "Unspecified",
    color: "#97aab3",
  },
  [Severity.Info]: { description: "信息", class: "Info", color: "#7499ff" },
  [Severity.Warning]: {
    description: "警告",
    class: "Warning",
    color: "#ffc859",
  },
  [Severity.Moderate]: {
    description: "一般严重",
    class: "Moderate",
    color: "#ffa059",
  },
  [Severity.Severe]: { description: "严重", class: "Severe", color: "#e97659" },
  [Severity.Disaster]: {
    description: "灾难",
    class: "Catastrophic",
    color: "#e45959",
  },
};

export enum customLevel {
  noConnected = -1,
  Normal = 1,
  General = 2,
  Disaster = 3,
}
export const customLevelDescriptions = {
  [customLevel.noConnected]: { label: "未接入", color: "#e45959" },
  [customLevel.Normal]: { label: "正常", color: "#67C23A" },
  [customLevel.General]: { label: "一般/严重", color: "#E6A23C" },
  [customLevel.Disaster]: { label: "灾难", color: "#F56C6C" },
};

// 辅助函数，用于将严重性级别映射到对应的中文描述和CSS颜色
export function getSeverityDescriptionAndColor(severity: Severity): {
  description: string;
  class: string;
} {
  return (
    severityDescriptionsAndColors[severity] || {
      description: "未知",
      class: "black",
    }
  );
}

// 按照 time 字段从近到远排序
export function sortAlertsByTime(alerts: any[], filed = "time") {
  return alerts.sort((a, b) => {
    const timeA = new Date(a[filed]);
    const timeB = new Date(b[filed]);

    // 如果时间解析失败，默认将该条目排在最后
    if (isNaN(timeA.getTime())) return 1;
    if (isNaN(timeB.getTime())) return -1;

    // 返回负值表示 a 应该排在 b 之前；正值表示 b 应该排在 a 之前
    return timeB.getTime() - timeA.getTime();
  });
}

export function isShowAddTag(time) {
  // 将传入的时间字符串转换为Date对象
  const targetTime = new Date(time);

  // 获取当前时间
  const currentTime = new Date();

  // 计算当前时间10分钟之前的时间
  const tenMinutesAgo = new Date(currentTime.getTime() - 10 * 600000);

  // 判断目标时间是否在当前时间的10分钟之内
  if (targetTime >= tenMinutesAgo) {
    return true;
  } else {
    return false;
  }
}
export const AlarmAllCategories = [
  "性能类",
  "资源类",
  "容量类",
  "可用性类",
  "错误日志类",
  "安全类",
  "业务类",
  "依赖服务类",
  "未分类",
];
export function tableCellstyle({ row, column, rowIndex, columnIndex }) {
  if (column["label"] === "问题") {
    let style = {};
    if (row["priority"] === "严重") {
      style = {
        background: "#E97659",
      };
    } else if (row["priority"] === "一般严重") {
      style = {
        background: "#FFA059",
      };
    } else if (row["priority"] === "警告") {
      style = {
        background: "#FFC859",
      };
    } else if (row["priority"] === "灾难") {
      style = {
        background: "#E45959",
      };
    } else {
      style = {
        background: "#7499FF",
      };
    }
    return style;
  }
  return {};
}
