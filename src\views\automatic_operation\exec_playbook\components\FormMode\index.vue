<script setup>
import DetailForm from '../DetailForm/viewResults.vue'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    content: {
        type: Object
    }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()

let myVisible = computed({
    get: function() {
        return props.modelValue
    },
    set: function(val) {
        emit('update:modelValue', val)
    }
})

const title = '查看任务执行状态'

function onCancel() {
    myVisible.value = false
}
</script>

<template>
    <div>
        <el-dialog v-model="myVisible" :title="title" width="1200px" :show-close="false" :destroy-on-close="true">
            <DetailForm ref="formRef" v-bind="$props" />
            <template #footer>
            <el-button size="large" @click="onCancel">取 消</el-button>
            </template>
        </el-dialog>
    </div>
</template>
