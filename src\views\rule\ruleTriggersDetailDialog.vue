<template>
  <el-dialog title="编辑触发器" v-model="dialogVisible" width="800px">
    <div class="h-60vh overflow-auto">
      <el-form v-loading="data.detaiLoading" :model="data.trigger_detail" :rules="rules" ref="form" :label-width="120">
        <el-form-item label="修改范围">
          <el-radio-group v-model="data.trigger_detail.scope">
            <el-radio label="host">当前主机</el-radio>
            <el-radio label="template">模板</el-radio>
          </el-radio-group>
          <span class="text-red ml-10px">*当前主机可以修改固定值;模板可修改固定值以及宏变量</span>
        </el-form-item>
        <el-form-item label="名称" prop="trigger_name">
          <div class="disable_div">
            {{ data.trigger_detail.trigger_name }}
          </div>
        </el-form-item>

        <el-form-item label="表达式" prop="trigger_expression">
          <div class="disable_div">
            {{ data.trigger_detail.trigger_expression }}
          </div>
        </el-form-item>

        <el-form-item label="处理后表达式" prop="processed_trigger_expression">
          <div class="disable_div">{{ data.trigger_detail.processed_trigger_expression }}</div>
        </el-form-item>
        <el-form-item label="表达式含义">
          <div class="disable_div">
            {{ data.trigger_detail.expression_meaning }}
          </div>
        </el-form-item>

        <el-form-item label="固定值" v-show="data.trigger_detail.scope === 'template'">
          <div class="variable_list">
            <div v-for="(item, indx) in data.trigger_detail.values['固定值']" key="key" class="variable_item">
              <span>
                <el-icon><Warning /></el-icon>
                {{ item["调整值含义"] || "未定义说明" }}
              </span>
              <div class="flex">
                <span class="flex-shrink-0 mr-18px">{{ indx }}</span>
                <el-input v-model="item.value" class="w-300px"></el-input>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="宏变量">
          <div class="variable_list">
            <div v-for="(item, indx) in data.trigger_detail.values['宏变量']" key="key" class="variable_item">
              <span>
                <el-icon><Warning /></el-icon>
                {{ item["调整值含义"] || "未定义说明" }}
              </span>
              <div class="flex">
                <span class="flex-shrink-0 mr-18px">{{ item["宏变量名称"] }}</span>
                <el-input v-model="item.value" class="w-auto mr-10px"></el-input>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="告警等级" prop="severity">
          <el-radio-group v-model="data.trigger_detail.severity" size="large" fill="#6cf">
            <el-radio-button label="信息" value="信息" :class="severityDescriptionsAndColors[Severity.Info].class" />
            <el-radio-button label="警告" value="警告" :class="severityDescriptionsAndColors[Severity.Warning].class" />
            <el-radio-button
              label="一般严重"
              value="一般严重"
              :class="severityDescriptionsAndColors[Severity.Moderate].class"
            />
            <el-radio-button label="严重" value="严重" :class="severityDescriptionsAndColors[Severity.Severe].class" />
            <el-radio-button
              label="灾难"
              value="灾难"
              :class="severityDescriptionsAndColors[Severity.Disaster].class"
            />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="停启用" prop="enabled">
          <el-switch v-model="data.trigger_detail.enabled" active-text="启用" inactive-text="停用" />
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="flex justify-center">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="clickSaveItemDetail">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { getTriggersDetailById, updateTriggersDetailById } from "@/api/hosts/triggers";
import { Severity, severityDescriptionsAndColors } from "@/utils/alarm";
import { Warning } from "@element-plus/icons-vue";

const props = defineProps({
  modelValue: Boolean,
  trigger_id: String,
});
const emit = defineEmits(["update:modelValue", "response"]);
const data = reactive({
  trigger_detail: { scope: "host", values: { macro_variables: [], fixed_values: [] } },
  detaiLoading: false,
});
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    if (newValue) {
      data.detaiLoading = true;
      getTriggersDetailById(props.trigger_id)
        .then((res) => {
          data.trigger_detail = res.data;
          data.trigger_detail.scope = "host";
          console.log(res.data.values);
        })
        .catch(() => {
          dialogVisible.value = false;
        })
        .finally(() => {
          data.detaiLoading = false;
        });
    }
  }
);
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const rules = reactive({
  enabled: [{ required: true, message: "停启用", trigger: "blur" }],
});

function clickSaveItemDetail() {
  let params = { scope: data.trigger_detail.scope, data: data.trigger_detail };

  updateTriggersDetailById(params)
    .then((res) => {
      emit("response");
    })
    .finally(() => {
      dialogVisible.value = false;
    });
}
</script>

<style lang="scss" scoped>
.disable_div {
  @apply b-1 b-[#c7c7c7] bg-[#ededed] p-4px b-rounded-[4px] w-full;
}
.variable_list {
  @apply b-1 b-[#CECECEFF] w-full px-10px;
  .variable_item {
    @apply b-b-1 b-b-[#CECECEFF] w-full pb-10px;
    &:last-child {
      @apply border-b-0;
    }
  }
}

/* 选中状态下的圆圈颜色 */
.Unspecified.is-active ::v-deep {
  .el-radio-button__inner {
    background-color: #97aab3 !important;
    border-color: #97aab3 !important;
  }
}

.Info.is-active ::v-deep {
  .el-radio-button__inner {
    background-color: #7499ff !important;
    border-color: #7499ff !important;
  }
}

.Warning.is-active ::v-deep {
  .el-radio-button__inner {
    background-color: #ffc859 !important;
    border-color: #ffc859 !important;
  }
}
.Moderate.is-active ::v-deep {
  .el-radio-button__inner {
    background-color: #ffa059 !important;
    border-color: #ffa059 !important;
  }
}
.Severe.is-active ::v-deep {
  .el-radio-button__inner {
    background-color: #e97659 !important;
    border-color: #e97659 !important;
  }
}
.Catastrophic.is-active ::v-deep {
  .el-radio-button__inner {
    background-color: #e45959 !important;
    border-color: #e45959 !important;
  }
}
</style>
