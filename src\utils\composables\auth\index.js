import { excludingFeatures, licenseInfo, licenseInfoOption } from '@/constants/auth'
import { license_info, } from '@/store/modules/license'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
/**
 * @description: 按钮全限-提供给v-auth使用
 * @return {*}
 */
function useAuth() {
  function hasPermission(permission) {
    const settingsStore = useSettingsStore()
    const userStore = useUserStore()
    let auth = []//用户权限集合
    if (userStore.permissions.length == 0) {
      auth = []
    } else {
      auth = JSON.parse(JSON.parse(userStore.permissions).replace(/'/g, '"'))
    }
    if (settingsStore.app.enablePermission) {
      return auth.some(v => {
        return v === permission
      })
    } else {
      return true
    }
  }

  function auth(value) {
    let auth
    if (typeof value === 'string') {
      auth = hasPermission(value)
    } else {
      auth = value.some(item => {
        return hasPermission(item)
      })
      let excludingAuth = [];
      excludingAuth = excludingFeatures[license_info.value.license.PackageNames]
      if ( excludingAuth && excludingAuth.length > 0) {
        if (typeof value == 'string') {
          if (excludingAuth.includes(value)) {
            auth = false
          }
        } else {
          if (value.some(routeAuth => {
            return excludingAuth.includes(routeAuth)
          })) {
            auth = false
          }
        }
      }
    }
    return auth
  }

  function authAll(value) {
    const auth = value.every(item => {
      return hasPermission(item)
    })
    return auth
  }

  return {
    hasPermission,
    auth,
    authAll
  }
}

export default useAuth
