.text-tag {
  ::v-deep span {
    @apply w-100px;
    @include text-overflow;
  }
}
.green-block {
  width: 10px;
  /* 设置宽度 */
  height: 10px;
  /* 设置高度 */
  background-color: rgb(50, 182, 235);
  /* 设置背景颜色为绿色 */
  margin-right: 5px;
  /* 设置右侧间距 */
  clip-path: circle(50%);
  display: inline-block;
}

.data-wrapper {
  display: inline-block;
  //   padding-left: 30px;
}

.data-text {
  margin: 0;
  font-size: 16px;
  display: inline-block;
}

.unit-text {
  margin-top: 5px;
  display: inline-block;
}

.yello-block {
  width: 10px;
  /* 设置宽度 */
  height: 10px;
  /* 设置高度 */
  background-color: yellow;
  /* 设置背景颜色为绿色 */
  margin-right: 5px;
  /* 设置右侧间距 */
  clip-path: circle(50%);
  display: inline-block;
  margin-left: 20px;
}

.red-block {
  width: 10px;
  /* 设置宽度 */
  height: 10px;
  /* 设置高度 */
  background-color: red;
  /* 设置背景颜色为绿色 */
  margin-right: 5px;
  /* 设置右侧间距 */
  clip-path: circle(50%);
  display: inline-block;
  margin-left: 20px;
}

:deep {
  .el-popper {
    max-width: 40%;
    max-height: 50vh;
    overflow: auto;

    span {
      word-break: break-word;
      white-space: pre-line;
      line-height: 16px;
    }
  }
}
.target_checkbox {
  :deep {
    .el-checkbox-button__inner {
      padding: 0px 0px 2px 0px;
      margin: 4px 6px;
      font-size: 12px;
      border: none;
      border-bottom: 1px dotted;

      // background: none;
      box-shadow: none;
    }
  }
}
