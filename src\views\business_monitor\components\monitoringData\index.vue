<script setup>
import LastDataTableList from "@/views/problem_list/components/last_data_table_list.vue";
const props = defineProps({
  ip: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
</script>

<template>
  <div>
    <LastDataTableList :hostip="props.ip" :zabbix_id="props.id"></LastDataTableList>
  </div>
</template>

<style lang="scss" scoped>
// scss
</style>
