import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取所有ansible主机ip
 * @returns
 */
export function getAllAnsibleHostIP() {
  return api.get("/deal_config");
}

/**
 * 获取DB4BIX文件绑定的ip地址
 * @returns
 */
export function GetDb4bixBindIP() {
  return api.get("/deal_config/db4bix_config");
}

/**
 * 获取DB4BIX的配置内容
 * @returns
 */
export function GetDb4bixConfig() {
  return api.patch("/deal_config/db4bix_config");
}

/**
 * 绑定DB4BIX配置文件所在服务器ip
 * @param {*} data
 * @returns
 */
export function BindDb4bixIp(data) {
  return api.post("/deal_config/db4bix_config", JSON.stringify(data));
}

/**
 * 新增、修改、删除配置文件内容
 * @param {*} data
 * @returns
 */
export function DealDb4bixConfig(data) {
  return api.post("/deal_config/deal_db4bix_config/", JSON.stringify(data));
}
