<script setup name="HomepageInformation">
import { onMounted, reactive, ref, onUnmounted, watch } from "vue";
import { useAuth, usePagination } from "@/utils/composables";

import { cmdbGetResourceList, updateResourceAsset } from "@/api/modules/cmdb/resource";
import { GetDb4bixConfig, BindDb4bixIp, getAllAnsibleHostIP } from "@/api/modules/configuration/oracleDb4bix";
import { ElMessage } from "element-plus";
import { View, Edit, Location, Refresh } from "@element-plus/icons-vue";
import CreateDBForm from "@/views/homepage/cmdb_database/createDatabase.vue";
import SocketUtil from "@/utils/cmdb_utils";
import { showNotification } from "@/plugins/element-ui/index";
import { ResourceTypeEnum } from "../cmdb_asset/components/constants";
import {
  goToPageAssetDetail,
  pageChangeNum,
  deleteResource,
  addZabbixForResource,
  showConfirmationDialog,
} from "../components/utils";
import ZabbixResult from "@/containers/zabbix_resulte_dialog/index.vue";
import MoreOperations from "../components/more_operations.vue";

const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();
const { auth } = useAuth();
const router = useRouter();
const zabbixLoading = ref(false);
const zabbixResultDialog = ref(false);
const zabbixResultData = ref([]);

// 定义localStorage的key
const STORAGE_KEY = 'cmdb_database_search_condition';

const data = reactive({
  serverLoading: false,
  submitLoading: false,
  title: "",
  assetTitle: "",
  dialogDB4: false,
  db4: "",
  ips: [],
  dataList: [],
  allList: [],
  template_list: [],
  search: {
    searchName: "",
    isSearch: false,
  },
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },
  router_id: 0,
  drawer: false,
});

// 监听搜索条件变化，更新localStorage
watch(() => data.search, (newVal) => {
  if (newVal.searchName) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify({
      searchName: newVal.searchName,
      isSearch: newVal.isSearch
    }));
  }
}, { deep: true });

onMounted(() => {
  data.title = ResourceTypeEnum.database;
  
  // 从localStorage获取筛选条件
  const savedSearch = localStorage.getItem(STORAGE_KEY);
  if (savedSearch) {
    try {
      const parsedSearch = JSON.parse(savedSearch);
      data.search.searchName = parsedSearch.searchName || '';
      data.search.isSearch = parsedSearch.isSearch || false;
    } catch (e) {
      console.error('解析保存的筛选条件出错', e);
    }
  }
  
  getData();
});

// 组件卸载时清除localStorage
onUnmounted(() => {
  localStorage.removeItem(STORAGE_KEY);
});

SocketUtil.socket.onmessage = (recv) => {
  let res = JSON.parse(recv.data);
  switch (res.message.status_code) {
    case 200:
      showNotification("采集成功", res.message.message, "success");
      break;
    case 500:
      showNotification("采集失败", res.message.message, "error");
      break;
  }
};
//加载初始化数据
function getData() {
  data.serverLoading = true;
  GetDb4bixConfig()
    .then((res) => {
      getDataMessage();
    })
    .catch(() => {
      getName();
      data.dialogDB4 = true;
      data.serverLoading = false;
    });
}
// 获取名称
function getName() {
  getAllAnsibleHostIP().then((res) => {
    data.ips = res.data;
  });
}
// db4提交
function db4Submit() {
  data.submitLoading = true;
  BindDb4bixIp(data.db4)
    .then((res) => {
      if (res.data != "fail") {
        ElMessage.success("绑定成功");
        data.dialogDB4 = false;
        getDataMessage();
      } else {
        ElMessage.error(res.message);
      }
    })
    .finally(() => {
      data.submitLoading = false;
    });
}
// 获取数据信息
function getDataMessage() {
  cmdbGetResourceList(data.title)
    .then((res) => {
      res.data.map((item) => {
        item.iscluster = false;
        if (item.children) {
          item.iscluster = true;
          item.children.map((itemC) => {
            itemC.isclusterNode = true;
          });
        }
      });
      data.dataList = res.data;
      if (data.dataList.length == 0) {
        data.dataList = [];
        return;
      }
      data.allList = data.dataList;
      
      // 如果有筛选条件，应用筛选
      if (data.search.isSearch && data.search.searchName) {
        searchByName();
      } else {
      changePageNum(data.dataList);
      }
    })
    .finally(() => {
      data.serverLoading = false;
    });
}

function changePageNum(lists) {
  let res = pageChangeNum(lists, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
  data.serverLoading = false;
}

const drawerTitle = computed(() => {
  return data.router_id === 0 ? "新增数据库" : "编辑数据库";
});

//添加页面跳转
function create() {
  data.drawer = true;
  data.router_id = 0;
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

//筛选函数
function queryData() {
  if (data.search.searchName === "") {
    getData();
    data.search.isSearch = false;
  } else {
    searchByName();
  }
}

// 搜索
function searchByName() {
  let list = [];
  const ipRegex = /^[\d.]+$/;
  if (ipRegex.test(data.search.searchName.trim())) {
    list = data.allList.filter((item) => {
      return item.ip.trim().indexOf(data.search.searchName.trim()) != -1;
    });
  } else {
    list = data.allList.filter((item) => {
      return item.databaseName.trim().toLowerCase().indexOf(data.search.searchName.trim().toLowerCase()) != -1;
    });
  }
  data.search.isSearch = true;
  changePageNum(list);
}

// 跳转详情
function jumpAssetDetail(row, isMonitoring = false, item = data.title) {
  let queryParams = { item, id: row.zabbix_id, ip: row.ip, relation_id: row.relation_id };
  if (item == ResourceTypeEnum.server && !row.iscluster && !row.isclusterNode) {
    queryParams.id = row.server_zabbix_id;
  }
  goToPageAssetDetail(queryParams, isMonitoring);
}

// 编辑数据库
function editDatabase(row) {
  data.drawer = true;
  data.router_id = row.relation_id;
}

//批量删除
async function batchDel() {
  let list = [];
  data.batch.selectionDataList.forEach((item) => {
    if (item.relation_id && item.zabbix_id) {
      list.push({ relation_id: item.relation_id, zabbix_id: item.zabbix_id });
    }
  });
  const result = await deleteResource(ResourceTypeEnum.database, list);
  if (!result) {
    return;
  }
  getData();
}

/**
 * 是否拥有权限
 */
const isHavingAuth = computed(() => {
  return auth(["admin", "cmdb_database_information.excute", "create_task.browse"]);
});

/**
 * 跳转执行任务
 * @param row
 */
function toExcute(row) {
  const ip = row.ip;
  const queryParams = {
    ip,
  };
  if (data.title === "服务器") {
    queryParams.group = row.group_list.splice(",");
  } else {
    queryParams.group = row.group_name;
  }
  router.push({
    name: "create_task",
    query: queryParams,
  });
}

// 一键安装所有
function installAll() {
  let list = data.allList.filter((item) => item.status === "offmonitor");
  if (list.length == 0) {
    ElMessage.error("请检查是否有未安装监控服务的设备");
    return;
  }
  let list_template =
    list.filter((item) => {
      return item.template_list.length == 0;
    }) || [];
  if (list_template.length != 0) {
    let serverName = list_template.map((item) => item.serverName);
    showNotification("注意", "以下数据库没有添加监控模板：[" + serverName.join(",") + "],请注意！", "error");
    return;
  }
  addZabbix(list);
}

//单个安装监控
function install(item, is_skip = false) {
  if (item.template_list.length == 0) {
    ElMessage.error("数据库未配置监控模板，请配置！");
    return;
  }
  addZabbix([item], is_skip);
}
// 添加zabbix主机
async function addZabbix(ipList, is_skip = false) {
  let ip_information_list = [];
  ipList.forEach((item) => {
    ip_information_list.push({
      ip: item.ip,
      type: "database",
      group_name: item.group_name,
      id: item.zabbix_id,
    });
  });
  let params = {
    ip_information_list,
    resource_type_name: "database",
  };
  if (is_skip) {
    params.is_skip_ansible_script_execution = true;
  }
  const confirm = await showConfirmationDialog(
    is_skip ? "确定只添加zabbix主机吗？添加主机后，安装监控只能从自动运维功能安装" : ""
  );
  if (confirm) {
    zabbixLoading.value = true;
    zabbixResultDialog.value = true;
    zabbixResultData.value = [];
    try {
      zabbixResultData.value = await addZabbixForResource(params);
    } catch (error) {
      console.log(error);
    }
    zabbixLoading.value = false;
    getData();
  } else {
    ElMessage.info("已取消");
  }
}

//更新详情
function refreshAsset(row) {
  updateResourceAsset({
    type: data.title,
    relation_id: row.relation_id,
  }).then((res) => {
    showNotification("提示", res.message, "info");
  });
}

//是否可以被选择
const selectable = (row) => {
  return !row.isclusterNode && !row.iscluster;
};
</script>
<template>
  <div>
    <page-main :title="data.title">
      <div class="flex justify-between">
        <el-space wrap>
          <el-button type="primary" @click="create">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            新增
          </el-button>

          <el-button type="primary" @click="installAll">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:coordinate" />
              </el-icon>
            </template>
            一键安装所有
          </el-button>
          <el-button type="danger" :disabled="!data.batch.selectionDataList.length" @click="batchDel()">删除</el-button>
        </el-space>
        <el-space>
          <div class="w-220px">
            <el-input
              v-model="data.search.searchName"
              placeholder="输入名称或ip,支持模糊查询"
              clearable
              @keyup.enter="queryData"
            />
          </div>
          <el-button type="primary" @click="queryData()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>

      <el-table
        v-loading="data.serverLoading"
        class="list-table"
        :data="data.dataList"
        row-key="zabbix_id"
        border
        stripe
        highlight-current-row
        @selection-change="data.batch.selectionDataList = $event"
      >
        <el-table-column v-if="data.batch.enable" type="selection" :selectable="selectable" align="center" fixed />
        <el-table-column prop="databaseName" label="名称" align="center">
          <template #default="scoped">
            <el-link
              @click="jumpAssetDetail(scoped.row)"
              type="primary"
              v-if="!scoped.row.iscluster && !scoped.row.isclusterNode"
            >
              {{ scoped.row.databaseName }}
            </el-link>
            <el-link v-else-if="scoped.row.iscluster" @click="jumpAssetDetail(scoped.row)" type="primary">
              {{ scoped.row.databaseName }}
            </el-link>
            <span v-else>{{ scoped.row.databaseName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="108px" align="center">
          <template #default="scoped">
            <el-tag :type="scoped.row.iscluster ? 'warning' : 'success'" size="large">
              {{ scoped.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="databaseSoft" label="软件" width="100px" align="center" />
        <el-table-column label="服务器IP" prop="ip" align="center" />
        <el-table-column label="服务器" prop="databaseServer" align="center">
          <template #default="scoped">
            <el-link @click="jumpAssetDetail(scoped.row, false, ResourceTypeEnum.server)" type="primary">
              {{ scoped.row.databaseServer }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="监控状态" width="105px" align="center">
          <template #default="scoped">
            <template v-if="!scoped.row.isclusterNode">
              <el-tag v-if="scoped.row.status == 'online'">已启用</el-tag>
              <el-tag v-else-if="scoped.row.status == 'offmonitor'" type="info" effect="dark">未监控</el-tag>
              <el-tag v-else type="danger">停用的</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="连接状态" width="90px" align="center">
          <template #default="scoped">
            <template v-if="!scoped.row.isclusterNode">
              <el-tag v-if="scoped.row.isnline == true" type="success">在线</el-tag>
              <el-tag v-else type="danger">离线</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="300px">
          <template #default="scoped">
            <template v-if="!scoped.row.iscluster && !scoped.row.isclusterNode">
              <el-button type="primary" plain @click="editDatabase(scoped.row)">编辑</el-button>
              <MoreOperations>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="scoped.row.status === 'offmonitor'"
                    :icon="Location"
                    @click="install(scoped.row)"
                  >
                    安装监控
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="scoped.row.status == 'offmonitor'"
                    :icon="Location"
                    @click="install(scoped.row, true)"
                  >
                    添加zabbix主机
                  </el-dropdown-item>

                  <el-dropdown-item
                    :icon="Edit"
                    @click="toExcute(scoped.row)"
                    v-if="scoped.row.isnline == true && isHavingAuth"
                  >
                    执行任务
                  </el-dropdown-item>
                  <el-dropdown-item
                    :icon="View"
                    @click="jumpAssetDetail(scoped.row, true)"
                    v-if="scoped.row.status === 'online' || scoped.row.status === 'offline'"
                  >
                    最新数据
                  </el-dropdown-item>
                  <el-dropdown-item :icon="Refresh" @click="refreshAsset(scoped.row)">更新详情</el-dropdown-item>
                </el-dropdown-menu>
              </MoreOperations>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>
    <el-dialog v-model="data.dialogDB4" center title="请先进行绑定" width="30%" :destroy-on-close="true">
      <el-form label-position="right">
        <el-form-item label="绑定db4bix的ip：">
          <el-select v-model="data.db4" class="m-2" placeholder="选择绑定的ip">
            <el-option label="localhost" value="localhost"></el-option>
            <el-option v-for="item in data.ips" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.dialogDB4 = false">取消</el-button>
          <el-button type="primary" @click="db4Submit()" :loading="data.submitLoading">提交</el-button>
        </span>
      </template>
    </el-dialog>

    <el-drawer v-model="data.drawer" size="50%" :destroy-on-close="true" :close-on-click-modal="false">
      <template #header>
        <h4>{{ drawerTitle }}</h4>
      </template>
      <el-divider class="cancel_top" />
      <CreateDBForm v-model="data.router_id" @closeDialog="data.drawer = false" @getResource="getData()"></CreateDBForm>
    </el-drawer>

    <ZabbixResult v-model="zabbixResultDialog" :stepData="zabbixResultData" :loading="zabbixLoading"></ZabbixResult>
  </div>
</template>
<style scoped>
.cancel_top {
  margin-top: 0px;
}
</style>
