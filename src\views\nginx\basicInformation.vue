<script setup>
import { onMounted, reactive, computed, watch, ref } from 'vue';
import { QuestionFilled, Document, Folder, Files } from '@element-plus/icons-vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import NginxTopology from "./nginx.vue";
import api from "@/plugins/axios";
import { backNginx, restoreNginx, getRestore } from "@/api/modules/nginx_management";
const emit = defineEmits(['update:config', 'resetGraph', 'update:ip']);

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  overview: {
    type: Object,
    default: () => ({}),
  }
});
const nginxTopologyDialogVisible = ref(false);
const backupTimeDialogVisible = ref(false);
const backupTimeList = ref([]);
const selectedBackupTime = ref('');
const backupTimeLoading = ref(false);

// 服务器信息
const serverInfo = computed(() => {
  return {
    name: props.item.name || '未知服务器',
    ip: props.item.ip || '0.0.0.0',
    status: props.item.nginx_status === 'Active',
    version: props.item.nginx_version || 'Unknown',
    ports: props.item.listening_ports_list || [],
    errorLog: props.item.error_log || '/var/log/nginx/error.log',
    accessLog: props.item.access_log || '/var/log/nginx/access.log',
    configFile: props.item.nginx_file_path || '/etc/nginx/nginx.conf',
    includePath: props.item.include_path || '/etc/nginx/conf.d/*.conf',
    clusterList: props.item.cluster_list || []
  };
});

const data = reactive({
  isShow: true
});

// 查看拓扑
function nginxTopology() {
  if (!props.item.ip) {
    ElMessage.warning('IP地址不能为空');
    return;
  }
  // 存储拓扑图状态和IP到localStorage
  localStorage.setItem('nginx_topology_visible', 'true');
  localStorage.setItem('nginx_topology_ip', props.item.ip);
  nginxTopologyDialogVisible.value = true;
}

// 配置备份
function backup() {
  if (!props.item.ip) {
    ElMessage.warning('IP地址不能为空');
    return;
  }
  ElMessageBox.confirm('确定要备份当前配置吗？', '备份确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    backNginx({ 'ip_address': props.item.ip })
      .then(() => {
        ElMessage.success('配置已成功备份!');
      })
      .catch((error) => {
        console.error('备份失败:', error);
        ElMessage.error('备份失败，请检查网络连接或联系管理员');
      });
  }).catch(() => {
    ElMessage.info('已取消备份');
  });
}

// 恢复配置
function revert() {
  if (!props.item.ip) {
    ElMessage.warning('IP地址不能为空');
    return;
  }

  // 显示确认对话框
  ElMessageBox.confirm('确定要恢复配置吗？', '恢复确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 获取备份时间列表
    backupTimeLoading.value = true;
    getRestore(props.item.ip)
      .then((res) => {
        backupTimeList.value = res.data || [];
        backupTimeLoading.value = false;

        if (backupTimeList.value.length === 0) {
          ElMessage.warning('没有可用的备份');
          return;
        }

        // 打开备份时间选择弹窗
        backupTimeDialogVisible.value = true;
      })
      .catch((error) => {
        backupTimeLoading.value = false;
        console.error('获取备份列表失败:', error);
        ElMessage.error('获取备份列表失败，请检查网络连接或联系管理员');
      });
  }).catch(() => {
    ElMessage.info('已取消恢复');
  });
}

// 执行恢复操作
function doRestore() {
  if (!selectedBackupTime.value) {
    ElMessage.warning('请选择备份时间');
    return;
  }

  backupTimeDialogVisible.value = false;

  // 调用恢复接口
  restoreNginx({
    'ip_address': props.item.ip,
    'backup_time': selectedBackupTime.value
  })
    .then(() => {
      ElMessage.success('配置已成功恢复!');
    })
    .catch((error) => {
      console.error('恢复失败:', error);
      ElMessage.error('恢复失败，请检查网络连接或联系管理员');
    });
}
</script>

<template>
  <div class="nginx-dashboard">

    <!-- 操作栏 -->
    <div class="action-bar" v-if="data.isShow">
      <div class="server-title">
        <div class="server-name">{{ serverInfo.name }}</div>
        <div class="server-status" :class="{ 'status-active': serverInfo.status }">
          {{ serverInfo.status ? '运行中' : '已停止' }}
        </div>
      </div>
      <div class="action-group">
        <button class="action-btn view-topology" @click="nginxTopology">
          <svg-icon name="ep:view" class="btn-icon"></svg-icon>
          <span>查看拓扑</span>
        </button>
        <button class="action-btn backup" @click="backup" v-auth="['admin', 'nginx_list.archive']">
          <svg-icon name="ep:folder" class="btn-icon"></svg-icon>
          <span>备份配置</span>
        </button>
        <button class="action-btn revert" @click="revert" v-auth="['admin', 'nginx_list.restore']">
          <svg-icon name="ep:refresh" class="btn-icon"></svg-icon>
          <span>恢复配置</span>
        </button>
      </div>
    </div>

    <!-- 基本信息面板 -->
    <div class="info-panel">
      <div class="panel-header">
        <svg-icon name="ep:info-filled" class="header-icon"></svg-icon>
        <span>服务器基本信息</span>
      </div>

      <div class="panel-body">
        <div class="info-row">
          <div class="info-item">
            <div class="item-label">IP地址</div>
            <div class="item-value highlight">{{ serverInfo.ip }}</div>
          </div>
          <div class="info-item">
            <div class="item-label">版本</div>
            <div class="item-value">{{ serverInfo.version }}</div>
          </div>
          <div class="info-item wide-item">
            <div class="item-label">监听端口</div>
            <div class="item-value">
              <div class="port-tags">
                <span v-for="(port, index) in serverInfo.ports.slice(0, 5)" :key="index" class="port-tag">
                  {{ port }}
                </span>
                <span v-if="serverInfo.ports.length > 5" class="port-tag more-tag">
                  +{{ serverInfo.ports.length - 5 }}
                </span>
                <span v-if="!serverInfo.ports.length" class="no-data">暂无端口配置</span>
              </div>
            </div>
          </div>
        </div>

        <div class="info-row files-row">
          <div class="info-item wide-item">
            <div class="item-label">
              <Document class="file-icon" />
              <span>错误日志</span>
            </div>
            <div class="item-value file-path">{{ serverInfo.errorLog }}</div>
          </div>
          <div class="info-item wide-item">
            <div class="item-label">
              <Document class="file-icon" />
              <span>访问日志</span>
            </div>
            <div class="item-value file-path">{{ serverInfo.accessLog }}</div>
          </div>
        </div>

        <div class="info-row files-row">
          <div class="info-item wide-item">
            <div class="item-label">
              <Folder class="file-icon" />
              <span>配置文件</span>
            </div>
            <div class="item-value file-path">{{ serverInfo.configFile }}</div>
          </div>
          <div class="info-item wide-item">
            <div class="item-label">
              <Files class="file-icon" />
              <span>包含路径</span>
            </div>
            <div class="item-value file-path">{{ serverInfo.includePath }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item full-width">
            <div class="item-label">集群信息</div>
            <div class="item-value">
              <div class="cluster-info">
                <div v-if="serverInfo.clusterList.length === 0" class="no-data">未配置集群</div>
                <div v-else class="cluster-nodes">
                  <span v-for="(node, index) in serverInfo.clusterList" :key="index" class="cluster-node">
                    {{ node }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <NginxTopology v-model="nginxTopologyDialogVisible" :ip="serverInfo.ip" />

  <!-- 备份时间选择弹窗 -->
  <el-dialog v-model="backupTimeDialogVisible" title="选择备份时间" width="500px" :close-on-click-modal="false"
    :close-on-press-escape="false">
    <div v-loading="backupTimeLoading" class="backup-time-container">
      <div v-if="backupTimeList.length === 0" class="no-backup-tip">
        当前没有可用的备份
      </div>
      <el-radio-group v-else v-model="selectedBackupTime" class="backup-time-list">
        <el-radio v-for="(time, index) in backupTimeList" :key="index" :label="time" border class="backup-time-item">
          {{ time }}
        </el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="backupTimeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="doRestore" :disabled="!selectedBackupTime">
          恢复
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
.nginx-dashboard {
  font-family: 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
  color: #2c3e50;
  background-color: #f8f9fc;
  border-radius: 8px;
}

/* 概览统计部分 */
.overview-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;
}

.overview-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.overview-icon {
  width: 52px;
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-right: 16px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.icon-svg {
  width: 30px;
  height: 30px;
}

.overview-content {
  flex: 1;
}

.overview-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 6px;
}

.overview-title {
  font-size: 14px;
  color: #606266;
}

.proxy-card {
  border-left: 4px solid #67c23a;
}

.proxy-card .overview-icon {
  background-color: #67c23a;
}

.proxy-card .icon-svg {
  color: white;
}

.redirect-card {
  border-left: 4px solid #e6a23c;
}

.redirect-card .overview-icon {
  background-color: #e6a23c;
}

.redirect-card .icon-svg {
  color: white;
}

.stream-card {
  border-left: 4px solid #409eff;
}

.stream-card .overview-icon {
  background-color: #409eff;
}

.stream-card .icon-svg {
  color: white;
}

.notfound-card {
  border-left: 4px solid #f56c6c;
}

.notfound-card .overview-icon {
  background-color: #f56c6c;
}

.notfound-card .icon-svg {
  color: white;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-radius: 6px;
  padding: 16px 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.server-title {
  display: flex;
  align-items: center;
}

.server-name {
  font-size: 18px;
  font-weight: 600;
  margin-right: 12px;
}

.server-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: #f56c6c;
  color: white;
  font-weight: 600;
}

.status-active {
  background-color: #67c23a;
}

.action-group {
  display: flex;
  gap: 10px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.btn-icon {
  width: 18px;
  height: 18px;
  color: white;
}

.view-config {
  background-color: #409eff;
}

.view-config:hover {
  background-color: #66b1ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.view-topology {
  background-color: #67c23a;
}

.view-topology:hover {
  background-color: #85ce61;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.backup {
  background-color: #e6a23c;
}

.backup:hover {
  background-color: #ebb563;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.revert {
  background-color: #f56c6c;
}

.revert:hover {
  background-color: #f78989;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

/* 信息面板 */
.info-panel {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.panel-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background-color: #409eff;
  color: white;
  border-bottom: 1px solid #ebeef5;
  font-size: 16px;
  font-weight: 600;
}

.header-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  color: white;
}

.panel-body {
  padding: 20px;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  gap: 20px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  min-width: 200px;
}

.wide-item {
  flex: 2;
  min-width: 300px;
}

.full-width {
  flex: 0 0 100%;
}

.item-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.item-value {
  font-size: 15px;
  color: #303133;
  font-weight: 500;
}

.highlight {
  color: #409eff;
  font-weight: 600;
  font-size: 16px;
}

.file-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  color: #606266;
}

.file-path {
  padding: 10px 14px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  word-break: break-all;
  border-left: 3px solid #409eff;
}

.files-row .info-item {
  margin-bottom: 10px;
}

.port-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.port-tag {
  display: inline-block;
  padding: 5px 10px;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  border: 1px solid rgba(64, 158, 255, 0.2);
}

.more-tag {
  background-color: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.no-data {
  color: #909399;
  font-style: italic;
  font-size: 13px;
}

.cluster-nodes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.cluster-node {
  display: inline-block;
  padding: 6px 10px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  border: 1px solid rgba(103, 194, 58, 0.2);
}

@media (max-width: 1200px) {
  .overview-section {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .server-title {
    margin-bottom: 16px;
  }

  .action-group {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 8px;
  }

  .info-item,
  .wide-item {
    flex: 0 0 100%;
  }
}

@media (max-width: 768px) {
  .overview-section {
    grid-template-columns: 1fr;
  }

  .action-btn span {
    display: none;
  }

  .action-btn {
    padding: 10px;
  }

  .panel-body {
    padding: 16px;
  }

  .nginx-dashboard {
    padding: 12px;
  }
}

.backup-time-container {
  min-height: 180px;
  padding: 10px 0;
}

.no-backup-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 180px;
  color: #909399;
  font-size: 16px;
}

.backup-time-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
  padding: 5px;
}

.backup-time-item {
  margin-right: 0;
  margin-bottom: 8px;
  width: 100%;
}
</style>
