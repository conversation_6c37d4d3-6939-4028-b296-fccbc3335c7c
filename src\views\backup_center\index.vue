<template>
  <LayoutContainer left-side-width="300px" :hide-left-side-toggle="false">
    <!-- 左侧 -->
    <template #leftSide>
      <el-input v-model="filterText" style="width: 100%" placeholder="搜索关键字" />

      <el-tree
        ref="treeRef"
        style="max-width: 600px"
        class="mt-20px filter-tree"
        :data="data"
        :props="defaultProps"
        default-expand-all
        :filter-node-method="filterNode"
        @node-click="onNodeClick"
      />
    </template>
    <div>
      当前选择的：{{ currentNode }}
      <div>
        <el-button type="primary" plain>配置备份</el-button>
        <el-button type="primary" plain>对比配置</el-button>
      </div>
    </div>
  </LayoutContainer>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElTree } from "element-plus";

interface Tree {
  [key: string]: any;
}

const filterText = ref("");
const treeRef = ref<InstanceType<typeof ElTree>>();

const defaultProps = {
  children: "children",
  label: "label",
};

watch(filterText, (val) => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};

const data: Tree[] = [
  {
    id: 1,
    label: "服务器",
    children: [
      {
        id: 4,
        label: "服务器1",
      },
    ],
  },
  {
    id: 2,
    label: "中间件",
    children: [
      {
        id: 5,
        label: "中间件1",
      },
      {
        id: 6,
        label: "中间件2",
      },
    ],
  },
];

const currentNode = ref();
function onNodeClick(node) {
  currentNode.value = toRaw(node);
}
</script>

<style scoped lang="scss">
.filter-tree {
  ::v-deep {
    .el-tree-node__content {
      height: 40px;
    }
  }
}
</style>
