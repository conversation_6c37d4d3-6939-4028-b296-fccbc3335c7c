
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import utc from 'dayjs/plugin/utc'

dayjs.locale('zh-cn')
dayjs.extend(utc);
export default dayjs
/**
 * @description: UTC格式化成本地时间
 * @param {*} timeStr
 * @param {*} formatter
 * @return {*}
 */
export function formatLocalTime(timeStr, formatter = 'YYYY-MM-DD HH:mm:ss') {
  if (!(timeStr && dayjs(timeStr))) return ''
  return dayjs.utc(timeStr).local().format(formatter)
}

/**
 * @description: 根据时间和时区偏移获取UTC+0时间
 * @param {*} dateString 时间戳和日期都可以，
 * @param {*} offset 时区偏移 北京为8
 * @return {*} 格式化后的日期时间
 */
export function getUTCWithOffset(dateString, offset = 8) {
  let date
  if (isNaN(Number(dateString))) {//如果是日期时间，Number为NaN
    date = dayjs(dateString).utcOffset(offset);
  } else {//如果能转成数字，说明是时间戳
    date = dayjs(Number(dateString)).utcOffset(offset);
  }
  return dayjs(date.utc()).format('YYYY-MM-DD HH:mm:ss');
}
/**
 * @description: 根据时间和时区偏移获取UTC+0时间
 * @param {*} dateString 时间戳和日期都可以，秒和毫秒都可以
 * @return {*} 返回的是秒级的时间戳
 */
export function getUTCTimestampWithOffset(dateString) {
  let date
  if (isNaN(Number(dateString))) {//如果是日期时间，Number为NaN
    date = dayjs(dateString)
  } else {//如果能转成数字，说明是时间戳
    date = dayjs(Number(dateString))
  }
  // console.log(dayjs(date.utc().format('YYYY-MM-DD HH:mm:ss')));
  return dayjs(date.utc().format('YYYY-MM-DD HH:mm:ss')).valueOf() / 1000;
}