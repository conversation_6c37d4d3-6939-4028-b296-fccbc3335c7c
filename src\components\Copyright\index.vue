<script setup name="Copyright">
import useSettingsStore from "@/store/modules/settings";
import { license_info } from "@/store/modules/license";
const data = reactive({
  supporterMail: "",
  supporterWebsite: "",
  PackageNames: "",
  developer: "",
});
const settingsStore = useSettingsStore();
// const supporterMail=license_info.value.license.supporterMail
// const supporterWebsite=license_info.value.license.supporterWebsite
// const PackageNames=license_info.value.license.PackageNames
// const developer=license_info.value.license.developer
onMounted(() => {
  data.supporterMail = license_info.value.license.supporterMail;
  data.supporterWebsite = license_info.value.license.supporterWebsite;
  data.PackageNames = license_info.value.license.PackageNames;
  data.developer = license_info.value.license.developer;
});
</script>

<template>
  <footer class="copyright">
    <span>Copyright</span>
    <el-icon size="18px">
      <svg-icon name="i-ri:copyright-line" />
    </el-icon>
    <span>{{ data.developer }}</span>
    <span>{{ data.supporterMail }}</span>
    <a :href="data.supporterWebsite" target="_blank" rel="noopener">{{ data.supporterWebsite }}</a>
    <span class="packname">{{ data.PackageNames }}</span>
    <!-- <a href="/version.md" target="_blank">版本更新</a> -->
    <router-link :to="{ name: 'version' }">版本日志</router-link>
  </footer>
</template>

<style lang="scss" scoped>
.copyright {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  span,
  a {
    padding: 0 5px;
  }
  .el-icon {
    margin: 0 2px;
  }
  a {
    text-decoration: none;
    color: var(--el-text-color-secondary);
    transition: var(--el-transition-color);
    &:hover {
      color: var(--el-text-color-primary);
    }
  }
  .packname {
    float: right;
  }
}
</style>
