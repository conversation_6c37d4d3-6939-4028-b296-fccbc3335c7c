import { createHtmlPlugin } from 'vite-plugin-html'

export default function createHtml(env, isBuild) {
    const { VITE_APP_TITLE, VITE_APP_DEBUG_TOOL } = env
    const html = createHtmlPlugin({
        inject: {
            data: {
                title: VITE_APP_TITLE,
                debugTool: VITE_APP_DEBUG_TOOL,
                copyrightScript: `
<script>
if ((navigator.language || navigator.browserLanguage).toLowerCase() === 'zh-cn') {
    console.info('由中联联壹提供技术支持');
} else {
    console.info('由中联联壹提供技术支持');
}
</script>
                `
            }
        },
        minify: isBuild
    })
    return html
}
