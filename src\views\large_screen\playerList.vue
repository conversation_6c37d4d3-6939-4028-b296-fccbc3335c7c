<script setup name="LargeScreenPlayerList">
import useSettingsStore from "@/store/modules/settings";
import { useTabbar } from "@/utils/composables";
import { SubmitGrafanaList, GetGrafanaList } from "@/api/modules/screen_management/screen";
import storage from "@/utils/storage";
import { onMounted, reactive } from "vue";
import { goBack } from "@/utils";
import { ArrowLeftBold } from "@element-plus/icons-vue";

const settingsStore = useSettingsStore();
const router = useRouter();
const route = useRoute();
const data = reactive({
  dataList: [],
  selectList: [],
  dialog: false,
  dashbord: [],
  dashbordList: [],
  selectRow: [],
  loading: true,
});

onMounted(() => {
  getData();
});
function getData() {
  data.loading = true;
  const dataList = JSON.parse(storage.session.get("player_list"));
  data.dashbordList = JSON.parse(dataList.data).flatMap((obj) => Object.values(obj)[0]);
  data.selectList = [];
  data.dashbordList.forEach((element) => {
    data.selectList.push({ key: element.title, lable: element.id });
  });
  if (dataList.opt == "create") {
    data.dataList = [];
    data.loading = false;
  } else {
    GetGrafanaList().then((res) => {
      data.loading = false;
      data.dataList = res.data;
      data.dashbord = [];
      res.data.forEach((element) => {
        data.dashbord.push(element.title);
      });
    });
  }
}
function onAddItem() {
  data.dialog = true;
}
function submit() {
  const result = data.dashbordList.filter((obj) => data.dashbord.includes(obj.title));
  data.dataList = result;
  data.dialog = false;
}
function upward(item) {
  data.dataList[item] = data.dataList.splice(item - 1, 1, data.dataList[item])[0];
}
function downward(item) {
  data.dataList[item] = data.dataList.splice(item + 1, 1, data.dataList[item])[0];
}
function deleteRow(item) {
  data.dataList.splice(item, 1);
}
function topping(item) {
  if (item != 0) {
    data.dataList.unshift(data.dataList.splice(item, 1)[0]);
  }
}
function save() {
  data.loading = true;

  SubmitGrafanaList(data.dataList).then((res) => {
    data.loading = false;
  });
}
</script>

<template>
  <div>
    <page-main v-loading="data.loading" title="大屏播放列表">
      <template #title>
        <el-icon class="cursor-pointer mr-10px mt-5px" @click="goBack()"><ArrowLeftBold /></el-icon>
        大屏播放列表
      </template>
      <el-table :data="data.dataList" style="width: 100%">
        <!-- <el-table  @selection-change="handleSelectionChange" :data="data.dataList" style="width: 100%"> -->
        <!-- <el-table-column :selectable="selectTable" type="selection" width="55" /> -->
        <el-table-column prop="title" label="名称" />
        <el-table-column prop="folderTitle" label="所属分组" />
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="tags" label="标签">
          <template #default="scope">
            <span style="padding-left: 5px" v-for="item in scope.row.tags" :key="item">
              <el-tag>{{ item }}</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="250">
          <template #default="scope">
            <el-button v-if="scope.$index != 0" type="primary" size="small" @click.prevent="upward(scope.$index)">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:arrow-up-bold" />
                </el-icon>
              </template>
            </el-button>
            <el-button
              v-if="scope.$index != data.dataList.length - 1"
              type="primary"
              size="small"
              @click.prevent="downward(scope.$index)"
            >
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:arrow-down-bold" />
                </el-icon>
              </template>
            </el-button>
            <el-button v-if="scope.$index != 0" type="primary" size="small" @click.prevent="topping(scope.$index)">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:upload" />
                </el-icon>
              </template>
            </el-button>
            <el-button type="danger" size="small" @click.prevent="deleteRow(scope.$index)">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:delete" />
                </el-icon>
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button class="mt-4" style="width: 100%" @click="onAddItem">添加面板</el-button>
      <div style="margin-top: 20px">
        <!-- <el-button @click="changePlace">交换位置</el-button> -->
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </page-main>
    <el-dialog :destroy-on-close="true" v-model="data.dialog" title="选择面板" class="dialog-content" width="75%">
      <el-transfer
        filterable
        :button-texts="['取消', '选择']"
        :titles="['面板列表', '选择']"
        v-model="data.dashbord"
        :data="data.selectList"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.dialog = false">取消</el-button>
          <el-button type="primary" @click="submit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
// scss
.dialog-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-transfer {
  --el-transfer-panel-width: 350px;
}
</style>
