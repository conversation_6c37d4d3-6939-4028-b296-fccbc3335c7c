<script setup>
import Storage from "@/utils/storage";
import { DealLogstashConfig } from "@/api/modules/configuration/oracleLogstash";
import { deepClone } from "@/utils";
import { DbType } from "@/constants/cmdb";

const formRef = ref();
const router = useRouter();

const data = ref({
  loading: false,
  isEdit: false,
  form: {
    dbtype: DbType.Oracle,
    dbip: "",
    dbname: "",
    user: "",
    passwd: "",
    port: "",
    instance: "",
  },
  rules: {
    dbip: [{ required: true, message: "请输入数据库服务器的ip地址", trigger: "blur" }],
    dbname: [{ required: true, message: "请数据采集服务器的信息", trigger: "blur" }],
    user: [{ required: true, message: "请输入登陆的用户", trigger: "blur" }],
    passwd: [{ required: true, message: "请输入连接用户的密码", trigger: "blur" }],
    port: [{ required: true, message: "请输入数据库的开放端口", trigger: "blur" }],
    instance: [{ required: true, message: "请输入数据库的实例名", trigger: "blur" }],
  },
  dataList: [],
  index: 0,
  spliceNum: 1,
});

onMounted(() => {
  if (Storage.local.get("cookieData") == null) {
    router.push({ name: "basic_configuration" });
    return;
  }
  const temp_data = JSON.parse(Storage.local.get("cookieData"));
  Storage.local.remove("cookieData");
  const id = temp_data.id;
  // 编辑
  if (temp_data.isEdit) {
    if (temp_data.dbtype == DbType.Oracle) {
      data.value.form = temp_data.dataList.oracle_config_list.find((item) => item.id === id);
    } else {
      data.value.form = temp_data.dataList.pg_config_list.find((item) => item.id === id);
    }

    data.value.index = temp_data.id;
  }
  data.value.dataList = deepClone(temp_data.dataList);
  data.value.isEdit = temp_data.isEdit;
  data.value.form.dbtype = temp_data.dbtype;
});

defineExpose({
  submit(callback) {
    formRef.value.validate((valid) => {
      if (valid) {
        let formData = deepClone(data.value.form);
        delete formData.dbtype;
        // 编辑
        if (data.value.isEdit) {
          const spliceNum = data.value.spliceNum;
          data.value.form.dbtype == DbType.Oracle
            ? data.value.dataList.oracle_config_list.splice(data.value.index, spliceNum, formData)
            : data.value.dataList.pg_config_list.splice(data.value.index, spliceNum, formData);
        } else {
          // 新增
          data.value.form.dbtype == DbType.Oracle
            ? data.value.dataList.oracle_config_list.push(formData)
            : data.value.dataList.pg_config_list.push(formData);
        }
        let params = {
          oracle_config_list: data.value.dataList.oracle_config_list,
          pg_config_list: data.value.dataList.pg_config_list,
        };
        data.value.loading = true;
        DealLogstashConfig(params)
          .then(() => {
            callback && callback();
          })
          .finally(() => {
            data.value.loading = false;
          });
      }
    });
  },
});
</script>

<template>
  <div v-loading="data.loading">
    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="200px" label-suffix="：">
      <el-form-item label="数据库类型" prop="dbtype">
        <el-radio :value="DbType.Oracle" v-model="data.form.dbtype" :disabled="data.isEdit">Oracle数据库</el-radio>
        <el-radio :value="DbType.Pg" v-model="data.form.dbtype" :disabled="data.isEdit">Pg数据库</el-radio>
      </el-form-item>
      <el-form-item label="ip地址" prop="dbip">
        <el-input v-model="data.form.dbip" placeholder="请输入被采集数据库ip" />
      </el-form-item>
      <el-form-item label="连接端口" prop="port">
        <el-input v-model="data.form.port" placeholder="请输入被采集数据库连接端口" />
      </el-form-item>
      <el-form-item label="登陆用户" prop="user">
        <el-input v-model="data.form.user" placeholder="请输入被采集数据库登陆用户" />
      </el-form-item>
      <el-form-item label="连接用户密码" prop="passwd">
        <el-input
          v-if="data.spliceNum == 0"
          v-model="data.form.passwd"
          type="password"
          show-password
          placeholder="请输入登陆用户的密码"
        />
        <el-input v-else v-model="data.form.passwd" type="password" placeholder="请输入登陆用户的密码" />
      </el-form-item>
      <el-form-item label="数据库名称" prop="dbname">
        <el-input v-model="data.form.dbname" placeholder="请输入被采集的数据库名" />
      </el-form-item>
      <el-form-item label="数据库实例名" prop="instance">
        <el-input v-model="data.form.instance" placeholder="请输入被采集的数据库实例名" />
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
// scss
</style>
