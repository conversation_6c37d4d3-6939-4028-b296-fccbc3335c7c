const Layout = () => import("@/layout/index.vue");
let children = [
  {
    path: "report",
    name: "report",
    redirect: "/report_manager/report/weekly_list",
    meta: {
      title: "巡检报告",
      auth: ["admin", "report.browse"],
    },
    children: [
      {
        path: "weekly_list",
        name: "weekly_list",
        component: () => import("@/views/inspection_report/weeklyReport/list.vue"),
        meta: {
          title: "周报管理",
          auth: ["admin", "weekly_list.browse"],
        },
      },
      {
        path: "report_detail",
        name: "report_detail",
        component: () => import("@/views/inspection_report/weeklyReport/detail.vue"),
        meta: {
          title: "查看周报",
          sidebar: false,
          activeMenu: "/report_manager/report/weekly_list",
          auth: ["admin", "weekly_list.browse"],
        },
      },
      {
        path: "daily_list",
        name: "daily_list",
        component: () => import("@/views/inspection_report/dailyReport/list.vue"),
        meta: {
          title: "日报管理",
          auth: ["admin", "daily_list.browse"],
        },
      },
      {
        path: "custom_inspection",
        name: "custom_inspection",
        component: () => import("@/views/inspection_report/custom_inspection/list.vue"),
        meta: {
          title: "自定义巡检",
          auth: ["admin", "custom_inspection.browse"],
        },
      },
    ],
  },
  {
    path: "backup_list",
    name: "backup_list",
    component: () => import("@/views/backup/list.vue"),
    meta: {
      title: "备份概览",
      auth: ["admin", "backup_list.browse"],
    },
    children: [
      {
        path: "backup_information",
        name: "backup_information",
        component: () => import("@/views/backup/detail.vue"),
        meta: {
          title: "备份概览详情",
          sidebar: false,
          activeMenu: "/report_manager/backup_list",
        },
      },
    ],
  },
  {
    path: "bussiness_analysis",
    name: "bussiness_analysis",
    component: () => import("@/views/business_monitor/business_analysis.vue"),
    meta: {
      title: "资源报表",
      auth: ["admin", "bussiness_analysis.browse"],
    },
  },
];
export default {
  path: "/report_manager",
  component: Layout,
  redirect: "/report_manager/report",
  name: "report_manager",
  meta: {
    auth: ["admin", "report_manager.browse"],
    title: "报表管理",
    icon: "business_monitoring",
  },
  children,
};
