import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 上传自愈功能包
 * @param {*} data
 * @returns
 */
export function uploadCurePackage(data) {
  return api.post("/self_healing_function/upload_package/", data);
}

/**
 * 获取所有自愈功能包信息
 */
export function getAllPackageInfo() {
  return api.get("/self_healing_function/get_all_healing_package/");
}

/**
 * 删除上传的功能包
 * @param {*} fileName
 * @returns
 */
export function deleteCurePackage(file) {
  return api.get(`/self_healing_function/delete_package/?file_name=${file.file_name}&should_delete_relation=${file.should_delete_relation}`);
}

/**
 * 获取所有触发器信息
 * @returns
 */
export function getAllTriggerInfo() {
  return api.get("/self_healing_function/all_trigger_info/");
}
/**
 * 添加自愈关系
 * @param {*} data
 * @returns
 */
export function addCureRelation(data) {
  return api.post("/self_healing_function/create_or_update_trigger_mapping/", JSON.stringify(data));
}

/**
 * 获取所有的自愈关系
 * @returns
 */
export function getAllCureRelation() {
  return api.get("/self_healing_function/get_all_trigger_mapping/");
}

/**
 * 删除自愈关系
 * @param {} trigger_id
 * @returns
 */
export function deleteCureRelation(trigger_id) {
  return api.get(`/self_healing_function/delete_trigger_mapping/${trigger_id}/`);
}

/**
 * 获取所有自愈触发记录
 * @returns
 */
export function queryAllTriggerRecordInfo() {
  return api.get("/self_healing_function/get_self_healing_results/");
}

/**
 * 批量删除自愈触发记录
 * @returns
 */
export function batchDelRecord(data) {
  return api.post("/self_healing_function/delete_self_healing_results/", JSON.stringify(data));
}

