const Layout = () => import("@/layout/index.vue");

const children = [
  {
    path: "monitorTemplateList",
    name: "monitor_template_list",
    component: () => import("@/views/template_configuration/monitor_template/list.vue"),
    meta: {
      title: "监控模板",
      auth: ["admin", "monitor_template_list.browse"],
    },
  },
  {
    path: "nginx_list",
    name: "nginx_list",
    component: () => import("@/views/nginx/index.vue"),
    meta: {
      title: "nginx配置",
      copyright: false,
      auth: ["admin", "nginx_list.browse"],
    },
  },
  {
    path: "task_list",
    name: "task_list",
    component: () => import("@/views/scheduled_task/list.vue"),
    meta: {
      title: "定时任务管理",
      sidebar: true,
      auth: ["admin", "task_list.browse"],
    },
  },
  {
    path: "information",
    name: "cmdb_information",
    component: () => import("@/views/model_configuration/cmdb_soft/information.vue"),
    meta: {
      title: "软件管理",
      auth: ["admin", "cmdb_information.browse"],
    },
    children: [
      {
        path: "create",
        name: "create_cmdb",
        component: () => import("@/views/model_configuration/cmdb_soft/create.vue"),
        meta: {
          title: "新增软件",
          sidebar: false,
          activeMenu: "/configuration_management/information",
        },
      },
    ],
  },
  {
    path: "dictionary",
    name: "dictionary",
    component: () => import("@/views/dictionary/index.vue"),
    meta: {
      title: "字典管理",
      auth: ["admin", "dictionary.browse"],
    },
  },
  {
    path: "basic_configuration",
    name: "basic_configuration",
    component: () => import("@/views/personal/configuration.vue"),
    meta: {
      title: "基础配置",
      auth: ["admin", "basic_configuration.browse"],
    },
    children: [
      {
        path: "logstashCreate",
        name: "logstashCreate",
        component: () => import("@/views/personal/components/logstash_conf/detail.vue"),
        meta: {
          title: "新增logstash",
          sidebar: false,
          activeMenu: "/configuration_management/basic_configuration",
        },
      },
      {
        path: "logstashEdit",
        name: "logstashEdit",
        component: () => import("@/views/personal/components/logstash_conf/detail.vue"),
        meta: {
          title: "编辑logstash",
          sidebar: false,
          activeMenu: "/configuration_management/basic_configuration",
        },
      },
      {
        path: "db4bixEdit",
        name: "db4bixEdit",
        component: () => import("@/views/personal/components/db4bix_conf/detail.vue"),
        meta: {
          title: "编辑db4bix",
          sidebar: false,
          activeMenu: "/configuration_management/basic_configuration",
        },
      },
      {
        path: "db4bixCreate",
        name: "db4bixCreate",
        component: () => import("@/views/personal/components/db4bix_conf/detail.vue"),
        meta: {
          title: "新增db4bix",
          sidebar: false,
          activeMenu: "/configuration_management/basic_configuration",
        },
      },
    ],
  },
];
export default {
  path: "/configuration_management",
  name: "configurationmanagement",
  redirect: "/configurationmanagement/monitorTemplateList",
  component: Layout,
  meta: {
    auth: ["admin", "configurationmanagement.browse"],
    title: "配置管理",
    icon: "applicationSoftware",
  },
  children,
};
