import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取所有业务资源
 * @param {*} data
 * @returns
 */
export function Topology(data) {//根据业务id获取业务的详细信息
  return api.get('/home_page/resource_topology/?business_id=' + data)
}
export function ResourceInfomation(data) {//根据资源id获取资源的详细信息
  return api.get('/home_page/detailed_information/?resource=' + JSON.stringify(data))
}
export function GetAllBussiness() {//获取所有业务
  return api.get('/home_page/all/')
}
export function SubmitEdges(data) {//根据资源id获取资源的详细信息
  return api.post('/home_page/resource_topology_port/', JSON.stringify(data))
}