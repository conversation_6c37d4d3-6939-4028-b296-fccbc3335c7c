<script setup>
import useSettingsStore from "@/store/modules/settings";
import { useTabbar } from "@/utils/composables";
import { onMounted, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Viewer } from "@bytemd/vue-next";
import gfm from "@bytemd/plugin-gfm";
import gfmLocale from "@bytemd/plugin-gfm/lib/locales/zh_Hans.json";
import highlight from "@bytemd/plugin-highlight";
import "bytemd/dist/index.css";
import "highlight.js/styles/vs.css";
const route = useRoute();
const plugins = [
    gfm({
        locale: gfmLocale,
    }),
    highlight({}),
];
const data = reactive({
    title: "",
    fileContent: "",
});
onMounted(() => {
    getConfigFile();
});
function getConfigFile() {
    data.title = route.query.fileName + "文件";
    data.fileContent = route.query.fileContent;
}

</script>
<template>
    <div>

        <page-main title="配置内容">
            <Viewer :value="'```sh' + data.fileContent + '\n```'" :plugins="plugins" style="font-size: 15px" />
        </page-main>
    </div>
</template>
<style scoped lang="scss"></style>
