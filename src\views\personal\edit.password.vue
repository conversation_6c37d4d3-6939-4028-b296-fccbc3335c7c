<route>
{
    name: 'personalEditPassword',
    meta: {
        title: "修改密码"
    }
}
</route>

<script setup name="PersonalEditPassword">
import useUserStore from "@/store/modules/user";
import storage from "@/utils/storage";

const route = useRoute();
const router = useRouter();

const userStore = useUserStore();
const newPassword = (rule, value, callback) => {
    var reg1 =
        /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*.])[\da-zA-Z~!@#$%^&*.]{8,}$/; //密码必须是8位以上、必须含有字母、数字、特殊符号
    if (!reg1.test(value)) {
        callback(new Error("密码必须是8位以上、必须含有字母、数字、特殊符号"));
    } else {
        callback();
    }
};
const validatePassword = (rule, value, callback) => {
    if (value !== form.value.newpassword) {
        callback(new Error("密码不一致"));
    } else {
        callback();
    }
};

const formRef = ref();
const form = ref({
    name: storage.local.get("account"),
    password: "",
    newpassword: "",
    checkpassword: "",
});

const rules = ref({
    password: [{ required: true, message: "请输入原密码", trigger: "blur" }],
    newpassword: [
        { required: true, message: "请输入新密码", trigger: "blur" },
        { required: true, trigger: "blur", validator: newPassword },
    ],
    checkpassword: [
        { required: true, message: "请输入新密码", trigger: "blur" },
        { validator: validatePassword },
    ],
});

function onSubmit() {
    formRef.value.validate((valid) => {
        if (valid) {
            userStore
                .editPassword(form.value)
                .then(() => {
                    userStore.logout().then(() => {
                        router.push({
                            name: "login",
                            query: {
                                redirect: route.fullPath,
                            },
                        });
                    });
                })
                .catch(() => { });
        }
    });
}
</script>

<template>
    <div>
        <page-main>
            <el-row>
                <el-col :md="24" :lg="12">
                    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
                        <el-form-item label="原密码" prop="password">
                            <el-input v-model="form.password" type="password" placeholder="请输入原密码" />
                        </el-form-item>
                        <el-form-item label="新密码" prop="newpassword">
                            <el-input v-model="form.newpassword" type="password" placeholder="请输入原密码" />
                        </el-form-item>
                        <el-form-item label="确认新密码" prop="checkpassword">
                            <el-input v-model="form.checkpassword" type="password" placeholder="请输入原密码" />
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
        </page-main>
        <fixed-action-bar>
            <el-button type="primary" size="large" @click="onSubmit">提交</el-button>
        </fixed-action-bar>
    </div>
</template>
