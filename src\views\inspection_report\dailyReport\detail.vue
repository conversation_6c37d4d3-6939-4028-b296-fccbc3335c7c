<script setup>
import { onMounted, ref } from "@vue/runtime-core";
import { getEnvViteApiBaseurlIp } from "@/utils/axios";
const route = useRoute();
const iframeRef = ref();
const iframe = ref({
  src: "",
  loading: true,
});

const data = ref({
  dataList: [],
  showDialog: false,
  fileName: "",
});

onMounted(() => {
  data.value.fileName = route.query.filename;
  let url = getEnvViteApiBaseurlIp();
  iframe.value.src = url + "/static/daily_inspection/" + route.query.filename + ".html";
  iframe.value.loading = false;
});
</script>
<template>
  <div>
    <div v-loading="iframe.loading" class="iframe">
      <iframe ref="iframeRef" :src="iframe.src" frameborder="0" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.iframe,
iframe {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  padding-bottom: 100px;
  background-color: white;
}

.back-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}
</style>
