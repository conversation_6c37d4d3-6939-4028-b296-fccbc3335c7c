const Layout = () => import("@/layout/index.vue");
const children = [
  {
    path: "serverInformation",
    name: "cmdb_server_information",
    component: () => import("@/views/homepage/cmdb_server/informationServer.vue"),
    meta: {
      title: "服务器",
      auth: ["admin", "cmdb_server_information.browse"],
    },
    children: [
      {
        path: "ssh",
        name: "ssh",
        component: () => import("@/views/homepage/cmdb_server/ssh/index.vue"),
        meta: {
          sidebar: false,
          title: "ssh连接",
          activeMenu: "/cmdb/serverInformation",
        },
      },
    ],
  },
  {
    path: "databaseInformation",
    name: "cmdb_database_information",
    component: () => import("@/views/homepage/cmdb_database/informationDatabase.vue"),
    meta: {
      title: "数据库",
      auth: ["admin", "cmdb_database_information.browse"],
    },
  },
  {
    path: "middlewareInformation",
    name: "cmdb_middleware_information",
    component: () => import("@/views/homepage/cmdb_middleware/informationMiddleware.vue"),
    meta: {
      title: "中间件",
      auth: ["admin", "cmdb_middleware_information.browse"],
    },
  },

  {
    path: "interfaceInformation",
    name: "cmdb_interface_information",
    component: () => import("@/views/homepage/cmdb_service_interface/informationInterface.vue"),
    meta: {
      title: "服务接口",
      auth: ["admin", "cmdb_interface_information.browse"],
    },
  },

  {
    path: "networkInformation",
    name: "cmdb_network_information",
    component: () => import("@/views/homepage/cmdb_network_equipment/informationNetwork.vue"),
    meta: {
      title: "网络设备",
      auth: ["admin", "cmdb_network_information.browse"],
    },
  },
  // 存储设备暂时不显示
  {
    path: "storage_device",
    name: "cmdb_asset_storage",
    component: () => import("@/views/homepage/cmdb_storage/index.vue"),
    meta: { sidebar: false, title: "存储设备" },
  },
  // 自助机
  {
    path: "machine",
    name: "cmdb_asset_machine",
    component: () => import("@/views/homepage/cmdb_storage/index.vue"),
    meta: { sidebar: false, title: "自助机" },
  },
  {
    path: "clusterInformation",
    name: "cmdb_cluster_information",
    component: () => import("@/views/homepage/cmdb_cluster/informationCluster.vue"),
    meta: {
      title: "集群",
      auth: ["admin", "cmdb_cluster_information.browse"],
    },
  },

  {
    path: "biz_hub",
    name: "biz_hub",
    component: () => import("@/views/home/<USER>"),
    meta: {
      title: "业务系统",
      cache: true,
      auth: ["admin", "biz_hub.browse"],
      copyright: false,
    },
  },
  {
    path: "asset",
    name: "cmdb_asset",
    component: () => import("@/views/homepage/cmdb_asset/asset.vue"),
    meta: {
      sidebar: false,
      title: "资源详情",
      auth: ["admin", "cmdb.browse"],
    },
  },
];
export default {
  path: "/cmdb",
  component: Layout,
  redirect: "/cmdb/serverInformation",
  name: "cmdb",
  meta: {
    auth: ["admin", "cmdb.browse"],
    title: "资源管理",
    icon: "server_information",
  },
  children,
};
