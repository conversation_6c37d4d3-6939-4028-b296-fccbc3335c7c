const Layout = () => import("@/layout/index.vue");

let children = [
  {
    path: "/publish/index",
    name: "publish",
    component: () => import("@/views/automatic_operation/publish/index.vue"),
    meta: {
      title: "功能列表",
      auth: ["admin", "publish.browse"],
    },
  },
  {
    path: "/exec_task/create_task",
    name: "create_task",
    component: () => import("@/views/automatic_operation/exec_playbook/components/DetailForm/createTask.vue"),
    meta: {
      title: "批量执行",
      auth: ["admin", "create_task.browse"],
    },
  },
  {
    path: "/exec_task/task",
    name: "task",
    component: () => import("@/views/automatic_operation/exec_playbook/list.vue"),
    meta: {
      title: "执行记录",
      auth: ["admin", "task.browse"],
    },
  },
  {
    path: "task_managment",
    name: "task_managment",
    component: () => import("@/views/function_list/playbook_management/index.vue"),
    meta: {
      title: "功能包管理",
      auth: ["admin", "task_managment.browse"],
    },
  },
  {
    path: "script_managment_center",
    name: "script_managment_center",
    component: () => import("@/views/script_managment/index.vue"),
    meta: {
      title: "脚本管理中心",
      auth: ["admin", "script_managment_center.browse"],
    },
  },
  {
    path: "self_healing",
    name: "self_healing",
    // component: () => import("@/views/automatic_operation/self_cure/cure_relation/index.vue"),
    meta: {
      title: "自愈功能",
      auth: ["admin", "self_healing.browse"],
    },
    children: [
      {
        path: "cure_package",
        name: "cure_package",
        component: () => import("@/views/automatic_operation/self_cure/cure_function_package/index.vue"),
        meta: {
          title: "自愈功能包",
          auth: ["admin", "cure_package.browse"],
        },
      },
      {
        path: "cure_relation",
        name: "cure_relation",
        component: () => import("@/views/automatic_operation/self_cure/cure_relation/index.vue"),
        meta: {
          title: "自愈关系",
          auth: ["admin", "cure_relation.browse"],
        },
      },
      {
        path: "cure_trigger_record",
        name: "cure_trigger_record",
        component: () => import("@/views/automatic_operation/self_cure/cure_trigger_record/recordIndex.vue"),
        meta: {
          title: "触发记录",
          auth: ["admin", "cure_trigger_record.browse"],
        },
      },
    ],
  },
];

export default {
  path: "/exec_task",
  component: Layout,
  redirect: "/exec_task/task",
  name: "exec_task",
  meta: {
    auth: ["admin", "exec_task.browse"],
    title: "自动运维",
    icon: "ep:tools",
  },
  children,
};
