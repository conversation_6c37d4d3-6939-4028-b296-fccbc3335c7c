/**
 * 问题清单
 */

import api from "@/plugins/axios";

export function getSolvedProblemMessage() {
  //获取已解决问题的表单信息
  return api.get("/problem/re_file", {});
}
export function deleteProblemListByIds(data) {
  //获取已解决问题的表单信息
  return api.post("/problem/delete_problem_list_by_ids", JSON.stringify(data));
}

//获取历史执行过程
export function getProblemOfHistory(data) {
  return api.get("/problem/get_history_execution?hostip=" + data);
}
export function saveHandProblem(data) {
  return api.post("/problem/save_hand_problem", JSON.stringify(data));
}

//规则管理
export function addingRules(data) {
  //添加规则
  return api.post("/problem/create_zabbix_problem_rule", JSON.stringify(data));
}
export function modifyingRules(data) {
  //修改规则
  return api.post("/problem/update_zabbix_problem_rule", JSON.stringify(data));
}
export function deleteRules(data) {
  //删除规则
  return api.post("/problem/delete_zabbix_problem_rule", JSON.stringify(data));
}
export function getProblemRules() {
  //获取规则
  return api.get("/problem/get_zabbix_problem_rule");
}
export function getProblemAndPlaybook() {
  //获取功能包和问题名称
  return api.get("/problem/get_all_playbook_name_and_problem_name");
}
