import storage from "@/utils/storage";
import { getUTCTimestampWithOffset, getUTCWithOffset } from "../../utils/dayjs";
import { DatePickerShortcuts } from "../../constants/element";


function disabledDate(time) {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Set the time to midnight
  // Disable dates after today
  return time.getTime() > today.getTime();
}
const useDateTimeStore = defineStore(
  // 唯一ID
  'datetime',
  {
    state: () => ({
      begin: Number(storage.local.get("begin")) || Number(new Date(new Date(new Date().getTime() - 3600 * 1000 * 24).getTime())),
      over: Number(storage.local.get("over")) || Number(new Date(new Date()).getTime()),
      lastOpenedDate: storage.local.get("lastOpenedDate") || null,
      disabledDate: disabledDate,
      shortcuts: DatePickerShortcuts
    }),
    getters: {

    },
    actions: {
      // 设置开始时间的毫秒数
      setBegin(value) {
        this.begin = Number(new Date(value).getTime())
      },
      // 设置设置时间的毫秒数
      setOver(value) {
        this.over = Number(new Date(value).getTime())
      },
      // 清空缓存
      clear() {
        storage.local.remove("begin")
        storage.local.remove("over")
      },
      // 获取Unix时间戳
      getUtcUnixTimestamp() {
        return [getUTCTimestampWithOffset(this.begin), getUTCTimestampWithOffset(this.over)]
      },
      // 获取时区日期时间
      getUtcDateTime() {
        return [getUTCWithOffset(this.begin), getUTCWithOffset(this.over)]
      },
      // 初始化结束时间为当前时间
      initializeOverTime() {
        const currentDate = new Date().toISOString().split('T')[0];
        if (this.lastOpenedDate !== currentDate) {
          this.over = Number(new Date().getTime());
          this.lastOpenedDate = currentDate;
          storage.local.set("over", this.over);
          storage.local.set("lastOpenedDate", currentDate);
        }
      }
    }
  }
)

export default useDateTimeStore