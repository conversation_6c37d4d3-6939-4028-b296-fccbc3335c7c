const { defineConfig } = require("eslint-define-config");

const isProd = process.env.NODE_ENV === "production";

// 生产环境上禁用，开发环境警告。方便开发调试
const disabledRuleOnProd = isProd ? "warn" : "warn";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const warningRuleOnProd = isProd ? "warn" : "warn";
const offRuleOnProd = isProd ? "off" : "warn";

module.exports = defineConfig({
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  plugins: ["prettier"],
  parser: "vue-eslint-parser",
  parserOptions: {
    parser: "@typescript-eslint/parser",
    ecmaVersion: 2020,
    sourceType: "module",
    jsxPragma: "React",
    ecmaFeatures: {
      jsx: true,
      tsx: true,
    },
  },
  extends: [
    "plugin:vue/vue3-recommended",
    "plugin:@typescript-eslint/recommended",
    // "airbnb-base",
    "prettier",
    "plugin:prettier/recommended",
    './.eslintrc-auto-import.json',
  ],
  rules: {
    "prettier/prettier": [
      "error",
      {
        endOfLine: "auto",
      },
    ],
    "arrow-body-style": "off",
    "prefer-arrow-callback": "off",
    "spaced-comment": "error",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/ban-ts-ignore": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/no-var-requires": "off",
    "@typescript-eslint/no-empty-function": "off",
    "no-use-before-define": "off",
    "@typescript-eslint/no-use-before-define": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/ban-types": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "space-before-function-paren": "off",

    // vue
    "vue/script-setup-uses-vars": "error",
    "vue/custom-event-name-casing": "off",
    "vue/attributes-order": "error",
    "vue/one-component-per-file": "off",
    "vue/html-closing-bracket-newline": "off",
    "vue/max-attributes-per-line": "off",
    "vue/multiline-html-element-content-newline": "off",
    "vue/singleline-html-element-content-newline": "off",
    "vue/attribute-hyphenation": "error",
    "vue/v-on-event-hyphenation": "error",
    "vue/multi-word-component-names": "off",
    // "vue/require-default-prop": "off",
    "vue/html-self-closing": [
      "off",
      {
        html: {
          void: "always",
          normal: "never",
          component: "always",
        },
        svg: "always",
        math: "always",
      },
    ],

    // 以下为简化开发用，生产环境禁用
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
      },
    ],
    "no-debugger": disabledRuleOnProd,
    "no-console": offRuleOnProd,
    "vue/no-v-html": "off",
  },
});
