import api from "@/plugins/axios/index";
import axios from "axios";
/**
 * 获取业务视图列表
 * @returns
 */
export function cmdbGetAllBusinessInfo() {
  //cmdb业务视图总览获取
  return api.get("/business/list");
}
/**
 * 获取业务视图详情
 * @param {*} data
 * @returns
 */
export function cmdbGetBusinessMessage(data) {
  //根据业务id获取业务的详细信息
  return api.get("/business?business_id=" + data);
}
/**
 * 获取服务器下软件信息
 * @param {*} data
 * @returns
 */
export function cmdbBusinessGetBelongSoftware(data) {
  //提交绑定服务器，获取数据库、中间件、业务应用三类数据
  return api.post("/business/list", JSON.stringify(data));
}
/**
 * 提交选取的软件信息
 * @param {*} data
 * @returns
 */
export function cmdbBusinessAddSoftMessage(data) {
  //提交选取的数据库、中间件、业务应用等数据
  return api.post("/business", JSON.stringify(data));
}
/**
 * 修改回显数据
 * @param {*} id
 * @returns
 */
export function cmdbGetBusinessMessageById(id) {
  //回显业务数据
  return api.patch("/business/list" + "?business_id=" + id);
}
/**
 * 删除业务类型
 * @param {*} id
 * @returns
 */
export function cmdbBusinessDelMessage(data) {
  //删除业务
  return api.post("/business/delete_cmdb_business/", data);
}

/**
 * 获取业务名称列表
 */
export function getBusinessNameList(dictionary_code) {
  return api.get("/dictionary/get_dictionary_code_info/", { params: dictionary_code });
}

/**
 * 服务控制台导入业务
 */
export function getBusinessFromConsole(data) {
  return api.post("/server/auto_input_resource/", data);
}
export function getBusinessLog() {
  return api.get("/home_page/auto_collect_web_log/");
}

