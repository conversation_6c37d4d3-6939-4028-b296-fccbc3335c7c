/**
 * nginx管理
 */
import api from "@/plugins/axios";

export function getNginxList() {
  //获取列表
  return api.get("/nginx_management/list/");
}
export function getNginxConfig(data) {
  //获取nginx配置文件
  return api.get(`/nginx_management/file/?ip_address=${data.ip}`);
}

//获取nginx错误日志
export function getNginxLog(data) {
  return api.post("/nginx_management/log/", JSON.stringify(data));
}
//获取nginx拓扑
export function getNginxTopology(data) {
  return api.post("/nginx_management/topology/", JSON.stringify(data));
}

//备份
export function backNginx(data) {
  return api.post("/nginx_management/backup/", JSON.stringify(data));
}
//恢复
export function restoreNginx(data) {
  return api.post("/nginx_management/restore/", JSON.stringify(data));
}
//恢复
export function getRestore(data) {
  return api.get(`/nginx_management/backup_file_list/?ip_address=${data}`,);
}
//保存nginx
export function saveNginx(data) {
  return api.post("/nginx_management/replace/", JSON.stringify(data));
}
//nginx分析
export function nginxAnalysis(data) {
  return api.post("/nginx_management/analysis/", JSON.stringify(data));
}

//获取nginx节点详情
export function getNginxNodeDetail(data) {
  return api.post("/nginx_management/node_detail/", JSON.stringify(data));
}

//获取nginx日志(支持错误日志和访问日志)
export function getNginxLogs(data) {
  return api.post("/nginx_management/nginx_business_log/", JSON.stringify(data));
}

//获取nginx日志(支持错误日志和访问日志)
export function getNginxAnalysis(data) {
  return api.post("/nginx_management/nginx_business_analysis/", JSON.stringify(data));
}
