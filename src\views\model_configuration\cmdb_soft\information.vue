<script setup name="HomepageInformation">
import { onMounted, reactive, ref } from "vue";
import { usePagination } from "@/utils/composables";
import {
  cmdbGetAllSoftList,
  cmdbBatchDelSoftMessage,
  cmdbBatchExportSoftMessage,
  cmdbBatchImportSoftMessage,
} from "@/api/modules/model_configuration/cmdb_soft";

import { ElMessageBox } from "element-plus";
import { ResourceTypeEnum } from "@/views/homepage/cmdb_asset/components/constants";
import SoftAsset from "@/views/model_configuration/cmdb_soft/components/detail.vue";
import { pageChangeNum } from "../../homepage/components/utils";

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();

const router = useRouter();

const data = reactive({
  softId: 0,
  softLoading: false,
  title: "",
  assetTitle: "",
  dataList: [],
  allList: [],
  extra_params: [],
  software_name: "",
  software_type: "",
  search: {
    searchStatus: "",
    searchName: "",
    isSearch: false,
  },
  ip_list: [],
  showAsset: false,
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },
});
onMounted(() => {
  data.title = ResourceTypeEnum.software;
  getData();
});

//加载初始化数据
function getData() {
  data.softLoading = true;
  cmdbGetAllSoftList()
    .then((res) => {
      data.dataList = res.data;
      data.allList = res.data;
      changePageNum(res.data);
    })
    .finally(() => {
      data.softLoading = false;
    });
}
//分页
function changePageNum(lists) {
  let res = pageChangeNum(lists, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
}

function edit(row) {
  router.push({
    name: "create_soft",
    query: { item: data.title, id: row.id },
  });
}
//添加页面跳转

function create() {
  router.push({
    name: "create_soft",
    query: {
      item: data.title,
    },
  });
}

// 每页数量切换

function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）

function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

//筛选函数

function queryData() {
  let searchName = data.search.searchName;
  let searchStatus = data.search.searchStatus;
  let list = data.allList; //给list初始赋值，以便后续两次条件不成立时，list不为空
  if (searchName === "" && searchStatus === "") {
    changePageNum(data.allList);
    data.search.isSearch = false;
    return;
  } else {
    if (searchName !== "") {
      list = data.allList.filter((item) => {
        data.search.isSearch = true;
        return item.software_name.toLowerCase().includes(searchName.toLowerCase());
      });
    }
    if (searchStatus !== "") {
      list = list.filter((item) => {
        return (
          (searchStatus == 0 && item.software_type === ResourceTypeEnum.database) ||
          (searchStatus == 1 && item.software_type === ResourceTypeEnum.middleware) ||
          (searchStatus == 2 && item.software_type === ResourceTypeEnum.businessApp)
        );
      });
    }
    changePageNum(list);
  }
}
//产看详细的信息列表

function detailMessage(row) {
  data.softId = row.id;
  data.showAsset = true;
  data.assetTitle = row.software_name + "详情信息";
}

//批量删除

function batchDel() {
  let list = [];
  ElMessageBox.confirm("是否删除选中数据？", "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    data.batch.selectionDataList.forEach((item) => {
      list.push(item.id);
    });
    cmdbBatchDelSoftMessage(list).then((res) => {
      if (res.status_code == 200) {
        getData();
      }
    });
  });
}

//导入软件清单
function uploadTxtfile() {
  const fileInput = document.createElement("input");
  fileInput.type = "file";
  fileInput.accept = ".txt";
  fileInput.addEventListener("change", (e) => {
    let formData = new FormData();
    formData.append("data", fileInput.files[0]);
    cmdbBatchImportSoftMessage(formData)
      .then((res) => {})
      .finally(() => {
        getData();
      });
  });
  fileInput.click();
}
//导出软件清单

function exportSoftMessageTxt() {
  cmdbBatchExportSoftMessage().then((res) => {
    const blob = new Blob([res], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "软件清单.txt";
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }, 0);
  });
}
</script>

<template>
  <div>
    <!-- 服务器展示 -->
    <page-main :title="data.title">
      <search-bar>
        <el-form :model="data.search" size="default" label-width="100px" label-suffix="：" @submit.native.prevent>
          <el-row>
            <el-col :span="12">
              <el-form-item label="名称过滤">
                <el-input
                  v-model="data.search.searchName"
                  placeholder="请输入名称,不区分大小写，可进行模糊过滤"
                  clearable
                  @keyup.enter.native="queryData"
                />
              </el-form-item>
            </el-col>
            <el-form-item class="w-auto!">
              <el-button type="primary" @click="queryData()">
                <template #icon>
                  <el-icon>
                    <svg-icon name="ep:search" />
                  </el-icon>
                </template>
                筛选
              </el-button>
            </el-form-item>
            <el-form-item class="w-auto!">
              <el-radio-group v-model="data.search.searchStatus" @change="queryData()">
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button :label="0">数据库</el-radio-button>
                <el-radio-button :label="1">中间件</el-radio-button>
                <el-radio-button :label="2">业务应用</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-row>
        </el-form>
      </search-bar>
      <el-space wrap>
        <el-button type="primary" @click="create">
          <template #icon>
            <el-icon>
              <svg-icon name="ep:plus" />
            </el-icon>
          </template>
          新增
        </el-button>
        <el-button type="primary" @click="exportSoftMessageTxt">
          <template #icon>
            <el-icon>
              <svg-icon name="ep:download" />
            </el-icon>
          </template>
          下载软件清单
        </el-button>
        <el-button type="primary" @click="uploadTxtfile">
          <template #icon>
            <el-icon>
              <svg-icon name="ant-design:import-outlined" />
            </el-icon>
          </template>
          导入软件模板
        </el-button>

        <el-button-group v-if="data.batch.enable">
          <el-button type="danger" :disabled="!data.batch.selectionDataList.length" @click="batchDel()">删除</el-button>
        </el-button-group>
      </el-space>
      <el-table
        v-loading="data.softLoading"
        class="list-table"
        :data="data.dataList"
        border
        stripe
        highlight-current-row
        @selection-change="data.batch.selectionDataList = $event"
      >
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column prop="software_name" label="名称">
          <template #default="scope">
            <el-link @click="detailMessage(scope.row)" type="primary">{{ scope.row.software_name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="software_type" label="类型">
          <template #default="scope">
            <el-tag v-if="scope.row.software_type === '数据库'" type="success">
              {{ scope.row.software_type }}
            </el-tag>
            <el-tag v-else-if="scope.row.software_type === '业务应用'" type="warning">
              {{ scope.row.software_type }}
            </el-tag>
            <el-tag v-else type="info">
              {{ scope.row.software_type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scoped">
            <el-button type="primary" plain @click="edit(scoped.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页插件 -->
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>

    <el-dialog v-model="data.showAsset" :title="data.assetTitle" :destroy-on-close="true">
      <SoftAsset :soft_id="data.softId" v-model="data.showAsset"></SoftAsset>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
// scss
.font_css {
  font-size: 20px;
  // font-family: SimSun;
  font-weight: bold;
  // padding-bottom: 10px;
}
</style>
