<script setup lang="ts">
import { nextTick, onMounted, reactive, ref } from "vue";
import { testWeeklyReportsPushConfig } from "@/api/modules/inspection_report/weeklyReport";
import {
  queryEnvConfigInfo,
  addEnvConfigInfo,
  pushDailyReportTest,
  getConfigForWeb,
  checkConfigIsComplete,
} from "@/api/modules/configuration/env_config";
import storage from "@/utils/storage";
import { showNotification } from "@/plugins/element-ui";

const router = useRouter();
const form = ref();
const dailyReport = ref();
const dailyBindReport = ref();

const data = reactive({
  daily: false,
  dailyBind: true,
  zabbixForm: {
    ip: "",
    username: "",
    password: "",
  },
  grafanaForm: {
    ip: "",
    username: "",
    password: "",
  },
  kibanaForm: {
    ip: "",
    username: "",
    password: "",
  },
  elasticsearchForm: {
    ip: "",
    username: "",
    password: "",
  },
  status: false,
  dailyReport: {
    hospital_name: "",
    logstash_url: "",
    part_id: "",
    Monitor_Server_Information_List: [],
  },
  formRules: {
    ip: [{ required: true, message: "请输入IP", trigger: "blur" }],
    username: [{ required: true, message: "请输入账户", trigger: "blur" }],
    password: [{ required: true, message: "请输入密码", trigger: "blur" }],
    hospital_name: [{ required: true, message: "请输入医院名称", trigger: "blur" }],
    logstash_url: [{ required: true, message: "请输入logstash地址", trigger: "blur" }],
    part_id: [{ required: true, message: "请输入群组ID", trigger: "blur" }],
    url: [{ required: true, message: "请输入周报url地址", trigger: "blur" }],
    weekly_id: [{ required: true, message: "请输入周报id", trigger: "blur" }],
  },
  DailyFormRules: {
    HOSPITAL_NAME: [{ required: true, message: "请输入名称", trigger: "blur" }],
    Part_ID: [{ required: true, message: "请输入医院分组", trigger: "blur" }],
  },
  dailyBindReport: {
    HOSPITAL_NAME: "",
    PROXY: "",
    Part_ID: "40",
    ES_CONFIG: {},
    ZABBIX_CONFIG: {},
    ENTERPRISE_WE_CHAT_CONFIG: {},
    PUSH_METHOD: "",
  },
  dailyPushType: false,
  weeklyReport: {
    url: "",
    weekly_id: "",
    push_type: false,
    kbn_version: "",
  },
  showForm: false,
  initialConfigLoading: false,
  weeklyReportLoading: false,
  dailyBindLoading: false,
  testDReportLoading: false,
  configReportLoading: false,
  testWReportLoading: false,
});
const props = defineProps({
  firstConfig: {
    type: Boolean,
    default: false,
  },
});
onMounted(() => {
  if (!props.firstConfig) {
    getAllEnvBasicConfig();
    // data.showForm = true;
  }
});

//获取zabbix、kibana、es、grafana的配置参数
function getAllEnvBasicConfig() {
  data.daily = false;
  data.dailyBind = true;
  queryEnvConfigInfo().then((res) => {
    data.zabbixForm = res.data.zabbix_config;
    data.grafanaForm = res.data.grafana_config;
    data.kibanaForm = res.data.kibana_config;
    data.elasticsearchForm = res.data.elastic_config;
    if (res.data.weekly_config == null) {
      data.weeklyReport = {
        url: "",
        weekly_id: "",
        push_type: false,
        kbn_version: "",
      };
    } else {
      data.weeklyReport = res.data.weekly_config;
    }
    if (res.data.daily_config == null) {
      data.dailyBindReport = {
        HOSPITAL_NAME: "",
        PROXY: "",
        Part_ID: "40",
        ES_CONFIG: {},
        ZABBIX_CONFIG: {},
        ENTERPRISE_WE_CHAT_CONFIG: {},
        PUSH_METHOD: "",
      };
    } else {
      data.dailyBindReport = res.data.daily_config;
    }
    if (res.data.daily_config.PUSH_METHOD == "kafka") {
      data.dailyPushType = true;
      return;
    }
  });
}
let configStatus = {
  elasticsearch: false,
  kibana: false,
  zabbix: false,
  grafana: false,
};

//提交配值信息公共接口 callback用于接口提交之后的操作【关闭loading】
function setConfigInformation(setType, formData, callback = () => {}) {
  addEnvConfigInfo({
    type: setType,
    data_resource: formData,
  })
    .then((res) => {
      if (setType != "weekly" || setType != "daily") {
        configStatus[setType] = true;
      }
      ElMessage.success(setType + "配置成功");
    })
    .catch(() => {
      ElMessage.error(setType + "配置失败");
    })
    .finally(() => {
      callback();
      submitAfter();
    });
}
//提交所有初始配置
function submitAllconfig() {
  Object.keys(configStatus).forEach((setType) => {
    if (!configStatus[setType]) {
      data.initialConfigLoading = true;
      setConfigInformation(setType, data[setType + "Form"], () => {
        data.initialConfigLoading = false;
      });
    }
  });
}

// 提交之后的动作,设置缓存，跳转页面
function submitAfter() {
  if (Object.values(configStatus).every((item) => item) && props.firstConfig) {
    const notification = showNotification("温馨提示", "初始化配置完成后，页面加载较慢，请稍等！", "info");

    checkConfigIsComplete().then((res) => {
      nextTick(() => {
        if (res.data == true) {
          getConfigForWeb().then((res) => {
            Object.keys(res.data).forEach((key) => {
              storage.local.set(key, res.data[key]);
            });
            notification.close();
            configStatus = {
              elasticsearch: false,
              kibana: false,
              zabbix: false,
              grafana: false,
            };
            router.push({
              name: "login",
            });
          });
        } else {
          ElMessage.error("初始化出错");
        }
      });
    });
  }
}

//测试日报推送
function testPushDReport() {
  //将分组设置为40
  data.testDReportLoading = true;
  pushDailyReportTest()
    .then((res) => {
      let message = res.data;
      message.forEach((element) => {
        if (element.level == "info") {
          ElMessage({
            message: element.message,
            type: "success",
          });
        } else {
          ElMessage.error(element.message);
        }
      });
    })
    .finally(() => {
      data.testDReportLoading = true;
    });
}

//配置日报脚本
function configPushDReport() {
  dailyReport.value.validate((valid) => {
    if (valid) {
      let isTrue = true;
      const isEdit = "isEdit";
      data.dailyReport.Monitor_Server_Information_List = data.dailyReport.Monitor_Server_Information_List.map(
        (item) => {
          // 创建一个新对象，包含原始对象的所有属性，但不包括要移除的属性
          const newItem = { ...item };
          delete newItem[isEdit];
          return newItem;
        }
      );
      data.dailyReport.Monitor_Server_Information_List.forEach((item) => {
        for (const key in item) {
          if (item.hasOwnProperty(key)) {
            if (item[key] === "" || item[key] === "待填写") {
              isTrue = false;
            }
          }
        }
      });
      if (isTrue == true && data.dailyReport.Monitor_Server_Information_List.length != 0) {
        let params = {
          HOSPITAL_NAME: data.dailyReport.hospital_name,
          LOGSTASH_URL: data.dailyReport.logstash_url,
          Part_ID: data.dailyReport.part_id,
          Monitor_Server_Information_List: data.dailyReport.Monitor_Server_Information_List,
        };
        data.configReportLoading = true;
        addEnvConfigInfo({
          type: "daily",
          data_resource: params,
        })
          .then((res) => {
            ElMessage({
              message: res.message || "修改成功",
              type: "success",
            });
          })
          .catch((res) => {
            ElMessage.error(res.message || "意外失败");
          })
          .finally(() => {
            data.configReportLoading = false;
          });
      } else {
        ElMessage.error(`信息列表中有包含空字符串或 '待填写'的内容。`);
      }
    }
  });
}

//测试周报脚本
function testPushWReport() {
  //对测试的脚本的发送组别写为40
  data.testWReportLoading = true;
  testWeeklyReportsPushConfig()
    .then((res) => {})
    .catch((res) => {})
    .finally(() => {
      data.testWReportLoading = false;
    });
}

//添加周报配置信息
const formRef = ref();
function configPushWReport() {
  formRef.value.validate((valid) => {
    if (valid) {
      data.weeklyReportLoading = true;
      setConfigInformation("weekly", data.weeklyReport, () => {
        data.weeklyReportLoading = false;
      });
    }
  });
}

let canAddExtraParam = computed(() => {
  return data.dailyReport.Monitor_Server_Information_List.every((item) => {
    return !item.isEdit;
  });
});

function addExtraParams() {
  data.dailyReport.Monitor_Server_Information_List.push({
    isEdit: true,
    ip: "",
    username: "",
    password: "",
  });
}

function removeExtraParams(index) {
  data.dailyReport.Monitor_Server_Information_List.splice(index, 1);
}

function addParams(data) {
  data.isEdit = false;
  if (data.ip == "" || data.username == "" || data.password == "") {
    ElMessage.warning({
      message: "自定义字段不能置空",
      center: true,
    });
    data.ip = "待填写";
    data.username = "待填写";
    data.password = "待填写";
  }
}

function changBind() {
  if (data.status == true) {
    data.daily = true;
    data.dailyBind = false;
  } else {
    data.daily = false;
    data.dailyBind = true;
  }
}
//提交日报配置信息
function configPushBindReport() {
  dailyBindReport.value.validate((valid) => {
    if (valid) {
      data.dailyBindLoading = true;
      if (data.dailyPushType) {
        data.dailyBindReport.PUSH_METHOD = "kafka";
      } else {
        data.dailyBindReport.PUSH_METHOD = "http";
      }
      setConfigInformation("daily", data.dailyBindReport, () => {
        data.dailyBindLoading = false;
      });
    }
  });
}
</script>
<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card>
          <el-form
            ref="form"
            :model="data.elasticsearchForm"
            label-width="150px"
            label-position="top"
            :rules="data.formRules"
          >
            <el-form-item label="日志数据源" prop="ip">
              <el-input v-model="data.elasticsearchForm.ip" class="input_css" placeholder="请输入包含端口号的地址">
                <template #prepend>http://</template>
                <!-- <template #append>:9200</template> -->
              </el-input>
            </el-form-item>

            <el-form-item label="登录账户(管理员)" prop="username">
              <el-input v-model="data.elasticsearchForm.username" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input type="password" show-password v-model="data.elasticsearchForm.password" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card>
          <el-form ref="form" :model="data.kibanaForm" label-position="top" label-width="150px" :rules="data.formRules">
            <el-form-item label="分析报告数据源" prop="ip">
              <el-input v-model="data.kibanaForm.ip" class="input_css">
                <template #prepend>http://</template>
                <template #append>/itoa</template>
              </el-input>
            </el-form-item>

            <el-form-item label="登录账户(管理员)" prop="username">
              <el-input v-model="data.kibanaForm.username" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input type="password" show-password v-model="data.kibanaForm.password" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-form ref="form" :model="data.zabbixForm" label-position="top" label-width="150px" :rules="data.formRules">
            <el-form-item label="监控数据源" prop="ip">
              <el-input v-model="data.zabbixForm.ip" class="input_css">
                <template #prepend>http://</template>
                <template #append>/monitor</template>
              </el-input>
            </el-form-item>

            <el-form-item label="登录账户(管理员)" prop="username">
              <el-input v-model="data.zabbixForm.username" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input type="password" show-password v-model="data.zabbixForm.password" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card>
          <el-form
            ref="form"
            :model="data.grafanaForm"
            label-position="top"
            label-width="100px"
            :rules="data.formRules"
          >
            <el-form-item label="大屏数据源" prop="ip">
              <el-input v-model="data.grafanaForm.ip" class="input_css">
                <template #prepend>http://</template>
                <template #append>/tv</template>
              </el-input>
            </el-form-item>

            <el-form-item label="登录账户(管理员)" prop="username">
              <el-input v-model="data.grafanaForm.username" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input type="password" show-password v-model="data.grafanaForm.password" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <div class="button_positon">
      <el-button type="primary" form="form" @click="submitAllconfig" :loading="data.initialConfigLoading">
        提交
      </el-button>
    </div>
    <el-divider border-style="dashed" />
    <el-divider>日报配置</el-divider>
    <el-row type="flex" justify="center" v-show="data.showForm">
      <el-col :md="12" :sm="18">
        <el-switch
          v-model="data.status"
          class="mb-10px"
          style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949; float: right"
          inline-prompt
          active-text="存活检测开启"
          inactive-text="存活检测关闭"
          @change="changBind"
        />
      </el-col>
    </el-row>
    <el-row v-show="data.daily" type="flex" justify="center">
      <el-col :md="12" :sm="18">
        <el-form ref="dailyReport" :model="data.dailyReport" label-width="80px" :rules="data.formRules">
          <el-form-item label="医院名称" prop="hospital_name">
            <el-input v-model="data.dailyReport.hospital_name" placeholder="XX医院" />
          </el-form-item>
          <el-form-item label="logstash" prop="logstash_url">
            <el-input v-model="data.dailyReport.logstash_url" placeholder="http://ip地址:端口" />
          </el-form-item>
          <el-form-item label="群组ID" prop="part_id">
            <el-input v-model="data.dailyReport.part_id" placeholder="群组ID" />
          </el-form-item>
          <el-form-item label="信息列表">
            <el-table :data="data.dailyReport.Monitor_Server_Information_List" style="width: 100%">
              <el-table-column label="IP">
                <template #default="scope">
                  <el-input v-if="scope.row.isEdit" v-model="scope.row.ip" size="small" />
                  <span v-else>{{ scope.row.ip }}</span>
                </template>
              </el-table-column>
              <el-table-column label="用户名">
                <template #default="scope">
                  <el-input v-if="scope.row.isEdit" v-model="scope.row.username" size="small" />
                  <span v-else>{{ scope.row.username }}</span>
                </template>
              </el-table-column>
              <el-table-column label="密码">
                <template #default="scope">
                  <el-input v-if="scope.row.isEdit" v-model="scope.row.password" size="small" />
                  <span v-else>{{ scope.row.password }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center">
                <template #default="scope">
                  <template v-if="scope.row.isEdit">
                    <el-button type="primary" plain size="small" @click="addParams(scope.row)">保存</el-button>
                  </template>
                  <template v-else>
                    <el-button type="primary" plain size="small" @click="scope.row.isEdit = true">编辑</el-button>
                    <el-popconfirm
                      title="是否要删除此行？"
                      style="margin-left: 10px"
                      @confirm="removeExtraParams(scope.$index)"
                    >
                      <template #reference>
                        <el-button type="danger" plain size="small">删除</el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <el-button :disabled="!canAddExtraParam" style="margin-top: 20px; width: 100%" @click="addExtraParams">
              <template #icon>
                <el-icon>
                  <svg-icon name="i-ep:plus" />
                </el-icon>
              </template>
              新增
            </el-button>
          </el-form-item>
          <div class="button_positon">
            <el-button type="primary" @click="configPushDReport" :loading="data.configReportLoading">
              立即配置
            </el-button>

            <el-button type="primary" @click="testPushDReport" :loading="data.testDReportLoading">测试日报</el-button>
          </div>
        </el-form>
      </el-col>
    </el-row>

    <el-row v-show="data.dailyBind" type="flex" justify="center">
      <el-col :md="12" :sm="18">
        <el-form ref="dailyBindReport" :model="data.dailyBindReport" label-width="80px" :rules="data.DailyFormRules">
          <el-form-item label="医院名称" prop="HOSPITAL_NAME">
            <el-input v-model="data.dailyBindReport.HOSPITAL_NAME" placeholder="XX日报" />
          </el-form-item>
          <el-form-item label="医院分组" prop="Part_ID">
            <el-input v-model="data.dailyBindReport.Part_ID" placeholder="请输入医院分组，例如：40" />
          </el-form-item>
          <el-form-item label="代理" prop="proxy">
            <el-input v-model="data.dailyBindReport.PROXY" placeholder="代理地址" />
          </el-form-item>
          <el-form-item label="推送方式">
            <el-switch
              v-model="data.dailyPushType"
              class="ml-2"
              inline-prompt
              style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
              active-text="kafka"
              inactive-text="http"
            />
          </el-form-item>
          <div class="button_positon">
            <el-button type="primary" @click="configPushBindReport" :loading="data.dailyBindLoading">
              立即配置
            </el-button>
          </div>
        </el-form>
      </el-col>
    </el-row>

    <el-divider border-style="dashed" />
    <el-divider>周报配置</el-divider>
    <el-row type="flex" justify="center">
      <el-col :md="12" :sm="18">
        <el-form :model="data.weeklyReport" label-width="80px" :rules="data.formRules" ref="formRef">
          <el-form-item label="周报地址" prop="url">
            <el-input v-model="data.weeklyReport.url" placeholder="请输入周报url地址" type="textarea" />
          </el-form-item>
          <el-form-item label="ES版本" prop="kbn_version">
            <el-input placeholder="请输入elasticsearch版本" v-model="data.weeklyReport.kbn_version" />
          </el-form-item>
          <el-form-item label="推送方式" prop="push_type">
            <el-switch
              v-model="data.weeklyReport.push_type"
              class="ml-2"
              inline-prompt
              style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
              active-text="企业微信"
              inactive-text="本地推送"
            />
          </el-form-item>

          <div class="button_positon">
            <el-button type="primary" @click="configPushWReport" :loading="data.weeklyReportLoading">
              立即配置
            </el-button>

            <el-button type="primary" @click="testPushWReport" :loading="data.testWReportLoading">测试周报</el-button>
          </div>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>
<style scoped lang="scss">
.button_positon {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.input_css {
  :deep(.el-input-group__append, ) {
    padding: 0 5px;
  }
  :deep(.el-input-group__prepend) {
    padding: 0 5px;
  }
}
</style>
