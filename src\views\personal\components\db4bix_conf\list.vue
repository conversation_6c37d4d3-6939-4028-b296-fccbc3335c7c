<script setup name="ExampleList">
import eventBus from "@/utils/eventBus";
import { usePagination } from "@/utils/composables";
// import { , GetAnsibleHostName } from "@/api/request";
import {
  getAllAnsibleHostIP,
  GetDb4bixBindIP,
  GetDb4bixConfig,
  BindDb4bixIp,
  DealDb4bixConfig,
} from "@/api/modules/configuration/oracleDb4bix";
import Storage from "@/utils/storage";
import { onMounted, watch } from "vue";

// 表单的封装好了的一些api
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
// const route = useRoute()

const data = ref({
  loading: false,
  /**
   * 详情展示模式
   * router 路由跳转
   * dialog 对话框
   * drawer 抽屉
   */
  formMode: "router",
  // 详情
  formModeProps: {
    visible: false,
    id: "",
  },
  pingStatus: false,
  // 搜索
  search: {
    host: "",
  },
  // 批量操作
  batch: {
    enable: false,
    selectionDataList: [],
  },
  // 列表数据
  dataList: [],
  NameToPassword: [],
  Template: {
    name: "",
    type: "",
    instance: "",
    host: "",
    port: "",
    user: "",
    password: "",
    pool: "Common",
    service_name: "",
  },
  ip_binding: "",
  host_name: [],
  allList: [],
});

onMounted(() => {
  getDataList();
  getAllAnsibleHostIP().then((res) => {
    res.data.forEach((item) => {
      data.value.host_name.push(item);
    });
  });
});

function formattingData(list) {
  let JsonData = [];
  list.map((item) => {
    let temp = { ...Object.assign(data.value.Template, item) };

    // temp.connectString =
    //   "(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=" +
    //   temp.host +
    //   ")(PORT=" +
    //   temp.port +
    //   "))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=" +
    //   temp.serviceName +
    //   ")))";
    // delete temp["service_name"];

    let index = data.value.NameToPassword.findIndex((item) => {
      return item.name == temp.name;
    });
    temp.password = data.value.NameToPassword[index].password;
    JsonData.push(temp);
  });

  return JsonData;
}

function dataFilter(dataList, params) {
  let list = dataList;
  // let reg = new RegExp(/SERVICE_NAME=\w*/i);

  list.forEach((item) => {
    data.value.NameToPassword.push({
      name: item.name,
      password: item.password,
    });
    // item.serviceName = reg.exec(item.connectString)[0].replace("SERVICE_NAME=", "");
    item.password = "******";
  });

  if (params.host != "" && params.host != undefined) {
    list = dataList.filter((item) => {
      return item ? item.host.includes(params.host) : true;
    });
  }

  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });

  return {
    list: pageList,
    total: list.length,
  };
}

function getDataList() {
  let params = getParams();
  data.value.search.host && (params.host = data.value.search.host);
  data.value.loading = true;
  GetDb4bixBindIP().then((res) => {
    data.value.ip_binding = res.data;
  });
  GetDb4bixConfig()
    .then((res) => {
      data.value.allList = res.data;
      paging(data.value.allList);
    })
    .catch((res) => {
      data.value.dataList = [];
    })
    .finally(() => {
      data.value.loading = false;
    });
}
function paging(lists) {
  let params = getParams();
  const result = dataFilter(lists, params);
  data.value.dataList = result.list;
  pagination.value.total = result.total;
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => paging(data.value.allList));
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => paging(data.value.allList));
}

// 字段排序
function sortChange(prop, order) {
  onSortChange(prop, order).then(() => paging(data.value.allList));
}

function onCreate() {
  if (data.value.ip_binding == "") {
    ElMessage.warning("请先绑定服务器ip");
    return;
  }
  Storage.local.set(
    "cookieData",
    JSON.stringify({
      dataList: data.value.allList,
      index: -1,
      NameToPassword: data.value.NameToPassword,
    })
  );
  router.push({
    name: "db4bixCreate",
  });
}
//编辑
function onEdit(index) {
  const pageIndex = (pagination.value.page - 1) * 10 + index;
  let objectData = {
    dataList: data.value.allList,
    index: pageIndex,
    NameToPassword: data.value.NameToPassword,
    opt: "edit",
  };
  if (data.value.search.host != "") {
    objectData.data = data.value.dataList[pageIndex];
  }
  Storage.local.set("cookieData", JSON.stringify(objectData));
  router.push({
    name: "db4bixEdit",
  });
}

//删除
function onDel(index) {
  data.value.loading = true;
  index = (pagination.value.page - 1) * 10 + index;
  let ipName = data.value.dataList[index].host;
  if (data.value.search.host == "") {
    data.value.allList.splice(index, 1);
  } else {
    data.value.allList = data.value.allList.filter((obj) => obj.name !== data.value.dataList[index].name);
  }
  DealDb4bixConfig(formattingData(data.value.allList))
    .then(() => {
      ElMessage.success(`删除服务器「${ipName}」的采集信息成功`);
      getDataList();
    })
    .finally(() => {
      data.value.loading = false;
    });
}

function bind_db4bix_server() {
  data.value.loading = true;
  BindDb4bixIp(data.value.ip_binding)
    .then((res) => {
      ElMessage.success("更新成功");
      getDataList();
    })
    .catch((res) => {
      data.value.dataList = [];
    })
    .finally(() => {
      data.value.loading = false;
    });
}
function queryData() {
  if (data.value.search.host === "" || data.value.search.host == null) {
    getDataList();
  }
  // 搜索
  let list = [];
  data.value.allList.filter((item) => {
    if (item.name.indexOf(data.value.search.host) !== -1) {
      list.push(item);
    }
  });
  paging(list);
}
</script>

<template>
  <div>
    <!-- <page-main title="监控数据库采集配置"> -->

    <search-bar>
      <el-form :model="data.search" size="default" label-width="100px" label-suffix="：">
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称过滤">
              <el-input
                v-model="data.search.host"
                placeholder="请输入名称进行查询"
                clearable
                @keydown.enter="queryData()"
              />
            </el-form-item>
          </el-col>
          <el-form-item class="w-auto!">
            <el-button type="primary" @click="queryData()">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:search" />
                </el-icon>
              </template>
              筛选
            </el-button>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="绑定ip">
              <el-select v-model="data.ip_binding" filterable placeholder="请选择绑定ip">
                <el-option label="本机" value="localhost" />
                <el-option v-for="item in data.host_name" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-form-item class="w-auto!">
            <el-button type="primary" @click="bind_db4bix_server()">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:search" />
                </el-icon>
              </template>
              绑定
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </search-bar>
    <el-space wrap>
      <el-button v-auth="['admin']" type="primary" @click="onCreate">
        <template #icon>
          <el-icon>
            <svg-icon name="ep:plus" />
          </el-icon>
        </template>
        新增监控信息
      </el-button>
    </el-space>
    <el-table
      ref="table"
      v-loading="data.loading"
      class="list-table"
      :data="data.dataList"
      border
      stripe
      highlight-current-row
      @sort-change="sortChange"
      @selection-change="data.batch.selectionDataList = $event"
    >
      <!-- <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed /> -->
      <el-table-column prop="name" label="名称" />
      <el-table-column prop="host" label="数据库地址" />
      <el-table-column prop="port" label="连接端口" />
      <el-table-column prop="user" label="连接用户" />
      <el-table-column prop="service_name" label="监控数据库服务名" />
      <el-table-column prop="instance" label="监控实例名称" />
      <el-table-column label="操作" width="250" align="center" fixed="right">
        <template #default="scope">
          <el-button v-auth="['admin']" type="primary" size="small" plain @click="onEdit(scope.$index)">
            编 辑
          </el-button>
          <el-button v-auth="['admin']" type="danger" size="small" plain @click="onDel(scope.$index)">删 除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pagination.page"
      :total="pagination.total"
      :page-size="pagination.size"
      :page-sizes="pagination.sizes"
      :layout="pagination.layout"
      :hide-on-single-page="false"
      class="pagination"
      background
      @size-change="sizeChange"
      @current-change="currentChange"
    />
    <!-- </page-main> -->
  </div>
</template>

<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
}
</style>
