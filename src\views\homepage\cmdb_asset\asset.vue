<script setup name="HomepageAsset">
import { onMounted, reactive } from "vue";
import useSettingsStore from "@/store/modules/settings";
import { useRouter, useRoute } from "vue-router";
import "vexip-ui/css/index.css";
import { updateResourceAsset } from "@/api/modules/cmdb/resource";
import { cmdbUpdateServerAsset } from "@/api/modules/cmdb/server";
import { Line } from "@antv/g2plot";
import { usePagination } from "@/utils/composables";
import { ElMessage } from "element-plus";
import SocketUtil from "@/utils/cmdb_utils";
import { ArrowLeftBold } from "@element-plus/icons-vue";
import AssetDetail from "./asset_detail.vue";
import { showNotification } from "@/plugins/element-ui";
import { ResourceTypeEnum } from "./components/constants";
import { GetModelDetailById } from "@/api/modules/basic_crud/crud";
import LastDataTableList from "@/views/problem_list/components/last_data_table_list.vue";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const settingsStore = useSettingsStore();
const router = useRouter();
const route = useRoute();

const activeName = ref("first");
var trendG2;
const now = new Date();
const assetDetailRef = ref();
const data = reactive({
  title: "",
  historyTitle: "",
  table: true,
  echarts: false,
  trendData: [], //历史数据
  echart: { max: "", min: "", lastData: "", unit: "" },
  search: {
    tag: [],
    searchName: "",
  },
  latestData: [], //最新数据
  allList: [],
  host_ip: "",
  itemid: "",
  loading: false,
  tags: [],
  requestParam: {},
  time: "",
  timeStamp: "",
  refreshData: {},
  updataLoading: false,
  resource_id: "",
  relation_id: "",
  hostName: "",
});

onMounted(() => {
  data.title = route.query.item;
  data.host_ip = route.query.ip;
  data.resource_id = route.query.id;
  data.relation_id = route.query.relation_id;
  if (route.query.tabpage) {
    activeName.value = route.query.tabpage;
    changeActive();
  }
});

// ws链接监听消息
SocketUtil.socket.onmessage = (recv) => {
  let res = JSON.parse(recv.data);
  switch (res.message.status_code) {
    case 200:
      showNotification("采集成功", res.message.message, "success");
      // 刷新资源详情的获取接口
      assetDetailRef.value.getresourcedetail();
      break;
    case 500:
      showNotification("采集失败", res.message.message, "error");
      break;
  }
};

function toGoBack() {
  if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
    router.go(-1);
  } else {
    router.go(-1);
  }
}

//手动更新cmdb详情数据
function refreshAsset() {
  ElMessageBox.confirm("确定更新详情吗？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    data.updataLoading = true;
    if (data.title == ResourceTypeEnum.server) {
      cmdbUpdateServerAsset([data.host_ip])
        .then((res) => {
          showNotification("提示", res.message, "info");
        })
        .finally(() => {
          data.updataLoading = false;
        });
    } else {
      updateResourceAsset({ type: data.title, relation_id: data.relation_id })
        .then((res) => {
          showNotification("提示", res.message, "info");
        })
        .finally(() => {
          data.updataLoading = false;
        });
    }
  });
}

/**
 * @description: tabs 切换
 * @return {*}
 */
function changeActive() {
  if (activeName.value == "second") {
  }
}
</script>

<template>
  <div>
    <!-- 服务器资产详情 -->
    <page-main>
      <template #title>
        <div class="flex items-center">
          <el-icon class="cursor-pointer" @click="toGoBack()"><ArrowLeftBold /></el-icon>
          <span class="ml-10px">{{ data.title }}资产详情</span>
        </div>
      </template>
      <el-button round @click="refreshAsset" v-if="activeName == 'first'" class="el-button--mini">
        <template #icon>
          <el-icon>
            <svg-icon name="ep:refresh" />
          </el-icon>
        </template>
        更新详情
      </el-button>

      <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-change="changeActive">
        <el-tab-pane label="资产详情" name="first" v-loading="data.updataLoading">
          <AssetDetail :type="data.title" :id="data.resource_id" :ip="data.host_ip" ref="assetDetailRef"></AssetDetail>
        </el-tab-pane>

        <el-tab-pane label="监控数据" name="second">
          <LastDataTableList :hostip="data.host_ip" :zabbix_id="data.resource_id"></LastDataTableList>
        </el-tab-pane>
      </el-tabs>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
.el-button--mini {
  position: inherit;
  float: right;
  z-index: 998;
}
</style>
