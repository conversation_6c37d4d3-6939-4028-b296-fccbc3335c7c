const translation = {
    'short_hostname': '短主机名',
    'ipv4_address': 'ipv4地址',
    'kernel_version': '内核版本',
    'node_name': '节点名',
    'form_factor': '内存类型',
    'virtualization_role': '虚拟机登陆角色',
    'virtualization_type': '虚拟机类型',
    'verdor': '系统供应商',
    'product_name': '使用产品名称',
    'product_serial': '产品序列号',
    'architecture': '操作系统架构',
    'machine': '基础架构',
    'processor_type': '处理器类型',
    'processor_count': '处理器数量',
    'processor_cores': '逻辑核心数量',
    'processor_threads_per_core': '处理器线程数',
    'processor_virtual_CPUs': '虚拟化cpu数量',
    'mem_total_GiB': '总运行内存',
    'mem_free_GiB': '可用运行内存',
    'swap_total_GiB': 'swap总内存',
    'swap_free_GiB': 'swap可用内存',
    'system': '系统类型',
    'OS_family': '系统具体所属',
    'distribution': '系统发行版',
    'distribution_version': '系统发行版版本号',
    'distribution_release': '发行版名称',
    'kernel': '内核版本号',
    'date_time': '时间戳',
    'SELinux': 'selinux安全机制',
    'package_manager': '软件包管理方式',
    'hostname': '主机名',
    'domain': '域名',
    'FQDN': '全限定域名',
    'All_IPv4': '所有的ipv4地址',
    'All_IPv6': '所有的ipv6地址',
    'ansible_disk_usage': '根文件夹使用信息',
    'ansible_fact_interfaces_information': '网络接口信息',
    'ansible_disk_information': '硬盘信息',
    'custom_monitor': '用户自定义模板收集信息'
}

export default translation
