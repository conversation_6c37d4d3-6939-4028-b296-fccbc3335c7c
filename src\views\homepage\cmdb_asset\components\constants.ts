// 需要标签的字段
export const tagField = [
  "file_system",
  "free_space",
  "network_ip_address_enabled",
  "service_start_state",
  "service_start_mode",
  "service_type",
  "service_state",
  "ansible_system",
  "ansible_architecture",
  "ansible_selinux",
  "size_available",
  "virtual",
  "active",
  "type",
  "cdb",
  "active_state",
  "ASM_Proxy_Instance",
  "isdefault",
  "archived",
  "isdba",
  "languages",
  "namespaces",
  "size",
  "canlogin",
  "superuser",
  "template_list",
  "type",
  "status",
];
// 字段的具体属性 'primary' | 'success' | 'info' | 'warning' | 'danger'
export const SuccessAttributeList = ["true", "是", "正常", "活跃"];
export const DangerAttributeList = ["false", "否", "停止", "休眠"];
export enum BlockTypeEnum {
  nomal = "nomal",
  table = "table",
  nestedtable = "nestedtable",
}

// 默认的svg图标名称
export const defaultSvgIconName = "system_info";

// nginx  配置文件:config_data   配置文件夹: cannot_parse_file_data  基础信息：base

// oracle 数据库基础信息：database  参数配置信息：parameters  redologs线程信息：redologs  用户环境：userenv  用户信息：users   表空间信息：pdbs_tablespaces_temptablespaces   表空间:tablespaces 临时表空间：temp_tablespaces    基础信息：base

// pg 数据库列表信息:databases  用户信息:roles  配置信息:settings 表空间信息:tablespaces   基础信息：base

// windows  系统信息:operating_system  硬件信息:processor_memory  硬盘信息:hard_disk 网络接口信息:network 软件安装信息:install_software  服务信息:windows_services    环境配置信息 ansible_env   基础信息：base

// Linux  环境变量信息:ansible_env   硬件信息:hardware  系统信息:operating_system  网络接口信息:network 分区信息:ansible_devices   挂载信息:ansible_mounts  基础信息：base
export const SvgIconNameList = {
  base: defaultSvgIconName,
  tablespaces: "db_tablespaces",
  // nginx
  config_data: "config_content",
  cannot_parse_file_data: "config_file",
  // oracle
  database: "db_info",
  parameters: "db_config",
  redologs: "db_redologs",
  userenv: "db_userenv",
  users: "db_userlist",
  pdbs_tablespaces_temptablespaces: "db_tablespaces",
  temp_tablespaces: "",
  // pg
  databases: "db_info",
  roles: "db_userlist",
  settings: "db_config",
  // windows
  operating_system: "system_info",
  processor_memory: "hardware_info",
  hard_disk: "farm",
  network: "network_info",
  install_software: "vmware",
  windows_services: "windows",
  ansible_env: "env_info",
  // Linux
  hardware: "hardware_info",
  ansible_devices: "topology-server",
  ansible_mounts: "mounts_info",
};

export const ResourceTypeEnum = {
  server: "服务器",
  database: "数据库",
  middleware: "中间件",
  businessApp: "业务应用",
  network: "网络设备",
  software: "软件",
  cluster: "集群",
  serviceInterface: "服务接口",
};
