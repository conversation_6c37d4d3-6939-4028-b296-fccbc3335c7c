import api from "@/plugins/axios/index";
import axios from "axios";
/**
 * 获取资源（数据库、中间件、网络设备、业务应用）列表
 * @param {*} data
 * @returns
 */
export function cmdbGetResourceList(data) {
  return api.get("/resource/get?type=" + data);
}
/**
 * 新增资源信息
 * @param {*} data
 * @returns
 */
export function cmdbAddResource(data) {
  return api.post("/resource", JSON.stringify(data));
}
/**
 * 修改资源信息
 * @param {*} data
 * @returns
 */
export function cmdbUpdateResource(data) {
  return api.post("/resource/update_cmdb_resource/", JSON.stringify(data));
}
/**
 * 获取资源信息详情
 * @param {} data
 * @returns
 */
export function cmdbGetResourceAsset(data) {
  //获取集群详情
  return api.post("/resource/get_resources_info/", JSON.stringify(data));
}
/**
 * 批量删除
 * @param {*} data
 * @returns
 */
export function cmdbDeleteResource(data) {
  //删除集群
  return api.post("/resource/delete_cmdb_resource/", data);
}

/**
 * 手动更新数据库详情
 * @param {*} data
 * @returns
 */
export function updateResourceAsset(data) {
  return api.post("/resource/get", JSON.stringify(data));
}

/**
 * 添加zabbix监控
 */
export function addMonitoring(data) {
  return api.post("/resource/operation", JSON.stringify(data));
}

/**
 * ssh连接验证
 * @param {*} data
 * @returns
 */
export function ssh(data) {
  return api.post("/resource/verify_ssh_connection_information/", JSON.stringify(data));
}

/**
 * 创建服务接口
 * @param {*} data
 */
export function createServiceInterface(data) {
  return api.post("/resource/service", JSON.stringify(data));
}

/**
 * 修改服务接口
 * @param {*} data
 * @returns
 */
export function updateServiceInterface(data) {
  return api.post("/resource/update_service/", JSON.stringify(data));
}

/**
 * 添加集群信息
 * @param {*} data
 * @returns
 */
export function addNewCluster(data) {
  return api.post("/resource/cluster", JSON.stringify(data));
}

// 获取资源自动cmdb数据 资源里面是zabbix_id，主页拓扑图上是id
export function getcmdbresourcedetail(data) {
  return api.get(`/resource/auto_cmdb?id=${data.id}`);
}

export function batchImportDatabaseInfoToUpdate(data) {
  return api.post("/resource/batch_modify_database_under_resource/", JSON.stringify(data));
}
