/**
 * 获取axios用的baseUrl,存在代理
 * @returns
 */
export function getBaseUrl() {
  return import.meta.env.DEV && import.meta.env.VITE_OPEN_PROXY === "true"
    ? "/proxy/"
    : import.meta.env.VITE_APP_API_BASEURL;
}

/**
 * 获取baseUrl,只返回env写的url
 * @returns
 */
export function getEnvViteApiBaseurl() {
  return import.meta.env.VITE_APP_API_BASEURL;
}

/**
 * 获取baseUrl 截取掉端口的ip
 * @returns
 */
export function getEnvViteApiBaseurlIp() {
  return getEnvViteApiBaseurl().slice(0, -4);
}
