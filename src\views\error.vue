<route>
{
    name: 'error',
    meta: {
        title: "系统出现异常",
        constant: true,
        layout: false
    }
}
</route>

<script setup>

</script>

<template>
    <div class="notfound">
        <svg-icon name="auth" class="icon" />
        <div class="content">
            <h1>授权异常</h1>
            <div class="desc">非本机授权文件，请检查是否服务器硬件发生变化。</div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.notfound {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1000px;
    @include position-center(xy);
    .icon {
        width: 500px;
        height: 500px;
    }
    .content {
        h1 {
            margin: 0;
            font-size: 52px;
            color: var(--el-text-color-primary);
        }
        .desc {
            margin: 20px 0 30px;
            font-size: 20px;
            color: var(--el-text-color-secondary);
        }
    }
}
</style>
