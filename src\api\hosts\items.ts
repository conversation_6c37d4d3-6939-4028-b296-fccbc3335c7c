import api from "@/plugins/axios";

export interface ItemsDetailParams {
  enabled: boolean;
  history_retention_days?: number;
  trends_retention_days?: number;
  item_id: number;
}
/**
 * 调整单个监控项的启用状态、历史数据保留时长和趋势数据保留时长。
 * @returns
 */
export function setItemsDetail(ItemsDetail: ItemsDetailParams) {
  return api.post(`/zabbix_alarm_configuration_management/update_item_params/`, ItemsDetail);
}

export interface ItemsstatusBatchParams {
  item_ids: number[];
  enabled: boolean;
}
/**
 * 批量启用或停用多个监控项。
 */
export function setItemsStatusBatch(ItemsstatusBatch: ItemsstatusBatchParams) {
  return api.post(`/zabbix_alarm_configuration_management/set_items_status/`, ItemsstatusBatch);
}

/**
 * 监控项的标签tag列表
 * @returns
 */
export function getHostItemsOfTargets(host_id) {
  return api.post(`/zabbix_alarm_configuration_management/get_host_items_of_targets/`, { host_id });
}
