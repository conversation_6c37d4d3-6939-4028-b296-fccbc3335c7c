import Unocss from 'unocss/vite'
import presetIcons from '@unocss/preset-icons'
import { presetUno } from 'unocss'
import { presetAttributify } from 'unocss'
import { presetTypography } from 'unocss'
import { transformerDirectives } from 'unocss'
import { transformerVariantGroup } from 'unocss'

export default function createUnocss() {
  return Unocss({
    presets: [
      // presetUno(),
      // presetAttributify(),
      // presetTypography(),
      // presetIcons({
      //   extraProperties: {
      //     'display': 'inline-block'
      //   }
      // })
    ],
    transformers: [
      // transformerDirectives(),
      // transformerVariantGroup(),
    ],

  })
}
