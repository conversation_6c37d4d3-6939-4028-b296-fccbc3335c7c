<script setup name="IframeLayout">
import { useRoute } from "vue-router";
const route = useRoute();
const iframe = ref({
  loading: true,
  src: route.query.url + "?orgId=1",
});

const timer = reactive({
  timeInter: null,
});
const apmIframe = ref();
onMounted(() => {
  timer.timeInter = setInterval(() => {
    cancel_css();
  }, 100);
  getData();
});
onUnmounted(() => {
  clearInterval(timer.timeInter); //销毁
  timer.timeInter = null;
});
function getData() {
  apmIframe.value.onload = () => {
    iframe.value.loading = false;
  };

}
const cancel_css = () => {

  const iframeElement = document.getElementById("myIframe");
  const iframeContent = iframeElement.contentDocument || iframeElement.contentWindow.document;
  const targetElement = iframeContent.getElementsByClassName("sidemenu")[0];
  if (targetElement) {
    targetElement.style.display = "none"; // 设置为display: none
  }
};

</script>

<template>
  <div>
    <div v-loading="iframe.loading" class="iframe">

      <iframe ref="apmIframe" id="myIframe" frameborder="no" border="0" :src="iframe.src"></iframe>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.iframe,
iframe {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
}
</style>
