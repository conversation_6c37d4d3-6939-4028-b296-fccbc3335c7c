<script setup name="LargeScreenIndex">
import { Get<PERSON>ra<PERSON>a, GetGrafanaList } from "@/api/modules/screen_management/screen";
import { useRouter } from "vue-router";
import storage from "@/utils/storage";
import { ArrowLeftBold } from "@element-plus/icons-vue";
import { goBack } from "@/utils";

const router = useRouter();
const data = reactive({
  dataList: [],
  tabs: [],
  loading: false,
  player: false,
});
onMounted(() => {
  getData();
  getPlayer();
});
function getData() {
  data.loading = true;
  GetGrafana().then((res) => {
    data.dataList = res.data;

    // 使用reduce方法根据group组合成新的对象数组
    data.tabs = data.dataList.reduce((acc, item) => {
      const group = item.folderTitle;

      // 检查新数组中是否已存在该group的对象
      const existingGroup = acc.find((obj) => obj[group]);

      // 如果存在，则将当前项添加到该group的对象中
      if (existingGroup) {
        existingGroup[group].push(item);
      } else {
        // 如果不存在，则创建一个新的group对象，并将当前项添加到该对象中
        const newGroup = { [group]: [item] };
        acc.push(newGroup);
      }

      return acc;
    }, []);
  });
  data.loading = false;
}
function getPlayer() {
  GetGrafanaList().then((res) => {
    if (res.data == null || res.data.length == 0) {
      data.player = false;
    } else {
      data.player = true;
    }
  });
}
function iframe(item) {
  router.push({
    name: "player_iframe",
    query: {
      url: item.url,
      uid: item.uid,
      name: item.title,
    },
  });
}
function onCreate() {
  const value = {
    // opt: "create",
    opt: data.player == true ? "edit" : "create",
    data: JSON.stringify(data.tabs),
  };
  storage.session.set("player_list", JSON.stringify(value));
  router.push({
    name: "player_list",
  });
}
</script>

<template>
  <div>
    <page-main v-loading="data.loading" title="大屏可视化配置">
      <template #title>
        <el-icon class="cursor-pointer mr-10px mt-5px" @click="goBack()"><ArrowLeftBold /></el-icon>
        大屏可视化配置
      </template>
      <div class="flex">
        <el-button type="primary" size="large" @click="onCreate" class="!h-44px flex-shrink-0">
          <template #icon>
            <el-icon>
              <svg-icon :name="data.player == true ? 'ep:edit' : 'ep:plus'" />
            </el-icon>
          </template>
          {{ data.player == true ? "编辑大屏播放" : "添加大屏播放" }}
        </el-button>

        <div class="configure-tips ml-10px flex-1">
          <el-alert :type="data.player == true ? 'success' : 'warning'" :closable="false" show-icon>
            <template #default>
              <div>
                <span class="text-18px pr-10px">{{ data.player == true ? "成功配置" : "警告" }}</span>
                <span class="text-14px">
                  {{
                    data.player == true
                      ? "你已经成功配置播放列表，点击编辑按钮可以进行列表调整"
                      : "暂未添加任务播放列表"
                  }}
                </span>
              </div>
            </template>
          </el-alert>
        </div>
      </div>
      <div class="tab-container mt-20px">
        <el-tabs :stretch="true" class="demo-tabs min-h-400px" type="card" tab-position="left">
          <el-tab-pane v-for="(item, index) in data.tabs" :key="index" :label="Object.keys(item)[0]">
            <template #label>
              <el-tooltip :content="Object.keys(item)[0]" effect="light" placement="right">
                <div class="custom-tabs-label flex items-center">
                  <el-icon size="18px"><svg-icon name="type" /></el-icon>
                  <div style="font-size: 14px; padding-left: 5px" class="w-160px text-overflow">
                    {{ Object.keys(item)[0] }}
                  </div>
                </div>
              </el-tooltip>
            </template>
            <el-row :gutter="10">
              <el-col
                style="padding-top: 10px"
                v-for="(item, index) in Object.values(item)[0]"
                :key="index"
                :xs="24"
                :sm="24"
                :md="12"
                :lg="8"
                :xl="4"
              >
                <Color
                  :header="item.title"
                  :num="123"
                  :tip="item.type"
                  @click="iframe(item)"
                  class="text-overflow"
                  :title="item.title"
                />
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
// scss
.el-alert {
  margin-top: 10px;
  margin-bottom: 10px;
}

.text-overflow {
  @include text-overflow;
}

.demo-tabs {
  ::v-deep {
    .el-tabs__item {
      height: 56px;
    }

    .el-tabs__header {
      margin-right: 20px;
    }
  }
}
.configure-tips {
  .el-alert {
    margin-top: 0px;
    margin-bottom: 0px;
  }
}
.text-overflow {
  ::v-deep {
    .el-card__header {
      @include text-overflow;
    }
  }
}
</style>
