<script setup>
import useSettingsStore from "@/store/modules/settings";
import { onMounted, reactive } from "vue";
import { useTabbar } from "@/utils/composables";
import { GetZabbixTemplate } from "@/api/modules/zabbix_api_management/zabbix";
import { cmdbAddResource, cmdbUpdateResource, cmdbGetResourceAsset } from "@/api/modules/cmdb/resource";
import { ElMessage } from "element-plus";
import { addResourceInfo } from "../components/utils";
import { async } from "@antv/x6/lib/registry/marker/async";

const settingsStore = useSettingsStore();

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(["closeDialog", "getResource", "update:modelValue"]);

const formRef = ref();
const data = reactive({
  extra_params: [],
  template_list: [],
  databaseForm: {
    ip: "",
    relation_id: 0,
    zabbix_id: 0,
    databaseName: "",
    template_list: [],
    group_name: "网络设备",
    configuration_group: [],
  },
  cloneButton: false,
  submitLoading: false,
  forRules: {
    databaseName: [{ required: true, message: "请输入网络设备名称", trigger: "blur" }],
    databaseSoft: [{ required: true, message: "请选择数据库软件", trigger: "blur" }],
    ip: [{ required: true, message: "请输入设备IP", trigger: "blur" }],
    db_template_list: [{ required: true, message: "请选择监控模板", trigger: "blur" }],
    group_name: [{ required: true, message: "请填写分组", trigger: "blur" }],
    software_type: [{ required: true, message: "请选择软件分类", trigger: "blur" }],
    software_name: [{ required: true, message: "请填写软件名称", trigger: "blur" }],
  },
});
onMounted(() => {
  if (props.modelValue != 0) {
    data.cloneButton = true;
    getData();
  }
  getTemplate();
  if (props.modelValue == 0) {
    showNetworkCI();
  }
});
function getTemplate() {
  GetZabbixTemplate().then((res) => {
    data.template_list = filterTemp(res.data);
  });
}
function filterTemp(list) {
  return list.filter((item) => item.name.includes("网络设备-"));
}
//获取数据库具体信息
function getData() {
  cmdbGetResourceAsset({ type: "网络设备", relation_id: props.modelValue }).then((res) => {
    data.databaseForm = res.data;

    res.data.configuration_group_en.forEach((item) => {
      const obj = {
        label: item[0],
        key: item[1],
        value: item[2],
      };

      data.extra_params.push(obj);
    });
  });
}
function goBack() {
  emit("closeDialog");
}

let canAddExtraParam = computed(() => {
  return data.extra_params.every((item) => {
    return !item.isEdit;
  });
});

function addExtraParams() {
  data.extra_params.push({
    isEdit: true,
    label: "",
    key: "",
    value: "",
  });
}

function addParams(data) {
  data.isEdit = false;
  if (data.value == "") {
    ElMessage.warning("自定义字段不能置空");
    data.value = "待填写";
  }
}

function removeExtraParams(index, row) {
  if (row.key == "团体名") {
    ElMessage.error("必要字段不能删除");
  } else {
    data.extra_params.splice(index, 1);
  }
}
function showNetworkCI() {
  data.extra_params = [];
  data.extra_params.push({
    isEdit: false,
    label: "SNMP_COMMUNITY",
    key: "团体名",
    value: undefined,
  });
}

//新增网络设备
function addEquipment() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      data.submitLoading = true;
      const isUpdate = props.modelValue != 0;
      try {
        const submitResult = await addResourceInfo(data.extra_params, data.databaseForm, isUpdate, "网络设备");
        if (submitResult.status_code == 200) {
          emit("getResource");
          goBack();
        }
      } catch (error) {}

      data.submitLoading = false;
    }
  });
}

//克隆主机
function handleClone() {
  emit("update:modelValue", 0);
  //将relation_id 和zabbix_id置为0
  data.databaseForm.relation_id = 0;
  data.databaseForm.zabbix_id = 0;
  //隐藏克隆按钮
  data.cloneButton = false;
}
</script>
<template>
  <div>
    <el-row type="flex" justify="center">
      <el-col>
        <el-form :model="data.databaseForm" label-width="100px" ref="formRef" :rules="data.forRules">
          <el-form-item label="ip" prop="ip">
            <el-input v-model="data.databaseForm.ip" placeholder="请输入网络设备ip" />
          </el-form-item>
          <el-form-item label="名称" prop="databaseName">
            <el-input v-model="data.databaseForm.databaseName" placeholder="请输入设备名称" />
          </el-form-item>
          <el-form-item label="群组" prop="group_name">
            <el-input v-model="data.databaseForm.group_name" placeholder="多个分组用“,”隔开，例如:内网服务器,Linux" />
          </el-form-item>
          <el-form-item label="监控模板" prop="template_list">
            <el-select v-model="data.databaseForm.template_list" filterable multiple placeholder="请选择监控模板">
              <el-option
                v-for="(item, index) in data.template_list"
                :label="item.name"
                :value="item.name"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="参数">
            <el-table :data="data.extra_params" style="width: 100%">
              <el-table-column label="名称(CN)" align="center">
                <template #default="scope">
                  <el-input v-if="scope.row.isEdit && !scope.row.key" v-model="scope.row.key" size="small" />
                  <span v-else>{{ scope.row.key }}</span>
                </template>
              </el-table-column>
              <el-table-column label="名称(EN)" align="center">
                <template #default="scope">
                  <el-input v-if="scope.row.isEdit && !scope.row.label" v-model="scope.row.label" size="small" />
                  <span v-else>{{ scope.row.label }}</span>
                </template>
              </el-table-column>

              <el-table-column label="值" align="center">
                <template #default="scope">
                  <el-input v-if="scope.row.isEdit" v-model="scope.row.value" size="small" />
                  <span v-else>{{ scope.row.value }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center">
                <template #default="scope">
                  <template v-if="scope.row.isEdit">
                    <el-button type="primary" plain size="small" @click="addParams(scope.row)">保存</el-button>
                  </template>
                  <template v-else>
                    <el-button type="primary" plain size="small" @click="scope.row.isEdit = true">编辑</el-button>
                    <el-popconfirm
                      title="是否要删除此行？"
                      style="margin-left: 10px"
                      @confirm="removeExtraParams(scope.$index, scope.row)"
                    >
                      <template #reference>
                        <el-button type="danger" plain size="small">删除</el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <div class="submit_button">
          <el-button type="primary" @click="addEquipment" :loading="data.submitLoading">提交</el-button>
          <el-button type="primary" plain @click="handleClone" v-if="data.cloneButton">克隆</el-button>
          <el-button @click="goBack">取消</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<style scoped lang="scss">
.line {
  max-width: 5%;
}
.fixed-textarea textarea {
  height: 200px; /* 设置合适的高度值 */
}
.submit_button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
