<script setup>
import { onMounted, reactive } from "vue";
const emit = defineEmits(["update:modelValue","sshSubmit"]);
const props = defineProps({
  modelValue: {
    type: Boolean,
    default:false
  },
});
const data = reactive({
    user: '',
    password:'',
})
const modelValue = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
})
function sshSubmit(){
  emit("sshSubmit", data)
  modelValue.value = false
}
</script>

<template>
  <div>
    <el-dialog v-model="modelValue" center title="ssh连接" width="30%" :destroy-on-close="true">
      <el-form label-position="right">
        <el-form-item label="输入服务器账户：">
          <el-input style="width: 300px" v-model="data.user" clearable />
        </el-form-item>
        <el-form-item label="输入服务器密码：">
          <el-input style="width: 300px" v-model="data.password" show-password clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="modelValue = false">取消</el-button>
          <el-button type="primary" @click="sshSubmit()">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

</style>
