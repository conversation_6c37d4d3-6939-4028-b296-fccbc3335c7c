<template>
  <page-main title="主机列表">
    <div class="mb-10px flex justify-between items-center">
      <div>
        <el-button
          type="primary"
          @click="batchCutMonitoringDisable(MonitoringStatus.enable)"
          :disabled="!data.batch.selectionDataList.length"
        >
          <template #icon>
            <el-icon><Unlock /></el-icon>
          </template>
          启用
        </el-button>
        <el-button
          type="primary"
          @click="batchCutMonitoringDisable(MonitoringStatus.disable)"
          :disabled="!data.batch.selectionDataList.length"
        >
          <template #icon>
            <el-icon><Lock /></el-icon>
          </template>
          停用
        </el-button>
      </div>
      <div>
        <el-space wrap>
          <div class="w-350px">
            <el-input
              v-model="data.search.hostname"
              placeholder="输入主机名进行筛选,不区分大小写"
              clearable
              @keyup.enter="searchHost()"
            />
          </div>

          <el-button type="primary" @click="searchHost()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>
    </div>
    <el-table
      v-loading="data.tableLoading"
      :data="data.dataList"
      style="width: 100%"
      stripe
      @selection-change="data.batch.selectionDataList = $event"
    >
      <el-table-column type="selection" align="center" fixed />
      <el-table-column prop="host_name" label="主机名称" width="width"></el-table-column>
      <el-table-column prop="enabled" label="状态(点击切换发布状态)" width="width">
        <template #default="scope">
          <el-button
            :type="scope.row.enabled ? 'success' : 'danger'"
            size="small"
            plain
            @click="cutMonitoringDisable(scope.row)"
          >
            {{ scope.row.enabled == true ? "已启用" : "已停用" }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" label="操作">
        <template #default="scope">
          <el-space>
            <router-link
              type="primary"
              :to="{
                name: 'ruleItemsList',
                params: { id: scope.row.host_id },
                query: { host_name: scope.row.host_name },
              }"
            >
              监控项
            </router-link>
            <router-link
              type="primary"
              :to="{
                name: 'ruleTriggersList',
                params: { id: scope.row.host_id },
                query: { host_name: scope.row.host_name },
              }"
            >
              触发器
            </router-link>
          </el-space>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pagination.page"
      :total="pagination.total"
      :page-size="pagination.size"
      :page-sizes="pagination.sizes"
      :layout="pagination.layout"
      :hide-on-single-page="false"
      class="paginationTable"
      background
      @size-change="sizeChange"
      @current-change="currentChange"
    />
  </page-main>
</template>

<script setup lang="ts">
import { getHostsList, setHostsdisableMonitoring, MonitoringStatus } from "@/api/hosts/hosts";
import { usePagination } from "@/utils/composables";
import { Lock, Unlock } from "@element-plus/icons-vue";

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
/**
 *
 */
const data = reactive({
  search: {
    hostname: "",
  },
  dataList: [],
  allList: [],
  tableLoading: false,
  batch: {
    selectionDataList: [],
  },
});
/**
 * 搜索
 */
const searchHost = () => {
  let list = [];
  data.allList.filter((item) => {
    if (item.host_name.toLowerCase().indexOf(data.search.hostname.toLowerCase()) !== -1) {
      list.push(item);
    }
  });
  let params = getParams();
  const filterres = dataFilter(list, params);
  data.dataList = filterres.list;
  pagination.value.total = filterres.total;
};

/**
 * 获取主机列表
 */
function getHostsListFn() {
  data.tableLoading = true;
  getHostsList()
    .then((res) => {
      data.allList = res.data;
      paging();
    })

    .catch((error) => {
      console.log(error);
    })
    .finally(() => {
      data.tableLoading = false;
    });
}
/**
 *
 * @param row 触发行的数据
 */
function cutMonitoringDisable(row) {
  ElMessageBox.confirm(row.enabled ? " 确定要停用主机?" : " 确定要启用主机？")
    .then(() => {
      setHostsdisableMonitoringFun([row.host_id], !row.enabled);
    })
    .catch(() => {});
}
/**
 *  批量停启用
 * @param row
 */
function batchCutMonitoringDisable(enabled) {
  ElMessageBox.confirm(enabled ? " 确定要启用主机？" : " 确定要停用主机?")
    .then(() => {
      console.log(data.batch.selectionDataList);
      const idList = data.batch.selectionDataList.map((item) => item.host_id);
      setHostsdisableMonitoringFun(idList, enabled);
    })
    .catch(() => {});
}
/**
 * 设置主机停启用
 * @param list
 * @param enabled
 */
function setHostsdisableMonitoringFun(list, enabled) {
  const params = {
    host_ids: list,
    status: enabled,
  };
  setHostsdisableMonitoring(params)
    .then((res) => {
      getHostsListFn();
    })
    .catch((error) => {
      console.log(error);
    });
}

// 每页条数改变
function sizeChange(size) {
  onSizeChange(size).then(() => {
    searchHost();
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.hostname != "") {
      searchHost();
    } else {
      paging();
    }
  });
}
function paging() {
  let params = getParams();
  let res = dataFilter(data.allList, params);
  data.dataList = res.list;
  pagination.value.total = res.total;
  data.tableLoading = false;
}
/**
 * 页面加载时
 */
onMounted(() => {
  getHostsListFn();
});

function dataFilter(dataList, params) {
  let list = [];
  dataList.forEach((element) => {
    list.push(element);
  });

  if (params.hostname != "" && params.hostname != undefined) {
    list = dataList.filter((item) => {
      return item ? item.hostname.includes(params.hostname) : true;
    });
  }

  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });
  return {
    list: pageList,
    total: list.length,
  };
}
</script>

<style lang="scss" scoped></style>
