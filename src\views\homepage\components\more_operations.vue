<!-- 更多操作 -->
<!-- 通过插槽插入按钮操作 -->
<template>
  <el-dropdown style="margin-left: 10px" trigger="click">
    <el-button>
      更多操作
      <el-icon class="el-icon--right"><arrow-down /></el-icon>
    </el-button>
    <template #dropdown>
      <slot />
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { ArrowDown } from "@element-plus/icons-vue";
</script>

<style scoped></style>
