<template>
  <el-dialog title="编辑监控项" v-model="dialogVisible" width="800px">
    <el-form :model="itemdata" :rules="rules" ref="formRef" :label-width="140">
      <el-form-item label="监控项名称" prop="item_name">
        <el-input v-model="itemdata.item_name" disabled></el-input>
      </el-form-item>

      <el-form-item label="更新间隔" prop="interval_seconds">
        <el-input
          v-model="itemdata.interval_seconds"
          placeholder="例如：30s、2h、1d"
          class="w-180px! mr-10px"
          @input="handleInput"
        ></el-input>
        <div class="tip">支持单位：s（秒）、m（分钟）、h（小时）、d（天）</div>
      </el-form-item>
      <el-form-item label="历史数据保留时长" prop="history_retention_days">
        <div class="flex flex-row">
          <el-input
            v-model="itemdata.history_retention_days"
            class="w-180px! mr-10px"
            @input="handlehistoryInput"
          ></el-input>
          <div class="tip">支持：0，7d-30d。单位：d（天）</div>
        </div>
      </el-form-item>

      <el-form-item label="停启用" prop="enabled">
        <el-switch v-model="itemdata.enabled" active-text="启用" inactive-text="停用" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="flex justify-center">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="clickSaveItemDetail">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { setItemsDetail } from "@/api/hosts/items";
import { deepClone } from "@/utils";

const props = defineProps({
  modelValue: Boolean,
  itemDetail: Object,
});
const emit = defineEmits(["update:modelValue", "response"]);
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const itemdata = ref({});
const formRef = ref();
watch(
  () => props.modelValue,
  (isOpen) => {
    if (isOpen) {
      // 深拷贝 + 兜底初始化
      itemdata.value = props.itemDetail ? JSON.parse(JSON.stringify(props.itemDetail)) : { name: "", age: 0 };
      nextTick(() => {});
      setTimeout(() => {}, 100);
      console.log("Form 数据:", itemdata.value);
    }
  }
);

const rules = reactive({
  interval_seconds: [
    { required: true, message: "更新间隔", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        // 解析输入内容
        const { number, unit, isValidFormat } = parseInterval(value);

        // 校验格式
        if (!isValidFormat) {
          callback(new Error("格式错误，示例：30s、10m、2h、1d"));
          return;
        }

        // 校验单位合法性
        const validUnits = ["s", "m", "h", "d"];
        if (!validUnits.includes(unit)) {
          callback(new Error("单位必须是 s、m、h、d 之一"));
          return;
        }

        // 校验数值范围
        if (number <= 0 || !Number.isFinite(number)) {
          callback(new Error("必须输入大于0的数字"));
          return;
        }

        // 转换为秒并校验总时长
        const seconds = calculateSeconds(number, unit);
        if (seconds < 1 || seconds > 86400) {
          callback(new Error("时间间隔必须在1秒~1天（86400秒）之间"));
          return;
        }

        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
  history_retention_days: [
    { required: true, message: "历史数据保留时长", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        const trimmedValue = value.trim().toLowerCase();

        // 1. 允许 0（不带单位）
        if (trimmedValue === "0") {
          callback();
          return;
        }
        // 2. 检查格式是否为 "数字 + d"
        const regex = /^(\d+)d$/;
        const match = trimmedValue.match(regex);
        if (!match) {
          callback(new Error("格式错误，示例：0 或 7d~30d"));
          return;
        }
        // 3. 提取数值并校验范围
        const num = parseInt(match[1], 10);
        if (num >= 7 && num <= 30) {
          callback();
        } else {
          callback(new Error("值必须在 7d~30d 之间"));
        }
      },
      trigger: ["blur", "change"],
    },
  ],
  enabled: [{ required: true, message: "停启用", trigger: "blur" }],
});
// 解析输入内容（例如 "30s" → { number:30, unit:'s', isValidFormat:true }）
const parseInterval = (input) => {
  const regex = /^(\d+)([smhd])$/i; // 匹配数字+单位
  const match = input.trim().match(regex);

  if (!match) {
    return { number: NaN, unit: "", isValidFormat: false };
  }

  return {
    number: parseFloat(match[1]),
    unit: match[2].toLowerCase(),
    isValidFormat: true,
  };
};
const handleInput = (value) => {
  itemdata.value.interval_seconds = value.toLowerCase();
};
const handlehistoryInput = (value) => {
  itemdata.value.history_retention_days = value.toLowerCase();
};
// 单位转秒
const calculateSeconds = (number, unit) => {
  switch (unit) {
    case "s":
      return number;
    case "m":
      return number * 60;
    case "h":
      return number * 3600;
    case "d":
      return number * 86400;
    default:
      return 0;
  }
};
function clickSaveItemDetail() {
  formRef.value.validate((valid) => {
    if (valid) {
      let params = {
        item_id: itemdata.value.item_id,
        enabled: itemdata.value.enabled, // 可选, true 或 false
        history_retention_days: itemdata.value.history_retention_days,
        interval_seconds: itemdata.value.interval_seconds, // 可选, 更新频率
      };

      setItemsDetail(params)
        .then((res) => {
          dialogVisible.value = false;
          emit("response");
        })
        .finally(() => {});
    }
  });
}
</script>

<style lang="scss" scoped>
.tip {
  @apply text-gray text-12px;
}
</style>
