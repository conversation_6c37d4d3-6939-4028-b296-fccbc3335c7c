<script setup>
import Storage from "@/utils/storage";
import { DealDb4bixConfig } from "@/api/modules/configuration/oracleDb4bix";

const formRef = ref();
const router = useRouter();
const data = ref({
  loading: false,
  form: {
    name: "",
    type: "oracle",
    instance: "",
    host: "",
    port: "",
    user: "",
    service_name: "",
    password: "",
    pool: "Common",
    // connectString: "",
  },
  rules: {
    name: [{ required: true, message: "请输入名称", trigger: "blur" }],
    instance: [{ required: true, message: "请输入监控数据库实例名", trigger: "blur" }],
    host: [{ required: true, message: "请输入监控数据库连接地址", trigger: "blur" }],
    port: [{ required: true, message: "请输入监控数据库连接端口", trigger: "blur" }],
    user: [{ required: true, message: "请输入监控数据库连接用户名", trigger: "blur" }],
    password: [{ required: true, message: "请输入监控数据库的连接密码", trigger: "blur" }],
    service_name: [{ required: true, message: "请输入监控数据库的服务名称", trigger: "blur" }],
  },
  // Template: {
  //   name: "",
  //   type: "oracle",
  //   instance: "",
  //   host: "",
  //   port: "",
  //   user: "",
  //   password: "",
  //   pool: "Common",
  //   // connectString: "",
  // },
  opt: "",
  dataList: [],
  index: 0,
  spliceNum: 1,
  NameToPassword: [],
});

onMounted(() => {
  getData();
});

//编辑时，获取cookie里的数据
function getData() {
  const temp_data = JSON.parse(Storage.local.get("cookieData"));
  Storage.local.remove("cookieData");
  const dataList = temp_data.dataList;
  data.value.dataList = dataList;
  data.value.NameToPassword = temp_data.NameToPassword;
  data.value.opt = temp_data.opt;
  if (temp_data.data) {
    if (temp_data.index == -1) {
      data.value.spliceNum = 0;
    } else {
      const index = data.value.dataList.findIndex((obj) => obj.name === temp_data.data.name);
      data.value.index = index;
      data.value.form = temp_data.data;
    }
  } else {
    if (temp_data.index == -1) {
      data.value.spliceNum = 0;
    } else {
      const index = temp_data.index;
      data.value.index = index;
      data.value.form = dataList[index];
    }
  }
}
function formattingData(list) {
  let JsonData = [];
  list.forEach((item) => {
    if (item.password == "******") {
      let index = data.value.NameToPassword.findIndex((temp) => {
        return item.name == temp.name;
      });
      item.password = data.value.NameToPassword[index].password;
    }
    JsonData.push(item);
  });

  return JsonData;
}

defineExpose({
  submit(callback) {
    data.value.loading = true;
    const index = data.value.index;
    const spliceNum = data.value.spliceNum;
    data.value.dataList.splice(index, spliceNum, data.value.form);

    formRef.value.validate((valid) => {
      if (valid) {
        const params = formattingData(data.value.dataList);
        DealDb4bixConfig(params)
          .then(() => {
            const message = spliceNum == 0 ? "添加成功" : "修改成功";
            ElMessage.success(message);
            callback && callback();
          })
          .finally(() => {
            data.value.loading = false;
          });
      } else {
        data.value.loading = false;
      }
    });
  },
});
</script>

<template>
  <div v-loading="data.loading">
    <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="200px" label-suffix="：">
      <el-form-item label="名称" prop="name">
        <el-input v-model="data.form.name" :disabled="data.opt == 'edit'" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="ip地址" prop="host">
        <el-input v-model="data.form.host" placeholder="请输入监控数据库连接ip" />
      </el-form-item>
      <el-form-item label="连接端口" prop="port">
        <el-input v-model="data.form.port" placeholder="请输入被采集数据库连接端口" />
      </el-form-item>
      <el-form-item label="登陆用户" prop="user">
        <el-input v-model="data.form.user" placeholder="请输入被采集数据库登陆用户" />
      </el-form-item>
      <el-form-item label="连接用户密码" prop="password">
        <el-input
          v-if="data.spliceNum == 0"
          v-model="data.form.password"
          type="password"
          show-password
          placeholder="请输入登陆用户的密码"
        />
        <el-input v-else v-model="data.form.password" type="password" placeholder="请输入登陆用户的密码" />
      </el-form-item>
      <el-form-item label="数据库实例名" prop="instance">
        <el-input v-model="data.form.instance" placeholder="请输入被采集的数据库名(暂只支持oracle)" />
      </el-form-item>
      <el-form-item label="数据库服务名" prop="service_name">
        <el-input v-model="data.form.service_name" placeholder="请输入被采集的数据库服务名(暂只支持oracle)" />
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
// scss
</style>
