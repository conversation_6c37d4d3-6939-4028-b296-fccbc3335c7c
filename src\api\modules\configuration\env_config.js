import api from "@/plugins/axios/index";
import axios from "axios";
/**
 * 获取所有基础配置详情
 * @returns
 */
export function queryEnvConfigInfo() {
  return api.get("/environment/get_environment_resource_info/");
}
/**
 * 添加配置信息
 * @param {*} data
 * @returns
 */
export function addEnvConfigInfo(data) {
  return api.post("/environment/create_or_update_variable/", JSON.stringify(data));
}
/**
 * 测试日报配置是否成功
 * @returns
 */
export function pushDailyReportTest() {
  return api.patch("/environment");
}
/**
 * 是否初次使用检测
 */
export function isFirstUseDetection() {
  return api.get("/environment/environment_first_check/");
}
/**
 * 判断配置是否完成
 * @returns
 */
export function checkConfigIsComplete() {
  return api.patch("/environment/upload_cache/");
}
/**
 * 初始化配置后，初始化资源访问地址
 * @returns true or false
 */
export function getConfigForWeb() {
  return api.get("/environment/get_front_end_init_variables/");
}
