<template>
  <page-main>
    <template #title>
      <div class="flex items-center">
        <el-icon class="cursor-pointer" @click="goBack()"><ArrowLeftBold /></el-icon>
        <span class="ml-10px">主机:{{ data.host_name }}监控项列表</span>
      </div>
    </template>
    <search-bar>
      <el-form ref="form" label-width="80px" label-suffix="：" @submit.native.prevent>
        <el-form-item label="名称">
          <el-space>
            <div class="w-350px">
              <el-input
                v-model="data.search.item_name"
                placeholder="输入监控项进行筛选,区分大小写"
                clearable
                @keyup.enter="searchItem()"
              />
            </div>
            <el-button type="primary" @click="searchItem()">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:search" />
                </el-icon>
              </template>
              筛选
            </el-button>
          </el-space>
        </el-form-item>
        <el-form-item label="标签">
          <div>
            <el-checkbox-group
              v-model="data.search.tags"
              size="small"
              @change="tagClick()"
              v-loading="data.targetLoading"
            >
              <template v-for="(item, index) in data.tagList">
                <el-checkbox-button
                  :key="item.target"
                  :value="item"
                  :label="item.target"
                  class="target_checkbox"
                  v-if="item.target != ''"
                >
                  {{ item.target }}
                  <span class="ml-3px">({{ item.count }})</span>
                </el-checkbox-button>
              </template>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </el-form>
    </search-bar>
    <div class="mb-10px flex items-center">
      <el-button
        type="primary"
        @click="batchCutMonitoringDisable(MonitoringStatus.enable)"
        :disabled="!data.batch.selectionDataList.length"
      >
        <template #icon>
          <el-icon><Unlock /></el-icon>
        </template>
        批量启用
      </el-button>
      <el-button
        type="primary"
        @click="batchCutMonitoringDisable(MonitoringStatus.disable)"
        :disabled="!data.batch.selectionDataList.length"
      >
        <template #icon>
          <el-icon><Lock /></el-icon>
        </template>
        批量停用
      </el-button>
    </div>
    <el-table
      v-loading="data.tableLoading"
      :data="data.tableData"
      style="width: 100%"
      stripe
      @selection-change="data.batch.selectionDataList = $event"
    >
      <el-table-column type="selection" align="center" fixed />
      <el-table-column prop="item_name" label="名称(点击修改)">
        <template #default="scope">
          <el-link @click="handleEditItem(scope.row)" underline type="primary">{{ scope.row.item_name }}</el-link>
        </template>
      </el-table-column>

      <el-table-column prop="interval_seconds" label="更新间隔"></el-table-column>
      <el-table-column prop="history_retention_days" label="历史记录"></el-table-column>
      <el-table-column prop="enabled" label="状态(点击切换发布状态)">
        <template #default="scope">
          <el-button
            :type="scope.row.enabled ? 'success' : 'danger'"
            size="small"
            plain
            @click="cutMonitoringDisable(scope.row)"
          >
            {{ scope.row.enabled == true ? "已启用" : "已停用" }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tags" label="标签">
        <template #default="scope">
          <el-tag v-for="tag in scope.row.tags" :key="tag" class="mr-5px">
            <span v-if="tag.tag == 'Application'">{{ tag.value }}</span>
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pagination.page"
      :total="pagination.total"
      :page-size="pagination.size"
      :page-sizes="pagination.sizes"
      :layout="pagination.layout"
      :hide-on-single-page="false"
      class="paginationTable"
      background
      @size-change="sizeChange"
      @current-change="currentChange"
    />
    <ruleItemDetailDialog
      v-model="data.showItemDetailDialog"
      :itemDetail="data.itemDetail"
      @response="getHostItemsList()"
    ></ruleItemDetailDialog>
  </page-main>
</template>

<script setup lang="ts">
import { getHostsMonitoringInfo, GetHostsMonitoringInfoParams, MonitoringStatus } from "@/api/hosts/hosts";
import { getHostItemsOfTargets, setItemsStatusBatch } from "@/api/hosts/items";
import { ArrowLeftBold, Lock, Unlock } from "@element-plus/icons-vue";
import ruleItemDetailDialog from "./ruleItemDetailDialog.vue";
import { usePagination } from "@/utils/composables";
import { goBack } from "@/utils";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
const route = useRoute();
const data = reactive({
  host_name: "" as any,
  host_id: "" as any,
  tableLoading: false,
  tableData: [],
  allData: [],
  batch: { selectionDataList: [] },
  search: {
    item_name: "",
    tags: [],
  },
  showItemDetailDialog: false,
  itemDetail: {} as any,
  targetLoading: false,
  tagList: [],
});
const routerParams = route.params;
const routerQuery = route.query;

/**
 *  批量设置监控项
 * @param enabled
 */
function batchCutMonitoringDisable(enabled) {
  ElMessageBox.confirm(enabled ? " 启用所选的监控项?" : " 停用所选的监控项??")
    .then(() => {
      const idList = data.batch.selectionDataList.map((item) => item.item_id);
      setItemsstatusBatchFun(idList, enabled);
    })
    .catch(() => {});
}

/**
 *  单个停启用
 * @param row
 */
function cutMonitoringDisable(row) {
  setItemsstatusBatchFun([row.item_id], !row.enabled);
}
/**
 *  停启用函数
 * @param list
 * @param enabled
 */
function setItemsstatusBatchFun(list, enabled) {
  const params = {
    item_ids: list,
    enabled: enabled,
  };
  setItemsStatusBatch(params)
    .then((res) => {
      getHostItemsList();
    })
    .catch((error) => {
      console.log(error);
    });
}
/**
 * 搜索
 */

function searchItem() {
  data.search.tags = [];
  getHostItemsList();
  getHostItemsTargetsList();
}

/**
 * 获取主机监控项列表
 */
function getHostItemsList() {
  data.tableLoading = true;

  let tags = [];
  data.search.tags.map((item) => {
    tags.push({ tag: "Application", value: item.target, operator: "1" });
  });
  const search = data.search.item_name ? data.search.item_name : {};

  getHostsMonitoringInfo({
    host_id: data.host_id,
    tags,
    item_name: search,
  } as GetHostsMonitoringInfoParams)
    .then((res) => {
      data.allData = res.data.items;
      paging();
    })
    .finally(() => {
      data.tableLoading = false;
    });
}
/**
 *
 */
function getHostItemsTargetsList() {
  getHostItemsOfTargets(data.host_id)
    .then((res) => {
      data.tagList = res.data;
    })
    .catch((error) => {})
    .finally(() => {});
}

/**
 * 点击新增/编辑监控项
 */
const handleEditItem = (row) => {
  data.showItemDetailDialog = true;
  data.itemDetail = row;
};

/**
 * 标签点击
 */
function tagClick() {
  getHostItemsList();
  if (data.search.tags.length == 0) {
    pagination.value.total = data.tagList.reduce((sum, e) => sum + Number(e.count || 0), 0);
  } else {
    pagination.value.total = data.search.tags.reduce((sum, e) => sum + Number(e.count || 0), 0);
  }
}
// 每页条数改变
function sizeChange(size) {
  onSizeChange(size).then(() => {
    searchItem();
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.item_name != "") {
      searchItem();
    } else {
      paging();
    }
  });
}
function paging() {
  let params = getParams();
  let res = dataFilter(data.allData, params);
  data.tableData = res.list;
  pagination.value.total = res.total;
  data.tableLoading = false;
}
function dataFilter(dataList, params) {
  let list = [];
  dataList.forEach((element) => {
    list.push(element);
  });

  if (params.item_name != "" && params.item_name != undefined) {
    list = dataList.filter((item) => {
      return item ? item.item_name.includes(params.item_name) : true;
    });
  }

  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });
  return {
    list: pageList,
    total: list.length,
  };
}
/**
 *
 */
onMounted(() => {
  if (!routerQuery.host_name || !routerParams.id) {
    router.push({
      name: "ruleHostList",
    });
  } else {
    data.host_name = routerQuery.host_name;
    data.host_id = routerParams.id;
    getHostItemsList();
    getHostItemsTargetsList();
  }
});
</script>

<style lang="scss" scoped>
.target_checkbox {
  :deep {
    .el-checkbox-button__inner {
      padding: 0px 0px 2px 0px;
      margin: 4px 6px;
      font-size: 12px;
      border: none;
      border-bottom: 1px dotted;

      // background: none;
      box-shadow: none;
    }
  }
}
</style>
