<route>
    {
        meta: {
            enabled: false
        }
    }
    </route>

<script setup>
import {
  GetAllPlaybook,
  UploadFunction,
  DeletePlaybook,
  UploadFunctionFile,
  CanclePublishPlaybook,
} from "@/api/modules/auto_operation_maintenance/operationMaintenance";
import { showNotification } from "@/plugins/element-ui";
import { usePagination } from "@/utils/composables";
import { ElMessage, ElMessageBox } from "element-plus";
import { pageChangeNum } from "@/views/homepage/components/utils";
import { ArrowLeftBold } from "@element-plus/icons-vue";
import { goBack } from "@/utils";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();

const data = ref({
  dialogTableVisible: false,
  describe: "",
  dialogUpload: false,
  function_list: [],
  formMode: "dialog",
  formMode1: "dialog",
  loading: false,
  // 搜索
  search: {
    keyWord: "",
    isSearch: false,
  },
  formModeProps: {
    visible: false,
    content: {},
    isAdd: false,
  },
  allList: [],
  //   formModeProps1: {
  //     visible: false,
  //     content: {},
  //     isAdd: false,

  //   },
  dialogVisible: false,
});

onMounted(() => {
  getPlaybook();
});

function CanclePublish(row) {
  ElMessageBox.confirm("确定要取消发布？")
    .then(() => {
      row.status = false;
      CanclePublishPlaybook(row.id, row).then((res) => {
        ElMessage.success({
          message: "取消发布成功",
          center: true,
        });
        getPlaybook();
      });
    })
    .catch(() => {
      // catch error
    });
}
// function data_Filter(dataList, params) {
//   let list = dataList;

//   let pageList = list.filter((item, index) => {
//     return index >= params.from && index < params.from + params.limit;
//   });

//   pageList.forEach((item) => {
//     item.params = item.params;
//   });

//   return {
//     list: pageList,
//     total: list.length,
//   };
// }

function getPlaybook() {
  data.value.loading = true;

  GetAllPlaybook()
    .then((res) => {
      let list = JSON.parse(res.data);
      data.value.function_list = list;
      data.value.allList = list;
      data.value.search.keyWord && (getParams.keyWord = data.value.search.keyWord);
      paging(data.value.allList);
      // let result = data_Filter(data.value.function_list, getParams());
      // data.value.function_list = result.list;
      // pagination.value.total = result.total;

      // data.value.loading = false;
    })
    .finally(() => {
      data.value.loading = false;
    });
}
function paging(lists) {
  let result = pageChangeNum(lists, getParams());
  data.value.function_list = result.list;
  pagination.value.total = result.total;
}

//发布方法
function upload_img(row) {
  if (row.category != "" && row.describe != "" && row.type != "" && row.picture != "" && row.name != "") {
    UploadFunction({ data: row.id }).then(() => getPlaybook());
    ElMessage.success("发布成功");
  } else {
    ElMessage.error("请先完成脚本的初始化设置");
  }
}

function add_function() {
  data.value.dialogUpload = true;
  //   data.value.formModeProps.content = {}
  //   data.value.formModeProps.visible = true;
  //   data.value.formModeProps.isAdd = true;
}
function beforeUpload(file) {
  const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
  const whiteList = ["zip"];
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElMessage.warning({
      message: "上传的文件只允许是zip文件",
      center: true,
    });
    return false;
  }
}
function chooseFile(file) {
  // data.value.loading = true
  data.value.dialogUpload = false;
  var forms = new FormData();

  forms.append("zipFile", file.file);

  const notification = ElNotification({
    title: "上传中",
    message: "文件正在上传中",
    position: "bottom-right",
    type: "info",
    duration: 0,
  });
  UploadFunctionFile(forms)
    .then((res) => {
      let message = "";
      const resdata = res.data;
      if (Array.isArray(resdata.message)) {
        message = resdata.message.join(",");
      } else {
        message = resdata.message;
      }

      if (resdata.result == "fail") {
        for (let i = 0; i < resdata.message.length; i++) {
          setTimeout(() => {
            showNotification("上传成功", resdata.message[i], "error");
          }, i * 1000);
        }
      } else if (resdata.result == "wait") {
        ElMessageBox.confirm(resdata.message, "警告", {
          confirmButtonText: "继续上传",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            UploadFunctionFile(forms).then((res) => {
              res = res.data;

              showNotification("上传成功", "文件上传成功");
              getPlaybook();
            });
          })
          .catch(() => {
            ElMessage({
              type: "info",
              message: "取消",
            });
          });
      } else {
        showNotification("上传成功", message);
        getPlaybook();
      }
    })
    .finally(() => {
      notification.close();
    })
    .catch(() => {
      showNotification("文件上传出现异常", "文件上传出现异常，请查看后端接口", "error");
    });
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.value.search.isSearch == true) {
      searchName();
    } else {
      //   getPlaybook();
      paging(data.value.allList);
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.value.search.isSearch == true) {
      searchName();
    } else {
      //   getPlaybook();
      paging(data.value.allList);
    }
  });
}

// 字段排序
function sortChange(prop, order) {
  onSortChange(prop, order).then(() => getPlaybook());
}
function onDel(row) {
  ElMessageBox.confirm(`确认删除吗？`)
    .then(() => {
      DeletePlaybook({ data: row }).then(() => {
        getPlaybook();
        ElMessage.success(`删除成功`);
      });
    })
    .catch(() => {});
}
function queryData() {
  if (data.value.search.keyWord === "" || data.value.search.keyWord == null) {
    getPlaybook();
    data.value.search.isSearch = false;
  }
  //搜索
  else {
    searchName();
    data.value.search.isSearch = true;
  }
}
function searchName() {
  let list = [];
  data.value.allList.filter((item) => {
    if (item.name.indexOf(data.value.search.keyWord) !== -1) {
      list.push(item);
    }
  });
  // let lists = data_Filter(list, getParams());
  // data.value.function_list = lists.list;
  // pagination.value.total = lists.total;
  paging(list);
}
function info(row) {
  data.value.dialogTableVisible = true;
  data.value.describe = row;
}
</script>

<template>
  <div class="example">
    <div class="bottom">
      <page-main title="运维功能包管理">
        <template #title>
          <div class="flex items-center">
            <el-icon class="cursor-pointer" @click="goBack()"><ArrowLeftBold /></el-icon>
            <span class="ml-10px">运维功能包管理</span>
          </div>
        </template>
        <div class="flex justify-between w-full">
          <el-button v-auth="['admin', 'publish.task_managment_create']" type="primary" @click="add_function">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            上传
          </el-button>
          <div class="flex">
            <div class="w-220px">
              <el-input
                v-model="data.search.keyWord"
                placeholder="请输入脚本名进行查询"
                clearable
                @keydown.enter="queryData()"
              />
            </div>
            <el-button type="primary" @click="queryData()" class="ml-10px">
              <template #icon>
                <el-icon>
                  <svg-icon name="i-ep:search" />
                </el-icon>
              </template>
              筛选
            </el-button>
          </div>
        </div>

        <el-table
          v-loading="data.loading"
          class="list-table"
          :data="data.function_list"
          border
          stripe
          highlight-current-row
          @sort-change="sortChange"
        >
          <el-table-column prop="name" label="功能名称" width="300px">
            <template #default="scope">
              <span v-if="scope.row.name == ''">待初始化</span>
              <span v-else>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="describe" label="功能描述">
            <template #default="scope">
              <span v-if="scope.row.describe == ''">待初始化</span>
              <span v-else>{{ scope.row.describe }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="category" label="功能类别" width="125px">
            <template #default="scope">
              <span v-if="scope.row.category == ''">待初始化</span>
              <span v-else>{{ scope.row.category }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="type" label="功能类型">
            <template #default="scope">
              <span v-if="scope.row.type == ''">待初始化</span>
              <span v-else>{{ scope.row.type }}</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="version" label="版本号" width="75px">
            <template #default="scope">
              <span v-if="scope.row.version == ''">待初始化</span>
              <span v-else>{{ scope.row.version }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template #default="scope">
              <el-button type="primary" size="small" plain @click="info(scope.row)">详细信息</el-button>
              <el-button
                v-auth="['admin', 'publish.task_managment_remove']"
                type="danger"
                size="small"
                plain
                @click="onDel(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="发布状态" align="center" width="120px" fixed="right">
            <template #default="scope">
              <Auth :value="['admin', 'publish.task_managment_release']">
                <el-button
                  type="danger"
                  size="small"
                  plain
                  v-if="scope.row.status == false"
                  @click="upload_img(scope.row)"
                >
                  未发布
                </el-button>

                <el-button
                  type="success"
                  size="small"
                  plain
                  v-else-if="scope.row.status == true"
                  @click="CanclePublish(scope.row)"
                >
                  已发布
                </el-button>
                <template #no-auth>
                  <p>你没有该权限</p>
                </template>
              </Auth>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog v-model="data.dialogTableVisible" title="功能详情" style="width: 75%">
          <el-descriptions v-model="data.describe" title="功能详细信息" :column="3" border style="width: 100%">
            <el-descriptions-item
              label="文件名："
              label-align="right"
              align="center"
              label-class-name="my-label"
              class-name="my-content"
              width="200px"
            >
              {{ data.describe.playbook_name }}
            </el-descriptions-item>
            <el-descriptions-item label="功能名称：" label-align="right" align="center">
              {{ data.describe.name }}
            </el-descriptions-item>
            <el-descriptions-item label="功能类型：" label-align="right" align="center">
              {{ data.describe.type }}
            </el-descriptions-item>
            <el-descriptions-item label="功能类别：" label-align="right" align="center">
              {{ data.describe.category }}
            </el-descriptions-item>
            <el-descriptions-item label="版本号" label-align="right" align="center">
              {{ data.describe.version }}
            </el-descriptions-item>
            <el-descriptions-item label="发布状态：" label-align="right" align="center">
              <el-tag v-if="data.describe.status == true" size="small" type="success">已发布</el-tag>
              <el-tag v-else size="small" type="danger">未发布</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="文件所处路径：" label-align="right" align="center">
              {{ data.describe.playbook_router }}
            </el-descriptions-item>
          </el-descriptions>
        </el-dialog>
        <el-dialog
          v-model="data.dialogUpload"
          title="上传"
          width="700px"
          :close-on-click-modal="false"
          append-to-body
          destroy-on-close
        >
          <el-form
            ref="formRef"
            :model="data.form"
            :rules="data.rules"
            label-position="left"
            label-width="120px"
            label-suffix="："
          >
            <el-form-item label="导入文件">
              <file-upload
                :multiple="true"
                class="upload-demo"
                :max="10"
                :ext="['zip']"
                :size="5000000"
                :auto-upload="true"
                type="file"
                accept=".zip"
                action=""
                :before-upload="beforeUpload"
                :http-request="chooseFile"
              />
            </el-form-item>
          </el-form>
        </el-dialog>
        <el-pagination
          :current-page="pagination.page"
          :total="pagination.total"
          :page-size="pagination.size"
          :page-sizes="pagination.sizes"
          :layout="pagination.layout"
          :hide-on-single-page="false"
          class="pagination"
          background
          @size-change="sizeChange"
          @current-change="currentChange"
        />
      </page-main>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.example {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .header {
    margin-bottom: 0;
  }

  .bottom {
    flex: 1;
    position: relative;
    overflow: scroll;

    :deep(.el-radio-group .el-radio-button .el-radio-button__inner) {
      display: flex;
      align-items: center;

      .el-icon {
        margin-right: 10px;
      }
    }
  }
}
</style>
