<style lang="scss" scoped>
.action-card {
  cursor: pointer;
  margin-bottom: 10px;

  &:hover {
    border: 1px solid var(--g-theme-color);

    .content .item > .name {
      color: var(--g-theme-color);
    }
  }

  .content {
    display: flex;
    word-wrap: break-word;

    .avatar {
      margin-right: 15px;
    }

    .item {
      padding-right: 10px;

      .name {
        margin-bottom: 12px;
        color: var(--el-text-color-primary);
        transition: var(--el-transition-color);
        font-size: 16px;
        line-height: 24px;
        font-weight: bold;
      }

      .intro {
        color: var(--el-text-color-secondary);
        transition: var(--el-transition-color);
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 10px;
        @include text-overflow(3);
      }
    }
  }
}
</style>

<script lang="ts" setup>
import { GetPublishPlaybook } from "@/api/modules/auto_operation_maintenance/operationMaintenance";
import { useRouter } from "vue-router";
import { groupBy } from "lodash-es";
import { Warning } from "@element-plus/icons-vue";
import useAuth from "@/utils/composables/auth";
import { oprationList } from "@/constants/auth";
const { auth, authAll } = useAuth();
const data = reactive({
  list_loading: false,
  function_list: [] as any,
});
const router = useRouter();

onMounted(() => {
  getPlaybook();
});

function getPlaybook() {
  data.list_loading = true;
  GetPublishPlaybook()
    .then((res: any) => {
      if (res.data.length > 0) {
        data.function_list = groupBy(res.data, "category");
      }
    })
    .finally(() => {
      data.list_loading = false;
    });
}

const Publish = (item: any) => {
  if (auth(["admin", "publish.task_create"])) {
    router.push({ name: "create_task", query: { task_id: item.id } });
  } else {
    ElMessage.warning("暂无权限");
  }
};
</script>

<template>
  <div>
    <page-main>
      <template #title>
        <div class="flex items-center justify-between">
          <div>
            功能列表
            <span class="text-12px text-red">
              <el-icon><Warning /></el-icon>
              请在工具管理页面增加或管理工具
            </span>
          </div>
          <div>
            <el-space size="10">
              <router-link to="/aotumation/publish/runnerlog" v-auth="['admin', 'publish.runnerlog']">
                <el-button>运维工具日志</el-button>
              </router-link>
              <router-link to="/aotumation/publish/task" v-auth="['admin', 'publish.task_list']">
                <el-button>执行记录</el-button>
              </router-link>
              <router-link to="/aotumation/publish/task_managment" v-auth="['admin', 'publish.task_managment']">
                <el-button>工具管理</el-button>
              </router-link>
            </el-space>
          </div>
        </div>
      </template>

      <div v-loading="data.list_loading" class="min-h-40vh">
        <div v-for="(item, index) in data.function_list" :key="index">
          <div class="my-20px flex items-center">
            <el-icon size="18px"><svg-icon name="configurationManagement" /></el-icon>
            <span class="text-18px font-bold ml-10px">{{ index }}</span>
          </div>
          <div class="grid grid-cols-5 gap-10px shrink-0">
            <el-card shadow="hover" v-for="childrenItem in item" class="action-card" @click="Publish(childrenItem)">
              <div class="flex flex-row content">
                <div class="w-50px mr-20px">
                  <el-avatar :size="60" shape="square" fit="fill" :src="childrenItem.picture"></el-avatar>
                </div>
                <div class="item">
                  <div class="name">
                    {{ childrenItem.name }}
                  </div>
                  <div class="intro">
                    {{ childrenItem.type }}
                  </div>
                  <div class="describe">
                    {{ childrenItem.describe }}
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </page-main>
  </div>
</template>
