import Mock from 'mockjs'

const AllList = []
for (let i = 0; i < 50; i++) {
  AllList.push(Mock.mock({
    id: '@id',
    title: '@ctitle(10, 20)'
  }))
}

export default [
  {
    url: '/mock/business_monitor/list',
    method: 'get',
    response: {
      error: '',
      status: 1,
      data: {
        "virtualized_resources": {
          "cpu_utilization": "50%",
          "memory_utilization": "60%",
          "disk_utilization": "40%",
          "associated_server": "80%"
        },
        "business_system": {
          "HIS系统": [
            {
              "ip": "************",
              "server_name": "HIS-DG服务器",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": ***********.0,
                  "remaining_quantity": ***********.0
                },
                "cpu_utilization": [
                  2.719656999999998,
                  2.760346999999996,
                  2.754355000000004,
                  2.6654139999999984,
                  2.9310959999999966,
                  2.744320000000002,
                  2.7823379999999958,
                  2.944834,
                  2.655699999999996,
                  2.855440999999999,
                  2.803621000000007,
                  2.8275000000000006,
                  2.8249660000000034,
                  2.7957059999999956,
                  2.698404999999994
                ],
                "memory_utilization": [
                  34.11893499999999,
                  34.121312,
                  34.13767300000001,
                  34.127354,
                  34.128677999999994,
                  34.121765999999994,
                  34.124866,
                  34.142404,
                  34.135075,
                  34.129965,
                  34.122636,
                  34.130736999999996,
                  34.123298000000005,
                  34.13032,
                  34.130883999999995
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            },
            {
              "ip": "**********",
              "server_name": "HIS数据库服务器",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 182445936640.0,
                  "remaining_quantity": 146826727424.0
                },
                "cpu_utilization": [
                  3.284854999999993,
                  3.050278000000006,
                  2.8621399999999966,
                  3.8394799999999947,
                  3.025272000000001,
                  2.8997679999999946,
                  3.341616000000002,
                  3.088954000000001,
                  3.6142289999999946,
                  4.017056999999994,
                  3.4508340000000004,
                  3.8791439999999966,
                  4.106425999999999,
                  3.901266000000007,
                  3.486198999999999
                ],
                "memory_utilization": [
                  74.003856,
                  73.99640600000001,
                  73.914548,
                  73.93671499999999,
                  73.996784,
                  73.970135,
                  73.91248300000001,
                  73.996878,
                  73.9886,
                  73.972038,
                  73.944265,
                  73.90567,
                  73.95334700000001,
                  73.919023,
                  73.94757200000001
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            }
          ],
          "挂号系统": [
            {
              "ip": "***********",
              "server_name": "专业版挂号系统数据库服务器",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 251131858944.0,
                  "remaining_quantity": 226185146368.0
                },
                "cpu_utilization": [
                  2.6817489999999964,
                  3.0413659999999965,
                  3.208494999999999,
                  2.050464000000005,
                  2.4716530000000034,
                  1.8893350000000027,
                  2.2072019999999952,
                  2.5320269999999994,
                  2.612757000000002,
                  1.669347000000002,
                  2.4876990000000063,
                  2.260221999999999,
                  2.243351000000004,
                  2.4700600000000037,
                  2.1098640000000017
                ],
                "memory_utilization": [
                  73.110079,
                  73.141336,
                  73.00000299999999,
                  72.884331,
                  72.832458,
                  72.85319,
                  72.855064,
                  72.90139500000001,
                  72.88588,
                  72.857305,
                  72.889302,
                  72.892352,
                  72.884998,
                  72.908742,
                  72.79388399999999
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            },
            {
              "ip": "************",
              "server_name": "专业版挂号系统应用服务器",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 101334290432.0
                },
                "cpu_utilization": [
                  42.492326,
                  65.499634,
                  45.580861,
                  24.624617999999998,
                  35.474575,
                  27.317133999999996,
                  21.643161000000006,
                  34.37878499999999,
                  33.14388099999999,
                  22.628591,
                  40.593728,
                  24.677927999999994,
                  38.944115,
                  37.474674,
                  26.710983
                ],
                "memory_utilization": [
                  58.686725,
                  54.435662,
                  67.401611,
                  47.383251,
                  54.071581,
                  55.304488,
                  52.897828,
                  55.872285,
                  60.159743,
                  44.360946,
                  57.641039,
                  51.284245,
                  50.119683,
                  55.591852,
                  49.677569
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            },
            {
              "ip": "************",
              "server_name": "专业版挂号系统应用2",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 92784005120.0
                },
                "cpu_utilization": [
                  38.469731,
                  51.4399,
                  47.965555,
                  34.307576999999995,
                  35.270911,
                  22.635523000000006,
                  46.238042,
                  46.946788,
                  41.911029,
                  16.632238,
                  42.544134,
                  41.20056,
                  18.228329000000002,
                  34.957927,
                  22.659402
                ],
                "memory_utilization": [
                  36.121688,
                  56.162208,
                  54.00915,
                  44.193799,
                  38.354618,
                  37.772635,
                  46.932144,
                  36.647798,
                  40.170451,
                  53.403541,
                  55.789233,
                  45.191139,
                  34.938221,
                  38.075502,
                  42.960172
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            }
          ],
          "专业版药房药库": [
            {
              "ip": "************",
              "server_name": "专业版药房管理系统1",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 100502429696.0
                },
                "cpu_utilization": [
                  4.2036449999999945,
                  3.7105159999999984,
                  4.0005840000000035,
                  3.8807939999999945,
                  4.1050460000000015,
                  3.810317999999995,
                  3.7782320000000027,
                  4.044179999999997,
                  3.766895000000005,
                  3.8524110000000036,
                  3.8130199999999945,
                  4.393713000000005,
                  3.5350820000000027,
                  3.8036010000000005,
                  3.4387609999999995
                ],
                "memory_utilization": [
                  29.256699999999995,
                  28.808052000000004,
                  28.965609,
                  29.341167999999996,
                  31.118446000000006,
                  31.580883,
                  31.424864999999997,
                  31.159437999999994,
                  31.066473000000002,
                  30.845962999999998,
                  29.924220000000005,
                  29.768823999999995,
                  29.743607999999995,
                  28.580062999999996,
                  28.422531000000006
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            },
            {
              "ip": "************",
              "server_name": "专业版药库管理系统2",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 101614927872.0
                },
                "cpu_utilization": [
                  3.9366799999999955,
                  3.758330000000001,
                  3.943691000000001,
                  4.057742000000005,
                  4.426974999999999,
                  4.036866000000003,
                  4.125285000000005,
                  3.963784000000004,
                  4.101130999999995,
                  4.385543999999996,
                  3.9380919999999975,
                  4.013852,
                  3.916825000000003,
                  4.051404000000005,
                  3.815665999999993
                ],
                "memory_utilization": [
                  23.223983000000004,
                  23.417315000000002,
                  23.7967,
                  23.587716,
                  22.811257999999995,
                  22.761398,
                  22.812748999999997,
                  23.110622000000006,
                  23.202071000000004,
                  23.048563,
                  23.595417999999995,
                  23.873889000000005,
                  22.935799000000003,
                  23.565978,
                  24.680978999999994
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            }
          ],
          "一体化护理": [
            {
              "ip": "************",
              "server_name": "一体化护理数据库服务器",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 251131858944.0,
                  "remaining_quantity": 227332190208.0
                },
                "cpu_utilization": [
                  0.9362619999999993,
                  0.8755309999999952,
                  0.8647710000000046,
                  0.9030630000000031,
                  0.9377209999999963,
                  0.883334000000005,
                  0.9154879999999963,
                  0.9465229999999991,
                  0.8830760000000026,
                  0.867369999999994,
                  0.9115610000000061,
                  0.8699730000000017,
                  0.8979839999999939,
                  0.8879100000000051,
                  0.8845830000000063
                ],
                "memory_utilization": [
                  70.699558,
                  70.69736,
                  70.64819299999999,
                  70.660096,
                  70.647159,
                  70.64989,
                  70.678514,
                  70.682475,
                  70.671491,
                  70.730013,
                  70.725849,
                  70.678783,
                  70.726265,
                  70.724827,
                  70.72544500000001
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            },
            {
              "ip": "************",
              "server_name": "一体化护理2",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 101697527808.0
                },
                "cpu_utilization": [
                  3.605126999999996,
                  3.558762999999999,
                  3.547593000000006,
                  3.566438000000005,
                  3.5889310000000023,
                  3.531203000000005,
                  3.5443829999999963,
                  3.5625639999999947,
                  3.683770999999993,
                  3.7236369999999965,
                  4.4292059999999935,
                  3.929511000000005,
                  3.951266000000004,
                  3.798497999999995,
                  3.8584340000000026
                ],
                "memory_utilization": [
                  24.076561999999996,
                  24.081754000000004,
                  24.089207000000002,
                  24.287308999999993,
                  24.288078999999996,
                  24.269794000000005,
                  24.298141,
                  24.309146,
                  24.157303,
                  24.153999,
                  24.090648,
                  24.032687999999993,
                  24.029929999999993,
                  24.041632000000007,
                  23.987472999999994
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            },
            {
              "ip": "************",
              "server_name": "一体化护理1",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 101698392064.0
                },
                "cpu_utilization": [
                  3.2339450000000056,
                  3.194328999999996,
                  3.258150999999998,
                  3.3543200000000013,
                  3.3151940000000053,
                  3.3357660000000067,
                  3.5259279999999933,
                  3.206304000000003,
                  3.2598349999999954,
                  3.2439820000000026,
                  3.2878829999999937,
                  3.246116999999998,
                  3.2210989999999953,
                  3.1925760000000025,
                  3.223855999999998
                ],
                "memory_utilization": [
                  22.678320999999997,
                  22.674321000000006,
                  22.651763000000003,
                  22.646248,
                  22.640260999999995,
                  22.618622000000002,
                  22.624063000000007,
                  22.671910999999994,
                  22.643017999999998,
                  22.628634000000005,
                  22.600336999999996,
                  22.590872000000005,
                  22.594150999999997,
                  22.592411999999996,
                  22.589133000000004
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            }
          ],
          "实验室信息管理系统": [
            {
              "ip": "************",
              "server_name": "专业版临生免系统1",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 101716819968.0
                },
                "cpu_utilization": [
                  3.9476980000000026,
                  4.262152,
                  4.341751000000002,
                  4.174660000000003,
                  5.143286000000003,
                  4.081888000000006,
                  4.026929999999993,
                  4.261617999999999,
                  4.642432999999997,
                  6.0714729999999975,
                  5.968997000000002,
                  4.813547999999997,
                  5.107191,
                  4.689813000000001,
                  5.201492999999999
                ],
                "memory_utilization": [
                  21.944097,
                  22.119318000000007,
                  21.685351999999995,
                  21.64277,
                  21.666471,
                  21.627540999999994,
                  21.656384000000003,
                  21.901539999999997,
                  21.770489999999995,
                  21.675687999999994,
                  21.66498,
                  21.692035000000004,
                  21.712506000000005,
                  21.763981,
                  21.659366000000006
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            },
            {
              "ip": "************",
              "server_name": "专业版临生免系统2",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 101666426880.0
                },
                "cpu_utilization": [
                  20.033518,
                  21.627046000000007,
                  21.465298000000004,
                  21.390297000000004,
                  20.560727999999997,
                  21.483155999999994,
                  21.339123,
                  21.443835000000007,
                  21.153966999999994,
                  19.018534000000002,
                  21.370697000000007,
                  21.454454,
                  21.286371000000003,
                  21.448783000000006,
                  21.479753000000002
                ],
                "memory_utilization": [
                  20.655043000000006,
                  20.644013,
                  20.727288,
                  20.721474999999998,
                  20.63964,
                  31.74485,
                  31.747805999999997,
                  31.749893,
                  31.749222000000003,
                  31.751383000000004,
                  20.746914000000004,
                  20.703190000000006,
                  20.833990999999997,
                  20.623368,
                  20.574004000000002
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            }
          ],
          "专业版病历": [
            {
              "ip": "**********",
              "server_name": "专业版电子病历系统服务器",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 251131858944.0,
                  "remaining_quantity": 227146305536.0
                },
                "cpu_utilization": [
                  1.047073999999995,
                  1.0966999999999985,
                  1.0538640000000044,
                  1.0280699999999996,
                  1.1183319999999952,
                  1.1076369999999969,
                  1.140336000000005,
                  1.1342889999999954,
                  1.062179999999998,
                  1.0873809999999935,
                  1.1566009999999949,
                  1.1001880000000028,
                  1.204938999999996,
                  1.073798999999994,
                  1.1048310000000043
                ],
                "memory_utilization": [
                  76.427578,
                  76.434222,
                  76.500489,
                  76.507683,
                  76.443247,
                  76.462338,
                  76.47055399999999,
                  76.456778,
                  75.85932,
                  76.484569,
                  76.440828,
                  76.492456,
                  76.497158,
                  76.455284,
                  76.503967
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            }
          ],
          "临床信息系统": [
            {
              "ip": "************",
              "server_name": "专业版住院医生站1",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 101438046208.0
                },
                "cpu_utilization": [
                  3.233024999999998,
                  3.1548899999999946,
                  3.213228000000001,
                  3.449933999999999,
                  3.250963999999996,
                  3.244100000000003,
                  3.1705690000000004,
                  3.2235199999999935,
                  3.2457919999999945,
                  3.209937999999994,
                  3.237215000000006,
                  3.1844870000000043,
                  3.238494000000003,
                  3.285347999999999,
                  3.2397050000000007
                ],
                "memory_utilization": [
                  23.570203000000006,
                  23.564688000000004,
                  23.568016999999998,
                  23.5668,
                  23.573358,
                  23.551049000000006,
                  23.543297999999993,
                  23.561260000000004,
                  23.56357,
                  23.540913000000003,
                  23.540068000000005,
                  23.533508999999995,
                  23.531645999999995,
                  23.534801,
                  23.550303999999997
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            },
            {
              "ip": "************",
              "server_name": "专业版住院医生站2",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 101403209728.0
                },
                "cpu_utilization": [
                  3.4413550000000015,
                  3.385694000000001,
                  3.4265570000000025,
                  3.4368790000000047,
                  3.2970470000000063,
                  3.3377810000000068,
                  3.395793999999995,
                  3.331873999999999,
                  3.291092000000006,
                  3.274889999999999,
                  3.3092769999999945,
                  3.5487830000000002,
                  3.603980000000007,
                  3.3915250000000015,
                  3.450513000000001
                ],
                "memory_utilization": [
                  26.449237999999994,
                  26.373565,
                  26.397936,
                  26.391352999999995,
                  26.385712999999996,
                  26.383501999999993,
                  26.396247000000002,
                  26.393489000000002,
                  26.383204000000006,
                  26.350982000000002,
                  26.348150000000004,
                  26.340323999999995,
                  26.343306,
                  26.3403,
                  26.415998000000002
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            },
            {
              "ip": "************",
              "server_name": "一张纸门诊医生站1",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 110540361728.0,
                  "remaining_quantity": 101623619584.0
                },
                "cpu_utilization": [
                  7.915777000000006,
                  7.994247000000001,
                  6.369984000000002,
                  7.657223999999999,
                  9.726101999999997,
                  8.122262000000006,
                  7.154031000000003,
                  6.873254000000003,
                  7.451977999999997,
                  7.999332999999993,
                  8.308840000000004,
                  7.405552,
                  8.607123000000001,
                  8.187841000000006,
                  7.936076
                ],
                "memory_utilization": [
                  40.057105,
                  39.997431,
                  40.201918,
                  40.115065,
                  40.080036,
                  39.990748,
                  40.13725,
                  40.115438,
                  40.074595,
                  39.960663,
                  40.193024,
                  40.116059,
                  40.019939,
                  39.955098,
                  40.114692
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            }
          ],
          "临床药学系统": [
            {
              "ip": "************",
              "server_name": "合理用药系统数据库服务器",
              "system_type": "Linux",
              "basic_service": {
                "total_storage_space": {
                  "total": 124492189696.0,
                  "remaining_quantity": 97918386176.0
                },
                "cpu_utilization": [
                  1.973117000000002,
                  2.0666920000000033,
                  1.8671049999999951,
                  1.9989850000000047,
                  1.869011999999998,
                  1.8768769999999932,
                  2.1103200000000015,
                  1.8847200000000015,
                  2.000165999999993,
                  1.954359999999994,
                  1.8333509999999933,
                  2.020190999999997,
                  2.1195649999999944,
                  1.9169990000000041,
                  1.931877
                ],
                "memory_utilization": [
                  54.767263,
                  54.762758,
                  54.762598,
                  54.805977,
                  54.767177,
                  54.759284,
                  54.777684,
                  54.779059,
                  54.813734,
                  54.774787,
                  54.764157,
                  54.773154,
                  54.775327,
                  54.797139,
                  54.759296
                ],
                "number_of_connection": 40
              },
              "warn_list": []
            }
          ]
        }
      }

    }
  },
  {
    url: '/mock/business_monitor/warning_list',
    method: 'get',
    response: {
      error: '',
      status: 1,
      data: {
        warning_list: [{
          level: '警告', type: '软件告警', time: '2018-04-15', message: '门诊电子病历提供的会诊过程服务出现异常，请及时查看！', history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        }, {
          level: '警告', type: '软件告警', time: '2018-04-13', message: '集成平台-主数据 提供的 mdm-实时-用户信息表服务出现异常，请及时查看！', history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        }, {
          level: '警告', type: '软件告警', time: '2018-04-11', message: 'empi提供的个人基本信息注册 服务出现异常，请及时查看！', history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        }, {
          level: '灾难', type: '硬件告警', time: '2018-04-11', message: '虚拟盘的空间不足，请及时查看！', history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        },]
      }
    }
  },
  {
    url: '/mock/business_monitor/log_list',
    method: 'get',
    response: {
      error: '',
      status: 1,
      data: [{ name: '数据中心', time: '2018/4/12', message: '共产生2058条数据，任务执行两次，失败1次' }, { name: '数据中心', time: '2018/4/12', message: '共产生2058条数据，任务执行两次，失败1次' }, { name: '数据中心', time: '2018/4/12', message: '共产生2058条数据，任务执行两次，失败1次' }, { name: '数据中心', time: '2018/4/12', message: '共产生2058条数据，任务执行两次，失败1次' }]
    }
  },
  {
    url: '/mock/business_monitor/topology',
    method: 'get',
    response: {
      error: '',
      status: 1,
      data: {
        business_list: [
          { name: 'HIS', relative: ['医保', 'cost'], service: '8', throughput: '<0.1 tpm', health_level: '70%', status: true, problem_level: 3, time_consuming: '20ms', type: 'subscriber' },
          { name: 'EMR', relative: ['平台'], service: '2', throughput: '<0.1 tpm', health_level: '30%', status: false, problem_level: 25, time_consuming: '7004ms', type: 'subscriber' },
          { name: '统一门户', relative: ['电子病历'], service: '23', throughput: '<0.1 tpm', health_level: '69%', status: true, problem_level: 0, time_consuming: '1ms', type: 'subscriber' },
          { name: '住院挂号', relative: ['医保'], service: '12', throughput: '<0.1 tpm', health_level: '90%', status: true, problem_level: 0, time_consuming: '40ms', type: 'subscriber' },
          { name: '科研平台', relative: ['医保'], service: '12', throughput: '<0.1 tpm', health_level: '96%', status: true, problem_level: 2, time_consuming: '30ms', type: 'subscriber' },
          { name: '门诊挂号', relative: ['cost'], service: '8', throughput: '<0.1 tpm', health_level: '91%', status: true, problem_level: 0, time_consuming: '12ms', type: 'subscriber' },
          { name: '护士站', relative: ['大数据'], service: '2', throughput: '<0.1 tpm', health_level: '72%', status: true, problem_level: 3, time_consuming: '32ms', type: 'subscriber' },
          { name: '病历系统', relative: ['门诊'], service: '8', throughput: '<0.1 tpm', health_level: '23%', status: false, problem_level: 32, time_consuming: '4002ms', type: 'subscriber' },
          { name: '大数据', relative: ['统一门户'], service: '5', throughput: '<0.1 tpm', health_level: '88%', problem_level: 1, status: true, time_consuming: '92ms', type: 'provider' },
          { name: '电子病历', relative: ['HIS', '科研平台'], service: '23', throughput: '<0.1 tpm', health_level: '85%', problem_level: 0, status: true, time_consuming: '88ms', type: 'provider' },
          { name: '医保', relative: ['病历系统'], service: '16', throughput: '<0.1 tpm', health_level: '79%', problem_level: 5, status: true, time_consuming: '9ms', type: 'provider' },
          { name: 'cost', relative: ['门诊挂号', '病历系统', '护士站'], service: '2', throughput: '<0.1 tpm', health_level: '34%', problem_level: 50, status: false, time_consuming: '1000ms', type: 'provider' },
          { name: '平台', relative: ['EMR'], service: '4', throughput: '<0.1 tpm', health_level: '82%', problem_level: 0, status: true, time_consuming: '20ms', type: 'provider' },
          { name: '门诊', relative: ['门诊挂号', '住院挂号'], service: '1', throughput: '<0.1 tpm', health_level: '89%', problem_level: 0, status: true, time_consuming: '11ms', type: 'provider' },
          { name: '合理用药', relative: ['护士站'], service: '1', throughput: '<0.1 tpm', health_level: '88%', problem_level: 2, status: true, time_consuming: '87ms', type: 'provider' },
          { name: '住院', relative: [], service: '1', throughput: '<0.1 tpm', health_level: '82%', problem_level: 0, status: true, time_consuming: '64ms', type: 'provider' }],
        center: { name: '集成中心', service: '20', problem: '20', time: '540ms', problem_level: 30 }
      }
    }
  },
  {
    url: '/mock/business_monitor/information',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        service_call: [
          { month: '00:00', city: '医保服务', temperature: 7 },
          { month: '00:00', city: '收费服务', temperature: 3.9 },
          { month: '02:00', city: '医保服务', temperature: 6.9 },
          { month: '02:00', city: '收费服务', temperature: 4.2 },
          { month: '04:00', city: '医保服务', temperature: 9.5 },
          { month: '04:00', city: '收费服务', temperature: 5.7 },
          { month: '06:00', city: '医保服务', temperature: 14.5 },
          { month: '06:00', city: '收费服务', temperature: 8.5 },
          { month: '08:00', city: '医保服务', temperature: 18.4 },
          { month: '08:00', city: '收费服务', temperature: 11.9 },
          { month: '10:00', city: '医保服务', temperature: 21.5 },
          { month: '10:00', city: '收费服务', temperature: 15.2 },
          { month: '12:00', city: '医保服务', temperature: 25.2 },
          { month: '12:00', city: '收费服务', temperature: 17 },
          { month: '14:00', city: '医保服务', temperature: 26.5 },
          { month: '14:00', city: '收费服务', temperature: 16.6 },
          { month: '16:00', city: '医保服务', temperature: 23.3 },
          { month: '16:00', city: '收费服务', temperature: 14.2 },
          { month: '18:00', city: '医保服务', temperature: 18.3 },
          { month: '18:00', city: '收费服务', temperature: 10.3 },
          { month: '20:00', city: '医保服务', temperature: 13.9 },
          { month: '20:00', city: '收费服务', temperature: 6.6 },
          { month: '22:00', city: '医保服务', temperature: 9.6 },
          { month: '22:00', city: '收费服务', temperature: 4.8 },
        ],
        time_consumption: [
          { month: '00:00', city: '医保服务', temperature: 6 },
          { month: '00:00', city: '收费服务', temperature: 1.9 },
          { month: '02:00', city: '医保服务', temperature: 10.9 },
          { month: '02:00', city: '收费服务', temperature: 22.2 },
          { month: '04:00', city: '医保服务', temperature: 33.5 },
          { month: '04:00', city: '收费服务', temperature: 11.7 },
          { month: '06:00', city: '医保服务', temperature: 13.5 },
          { month: '06:00', city: '收费服务', temperature: 19.5 },
          { month: '08:00', city: '医保服务', temperature: 18.4 },
          { month: '08:00', city: '收费服务', temperature: 1.9 },
          { month: '10:00', city: '医保服务', temperature: 20.5 },
          { month: '10:00', city: '收费服务', temperature: 15.2 },
          { month: '12:00', city: '医保服务', temperature: 25.2 },
          { month: '12:00', city: '收费服务', temperature: 18 },
          { month: '14:00', city: '医保服务', temperature: 12.5 },
          { month: '14:00', city: '收费服务', temperature: 16.6 },
          { month: '16:00', city: '医保服务', temperature: 16.3 },
          { month: '16:00', city: '收费服务', temperature: 13.2 },
          { month: '18:00', city: '医保服务', temperature: 13.3 },
          { month: '18:00', city: '收费服务', temperature: 14.3 },
          { month: '20:00', city: '医保服务', temperature: 15.9 },
          { month: '20:00', city: '收费服务', temperature: 6.6 },
          { month: '22:00', city: '医保服务', temperature: 9.6 },
          { month: '22:00', city: '收费服务', temperature: 4.8 },
        ],
        call_top: [
          { category: '高耗时调用', value: 38 },
          { category: '高频次调用', value: 52 },
        ],
        fail: [
          { month: '00:00', city: '医保服务', temperature: 8 },
          { month: '00:00', city: '收费服务', temperature: 2.9 },
          { month: '02:00', city: '医保服务', temperature: 1.9 },
          { month: '02:00', city: '收费服务', temperature: 3.2 },
          { month: '04:00', city: '医保服务', temperature: 4.5 },
          { month: '04:00', city: '收费服务', temperature: 8.7 },
          { month: '06:00', city: '医保服务', temperature: 4.5 },
          { month: '06:00', city: '收费服务', temperature: 8.5 },
          { month: '08:00', city: '医保服务', temperature: 8.4 },
          { month: '08:00', city: '收费服务', temperature: 1.9 },
          { month: '10:00', city: '医保服务', temperature: 2.5 },
          { month: '10:00', city: '收费服务', temperature: 5.2 },
          { month: '12:00', city: '医保服务', temperature: 5.2 },
          { month: '12:00', city: '收费服务', temperature: 7 },
          { month: '14:00', city: '医保服务', temperature: 6.5 },
          { month: '14:00', city: '收费服务', temperature: 6.6 },
          { month: '16:00', city: '医保服务', temperature: 3.3 },
          { month: '16:00', city: '收费服务', temperature: 4.2 },
          { month: '18:00', city: '医保服务', temperature: 8.3 },
          { month: '18:00', city: '收费服务', temperature: 1.3 },
          { month: '20:00', city: '医保服务', temperature: 3.9 },
          { month: '20:00', city: '收费服务', temperature: 6.6 },
          { month: '22:00', city: '医保服务', temperature: 9.6 },
          { month: '22:00', city: '收费服务', temperature: 4.8 },
        ],
        event_list: [
          { name: '医保服务', load: '2.1ms', throughput: '9.5tpm', fail: '5%' },
          { name: '收费服务', load: '4.3ms', throughput: '7.4tpm', fail: '3%' },
          // { name: 'GET SysParameter/QuerySysParameterListById', load: '2.3ms', throughput: '8.6tpm', fail: '4%' },
          // { name: 'POST CommonInvoke/EmrDecompressString', load: '4.4ms', throughput: '9.3tpm', fail: '6%' },
          // { name: 'GET User/CheckToken', load: '1.3ms', throughput: '9.2tpm', fail: '2%' }
        ],
        fail_list: [
          { type: '404', name: 'Page Not Found', last_time: '2022-01-15 10:30:00', times: 3 },
          { type: '500', name: 'Internal Server Error', last_time: '2022-01-16 15:45:00', times: 5 },
          { type: '403', name: 'Forbidden', last_time: '2022-01-14 08:20:00', times: 2 },
          { type: '401', name: 'Unauthorized', last_time: '2022-01-17 12:00:00', times: 4 },
          { type: '400', name: 'Bad Request', last_time: '2022-01-13 16:10:00', times: 1 }
        ],
        example: [{ name: '护士站', load: '2.1ms', throughput: '9.5tpm', fail: '5%', cpu: '0.3%', memory: '78%' },
        { name: 'nginx10', load: '4.3ms', throughput: '7.4tpm', fail: '3%', cpu: '0.3%', memory: '78%' }],
        warning_list: [{
          name: '电子病历',
          date: '2022-01-01 10:00:00',
          type: '硬件告警',
          level: '灾难',
          content: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          service_name: '电子病历管理系统',
          call_system: '病历质控系统',
          subscribe_system: 'WJ1001',
          time_consuming: '954ms',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        },
        {
          name: '电子病历',
          date: '2022-01-01 11:00:00',
          type: '软件告警',
          level: '警告',
          content: '虚拟机内存占用率超出阙值！',
          service_name: '电子病历管理系统',
          call_system: '病历质控系统',
          subscribe_system: 'WJ1001',
          time_consuming: '954ms',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        }, {
          name: '电子病历',
          date: '2022-01-01 10:00:00',
          type: '软件告警',
          level: '警告',
          content: '存储使用容量超过阙值！',
          service_name: '电子病历管理系统',
          call_system: '病历质控系统',
          subscribe_system: 'WJ1001',
          time_consuming: '954ms',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        }, {
          name: '电子病历',
          date: '2022-01-01 10:00:00',
          type: '硬件告警',
          level: '灾难',
          content: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          service_name: '电子病历管理系统',
          call_system: '病历质控系统',
          subscribe_system: 'WJ1001',
          time_consuming: '954ms',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        },
        {
          name: '电子病历',
          date: '2022-01-01 11:00:00',
          type: '软件告警',
          level: '警告',
          content: '虚拟机内存占用率超出阙值！',
          service_name: '电子病历管理系统',
          call_system: '病历质控系统',
          subscribe_system: 'WJ1001',
          time_consuming: '954ms',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        }, {
          name: '电子病历',
          date: '2022-01-01 10:00:00',
          type: '软件告警',
          level: '警告',
          content: '存储使用容量超过阙值！',
          service_name: '电子病历管理系统',
          call_system: '病历质控系统',
          subscribe_system: 'WJ1001',
          time_consuming: '954ms',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        }, {
          name: '电子病历',
          date: '2022-01-01 10:00:00',
          type: '硬件告警',
          level: '灾难',
          content: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          service_name: '电子病历管理系统',
          call_system: '病历质控系统',
          subscribe_system: 'WJ1001',
          time_consuming: '954ms',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        },
        {
          name: '电子病历',
          date: '2022-01-01 11:00:00',
          type: '软件告警',
          level: '警告',
          content: '虚拟机内存占用率超出阙值！',
          service_name: '电子病历管理系统',
          call_system: '病历质控系统',
          subscribe_system: 'WJ1001',
          time_consuming: '954ms',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        }, {
          name: '电子病历',
          date: '2022-01-01 10:00:00',
          type: '软件告警',
          level: '警告',
          content: '存储使用容量超过阙值！',
          service_name: '电子病历管理系统',
          call_system: '病历质控系统',
          subscribe_system: 'WJ1001',
          time_consuming: '954ms',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          history_list: [
            {
              "eventid": "10170",
              "r_eventid": 0,
              "startime": "2023-11-27 02:18:07",
              "endtime": "",
              "duration": [
                "0天",
                "1小时",
                "14分钟",
                "8秒"
              ],
              "status": "问题",
              "acknowledged": "不",
              "tags": [
                {
                  "tag": "Application",
                  "value": "Status"
                }
              ]
            }
          ]
        },],
        log_list: [
          {
            date: '2022-01-01 10:00:00',
            name: '集成平台',
            status: 'fail',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          {
            date: '2022-01-02 12:00:00',
            name: '电子病历',
            status: 'warning',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          // 添加更多数据...
          {
            date: '2022-01-01 13:00:00',
            name: '病历质控',
            status: 'warning',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          {
            date: '2022-01-01 15:00:00',
            name: '电子病历',
            status: 'fail',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          {
            date: '2022-01-01 18:00:00',
            name: '电子病历',
            status: 'fail',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          {
            date: '2022-01-01 19:00:00',
            name: '电子病历',
            status: 'fail',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          }, {
            date: '2022-01-01 10:00:00',
            name: '集成平台',
            status: 'fail',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          {
            date: '2022-01-02 12:00:00',
            name: '电子病历',
            status: 'warning',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          // 添加更多数据...
          {
            date: '2022-01-01 13:00:00',
            name: '病历质控',
            status: 'warning',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          {
            date: '2022-01-01 15:00:00',
            name: '电子病历',
            status: 'fail',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          {
            date: '2022-01-01 18:00:00',
            name: '电子病历',
            status: 'fail',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
          {
            date: '2022-01-01 19:00:00',
            name: '电子病历',
            status: 'fail',
            log: '电子病历管理系统调用集成平台的病人信息查询服务耗时627ms',
          },
        ],
        server_list: [
          {
            name: '服务器1',
            ip: '***********',
            type: 'server',
            status: true,
          },
          {
            name: '服务器2',
            ip: '***********',
            type: 'server',
            status: false,
          },
          {
            name: '服务器3',
            ip: '***********',
            type: 'server',
            status: true,
          },
          {
            name: '服务器4-中间件',
            ip: '***********',
            type: 'middleware',
            status: true,
          },
          {
            name: '服务器5',
            ip: '***********',
            type: 'database',
            status: true,
          },
          {
            name: '服务器6',
            ip: '***********',
            type: 'database',
            status: false,
          },
        ],
        config: {
          名称: 'xxxx业务',
          配置信息: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
          业务部署位置: '/usr/local/program',
          数据库: 'pgsql'
        }
      }
    }
  },
  {
    url: '/mock/business_monitor/analysis_list',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        relationship_list: [{
          name: '集成平台服务器',
          ip: '***********40',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！'
        }, {
          name: '集成平台服务器',
          ip: '***********40',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！'
        }, {
          name: '集成平台服务器',
          ip: '***********40',
          message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
          advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！'
        }],
        topology: [{
          name: 'HIS',
          status: 'success',
          type: 'font',
          ip: '***********',
          relative: ['监控服务器'],
          describe: '未发现异常'
        }, {
          name: '护士站',
          status: 'success',
          type: 'after',
          ip: '***********',
          relative: ['护士站数据库'],
          describe: '未发现异常'
        }, {
          name: '挂号',
          status: 'warning',
          type: 'after',
          ip: '***********',
          relative: ['挂号数据库'],
          describe: '内存消耗高'
        }, {
          name: '电子病历',
          status: 'success',
          type: 'after',
          ip: '***********',
          relative: ['电子病历数据库'],
          describe: '未发现异常'
        },
        {
          name: '挂号数据库',
          status: 'danger',
          type: 'database',
          ip: '***********',
          relative: [],
          describe: '挂号数据库磁盘空间不足'
        },
        {
          name: '电子病历数据库',
          status: 'success',
          type: 'database',
          ip: '***********',
          relative: [],
          describe: '未发现异常'
        },
        {
          name: '护士站数据库',
          status: 'success',
          type: 'database',
          ip: '***********',
          relative: [],
          describe: '未发现异常'
        },
        {
          name: '监控服务器',
          status: 'success',
          type: 'middleware',
          ip: '***************',
          relative: ['护士站', '挂号', '电子病历'],
          describe: '未发现异常'
        },
        ]
      }
    }
  },
  {
    url: '/mock/business_monitor/vmware',
    method: 'post',
    response: {
      data: {
        name: '集成平台服务器2',
        status: '告警',
        ip: '***********',
        nodes: '集成平台集群',
        type: 'storeDrive',
        vm_system: 'oss',
        cpu_cores: '4',
        system: 'centos7',
        version: 'v1.0',
        describe: '虚拟化服务器',
        cpu: [
          {
            "Date": "2010-01",
            "value": 20
          },
          {
            "Date": "2010-02",
            "value": 30
          },
          {
            "Date": "2010-03",
            "value": 40
          },
          {
            "Date": "2010-04",
            "value": 20
          },
          {
            "Date": "2010-05",
            "value": 60
          },
          {
            "Date": "2010-06",
            "value": 80
          },
          {
            "Date": "2010-07",
            "value": 10
          },
          {
            "Date": "2010-08",
            "value": 22
          },
          {
            "Date": "2010-09",
            "value": 33
          },
          {
            "Date": "2010-10",
            "value": 20
          },
          {
            "Date": "2010-11",
            "value": 10
          },
          {
            "Date": "2010-12",
            "value": 10
          },
          {
            "Date": "2011-01",
            "value": 90
          },
          {
            "Date": "2011-02",
            "value": 20
          },
          {
            "Date": "2011-03",
            "value": 55
          },
          {
            "Date": "2011-04",
            "value": 66
          },
          {
            "Date": "2011-05",
            "value": 77
          },
          {
            "Date": "2011-06",
            "value": 88
          },
          {
            "Date": "2011-07",
            "value": 12
          },
          {
            "Date": "2011-08",
            "value": 33
          }
        ],
        memory: [
          {
            "Date": "2010-01",
            "value": 20
          },
          {
            "Date": "2010-02",
            "value": 30
          },
          {
            "Date": "2010-03",
            "value": 40
          },
          {
            "Date": "2010-04",
            "value": 20
          },
          {
            "Date": "2010-05",
            "value": 60
          },
          {
            "Date": "2010-06",
            "value": 80
          },
          {
            "Date": "2010-07",
            "value": 10
          },
          {
            "Date": "2010-08",
            "value": 22
          },
          {
            "Date": "2010-09",
            "value": 33
          },
          {
            "Date": "2010-10",
            "value": 20
          },
          {
            "Date": "2010-11",
            "value": 10
          },
          {
            "Date": "2010-12",
            "value": 10
          },
          {
            "Date": "2011-01",
            "value": 90
          },
          {
            "Date": "2011-02",
            "value": 20
          },
          {
            "Date": "2011-03",
            "value": 55
          },
          {
            "Date": "2011-04",
            "value": 66
          },
          {
            "Date": "2011-05",
            "value": 77
          },
          {
            "Date": "2011-06",
            "value": 88
          },
          {
            "Date": "2011-07",
            "value": 12
          },
          {
            "Date": "2011-08",
            "value": 33
          }
        ],
        iops: [
          {
            "Date": "2010-01",
            "value": 20
          },
          {
            "Date": "2010-02",
            "value": 30
          },
          {
            "Date": "2010-03",
            "value": 40
          },
          {
            "Date": "2010-04",
            "value": 20
          },
          {
            "Date": "2010-05",
            "value": 60
          },
          {
            "Date": "2010-06",
            "value": 80
          },
          {
            "Date": "2010-07",
            "value": 10
          },
          {
            "Date": "2010-08",
            "value": 22
          },
          {
            "Date": "2010-09",
            "value": 33
          },
          {
            "Date": "2010-10",
            "value": 20
          },
          {
            "Date": "2010-11",
            "value": 10
          },
          {
            "Date": "2010-12",
            "value": 10
          },
          {
            "Date": "2011-01",
            "value": 90
          },
          {
            "Date": "2011-02",
            "value": 20
          },
          {
            "Date": "2011-03",
            "value": 55
          },
          {
            "Date": "2011-04",
            "value": 66
          },
          {
            "Date": "2011-05",
            "value": 77
          },
          {
            "Date": "2011-06",
            "value": 88
          },
          {
            "Date": "2011-07",
            "value": 12
          },
          {
            "Date": "2011-08",
            "value": 33
          }
        ],
        network: [
          {
            "Date": "2010-01",
            "value": 20
          },
          {
            "Date": "2010-02",
            "value": 30
          },
          {
            "Date": "2010-03",
            "value": 40
          },
          {
            "Date": "2010-04",
            "value": 20
          },
          {
            "Date": "2010-05",
            "value": 60
          },
          {
            "Date": "2010-06",
            "value": 80
          },
          {
            "Date": "2010-07",
            "value": 10
          },
          {
            "Date": "2010-08",
            "value": 22
          },
          {
            "Date": "2010-09",
            "value": 33
          },
          {
            "Date": "2010-10",
            "value": 20
          },
          {
            "Date": "2010-11",
            "value": 10
          },
          {
            "Date": "2010-12",
            "value": 10
          },
          {
            "Date": "2011-01",
            "value": 90
          },
          {
            "Date": "2011-02",
            "value": 20
          },
          {
            "Date": "2011-03",
            "value": 55
          },
          {
            "Date": "2011-04",
            "value": 66
          },
          {
            "Date": "2011-05",
            "value": 77
          },
          {
            "Date": "2011-06",
            "value": 88
          },
          {
            "Date": "2011-07",
            "value": 12
          },
          {
            "Date": "2011-08",
            "value": 33
          }
        ]
      }
    },
  },
  {
    url: '/mock/business_monitor/relationship_resource',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        hardware_inventory: {
          name: '集成平台服务器2',
          status: '故障',
          ip: '***********',
          nodes: '集成平台集群',
          type: 'storeDrive',
          vm_system: 'oss',
          cpu_cores: '4',
          system: 'centos7',
          version: 'v1.0',
          cpu: [
            {
              "Date": "2010-01",
              "value": 30
            },
            {
              "Date": "2010-02",
              "value": 10
            },
            {
              "Date": "2010-03",
              "value": 44
            },
            {
              "Date": "2010-04",
              "value": 33
            },
            {
              "Date": "2010-05",
              "value": 77
            },
            {
              "Date": "2010-06",
              "value": 23
            },
            {
              "Date": "2010-07",
              "value": 20
            },
            {
              "Date": "2010-08",
              "value": 20
            },
            {
              "Date": "2010-09",
              "value": 30
            },
            {
              "Date": "2010-10",
              "value": 20
            },
            {
              "Date": "2010-11",
              "value": 10
            },
            {
              "Date": "2010-12",
              "value": 10
            },
            {
              "Date": "2011-01",
              "value": 90
            },
            {
              "Date": "2011-02",
              "value": 20
            },
            {
              "Date": "2011-03",
              "value": 55
            },
            {
              "Date": "2011-04",
              "value": 66
            },
            {
              "Date": "2011-05",
              "value": 77
            },
            {
              "Date": "2011-06",
              "value": 88
            },
            {
              "Date": "2011-07",
              "value": 12
            },
            {
              "Date": "2011-08",
              "value": 33
            }
          ],
          memory: [
            {
              "Date": "2010-01",
              "value": 11
            },
            {
              "Date": "2010-02",
              "value": 45
            },
            {
              "Date": "2010-03",
              "value": 33
            },
            {
              "Date": "2010-04",
              "value": 38
            },
            {
              "Date": "2010-05",
              "value": 50
            },
            {
              "Date": "2010-06",
              "value": 55
            },
            {
              "Date": "2010-07",
              "value": 23
            },
            {
              "Date": "2010-08",
              "value": 28
            },
            {
              "Date": "2010-09",
              "value": 45
            },
            {
              "Date": "2010-10",
              "value": 33
            },
            {
              "Date": "2010-11",
              "value": 30
            },
            {
              "Date": "2010-12",
              "value": 29
            },
            {
              "Date": "2011-01",
              "value": 10
            },
            {
              "Date": "2011-02",
              "value": 11
            },
            {
              "Date": "2011-03",
              "value": 55
            },
            {
              "Date": "2011-04",
              "value": 66
            },
            {
              "Date": "2011-05",
              "value": 77
            },
            {
              "Date": "2011-06",
              "value": 88
            },
            {
              "Date": "2011-07",
              "value": 12
            },
            {
              "Date": "2011-08",
              "value": 33
            }
          ],
          iops: [
            {
              "Date": "2010-01",
              "value": 20
            },
            {
              "Date": "2010-02",
              "value": 30
            },
            {
              "Date": "2010-03",
              "value": 40
            },
            {
              "Date": "2010-04",
              "value": 20
            },
            {
              "Date": "2010-05",
              "value": 60
            },
            {
              "Date": "2010-06",
              "value": 80
            },
            {
              "Date": "2010-07",
              "value": 10
            },
            {
              "Date": "2010-08",
              "value": 22
            },
            {
              "Date": "2010-09",
              "value": 33
            },
            {
              "Date": "2010-10",
              "value": 20
            },
            {
              "Date": "2010-11",
              "value": 10
            },
            {
              "Date": "2010-12",
              "value": 10
            },
            {
              "Date": "2011-01",
              "value": 90
            },
            {
              "Date": "2011-02",
              "value": 20
            },
            {
              "Date": "2011-03",
              "value": 55
            },
            {
              "Date": "2011-04",
              "value": 66
            },
            {
              "Date": "2011-05",
              "value": 77
            },
            {
              "Date": "2011-06",
              "value": 88
            },
            {
              "Date": "2011-07",
              "value": 12
            },
            {
              "Date": "2011-08",
              "value": 33
            }
          ],
          network: [
            {
              "Date": "2010-01",
              "value": 76
            },
            {
              "Date": "2010-02",
              "value": 23
            },
            {
              "Date": "2010-03",
              "value": 44
            },
            {
              "Date": "2010-04",
              "value": 60
            },
            {
              "Date": "2010-05",
              "value": 85
            },
            {
              "Date": "2010-06",
              "value": 75
            },
            {
              "Date": "2010-07",
              "value": 23
            },
            {
              "Date": "2010-08",
              "value": 75
            },
            {
              "Date": "2010-09",
              "value": 46
            },
            {
              "Date": "2010-10",
              "value": 64
            },
            {
              "Date": "2010-11",
              "value": 23
            },
            {
              "Date": "2010-12",
              "value": 57
            },
            {
              "Date": "2011-01",
              "value": 67
            },
            {
              "Date": "2011-02",
              "value": 58
            },
            {
              "Date": "2011-03",
              "value": 55
            },
            {
              "Date": "2011-04",
              "value": 66
            },
            {
              "Date": "2011-05",
              "value": 77
            },
            {
              "Date": "2011-06",
              "value": 88
            },
            {
              "Date": "2011-07",
              "value": 12
            },
            {
              "Date": "2011-08",
              "value": 33
            }
          ],
          service: [
            {
              "Date": "2010-01",
              "value": 20
            },
            {
              "Date": "2010-02",
              "value": 30
            },
            {
              "Date": "2010-03",
              "value": 40
            },
            {
              "Date": "2010-04",
              "value": 20
            },
            {
              "Date": "2010-05",
              "value": 60
            },
            {
              "Date": "2010-06",
              "value": 80
            },
            {
              "Date": "2010-07",
              "value": 10
            },
            {
              "Date": "2010-08",
              "value": 22
            },
            {
              "Date": "2010-09",
              "value": 33
            },
            {
              "Date": "2010-10",
              "value": 20
            },
            {
              "Date": "2010-11",
              "value": 10
            },
            {
              "Date": "2010-12",
              "value": 10
            },
            {
              "Date": "2011-01",
              "value": 90
            },
            {
              "Date": "2011-02",
              "value": 20
            },
            {
              "Date": "2011-03",
              "value": 55
            },
            {
              "Date": "2011-04",
              "value": 66
            },
            {
              "Date": "2011-05",
              "value": 77
            },
            {
              "Date": "2011-06",
              "value": 88
            },
            {
              "Date": "2011-07",
              "value": 12
            },
            {
              "Date": "2011-08",
              "value": 33
            }
          ],
          time: [
            {
              "Date": "2010-01",
              "value": 20
            },
            {
              "Date": "2010-02",
              "value": 30
            },
            {
              "Date": "2010-03",
              "value": 40
            },
            {
              "Date": "2010-04",
              "value": 20
            },
            {
              "Date": "2010-05",
              "value": 60
            },
            {
              "Date": "2010-06",
              "value": 80
            },
            {
              "Date": "2010-07",
              "value": 10
            },
            {
              "Date": "2010-08",
              "value": 22
            },
            {
              "Date": "2010-09",
              "value": 33
            },
            {
              "Date": "2010-10",
              "value": 20
            },
            {
              "Date": "2010-11",
              "value": 10
            },
            {
              "Date": "2010-12",
              "value": 10
            },
            {
              "Date": "2011-01",
              "value": 90
            },
            {
              "Date": "2011-02",
              "value": 20
            },
            {
              "Date": "2011-03",
              "value": 55
            },
            {
              "Date": "2011-04",
              "value": 66
            },
            {
              "Date": "2011-05",
              "value": 77
            },
            {
              "Date": "2011-06",
              "value": 88
            },
            {
              "Date": "2011-07",
              "value": 12
            },
            {
              "Date": "2011-08",
              "value": 33
            }
          ],
        },
        relationship_resource_topology: [{
          business_name: '集成平台',
          description: '描述信息',
          status: 'success',
          category: 'host',
          relative: ['虚拟盘2', '虚拟盘3', '主机1']
        }, {
          business_name: '虚拟盘2',
          description: '描述信息',
          status: 'success',
          category: 'vm',
          relative: ['主机2']
        }, {
          business_name: '虚拟盘3',
          description: '描述信息',
          status: 'danger',
          category: 'vm',
          relative: ['数据库3']
        }, {
          business_name: '主机1',
          description: '描述信息',
          status: 'danger',
          category: 'host',
          relative: ['主机2', '数据库3']
        }, {
          business_name: '主机2',
          description: '描述信息',
          status: 'danger',
          category: 'host',
          relative: ['主机3', '主机4']
        }, {
          business_name: '主机3',
          description: '描述信息',
          status: 'success',
          category: 'host',
          relative: ['数据库1', '数据库2']
        }, {
          business_name: '主机4',
          description: '描述信息',
          status: 'success',
          category: 'host',
          relative: []
        },
        {
          business_name: '数据库1',
          description: '描述信息',
          status: 'danger',
          category: 'database',
          relative: []
        }, {
          business_name: '数据库2',
          description: '描述信息',
          status: 'success',
          category: 'database',
          relative: ['数据库3']
        }, {
          business_name: '数据库3',
          description: '描述信息',
          status: 'success',
          category: 'database',
          relative: []
        }]
      }
    }
  },
  {
    url: '/mock/business_monitor/event',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: { status: 200, message: '标记成功' }
    }
  },
  {
    url: '/mock/business_monitor/eventList',
    method: 'get',
    response: {
      error: '',
      status: 1,
      data: [{
        name: '集成中心负载高',
        resource: '服务器',
        describe: '集成中心负载高',
        people: '张三',
        start_time: '2023-10.10',
        over_time: '',
        status: false,
        reason: ''
      }, {
        name: '挂号数据库磁盘读写高',
        resource: '数据库',
        describe: '挂号数据库磁盘读写高',
        people: '李四',
        start_time: '2023-10.10',
        over_time: '2023-10.11',
        status: true,
        reason: 'sql语句有问题'
      }]
    }
  },
  {
    url: '/mock/business_monitor/detailedInformation',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        top_cpu: [{ type: 'zabbix', value: 27 },
        { type: 'grafana', value: 25 },
        { type: 'kibana', value: 18 },
        { type: 'kafka', value: 15 },
        { type: 'apach', value: 10 },
        { type: '其他', value: 5 },],
        top_memory: [{ type: 'zabbix', value: 50 },
        { type: 'grafana', value: 11 },
        { type: 'kibana', value: 13 },
        { type: 'kafka', value: 12 },
        { type: 'apach', value: 5 },
        { type: '其他', value: 5 }],
        top_io: [{ type: 'zabbix', value: 76 },
        { type: 'grafana', value: 23 },
        { type: 'kibana', value: 22 },
        { type: 'kafka', value: 12 },
        { type: 'apach', value: 9 },
        { type: '其他', value: 2 }],
        health_level: 0.75
      }
    }
  },
  {
    url: '/mock/business_monitor/network_tolopy',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        "nodes": [
          {
            "id": 3,
            "label": "oracle-129-Oracle-**************",
            "description": "oracle-数据库",
            "types": "oracle",
            "status": "success",
            "problem": [],
            "ip": "**************"
          },
          {
            "id": 7,
            "label": "测试-Oracle-**************",
            "description": "oracle-数据库",
            "types": "oracle",
            "status": "success",
            "problem": [],
            "ip": "**************"
          },
          {
            "id": 30,
            "label": "测试-PG-**************",
            "description": "pg-数据库",
            "types": "pg",
            "status": "warning",
            "problem": [
              {
                "triggerid": "18977",
                "lastchange": "2024-05-28 19:25:11",
                "hostip": "**************",
                "hostname": "测试-Linux-**************",
                "hostid": "10084",
                "priority": "严重",
                "description": "内存使用率过高 (>{20分钟内持续大于 90%)",
                "histroy_list": []
              }
            ],
            "ip": "**************"
          },
          {
            "id": 5,
            "label": "测试-iis-**************",
            "description": "iis-中间件",
            "types": "iis",
            "status": "warning",
            "problem": [
              {
                "triggerid": "18977",
                "lastchange": "2024-05-28 19:25:11",
                "hostip": "**************",
                "hostname": "测试-Linux-**************",
                "hostid": "10084",
                "priority": "严重",
                "description": "内存使用率过高 (>{20分钟内持续大于 90%)",
                "histroy_list": []
              }
            ],
            "ip": "**************"
          },
          {
            "id": 24,
            "label": "测试-nginx-**************",
            "description": "nginx-中间件",
            "types": "nginx",
            "status": "warning",
            "problem": [
              {
                "triggerid": "18977",
                "lastchange": "2024-05-28 19:25:11",
                "hostip": "**************",
                "hostname": "测试-Linux-**************",
                "hostid": "10084",
                "priority": "严重",
                "description": "内存使用率过高 (>{20分钟内持续大于 90%)",
                "histroy_list": []
              }
            ],
            "ip": "**************"
          },
          {
            "id": 26,
            "label": "测试-Oracle-***************",
            "description": "oracle-数据库",
            "types": "oracle",
            "status": "success",
            "problem": [],
            "ip": "***************"
          },
          {
            "id": 28,
            "label": "测试-Oracle-***************",
            "description": "oracle-数据库",
            "types": "oracle",
            "status": "success",
            "problem": [],
            "ip": "***************"
          }
        ],
        "edges": [

        ],
        "combos": [
          {
            "id": 2,
            "label": "130服务器-Linux-**************",
            "description": "linux-服务器",
            "types": "linux",
            "status": "success",
            "problem": [],
            "ip": "**************",
            "child": [
              3,
              7
            ]
          },
          {
            "id": 1,
            "label": "测试-Linux-**************",
            "description": "linux-服务器",
            "types": "linux",
            "status": "warning",
            "problem": [
              {
                "triggerid": "18977",
                "lastchange": "2024-05-28 19:25:11",
                "hostip": "**************",
                "hostname": "测试-Linux-**************",
                "hostid": "10084",
                "priority": "严重",
                "description": "内存使用率过高 (>{20分钟内持续大于 90%)",
                "histroy_list": []
              }
            ],
            "ip": "**************",
            "child": [
              30,
              5,
              24
            ]
          },
          {
            "id": 25,
            "label": "本地100服务器-Linux-***************",
            "description": "linux-服务器",
            "types": "linux",
            "status": "success",
            "problem": [],
            "ip": "***************",
            "child": [
              26,
              28
            ]
          }
        ]
      }
    }
  },
  {
    url: '/mock/business_monitor/warning',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        advice: [{ cpu: '一切正常' }, { 内存: '需要增加内存' }, { 磁盘: '磁盘空间剩余不多，需要加磁盘空间' }],
        problem_list: [
          {
            "triggerid": "2",
            "lastchange": "2023-11-24 08:36:15",
            "hostip": "***********",
            "hostname": "Server3",
            "hostid": "5678",
            "priority": "灾难",
            "description": "CPU usage is high",
            "histroy_list": [
              {
                "eventid": "201",
                "r_eventid": 202,
                "startime": "2023-11-23 08:00:00",
                "endtime": "2023-11-23 09:00:00",
                "duration": [
                  "1 hour"
                ],
                "status": "Problem",
                "acknowledged": "No",
                "tags": [
                  {
                    "tag": "Application",
                    "value": "CPU"
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  },
  {
    url: '/mock/business_monitor/warningList',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: [{
        name: '某某某服务器',
        date: '2022-01-01 11:00:00',
        type: '软件告警',
        level: '警告',
        content: '虚拟机内存占用率超出阙值！',
        service_name: '电子病历管理系统',
        call_system: '病历质控系统',
        subscribe_system: 'WJ1001',
        time_consuming: '954ms',
        message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        history_list: [
          {
            "eventid": "10170",
            "r_eventid": 0,
            "startime": "2023-11-27 02:18:07",
            "endtime": "",
            "duration": [
              "0天",
              "1小时",
              "14分钟",
              "8秒"
            ],
            "status": "问题",
            "acknowledged": "不",
            "tags": [
              {
                "tag": "Application",
                "value": "Status"
              }
            ]
          }
        ]
      }, {
        name: '某某某服务器',
        date: '2022-01-01 10:00:00',
        type: '软件告警',
        level: '警告',
        content: '存储使用容量超过阙值！',
        service_name: '电子病历管理系统',
        call_system: '病历质控系统',
        subscribe_system: 'WJ1001',
        time_consuming: '954ms',
        message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        history_list: [
          {
            "eventid": "10170",
            "r_eventid": 0,
            "startime": "2023-11-27 02:18:07",
            "endtime": "",
            "duration": [
              "0天",
              "1小时",
              "14分钟",
              "8秒"
            ],
            "status": "问题",
            "acknowledged": "不",
            "tags": [
              {
                "tag": "Application",
                "value": "Status"
              }
            ]
          }
        ]
      }, {
        name: '某某某服务器',
        date: '2022-01-01 10:00:00',
        type: '软件告警',
        level: '警告',
        content: '存储使用容量超过阙值！',
        service_name: '电子病历管理系统',
        call_system: '病历质控系统',
        subscribe_system: 'WJ1001',
        time_consuming: '954ms',
        message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        history_list: [
          {
            "eventid": "10170",
            "r_eventid": 0,
            "startime": "2023-11-27 02:18:07",
            "endtime": "",
            "duration": [
              "0天",
              "1小时",
              "14分钟",
              "8秒"
            ],
            "status": "问题",
            "acknowledged": "不",
            "tags": [
              {
                "tag": "Application",
                "value": "Status"
              }
            ]
          }
        ]
      }, {
        name: '某某某服务器',
        date: '2022-01-01 10:00:00',
        type: '软件告警',
        level: '警告',
        content: '存储使用容量超过阙值！',
        service_name: '电子病历管理系统',
        call_system: '病历质控系统',
        subscribe_system: 'WJ1001',
        time_consuming: '954ms',
        message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        history_list: [
          {
            "eventid": "10170",
            "r_eventid": 0,
            "startime": "2023-11-27 02:18:07",
            "endtime": "",
            "duration": [
              "0天",
              "1小时",
              "14分钟",
              "8秒"
            ],
            "status": "问题",
            "acknowledged": "不",
            "tags": [
              {
                "tag": "Application",
                "value": "Status"
              }
            ]
          }
        ]
      }, {
        name: '某某某服务器',
        date: '2022-01-01 10:00:00',
        type: '软件告警',
        level: '警告',
        content: '存储使用容量超过阙值！',
        service_name: '电子病历管理系统',
        call_system: '病历质控系统',
        subscribe_system: 'WJ1001',
        time_consuming: '954ms',
        message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        history_list: [
          {
            "eventid": "10170",
            "r_eventid": 0,
            "startime": "2023-11-27 02:18:07",
            "endtime": "",
            "duration": [
              "0天",
              "1小时",
              "14分钟",
              "8秒"
            ],
            "status": "问题",
            "acknowledged": "不",
            "tags": [
              {
                "tag": "Application",
                "value": "Status"
              }
            ]
          }
        ]
      }, {
        name: '某某某服务器',
        date: '2022-01-01 10:00:00',
        type: '软件告警',
        level: '警告',
        content: '存储使用容量超过阙值！',
        service_name: '电子病历管理系统',
        call_system: '病历质控系统',
        subscribe_system: 'WJ1001',
        time_consuming: '954ms',
        message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        history_list: [
          {
            "eventid": "10170",
            "r_eventid": 0,
            "startime": "2023-11-27 02:18:07",
            "endtime": "",
            "duration": [
              "0天",
              "1小时",
              "14分钟",
              "8秒"
            ],
            "status": "问题",
            "acknowledged": "不",
            "tags": [
              {
                "tag": "Application",
                "value": "Status"
              }
            ]
          }
        ]
      }, {
        name: '某某某服务器',
        date: '2022-01-01 10:00:00',
        type: '软件告警',
        level: '警告',
        content: '存储使用容量超过阙值！',
        service_name: '电子病历管理系统',
        call_system: '病历质控系统',
        subscribe_system: 'WJ1001',
        time_consuming: '954ms',
        message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        history_list: [
          {
            "eventid": "10170",
            "r_eventid": 0,
            "startime": "2023-11-27 02:18:07",
            "endtime": "",
            "duration": [
              "0天",
              "1小时",
              "14分钟",
              "8秒"
            ],
            "status": "问题",
            "acknowledged": "不",
            "tags": [
              {
                "tag": "Application",
                "value": "Status"
              }
            ]
          }
        ]
      }, {
        name: '某某某服务器',
        date: '2022-01-01 10:00:00',
        type: '软件告警',
        level: '警告',
        content: '存储使用容量超过阙值！',
        service_name: '电子病历管理系统',
        call_system: '病历质控系统',
        subscribe_system: 'WJ1001',
        time_consuming: '954ms',
        message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        history_list: [
          {
            "eventid": "10170",
            "r_eventid": 0,
            "startime": "2023-11-27 02:18:07",
            "endtime": "",
            "duration": [
              "0天",
              "1小时",
              "14分钟",
              "8秒"
            ],
            "status": "问题",
            "acknowledged": "不",
            "tags": [
              {
                "tag": "Application",
                "value": "Status"
              }
            ]
          }
        ]
      }, {
        name: '某某某服务器',
        date: '2022-01-01 10:00:00',
        type: '软件告警',
        level: '警告',
        content: '存储使用容量超过阙值！',
        service_name: '电子病历管理系统',
        call_system: '病历质控系统',
        subscribe_system: 'WJ1001',
        time_consuming: '954ms',
        message: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        advice: '电子病历管理系统，提供的病人查询服务，服务出现异常，请及时查看！',
        history_list: [
          {
            "eventid": "10170",
            "r_eventid": 0,
            "startime": "2023-11-27 02:18:07",
            "endtime": "",
            "duration": [
              "0天",
              "1小时",
              "14分钟",
              "8秒"
            ],
            "status": "问题",
            "acknowledged": "不",
            "tags": [
              {
                "tag": "Application",
                "value": "Status"
              }
            ]
          }
        ]
      },]
    }
  },
  {
    url: '/mock/business_monitor/resouce',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        unit: '%', data: [
          {
            "name": "今日",
            "time": "00:00",
            "value": 24
          },
          {
            "name": "今日",
            "time": "01:00",
            "value": 12
          },
          {
            "name": "今日",
            "time": "02:00",
            "value": 23
          },
          {
            "name": "今日",
            "time": "03:00",
            "value": 38
          },
          {
            "name": "今日",
            "time": "04:00",
            "value": 35
          },
          {
            "name": "今日",
            "time": "05:00",
            "value": 45
          },
          {
            "name": "今日",
            "time": "06:00",
            "value": 25
          },
          {
            "name": "今日",
            "time": "07:00",
            "value": 35
          },
          {
            "name": "今日",
            "time": "08:00",
            "value": 45
          },
          {
            "name": "今日",
            "time": "09:00",
            "value": 51
          },
          {
            "name": "今日",
            "time": "10:00",
            "value": 60
          },
          {
            "name": "今日",
            "time": "11:00",
            "value": 75
          },
          {
            "name": "今日",
            "time": "12:00",
            "value": 85
          },
          {
            "name": "今日",
            "time": "13:00",
            "value": 70
          },
          {
            "name": "今日",
            "time": "14:00",
            "value": 78
          },
          {
            "name": "昨日",
            "time": "00:00",
            "value": 23
          },
          {
            "name": "昨日",
            "time": "01:00",
            "value": 11
          },
          {
            "name": "昨日",
            "time": "02:00",
            "value": 43
          },
          {
            "name": "昨日",
            "time": "03:00",
            "value": 34
          },
          {
            "name": "昨日",
            "time": "04:00",
            "value": 54
          },
          {
            "name": "昨日",
            "time": "05:00",
            "value": 40
          },
          {
            "name": "昨日",
            "time": "06:00",
            "value": 65
          },
          {
            "name": "昨日",
            "time": "07:00",
            "value": 85
          },
          {
            "name": "昨日",
            "time": "08:00",
            "value": 45
          },
          {
            "name": "昨日",
            "time": "09:00",
            "value": 51
          },
          {
            "name": "昨日",
            "time": "10:00",
            "value": 60
          },
          {
            "name": "昨日",
            "time": "11:00",
            "value": 75
          },
          {
            "name": "昨日",
            "time": "12:00",
            "value": 85
          },
          {
            "name": "昨日",
            "time": "13:00",
            "value": 78
          },
          {
            "name": "昨日",
            "time": "14:00",
            "value": 87
          },
          {
            "name": "昨日",
            "time": "15:00",
            "value": 76
          },
          {
            "name": "昨日",
            "time": "16:00",
            "value": 80
          },
          {
            "name": "昨日",
            "time": "17:00",
            "value": 66
          },
          {
            "name": "昨日",
            "time": "18:00",
            "value": 54
          },
          {
            "name": "昨日",
            "time": "19:00",
            "value": 80
          },
          {
            "name": "昨日",
            "time": "20:00",
            "value": 55
          },
          {
            "name": "昨日",
            "time": "21:00",
            "value": 43
          },
          {
            "name": "昨日",
            "time": "22:00",
            "value": 35
          },
          {
            "name": "昨日",
            "time": "23:00",
            "value": 22
          },
          {
            "name": "昨日",
            "time": "24:00",
            "value": 13
          },
          {
            "name": "上一周同一天",
            "time": "00:00",
            "value": 34
          },
          {
            "name": "上一周同一天",
            "time": "01:00",
            "value": 89
          },
          {
            "name": "上一周同一天",
            "time": "02:00",
            "value": 80
          },
          {
            "name": "上一周同一天",
            "time": "03:00",
            "value": 75
          },
          {
            "name": "上一周同一天",
            "time": "04:00",
            "value": 98
          },
          {
            "name": "上一周同一天",
            "time": "05:00",
            "value": 83
          },
          {
            "name": "上一周同一天",
            "time": "06:00",
            "value": 23
          },
          {
            "name": "上一周同一天",
            "time": "07:00",
            "value": 42
          },
          {
            "name": "上一周同一天",
            "time": "08:00",
            "value": 67
          },
          {
            "name": "上一周同一天",
            "time": "09:00",
            "value": 55
          },
          {
            "name": "上一周同一天",
            "time": "10:00",
            "value": 98
          },
          {
            "name": "上一周同一天",
            "time": "11:00",
            "value": 55
          },
          {
            "name": "上一周同一天",
            "time": "12:00",
            "value": 33
          },
          {
            "name": "上一周同一天",
            "time": "13:00",
            "value": 44
          },
          {
            "name": "上一周同一天",
            "time": "14:00",
            "value": 78
          },
          {
            "name": "上一周同一天",
            "time": "15:00",
            "value": 87
          },
          {
            "name": "上一周同一天",
            "time": "16:00",
            "value": 64
          },
          {
            "name": "上一周同一天",
            "time": "17:00",
            "value": 61
          },
          {
            "name": "上一周同一天",
            "time": "18:00",
            "value": 69
          },
          {
            "name": "上一周同一天",
            "time": "19:00",
            "value": 75
          },
          {
            "name": "上一周同一天",
            "time": "20:00",
            "value": 70
          },
          {
            "name": "上一周同一天",
            "time": "21:00",
            "value": 85
          },
          {
            "name": "上一周同一天",
            "time": "22:00",
            "value": 34
          },
          {
            "name": "上一周同一天",
            "time": "23:00",
            "value": 22
          },
          {
            "name": "上一周同一天",
            "time": "24:00",
            "value": 11
          },
        ]
      }
    }
  }, {
    url: '/mock/business_monitor/homepage',
    method: 'get',
    response: {
      error: '',
      status: 1,
      data: [
        {
          "name": "4562",
          "status": "success",
          "business_id": 21,
          "belong": "中联产品",
          "problem": 0,
          "server": 0,
          "health": 0,
          "list": [
            {
              "name": "打包服务器",
              "type": "服务器",
              "status": "success"
            },
            {
              "name": "测试服务器",
              "type": "服务器",
              "status": "success"
            },
            {
              "name": "PG数据库",
              "type": "数据库",
              "status": "success"
            },
            {
              "name": "测试服务器",
              "type": "中间件",
              "status": "success"
            },
            {
              "name": "42服务器",
              "type": "服务器",
              "status": "down"
            }
          ],
          "problemList": []
        },
        {
          "name": "测试业务11",
          "status": "warning",
          "business_id": 1,
          "belong": '中联产品',
          "problem": 1,
          "server": 0,
          "health": 0,
          "list": [
            {
              "name": "142服务器",
              "type": "服务器",
              "status": "success"
            }
          ],
          "problemList": [
            {
              "triggerid": "11296",
              "lastchange": "2023-12-11 18:12:56",
              "hostip": "*************",
              "hostname": "公司-142服务器",
              "hostid": "10221",
              "priority": "一般严重",
              "description": "与监控系统时间不一致 (diff with Zabbix server > 60s)",
              "histroy_list": []
            }
          ]
        },
        {
          "name": "测试业务2",
          "status": "warning",
          "business_id": 16,
          "belong": '中联产品',
          "problem": 1,
          "server": 0,
          "health": 0,
          "list": [
            {
              "name": "142服务器",
              "type": "服务器",
              "status": "success"
            },
            {
              "name": "12服务器",
              "type": "服务器",
              "status": "down"
            },
            {
              "name": "12服务器",
              "type": "中间件",
              "status": "down"
            }
          ],
          "problemList": [
            {
              "triggerid": "11296",
              "lastchange": "2023-12-11 18:12:56",
              "hostip": "*************",
              "hostname": "公司-142服务器",
              "hostid": "10221",
              "priority": "一般严重",
              "description": "与监控系统时间不一致 (diff with Zabbix server > 60s)",
              "histroy_list": []
            }
          ]
        },
        {
          "name": "测试业务3",
          "status": "warning",
          "business_id": 20,
          "belong": '其他产品',
          "problem": 1,
          "server": 0,
          "health": 0,
          "list": [
            {
              "name": "142服务器",
              "type": "服务器",
              "status": "success"
            },
            {
              "name": "1111",
              "type": "数据库",
              "status": "success"
            },
            {
              "name": "1221",
              "type": "数据库",
              "status": "success"
            },
            {
              "name": "142服务器",
              "type": "中间件",
              "status": "success"
            },
            {
              "name": "142服务器",
              "type": "中间件",
              "status": "success"
            },
            {
              "name": "打包服务器",
              "type": "服务器",
              "status": "success"
            },
            {
              "name": "140服务器",
              "type": "服务器",
              "status": "success"
            },
            {
              "name": "Oracle测试数据库",
              "type": "数据库",
              "status": "success"
            }
          ],
          "problemList": [
            {
              "triggerid": "11296",
              "lastchange": "2023-12-11 18:12:56",
              "hostip": "*************",
              "hostname": "公司-142服务器",
              "hostid": "10221",
              "priority": "一般严重",
              "description": "与监控系统时间不一致 (diff with Zabbix server > 60s)",
              "histroy_list": []
            }
          ]
        },
        {
          "name": "2",
          "status": "warning",
          "business_id": 19,
          "belong": '中联产品',
          "problem": 1,
          "server": 0,
          "health": 0,
          "list": [
            {
              "name": "142服务器",
              "type": "服务器",
              "status": "success"
            },
            {
              "name": "142服务器",
              "type": "中间件",
              "status": "success"
            },
            {
              "name": "140服务器",
              "type": "服务器",
              "status": "success"
            },
            {
              "name": "Oracle测试数据库",
              "type": "数据库",
              "status": "success"
            }
          ],
          "problemList": [
            {
              "triggerid": "11296",
              "lastchange": "2023-12-11 18:12:56",
              "hostip": "*************",
              "hostname": "公司-142服务器",
              "hostid": "10221",
              "priority": "一般严重",
              "description": "与监控系统时间不一致 (diff with Zabbix server > 60s)",
              "histroy_list": []
            }
          ]
        }
      ]
    }
  },
]
