<template>
  <page-main title="自定义巡检">
    <search-bar>
      <el-form :model="data.search" size="default" label-width="100px" label-suffix="：">
        <el-row>
          <el-col :span="6">
            <el-form-item label="报告名称">
              <el-input
                v-model="data.search.inspection_report_name"
                placeholder="输入报告名称"
                clearable
                @keydown.enter="queryData"
                @clear="getDataList"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select multiple clearable v-model="data.search.status" placeholder="巡检状态" style="width: 240px">
                <el-option
                  v-for="item in InspectionStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-button type="primary" @click="queryData" :icon="Search">筛选</el-button>
          </el-col>
        </el-row>
      </el-form>
    </search-bar>
    <el-space wrap>
      <el-space wrap>
        <el-button type="primary" @click="addCustomInspection" :icon="DocumentRemove">新增自定义巡检</el-button>
        <el-button
          type="danger"
          @click="batchDelete"
          :disabled="data.batch.selectionDataList.length < 1"
          :loading="data.batch.batchDeleteLoading"
          :icon="Delete"
        >
          删除报告
        </el-button>
        <el-button type="" @click="getDataList" :loading="data.listLoading" :icon="Refresh">刷新</el-button>
        <span class="text-gray">提示: 刷新列表查看最新创建状态</span>
      </el-space>
    </el-space>
    <el-table
      v-loading="data.listLoading"
      class="list-table"
      :data="data.dataList"
      border
      stripe
      highlight-current-row
      height="100%"
      @selection-change="data.batch.selectionDataList = $event"
    >
      <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
      <el-table-column prop="inspection_report_name" label="报告名称" />
      <el-table-column prop="create_time" label="巡检时间" />
      <el-table-column prop="system_list" label="巡检业务" width="380" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.system_list ? scope.row.system_list?.join(", ") : "" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="last_days" label="时间窗口(天)" width="120" />

      <el-table-column prop="describ" label="备注" />
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <el-tag type="success" size="large" v-if="scope.row.status === true">创建成功</el-tag>
          <el-tag type="danger" size="large" v-else-if="scope.row.status === false">创建失败</el-tag>
          <el-tag type="info" size="large" v-else>创建中</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template #default="scope">
          <el-link type="primary" :href="downloadurl(scope.row)" target="_blank" v-if="scope.row.status == true">
            导出
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pagination.page"
      :total="pagination.total"
      :page-size="pagination.size"
      :page-sizes="pagination.sizes"
      :layout="pagination.layout"
      :hide-on-single-page="false"
      class="pagination"
      background
      @size-change="sizeChange"
      @current-change="currentChange"
    />
    <createCustomInspectionDialog
      v-model="data.showCreateDialog"
      @refresh-list="getDataList"
    ></createCustomInspectionDialog>
  </page-main>
</template>
<script setup lang="ts">
import { DocumentRemove, Search, Guide, Delete, Refresh } from "@element-plus/icons-vue";
import { usePagination } from "@/utils/composables";
import createCustomInspectionDialog from "./create_custom_inspection_dialog.vue";
import { deleteInspectionReports, getAllInspectionReports } from "@/api/modules/inspection_report/custom_inspection";
import { InspectionReport } from "@/api/modules/inspection_report/types";
import { getBaseUrl } from "@/utils/axios";
import { LastDaysOptions, InspectionStatusOptions } from "./constants";
import { deepClone } from "@/utils";
import { pageChangeNum } from "@/views/homepage/components/utils";
const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();
const data = reactive({
  listLoading: false,
  showCreateDialog: false,
  // 搜索
  search: {
    isSearch: false,
    inspection_report_name: "",
  },
  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
    batchDeleteLoading: false,
  },
  // 列表数据
  dataList: [] as InspectionReport[],
  allList: [] as InspectionReport[],
});

onMounted(() => {
  getDataList();
});

// 拼接下载地址
function downloadurl(row) {
  return getBaseUrl() + `/inspection_report/download_inspection_file/?inspection_report_id=${row.id}`;
}

// 获取数据列表
function getDataList() {
  data.listLoading = true;
  getAllInspectionReports()
    .then((res) => {
      data.dataList = res.data;
      data.allList = res.data;
      paging(res.data);
    })
    .finally(() => {
      data.listLoading = false;
    });
}

// 批量删除
function batchDelete() {
  data.batch.batchDeleteLoading = true;
  const ids = data.batch.selectionDataList.map((report) => report.id);
  deleteInspectionReports(ids)
    .then((res) => {
      getDataList();
    })
    .finally(() => {
      data.batch.batchDeleteLoading = false;
    });
}

// 新增自定义巡检
function addCustomInspection() {
  data.showCreateDialog = true;
}

// 筛选
function queryData() {
  let list = deepClone(data.allList);
  if (data.search.inspection_report_name == "" && data.search.status.length == 0) {
    data.search.isSearch = false;
    getDataList();
    return;
  }

  if (data.search.inspection_report_name) {
    list = list.filter((item) => {
      return item.inspection_report_name.indexOf(data.search.inspection_report_name) !== -1;
    });
  }

  if (data.search.status.length > 0) {
    list = list.filter((item) => {
      return data.search.status.includes(item.status);
    });
  }

  data.search.isSearch = true;
  paging(list);
}

// 分页
function paging(lists) {
  let result = pageChangeNum(lists, getParams());

  data.dataList = result.list;
  pagination.value.total = result.total;
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      queryData();
    } else {
      paging(data.allList);
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      queryData();
    } else {
      paging(data.allList);
    }
  });
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-table .el-popper {
    width: 500px;
  }
}
</style>
