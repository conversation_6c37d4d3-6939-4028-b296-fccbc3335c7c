<script setup name="HomepageInformation">
import useSettingsStore from "@/store/modules/settings";
import { useTabbar } from "@/utils/composables";
import { onMounted, reactive, ref, onUnmounted, watch } from "vue";
import { usePagination } from "@/utils/composables";
import { cmdbGetResourceList, cmdbDeleteResource, addMonitoring } from "@/api/modules/cmdb/resource";
import { ElMessage, ElMessageBox } from "element-plus";

import { showNotification } from "@/plugins/element-ui/index";
import { View, Location, Remove } from "@element-plus/icons-vue";
import {
  goToPageAssetDetail,
  pageChangeNum,
  deleteResource,
  addZabbixForResource,
  showConfirmationDialog,
} from "../components/utils";
import { ResourceTypeEnum } from "../cmdb_asset/components/constants";
import ZabbixResult from "@/containers/zabbix_resulte_dialog/index.vue";
import MoreOperations from "../components/more_operations.vue";
import CreateNetwork from "@/views/homepage/cmdb_network_equipment/createNetworkEquip.vue";

const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();
const router = useRouter();
const settingsStore = useSettingsStore();
const zabbixLoading = ref(false);
const zabbixResultDialog = ref(false);
const zabbixResultData = ref([]);

// 定义localStorage的key
const STORAGE_KEY = 'cmdb_network_search_condition';

const data = reactive({
  serverLoading: false,
  title: "",
  dataList: [],
  allList: [],
  template_list: [],
  search: {
    searchName: "",
    isSearch: false,
  },

  // 批量操作
  batch: {
    enable: true,
    selectionDataList: [],
  },
  drawer: false,
  router_id: 0,
});

// 监听搜索条件变化，更新localStorage
watch(() => data.search, (newVal) => {
  if (newVal.searchName) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify({
      searchName: newVal.searchName,
      isSearch: newVal.isSearch
    }));
  }
}, { deep: true });

onMounted(() => {
  data.title = ResourceTypeEnum.network;
  
  // 从localStorage获取筛选条件
  const savedSearch = localStorage.getItem(STORAGE_KEY);
  if (savedSearch) {
    try {
      const parsedSearch = JSON.parse(savedSearch);
      data.search.searchName = parsedSearch.searchName || '';
      data.search.isSearch = parsedSearch.isSearch || false;
    } catch (e) {
      console.error('解析保存的筛选条件出错', e);
    }
  }
  
  getData();
});

// 组件卸载时清除localStorage
onUnmounted(() => {
  localStorage.removeItem(STORAGE_KEY);
});

//加载初始化数据
function getData() {
  cmdbGetResourceList(data.title).then((res) => {
    data.dataList = res.data;
    data.allList = data.dataList;
    
    // 如果有筛选条件，应用筛选
    if (data.search.isSearch && data.search.searchName) {
      searchByName();
    } else {
    changePageNum(data.allList);
    }
  });
}

//分页
function changePageNum(lists) {
  let res = pageChangeNum(lists, getParams());
  data.dataList = res.list;
  pagination.value.total = res.total;
  data.serverLoading = false;
}

function goBack() {
  router.go(-1);
}

const drawerTitle = computed(() => {
  return data.router_id === 0 ? "新增网络设备" : "编辑网络设备";
});

//添加页面跳转
function create() {
  data.router_id = 0;
  data.drawer = true;
}

// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

//筛选函数
function queryData() {
  if (data.search.searchName === "") {
    getData();
    data.search.isSearch = false;
  } else {
    searchByName();
    data.search.isSearch = true;
  }
}

function searchByName() {
  let list = [];
  const ipRegex = /^[\d.]+$/;
  if (ipRegex.test(data.search.searchName.trim())) {
    list = data.allList.filter((item) => {
      return item.ip.trim().indexOf(data.search.searchName.trim()) != -1;
    });
  } else {
    list = data.allList.filter((item) => {
      return item.databaseName.trim().toLowerCase().indexOf(data.search.searchName.trim().toLowerCase()) !== -1;
    });
  }
  changePageNum(list);
}

function editDatabase(row) {
  // router.push({
  //   path: "/cmdb/networkInformation/create_network_equipment",
  //   query: { id: row.relation_id, zabbix_id: row.zabbix_id },
  // });
  data.router_id = row.relation_id;
  data.drawer = true;
}

//批量删除

async function batchDel() {
  let list = [];
  data.batch.selectionDataList.forEach((item) => {
    list.push({ relation_id: item.relation_id, zabbix_id: item.zabbix_id });
  });
  const result = await deleteResource(ResourceTypeEnum.network, list);
  if (!result) {
    return;
  }
  getData();
}

// 跳转详情
function jumpAssetDetail(row, isMonitoring = false) {
  const queryParams = { item: data.title, id: row.zabbix_id, ip: row.ip, relation_id: row.relation_id };
  goToPageAssetDetail(queryParams, isMonitoring);
}

//单个主机安装
function installZabbix(item) {
  if (item.template_list.length == 0) {
    ElMessage.error("网络设备未配置监控模板，请配置！");
    return;
  }
  addZabbix([item]);
}
function batchInstall() {
  let list = [];
  list = data.allList.filter((item) => item.status == "offmonitor");
  if (list.length == 0) {
    ElMessage.error("请检查是否有未安装监控服务的设备");
    return;
  }
  let list_template = list.filter((item) => {
    return item.template_list.length == 0;
  });
  if (list_template.length != 0) {
    let serverName = list_template.map((item) => item.name);
    showNotification("注意", "以下网络设备没有添加监控模板：[" + serverName.join(",") + "],请注意！", "error");
    return;
  }
  addZabbix(list);
}
//zabbix-agent安装

async function addZabbix(list) {
  let ip_information_list = [];
  list.forEach((item) => {
    ip_information_list.push({
      ip: item.ip,
      type: "network",
      group_name: item.group_name,
      id: item.zabbix_id,
    });
  });
  let params = {
    resource_type_name: "network",
    ip_information_list,
  };
  const confirm = await showConfirmationDialog();
  if (confirm) {
    zabbixLoading.value = true;
    zabbixResultDialog.value = true;
    zabbixResultData.value = [];
    try {
      zabbixResultData.value = await addZabbixForResource(params);
    } catch (error) {}
    zabbixLoading.value = false;
    getData();
  } else {
    ElMessage.info("已取消监控安装!");
  }
}
</script>

<template>
  <div>
    <page-main :title="data.title">
      <!-- 网络设备 -->
      <div>
        <div class="flex justify-between">
          <el-space wrap>
            <el-button type="primary" @click="create">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:plus" />
                </el-icon>
              </template>
              新增
            </el-button>
            <el-button type="primary" @click="batchInstall">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:coordinate" />
                </el-icon>
              </template>
              一键安装所有
            </el-button>
            <el-button-group v-if="data.batch.enable">
              <el-button type="danger" :disabled="!data.batch.selectionDataList.length" @click="batchDel()">
                删除
              </el-button>
            </el-button-group>
          </el-space>
          <el-space wrap>
            <div class="w-220px">
              <el-input
                v-model="data.search.searchName"
                placeholder="请输入名称和IP查询"
                clearable
                @keyup.enter="queryData"
              />
            </div>
            <el-button type="primary" @click="queryData()">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:search" />
                </el-icon>
              </template>
              筛选
            </el-button>
          </el-space>
        </div>

        <el-table
          v-loading="data.serverLoading"
          class="list-table"
          :data="data.dataList"
          border
          stripe
          highlight-current-row
          @selection-change="data.batch.selectionDataList = $event"
        >
          <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
          <el-table-column label="IP" prop="ip" />

          <el-table-column label="名称" prop="databaseName">
            <template #default="scope">
              <el-link @click="jumpAssetDetail(scope.row)" type="primary">{{ scope.row.databaseName }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="监控状态" width="105px" align="center">
            <template #default="scoped">
              <el-tag v-if="scoped.row.status == 'online'">已启用</el-tag>
              <el-tag v-else-if="scoped.row.status == 'offmonitor'" type="info" effect="dark">未监控</el-tag>
              <el-tag v-else type="danger">停用的</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center">
            <template #default="scoped">
              <el-button type="primary" plain @click="editDatabase(scoped.row)">编辑</el-button>

              <MoreOperations>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="scoped.row.status == 'offmonitor'"
                    :icon="Location"
                    @click="installZabbix(scoped.row)"
                  >
                    安装监控
                  </el-dropdown-item>
                  <el-dropdown-item
                    @click="jumpAssetDetail(scoped.row, true)"
                    v-if="scoped.row.status == 'online' || scoped.row.status == 'offline'"
                  >
                    最新数据
                  </el-dropdown-item>

                  <!-- <el-dropdown-item :icon="Remove" @click="deleteCluster(scoped.row)">删除设备</el-dropdown-item> -->
                </el-dropdown-menu>
              </MoreOperations>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页插件 -->
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>
    <ZabbixResult v-model="zabbixResultDialog" :stepData="zabbixResultData" :loading="zabbixLoading"></ZabbixResult>
    <el-drawer v-model="data.drawer" size="50%" :destroy-on-close="true" :close-on-click-modal="false">
      <template #header>
        <h4>{{ drawerTitle }}</h4>
      </template>
      <el-divider class="cancel_top" />
      <CreateNetwork
        v-model="data.router_id"
        @closeDialog="data.drawer = false"
        @getResource="getData()"
      ></CreateNetwork>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
// scss
.cancel_top {
  margin-top: 0px;
}
</style>
