<template>
  <el-dialog title="新增院区" v-model="dialogVisible" width="600" :close-on-click-modal="false">
    <el-form :model="form" label-position="top" label-suffix=":">
      <el-form-item label="院区名称" :label-width="80">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <!-- <el-form-item label="成员" :label-width="80">
        <el-select v-model="form.person" placeholder="">
          <el-option v-for="item in personList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item> -->
    </el-form>
    <div slot="footer" class="flex justify-end">
      <el-button @click="dialogClose()">取 消</el-button>
      <el-button type="primary" @click="dialogClose()">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});
const dialogVisible = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emits("update:modelValue", val);
  },
});
const emits = defineEmits(["update:modelValue"]);
const dialogClose = () => {
  dialogVisible.value = false;
};
const form = reactive({
  name: "",
});
watch(
  () => dialogVisible.value,
  (newValue, oldValue) => {
    if (newValue) {
    }
  }
);
onMounted(() => {});
onUpdated(() => {});
</script>

<style scoped></style>
