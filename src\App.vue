<script setup>
import { useI18n } from "vue-i18n";
import { getElementLocales } from "@/locales";
import vueMxObject from "@/utils/VueMxGraphLoader";
import useSettingsStore from "@/store/modules/settings";
import useMenuStore from "@/store/modules/menu";
import useTabbarStore from "@/store/modules/tabbar";
import { GetLicenseFileContents } from "@/api/modules/license/license";
import { isFirstUseDetection, getConfigForWeb } from "@/api/modules/configuration/env_config";
import { dialogVisible, error_message } from "@/store/refs";
import { license_info } from "@/store/modules/license";
import storage from "@/utils/storage";
import useTaskJournalStore from "@/store/modules/taskJournal";
import WebSocketConnection from "@/utils/websocket";
import api from "./plugins/axios";
import { businessTopology } from "@/api/modules/business_topo";
import { GetAllBussiness } from "@/api/modules/topology/topology";
import { IsXmlInit, UpdateBusinessXml } from "@/api/modules/init_business/index";

import { startActivityMonitor } from "./constants/activityMonitor";
startActivityMonitor();
const { vueMxClient, vueMxUtils, vueMxGraph } = vueMxObject;
const router = useRouter();
const route = useRoute();
const settingsStore = useSettingsStore();
const menuStore = useMenuStore();
const tabbarStore = useTabbarStore();
const { t, te } = useI18n();
let graph;
provide("generateI18nTitle", generateI18nTitle);

const locales = computed(() => getElementLocales());
let kafka_url = import.meta.env.VITE_KAFKA_BASEURL + "/#/user/login?auto=true";
// const kafka_url = 'http://192.168.12.130:8000/#/user/login?auto=true'
const buttonConfig = ref({
  autoInsertSpace: true,
});
const enableWatermark = computed({
  get() {
    return settingsStore.app.enableWatermark;
  },
  set(newValue) {
    settingsStore.$patch((state) => {
      state.app.enableWatermark = newValue;
    });
  },
});

// 侧边栏主导航当前实际宽度
const mainSidebarActualWidth = computed(() => {
  let actualWidth = getComputedStyle(document.documentElement).getPropertyValue("--g-main-sidebar-width");
  actualWidth = parseInt(actualWidth);
  if (
    settingsStore.menu.menuMode === "single" ||
    (["head", "only-head"].includes(settingsStore.menu.menuMode) && settingsStore.mode !== "mobile")
  ) {
    actualWidth = 0;
  }
  return `${actualWidth}px`;
});

// 侧边栏次导航当前实际宽度
const subSidebarActualWidth = computed(() => {
  let actualWidth = getComputedStyle(document.documentElement).getPropertyValue("--g-sub-sidebar-width");
  actualWidth = parseInt(actualWidth);
  if (settingsStore.menu.subMenuCollapse && settingsStore.mode !== "mobile") {
    actualWidth = parseInt(
      getComputedStyle(document.documentElement).getPropertyValue("--g-sub-sidebar-collapse-width")
    );
  }
  if (["only-side", "only-head"].includes(settingsStore.menu.menuMode) && settingsStore.mode !== "mobile") {
    actualWidth = 0;
  }
  if (
    settingsStore.menu.subMenuOnlyOneHide &&
    menuStore.sidebarMenus.length === 1 &&
    (!menuStore.sidebarMenus[0].children ||
      menuStore.sidebarMenus[0]?.children.every((item) => item.meta.sidebar === false))
  ) {
    actualWidth = 0;
  }
  return `${actualWidth}px`;
});

watch(
  () => settingsStore.app.colorScheme,
  (val) => {
    if (val === "") {
      settingsStore.$patch((state) => {
        state.app.colorScheme =
          window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
      });
    } else {
      if (settingsStore.app.colorScheme === "dark") {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    }
  },
  {
    immediate: true,
  }
);

watch(
  () => settingsStore.mode,
  () => {
    if (settingsStore.mode === "pc") {
      settingsStore.$patch((state) => {
        state.menu.subMenuCollapse = settingsStore.subMenuCollapseLastStatus;
      });
    } else if (settingsStore.mode === "mobile") {
      settingsStore.$patch((state) => {
        state.menu.subMenuCollapse = true;
      });
    }
    document.body.setAttribute("data-mode", settingsStore.mode);
  },
  {
    immediate: true,
  }
);

watch(
  () => settingsStore.app.theme,
  () => {
    document.body.setAttribute("data-theme", settingsStore.app.theme);
  },
  {
    immediate: true,
  }
);

watch(
  () => settingsStore.menu.menuMode,
  () => {
    document.body.setAttribute("data-menu-mode", settingsStore.menu.menuMode);
  },
  {
    immediate: true,
  }
);

watch(
  () => settingsStore.layout.widthMode,
  () => {
    document.body.setAttribute("data-app-width-mode", settingsStore.layout.widthMode);
  },
  {
    immediate: true,
  }
);

watch(
  [() => settingsStore.app.enableDynamicTitle, () => settingsStore.title],
  () => {
    if (settingsStore.app.enableDynamicTitle && settingsStore.title) {
      let title = settingsStore.titleFirst
        ? settingsStore.title
        : generateI18nTitle(route.meta.i18n, settingsStore.title);
      document.title = `${title} - ${import.meta.env.VITE_APP_TITLE}`;
    } else {
      document.title = import.meta.env.VITE_APP_TITLE;
    }
    if (settingsStore.title && settingsStore.tabbar.enable) {
      tabbarStore.editTitle({
        tabId: tabbarStore.mergeTabs ? route.meta.activeMenu || route.fullPath : route.fullPath,
        title: settingsStore.title,
      });
    }
  },
  {
    immediate: true,
  }
);

watch(
  () => settingsStore.app.defaultLang,
  () => {
    api.defaults.headers.common["Accept-Language"] = settingsStore.app.defaultLang;
  },
  {
    immediate: true,
  }
);

onMounted(() => {
  getLicense();
  initialization();
  connectws();
  isInitXml();
  window.onresize = () => {
    settingsStore.setMode(document.documentElement.clientWidth);
  };
  window.onresize();
});

function generateI18nTitle(key, defaultTitle) {
  return settingsStore.toolbar.enableI18n && !!key && te(key)
    ? t(key)
    : typeof defaultTitle === "function"
    ? defaultTitle()
    : defaultTitle;
}
function isInitXml() {
  IsXmlInit()
    .then((res) => {
      if (res.data !== true) {
        getXml();
      }
    })
    .catch(() => {
      console.log("周报业务配置失败");
    });
}
//读取授权文件
function getLicense() {
  GetLicenseFileContents().then((res) => {
    license_info.value = res.data;
    if (license_info.value.license.oragnName == "重庆中联（内部开发测试）") {
      license_info.value.license.PackageNames = "旗舰版";
      enableWatermark.value = true;
    }
  });
}

function getStorge() {
  getConfigForWeb().then((res) => {
    Object.keys(res.data).forEach((key) => {
      storage.local.set(key, res.data[key]);
    });
  });
}

function initialization() {
  isFirstUseDetection().then((res) => {
    if (res.data == false) {
      return router.push({
        name: "global_settings",
      });
    }
    getStorge();
  });
}
// 监听全局ws连接缓存的变化，然后给这些连接注册全局监听
const taskJournalStore = useTaskJournalStore();
const wsConnections = ref([]);
watch(
  taskJournalStore.$state,
  () => {
    connectws();
  },
  { immediate: true }
);
// 创建ws连接
function connectws() {
  // 待连接数
  const taskList = taskJournalStore.getTaskList;
  for (let index = 0; index < taskList.length; index++) {
    const element = taskList[index];
    if (element.wsStatus == "wait") {
      // 创建连接
      const socket = new WebSocketConnection(element.wsUrl);
      wsConnections.value.push(socket);
      element.wsStatus = "open";
    }
  }

  // 待连接数大于0
  // if (waitTask.length > 0) {
  //   waitTask.forEach((element) => {

  //   });
  //   // 把所有的全部改成已连接
  //   // taskList.forEach((element) => {
  //   //   element.wsStatus = "open";
  //   // });
  //   // 重新赋值
  // }
  // taskJournalStore.editTask(taskList);
}
function getXml() {
  GetAllBussiness().then((result) => {
    let list = result.data;
    if (list.length > 0) {
      const formData = new FormData(); // 创建一个 FormData 实例
      let array = [];
      list.forEach((item, index) => {
        businessTopology(item.business_id)
          .then((res) => {
            if (!vueMxClient.isBrowserSupported()) {
              vueMxUtils.error("浏览器不支持", 200, false);
              return;
            }

            const container = document.getElementById("drawioContainer");
            graph = new vueMxGraph(container);
            const model = graph.getModel();
            graph.setResizeContainer(true);
            graph.setTooltips(false);
            graph.setPanning(true);

            // 执行布局并增加节点间距
            model.beginUpdate();
            try {
              let width;
              if (width === "") {
                width = container.offsetWidth;
              }

              let vertexMap = {};
              // 插入节点并保留原样式
              res.data.node.forEach((element, index) => {
                let style = {
                  [mxConstants.STYLE_SHAPE]: mxConstants.SHAPE_LABEL,
                  [mxConstants.STYLE_STROKECOLOR]: "none",
                  [mxConstants.STYLE_FILLCOLOR]: "none",
                  [mxConstants.STYLE_FONTCOLOR]: "#000000",
                  [mxConstants.STYLE_ALIGN]: mxConstants.ALIGN_CENTER,
                  [mxConstants.STYLE_VERTICAL_ALIGN]: mxConstants.ALIGN_BOTTOM,
                  [mxConstants.STYLE_FONTSIZE]: 12,
                  [mxConstants.STYLE_FONTWEIGHT]: "bold",
                  [mxConstants.STYLE_IMAGE_ALIGN]: mxConstants.ALIGN_CENTER,
                  [mxConstants.STYLE_IMAGE_VERTICAL_ALIGN]: mxConstants.ALIGN_TOP,
                  [mxConstants.STYLE_IMAGE]: `${element.img_url}`,
                  [mxConstants.STYLE_IMAGE_WIDTH]: 30,
                  [mxConstants.STYLE_IMAGE_HEIGHT]: 30,
                  [mxConstants.STYLE_SPACING_TOP]: 30,
                };

                graph.getStylesheet().putCellStyle(`${element.groupName + "-" + element.productName}`, style);
                const vertex = graph.insertVertex(
                  graph.getDefaultParent(),
                  null,
                  element.productName,
                  width / 2 - 80 + index * 50,
                  50,
                  200,
                  200,
                  `${element.groupName + "-" + element.productName}`
                );
                vertex.userData = element;
                const textSize = graph.getPreferredSizeForCell(vertex);
                vertex.geometry.width = textSize.width + 10;
                vertex.geometry.height = textSize.height + 10;
                vertexMap[index] = vertex; // 保存对节点的引用
              });

              res.data.link.forEach((element) => {
                let sourceNodes = [];
                let targetNodes = [];
                for (let key in vertexMap) {
                  if (vertexMap[key].userData.serverUrl === element.node_from_name) {
                    sourceNodes.push(vertexMap[key]);
                  }
                  if (vertexMap[key].userData.serverUrl === element.node_to_name) {
                    targetNodes.push(vertexMap[key]);
                  }
                }
                sourceNodes.forEach((sourceNode) => {
                  targetNodes.forEach((targetNode) => {
                    graph.insertEdge(
                      graph.getDefaultParent(),
                      null,
                      element.port,
                      sourceNode,
                      targetNode,
                      `port=${element.port}`
                    );
                  });
                });
              });

              const layout = new mxCompactTreeLayout(graph, false);
              layout.edgeRouting = false; // 禁用边缘路由
              layout.levelDistance = 50; // 设置层级之间的垂直间距
              layout.nodeDistance = 20; // 设置节点之间的水平间距
              layout.execute(graph.getDefaultParent());
              setTimeout(() => {
                graph.fit();
                graph.center();
              }, 100);
            } finally {
              model.endUpdate();

              const encoder = new mxCodec();
              const node = encoder.encode(graph.getModel());
              const xml = new XMLSerializer().serializeToString(node);
              array.push(JSON.stringify({ business_id: item.business_id, xml: xml }));
              // 使用 append 方法正确地将字段添加到 formData
              if (index === list.length - 1) {
                formData.append("business", JSON.stringify(array));
                UpdateBusinessXml(formData).then((res) => {
                  // 处理响应
                });
              }
            }
          })
          .catch(() => {
            console.log("未导入服务控制台");
          });
      });
    }
  });
}
</script>
<template>
  <iframe v-show="false" :src="kafka_url" />
  <el-config-provider
    :locale="locales[settingsStore.app.defaultLang]"
    :size="settingsStore.app.elementSize"
    :button="buttonConfig"
  >
    <RouterView
      :style="{
        '--g-main-sidebar-actual-width': mainSidebarActualWidth,
        '--g-sub-sidebar-actual-width': subSidebarActualWidth,
      }"
    />
    <!-- <ReloadPrompt /> -->
    <el-dialog v-model="dialogVisible" title="查看详情" width="50%" class="dialog">
      <ul>
        <li v-for="(item, index) in error_message" :key="index">
          {{ `${index + 1}:${JSON.stringify(item).replace(/{|}/g, "")}` }}
        </li>
      </ul>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-config-provider>
  <div v-show="false" id="drawioContainer"></div>
</template>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}

.contain {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.dialog {
  height: 50%;
  overflow-y: auto;

  ul {
    width: 100%;
    height: 100%;

    li {
      white-space: nowrap;
      width: 100%;
      // line-height: 10%;
      overflow-x: auto;
    }
  }
}

.dialog-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.margin-top {
  min-width: 550px;
  padding-bottom: 10px;
}

ul {
  list-style: none;
}

.el-notification.right {
  width: 250px;
}
</style>
