<script setup>
import storage from '@/utils/storage'
import { onMounted } from 'vue'
let url  = import.meta.env.VITE_KAFKA_BASEURL+'/#/custom/topic/list';
// let url = 'http://**************:8000/#/topic/list'
const router = useRouter()
const route = useRoute()
const iframeRef = ref()
const iframe = ref({
    loading: true,
    src: ''
})
onMounted(()=>{
    getData()
})
function getData(){
 iframe.value.src = url
    iframeRef.value.onload = () => {
        iframe.value.loading = false
    }
}
</script>

<template>
    <div>
        <div v-loading="iframe.loading" class="iframe">
        <iframe ref="iframeRef" :src="iframe.src" frameborder="0" />
    </div>
    </div>
</template>

<style lang="scss" scoped>
.iframe,
iframe {
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
}
</style>
