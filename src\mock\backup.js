import Mock from 'mockjs'

const AllList = []
for (let i = 0; i < 50; i++) {
    AllList.push(Mock.mock({
        id: '@id',
        title: '@ctitle(10, 20)'
    }))
}

export default [
    {
        url: '/mock/backup/list',
        method: 'post',
        response:  {
                error: '',
                status: 1,
                data: {"database_overview": {
                    "backup_instance": 3,
                    "backup_information": [
                        {
                            "backup_input_io": 1572079446.4203823,
                            "backup_input_total": 1974531784704,
                            "backup_output_io": 16867732.382165603,
                            "backup_output_total": 21185871872,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:20:56",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 84796639.40729187,
                            "backup_input_total": 434921963520,
                            "backup_output_io": 21810414.480795477,
                            "backup_output_total": 111865615872,
                            "backup_status": "COMPLETED",
                            "backup_take": "01:25:29",
                            "name": "webemr"
                        },
                        {
                            "backup_input_io": 913490673.6081772,
                            "backup_input_total": 2144876101632,
                            "backup_output_io": 21957829.124361157,
                            "backup_output_total": 51556982784,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:39:08",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 41664026.86349489,
                            "backup_input_total": 93702396416,
                            "backup_output_io": 14760957.040462427,
                            "backup_output_total": 33197392384,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:37:29",
                            "name": "ris"
                        },
                        {
                            "backup_input_io": 84120843.9846508,
                            "backup_input_total": 438437838848,
                            "backup_output_io": 21685051.82501919,
                            "backup_output_total": 113022490112,
                            "backup_status": "COMPLETED",
                            "backup_take": "01:26:52",
                            "name": "webemr"
                        },
                        {
                            "backup_input_io": 142049379.54115435,
                            "backup_input_total": 1963974721536,
                            "backup_output_io": 36776568.79733835,
                            "backup_output_total": 508472840192,
                            "backup_status": "COMPLETED",
                            "backup_take": "03:50:26",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 811947148.1753515,
                            "backup_input_total": 1963288204288,
                            "backup_output_io": 279661.4722911497,
                            "backup_output_total": 676221440,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:40:18",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 41639952.16362828,
                            "backup_input_total": 93648252416,
                            "backup_output_io": 14750798.996887505,
                            "backup_output_total": 33174546944,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:37:29",
                            "name": "ris"
                        },
                        {
                            "backup_input_io": 69742951.18869303,
                            "backup_input_total": 2195856818176,
                            "backup_output_io": 69208960.80088931,
                            "backup_output_total": 2179044130816,
                            "backup_status": "COMPLETED",
                            "backup_take": "08:44:45",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 41374600.98245614,
                            "backup_input_total": 94334090240,
                            "backup_output_io": 14651288.252631579,
                            "backup_output_total": 33404937216,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:38:00",
                            "name": "ris"
                        },
                        {
                            "backup_input_io": 247093740.97725737,
                            "backup_input_total": 2194686607360,
                            "backup_output_io": 29394296.07385724,
                            "backup_output_total": 261080137728,
                            "backup_status": "COMPLETED",
                            "backup_take": "02:28:02",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 84414422.62626262,
                            "backup_input_total": 434565447680,
                            "backup_output_io": 21707985.454545453,
                            "backup_output_total": 111752709120,
                            "backup_status": "COMPLETED",
                            "backup_take": "01:25:48",
                            "name": "webemr"
                        },
                        {
                            "backup_input_io": 41732455.01195219,
                            "backup_input_total": 94273615872,
                            "backup_output_io": 14775205.793714033,
                            "backup_output_total": 33377189888,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:37:39",
                            "name": "ris"
                        },
                        {
                            "backup_input_io": 283930119.8687847,
                            "backup_input_total": 2198470918144,
                            "backup_output_io": 9147301.674286453,
                            "backup_output_total": 70827556864,
                            "backup_status": "COMPLETED",
                            "backup_take": "02:09:03",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 226819751.6777583,
                            "backup_input_total": 2201739329536,
                            "backup_output_io": 26459116.9061502,
                            "backup_output_total": 256838647808,
                            "backup_status": "COMPLETED",
                            "backup_take": "02:41:47",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 224765974.57901183,
                            "backup_input_total": 2201807486976,
                            "backup_output_io": 25875338.505512454,
                            "backup_output_total": 253474816000,
                            "backup_status": "COMPLETED",
                            "backup_take": "02:43:16",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 84994532.2463571,
                            "backup_input_total": 437466857472,
                            "backup_output_io": 21894153.052263454,
                            "backup_output_total": 112689205760,
                            "backup_status": "COMPLETED",
                            "backup_take": "01:25:47",
                            "name": "webemr"
                        },
                        {
                            "backup_input_io": 41574929.75722543,
                            "backup_input_total": 93502017024,
                            "backup_output_io": 14752471.13561583,
                            "backup_output_total": 33178307584,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:37:29",
                            "name": "ris"
                        },
                        {
                            "backup_input_io": 1109520256.4124596,
                            "backup_input_total": 2065926717440,
                            "backup_output_io": 15123949.851772288,
                            "backup_output_total": 28160794624,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:31:02",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 1082990656.775962,
                            "backup_input_total": 2054433275904,
                            "backup_output_io": 12564796.322614655,
                            "backup_output_total": 23835418624,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:31:37",
                            "name": "his"
                        },
                        {
                            "backup_input_io": 84998889.74784651,
                            "backup_input_total": 434174328832,
                            "backup_output_io": 21847133.218480814,
                            "backup_output_total": 111595156480,
                            "backup_status": "COMPLETED",
                            "backup_take": "01:25:08",
                            "name": "webemr"
                        },
                        {
                            "backup_input_io": 85297003.12770137,
                            "backup_input_total": 434161745920,
                            "backup_output_io": 21923442.97367387,
                            "backup_output_total": 111590324736,
                            "backup_status": "COMPLETED",
                            "backup_take": "01:24:50",
                            "name": "webemr"
                        },
                        {
                            "backup_input_io": 41549964.95271763,
                            "backup_input_total": 94027570688,
                            "backup_output_io": 14723745.315068493,
                            "backup_output_total": 33319835648,
                            "backup_status": "COMPLETED",
                            "backup_take": "00:37:43",
                            "name": "ris"
                        },
                        {
                            "backup_input_io": 207805109.14832717,
                            "backup_input_total": 2192551706624,
                            "backup_output_io": 22763413.43645152,
                            "backup_output_total": 240176775168,
                            "backup_status": "COMPLETED",
                            "backup_take": "02:55:51",
                            "name": "his"
                        }
                    ],
                    "capacity_trends": [
                        {
                            "backup_input_total": 2198470918144,
                            "backup_output_total": 70827556864,
                            "database_name": "his",
                            "date": "2023-12-31"
                        },
                        {
                            "backup_input_total": 2195856818176,
                            "backup_output_total": 2179044130816,
                            "database_name": "his",
                            "date": "2023-12-31"
                        },
                        {
                            "backup_input_total": 94273615872,
                            "backup_output_total": 33377189888,
                            "database_name": "ris",
                            "date": "2024-01-04"
                        },
                        {
                            "backup_input_total": 2065926717440,
                            "backup_output_total": 28160794624,
                            "database_name": "his",
                            "date": "2024-01-03"
                        },
                        {
                            "backup_input_total": 2201739329536,
                            "backup_output_total": 256838647808,
                            "database_name": "his",
                            "date": "2024-01-03"
                        },
                        {
                            "backup_input_total": 94027570688,
                            "backup_output_total": 33319835648,
                            "database_name": "ris",
                            "date": "2024-01-02"
                        },
                        {
                            "backup_input_total": 93502017024,
                            "backup_output_total": 33178307584,
                            "database_name": "ris",
                            "date": "2024-01-01"
                        },
                        {
                            "backup_input_total": 2054433275904,
                            "backup_output_total": 23835418624,
                            "database_name": "his",
                            "date": "2024-01-02"
                        },
                        {
                            "backup_input_total": 2201807486976,
                            "backup_output_total": 253474816000,
                            "database_name": "his",
                            "date": "2024-01-02"
                        },
                        {
                            "backup_input_total": 434174328832,
                            "backup_output_total": 111595156480,
                            "database_name": "webemr",
                            "date": "2023-12-31"
                        },
                        {
                            "backup_input_total": 434161745920,
                            "backup_output_total": 111590324736,
                            "database_name": "webemr",
                            "date": "2023-12-30"
                        },
                        {
                            "backup_input_total": 438437838848,
                            "backup_output_total": 113022490112,
                            "database_name": "webemr",
                            "date": "2024-01-03"
                        },
                        {
                            "backup_input_total": 1963974721536,
                            "backup_output_total": 508472840192,
                            "database_name": "his",
                            "date": "2023-12-29"
                        },
                        {
                            "backup_input_total": 2194686607360,
                            "backup_output_total": 261080137728,
                            "database_name": "his",
                            "date": "2023-12-29"
                        },
                        {
                            "backup_input_total": 434921963520,
                            "backup_output_total": 111865615872,
                            "database_name": "webemr",
                            "date": "2024-01-01"
                        },
                        {
                            "backup_input_total": 1974531784704,
                            "backup_output_total": 21185871872,
                            "database_name": "his",
                            "date": "2024-01-01"
                        },
                        {
                            "backup_input_total": 434565447680,
                            "backup_output_total": 111752709120,
                            "database_name": "webemr",
                            "date": "2023-12-29"
                        },
                        {
                            "backup_input_total": 1963288204288,
                            "backup_output_total": 676221440,
                            "database_name": "his",
                            "date": "2024-01-01"
                        },
                        {
                            "backup_input_total": 94334090240,
                            "backup_output_total": 33404937216,
                            "database_name": "ris",
                            "date": "2024-01-03"
                        },
                        {
                            "backup_input_total": 93648252416,
                            "backup_output_total": 33174546944,
                            "database_name": "ris",
                            "date": "2023-12-31"
                        },
                        {
                            "backup_input_total": 437466857472,
                            "backup_output_total": 112689205760,
                            "database_name": "webemr",
                            "date": "2024-01-02"
                        },
                        {
                            "backup_input_total": 2144876101632,
                            "backup_output_total": 51556982784,
                            "database_name": "his",
                            "date": "2023-12-30"
                        },
                        {
                            "backup_input_total": 2192551706624,
                            "backup_output_total": 240176775168,
                            "database_name": "his",
                            "date": "2023-12-30"
                        },
                        {
                            "backup_input_total": 93702396416,
                            "backup_output_total": 33197392384,
                            "database_name": "ris",
                            "date": "2023-12-30"
                        }
                    ],
                    "time_period": [
                        {
                            "begin_time": "2023-12-30T15:30:05.000Z",
                            "database_name": "webemr",
                            "over_time": "2023-12-30T16:54:55.000Z"
                        },
                        {
                            "begin_time": "2023-12-31T14:26:13.000Z",
                            "database_name": "his",
                            "over_time": "2023-12-31T23:10:58.000Z"
                        },
                        {
                            "begin_time": "2023-12-29T16:31:55.000Z",
                            "database_name": "his",
                            "over_time": "2023-12-29T20:22:21.000Z"
                        },
                        {
                            "begin_time": "2024-01-01T09:55:04.000Z",
                            "database_name": "ris",
                            "over_time": "2024-01-01T10:32:33.000Z"
                        },
                        {
                            "begin_time": "2024-01-01T16:32:04.000Z",
                            "database_name": "his",
                            "over_time": "2024-01-01T17:12:22.000Z"
                        },
                        {
                            "begin_time": "2023-12-31T15:30:05Z",
                            "database_name": "webemr",
                            "over_time": "2023-12-31T16:55:13Z"
                        },
                        {
                            "begin_time": "2023-12-29T14:29:13Z",
                            "database_name": "his",
                            "over_time": "2023-12-29T16:57:15Z"
                        },
                        {
                            "begin_time": "2024-01-03T16:32:07.000Z",
                            "database_name": "his",
                            "over_time": "2024-01-03T17:03:09.000Z"
                        },
                        {
                            "begin_time": "2024-01-02T16:32:07.000Z",
                            "database_name": "his",
                            "over_time": "2024-01-02T17:03:44.000Z"
                        },
                        {
                            "begin_time": "2023-12-29T16:31:55Z",
                            "database_name": "his",
                            "over_time": "2023-12-29T20:22:21Z"
                        },
                        {
                            "begin_time": "2023-12-29T15:30:06Z",
                            "database_name": "webemr",
                            "over_time": "2023-12-29T16:55:54Z"
                        },
                        {
                            "begin_time": "2024-01-04T09:55:05.000Z",
                            "database_name": "ris",
                            "over_time": "2024-01-04T10:32:44.000Z"
                        },
                        {
                            "begin_time": "2023-12-30T14:29:04Z",
                            "database_name": "his",
                            "over_time": "2023-12-30T17:24:55Z"
                        },
                        {
                            "begin_time": "2024-01-02T15:30:06.000Z",
                            "database_name": "webemr",
                            "over_time": "2024-01-02T16:55:53.000Z"
                        },
                        {
                            "begin_time": "2023-12-29T15:30:06.000Z",
                            "database_name": "webemr",
                            "over_time": "2023-12-29T16:55:54.000Z"
                        },
                        {
                            "begin_time": "2023-12-30T16:31:57Z",
                            "database_name": "his",
                            "over_time": "2023-12-30T17:11:05Z"
                        },
                        {
                            "begin_time": "2024-01-01T14:29:24.000Z",
                            "database_name": "his",
                            "over_time": "2024-01-01T14:50:20.000Z"
                        },
                        {
                            "begin_time": "2023-12-31T09:55:05.000Z",
                            "database_name": "ris",
                            "over_time": "2023-12-31T10:32:34.000Z"
                        },
                        {
                            "begin_time": "2024-01-03T15:30:05.000Z",
                            "database_name": "webemr",
                            "over_time": "2024-01-03T16:56:57.000Z"
                        },
                        {
                            "begin_time": "2023-12-31T16:32:02Z",
                            "database_name": "his",
                            "over_time": "2023-12-31T18:41:05Z"
                        },
                        {
                            "begin_time": "2024-01-03T09:55:04.000Z",
                            "database_name": "ris",
                            "over_time": "2024-01-03T10:33:04.000Z"
                        },
                        {
                            "begin_time": "2023-12-29T14:29:13.000Z",
                            "database_name": "his",
                            "over_time": "2023-12-29T16:57:15.000Z"
                        },
                        {
                            "begin_time": "2024-01-02T09:55:05.000Z",
                            "database_name": "ris",
                            "over_time": "2024-01-02T10:32:48.000Z"
                        },
                        {
                            "begin_time": "2024-01-01T15:30:05Z",
                            "database_name": "webemr",
                            "over_time": "2024-01-01T16:55:34Z"
                        },
                        {
                            "begin_time": "2023-12-30T16:31:57.000Z",
                            "database_name": "his",
                            "over_time": "2023-12-30T17:11:05.000Z"
                        },
                        {
                            "begin_time": "2023-12-31T16:32:02.000Z",
                            "database_name": "his",
                            "over_time": "2023-12-31T18:41:05.000Z"
                        },
                        {
                            "begin_time": "2024-01-01T14:29:24Z",
                            "database_name": "his",
                            "over_time": "2024-01-01T14:50:20Z"
                        },
                        {
                            "begin_time": "2023-12-30T14:29:04.000Z",
                            "database_name": "his",
                            "over_time": "2023-12-30T17:24:55.000Z"
                        },
                        {
                            "begin_time": "2024-01-01T15:30:05.000Z",
                            "database_name": "webemr",
                            "over_time": "2024-01-01T16:55:34.000Z"
                        },
                        {
                            "begin_time": "2024-01-03T14:29:17.000Z",
                            "database_name": "his",
                            "over_time": "2024-01-03T17:11:04.000Z"
                        },
                        {
                            "begin_time": "2023-12-31T15:30:05.000Z",
                            "database_name": "webemr",
                            "over_time": "2023-12-31T16:55:13.000Z"
                        },
                        {
                            "begin_time": "2023-12-31T09:55:05Z",
                            "database_name": "ris",
                            "over_time": "2023-12-31T10:32:34Z"
                        },
                        {
                            "begin_time": "2023-12-30T09:55:04.000Z",
                            "database_name": "ris",
                            "over_time": "2023-12-30T10:32:33.000Z"
                        },
                        {
                            "begin_time": "2023-12-30T09:55:04Z",
                            "database_name": "ris",
                            "over_time": "2023-12-30T10:32:33Z"
                        },
                        {
                            "begin_time": "2024-01-02T14:29:15.000Z",
                            "database_name": "his",
                            "over_time": "2024-01-02T17:12:31.000Z"
                        },
                        {
                            "begin_time": "2024-01-01T16:32:04Z",
                            "database_name": "his",
                            "over_time": "2024-01-01T17:12:22Z"
                        },
                        {
                            "begin_time": "2023-12-30T15:30:05Z",
                            "database_name": "webemr",
                            "over_time": "2023-12-30T16:54:55Z"
                        },
                        {
                            "begin_time": "2024-01-01T09:55:04Z",
                            "database_name": "ris",
                            "over_time": "2024-01-01T10:32:33Z"
                        }
                    ],
                    "warning_list": []
                }
        }}
    },
    {
        url: '/mock/backup/detail',
        method: 'get',
        response: option => {
            let info = AllList.filter(item => item.id == option.query.id)
            return {
                error: '',
                status: 1,
                data: info[0]
            }
        }
    },
    {
        url: '/mock/backup/information',
        method: 'post',
        response: {
            error: '',
            status: 1,
            data:[
                {
                    "database_information": {
                        "name": "webemr",
                        "version": "\"19.0.0.0.0\"",
                        "status": "\"OPEN\"",
                        "start_time": "2023-12-05T17:00:06Z",
                        "end_time": "2023-12-05T17:01:30Z",
                        "type": "DB INCR",
                        "recent_status": "COMPLETED",
                        "backup_history": [
                            {
                                "date": "2023-11-16",
                                "type": "DB INCR",
                                "input_size": "   24.88G",
                                "output_size": "   14.34G",
                                "backup_duration": 45.0
                            },
                            {
                                "date": "2023-11-10",
                                "type": "DB INCR",
                                "input_size": "   19.57G",
                                "output_size": "   18.87G",
                                "backup_duration": 56.0
                            },
                            {
                                "date": "2023-11-11",
                                "type": "DB INCR",
                                "input_size": "   28.72G",
                                "output_size": "   20.08G",
                                "backup_duration": 40
                            },
                            {
                                "date": "2023-11-06",
                                "type": "DB INCR",
                                "input_size": "   20.22G",
                                "output_size": "   12.85G",
                                "backup_duration": 37.0
                            },
                            {
                                "date": "2023-11-07",
                                "type": "DB INCR",
                                "input_size": "   20.16G",
                                "output_size": "   11.49G",
                                "backup_duration": 37.0
                            },
                            {
                                "date": "2023-11-12",
                                "type": "DB INCR",
                                "input_size": "   26.26G",
                                "output_size": "   16.65G",
                                "backup_duration": 35.0
                            },
                            {
                                "date": "2023-11-13",
                                "type": "DB INCR",
                                "input_size": "   27.09G",
                                "output_size": "   17.49G",
                                "backup_duration": 49.0
                            },
                            {
                                "date": "2023-11-14",
                                "type": "DB INCR",
                                "input_size": "   24.99G",
                                "output_size": "   14.51G",
                                "backup_duration": 41
                            },
                            {
                                "date": "2023-11-17",
                                "type": "DB INCR",
                                "input_size": "   24.78G",
                                "output_size": "   24.12G",
                                "backup_duration": 61.0
                            },
                            {
                                "date": "2023-11-19",
                                "type": "DB INCR",
                                "input_size": "   21.45G",
                                "output_size": "   10.46G",
                                "backup_duration": 41
                            }
                        ],
                        "dbip": "**********"
                    },
                    "backup_trend": {
                        "use_trends": [
                            {
                                "value": 0.12199503580729167,
                                "time": "2023-10-09 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.12446794649061849,
                                "time": "2023-10-10 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.11682972239183531,
                                "time": "2023-10-11 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.12044542100694444,
                                "time": "2023-10-12 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.1399468315972222,
                                "time": "2023-10-13 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14015163845486112,
                                "time": "2023-10-14 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14388427734375,
                                "time": "2023-10-15 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14453871503648366,
                                "time": "2023-10-16 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.13422987196180555,
                                "time": "2023-10-17 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.1416042751736111,
                                "time": "2023-10-18 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.13757527669270833,
                                "time": "2023-10-19 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.143218994140625,
                                "time": "2023-10-20 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14168226453993055,
                                "time": "2023-10-21 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14978773328993056,
                                "time": "2023-10-22 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.13657827158617095,
                                "time": "2023-10-23 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14257676866319444,
                                "time": "2023-10-24 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14022352430555557,
                                "time": "2023-10-25 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.1424282497829861,
                                "time": "2023-10-26 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.1484402126736111,
                                "time": "2023-10-27 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14784749348958334,
                                "time": "2023-10-28 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.1507568359375,
                                "time": "2023-10-29 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14137301974826388,
                                "time": "2023-10-30 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14379408094618057,
                                "time": "2023-10-31 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14051174587673612,
                                "time": "2023-11-01 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14418650104239056,
                                "time": "2023-11-02 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.1357998318142361,
                                "time": "2023-11-03 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14880167643229167,
                                "time": "2023-11-04 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14596761067708333,
                                "time": "2023-11-05 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14007161458333334,
                                "time": "2023-11-06 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.1522793240017361,
                                "time": "2023-11-07 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14486829969618056,
                                "time": "2023-11-08 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.14374186197916666,
                                "time": "2023-11-09 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.11909548975840337,
                                "time": "2023-11-10 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.12372775607638889,
                                "time": "2023-11-11 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.13415009880993745,
                                "time": "2023-11-12 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.12165459526909722,
                                "time": "2023-11-13 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.1366943359375,
                                "time": "2023-11-14 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.3287875705295139,
                                "time": "2023-11-15 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.16150783962673612,
                                "time": "2023-11-16 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.16651407877604166,
                                "time": "2023-11-17 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.19786079199965254,
                                "time": "2023-11-18 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.16715630425347222,
                                "time": "2023-11-19 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.16247490776909723,
                                "time": "2023-11-20 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.16706882052951388,
                                "time": "2023-11-21 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.18968573676215278,
                                "time": "2023-11-22 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.47352362738715276,
                                "time": "2023-11-23 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.6868410408703961,
                                "time": "2023-11-24 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.19966634114583334,
                                "time": "2023-11-25 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.5569864908854166,
                                "time": "2023-11-26 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.18739556206597222,
                                "time": "2023-11-27 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.31506144205729164,
                                "time": "2023-11-28 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.2697258843315972,
                                "time": "2023-11-29 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.3055365668402778,
                                "time": "2023-11-30 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.28091295030381946,
                                "time": "2023-12-01 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.3672967609885337,
                                "time": "2023-12-02 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.31153089735243056,
                                "time": "2023-12-03 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.2849765353732639,
                                "time": "2023-12-04 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.34896255120998476,
                                "time": "2023-12-05 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.29089558919270836,
                                "time": "2023-12-06 06:27:23",
                                "unit": ""
                            },
                            {
                                "value": 0.2676595052083333,
                                "time": "2023-12-07 06:27:23",
                                "unit": ""
                            }
                        ],
                        "speed_trends": [
                            {
                                "date": "2023-11-06",
                                "input_speed": "559.60MB/s",
                                "output_speed": "355.63MB/s"
                            },
                            {
                                "date": "2023-11-07",
                                "input_speed": "557.94MB/s",
                                "output_speed": "317.99MB/s"
                            },
                            {
                                "date": "2023-11-10",
                                "input_speed": "357.85MB/s",
                                "output_speed": "345.05MB/s"
                            },
                            {
                                "date": "2023-11-11",
                                "input_speed": "735.23MB/s",
                                "output_speed": "514.05MB/s"
                            },

                            {
                                "date": "2023-11-12",
                                "input_speed": "768.29MB/s",
                                "output_speed": "487.13MB/s"
                            },
                            {
                                "date": "2023-11-13",
                                "input_speed": "566.13MB/s",
                                "output_speed": "365.51MB/s"
                            },
                            {
                                "date": "2023-11-14",
                                "input_speed": "624.14MB/s",
                                "output_speed": "362.40MB/s"
                            },
                            {
                                "date": "2023-11-16",
                                "input_speed": "566.16MB/s",
                                "output_speed": "326.31MB/s"
                            },

                            {
                                "date": "2023-11-17",
                                "input_speed": "415.98MB/s",
                                "output_speed": "404.90MB/s"
                            },
                            {
                                "date": "2023-11-19",
                                "input_speed": "535.73MB/s",
                                "output_speed": "261.24MB/s"
                            }
                        ]
                    }
                }
            ]
            // data: [
            //     {
            //         "database_information": {
            //             "name": "HIS",
            //             "version": "V1.0",
            //             "status": "open",
            //             "start_time": "2023-11-22T17:00:09.000Z",
            //             "end_time": "2023-11-22T18:00:09.000Z",
            //             "type": "DB",
            //             "recent_status": "正常",
            //             "backup_history": [
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 },
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 }
            //             ],
            //         },
            //         "backup_trend": {
            //             "speed_trends": [
            //                 {
            //                     "date": "2023-11-30 9.00",
            //                     "input_speed": 2,
            //                     "output_speed": 3
            //                 },
            //                 {
            //                     "date": "2023-11-30 10.00",
            //                     "input_speed": 4,
            //                     "output_speed": 5
            //                 },{
            //                     "date": "2023-11-30 11.00",
            //                     "input_speed": 1,
            //                     "output_speed": 5
            //                 },
            //                 {
            //                     "date": "2023-11-30 12.00",
            //                     "input_speed": 3,
            //                     "output_speed": 6
            //                 },
            //                 {
            //                     "date": "2023-11-30 13.00",
            //                     "input_speed": 2,
            //                     "output_speed": 5
            //                 },
            //                 {
            //                     "date": "2023-11-30 14.00",
            //                     "input_speed": 2,
            //                     "output_speed": 3
            //                 },
            //                 {
            //                     "date": "2023-11-30 15.00",
            //                     "input_speed": 3,
            //                     "output_speed": 2
            //                 },
            //                 {
            //                     "date": "2023-11-30 16.00",
            //                     "input_speed": 1,
            //                     "output_speed": 1
            //                 },{
            //                     "date": "2023-11-30 17.00",
            //                     "input_speed": 3,
            //                     "output_speed": 3
            //                 },{
            //                     "date": "2023-11-30 18.00",
            //                     "input_speed": 6,
            //                     "output_speed":3
            //                 }
            //             ],
            //             "use_trends": [
            //                 {
            //                     "date": "2023-11-30 9.00",
            //                     "value": 11,
            //                     "unit": "%"
            //                 },
            //                 {
            //                     "date": "2023-11-30 10.00",
            //                     "value": 14,
            //                     "unit": "%"
            //                 },{
            //                     "date": "2023-11-30 11.00",
            //                     "value": 22,
            //                     "unit": "%"
            //                 },{
            //                     "date": "2023-11-30 12.00",
            //                     "value": 34,
            //                     "unit": "%"
            //                 },{
            //                     "date": "2023-11-30 13.00",
            //                     "value": 30,
            //                     "unit": "%"
            //                 },{
            //                     "date": "2023-11-30 14.00",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "2023-11-30 15.00",
            //                     "value": 40,
            //                     "unit": "%"
            //                 },{
            //                     "date": "2023-11-30 16.00",
            //                     "value": 50,
            //                     "unit": "%"
            //                 },{
            //                     "date": "2023-11-30 17.00",
            //                     "value": 60,
            //                     "unit": "%"
            //                 },{
            //                     "date": "2023-11-30 18.00",
            //                     "value": 33,
            //                     "unit": "%"
            //                 },{
            //                     "date": "2023-11-30 19.00",
            //                     "value": 50,
            //                     "unit": "%"
            //                 }
            //             ]
            //         }
            //     },
            //     {
            //         "database_information": {
            //             "name": "HIS",
            //             "version": "V1.0",
            //             "status": "open",
            //             "start_time": "2023-11-22T17:00:09.000Z",
            //             "end_time": "2023-11-22T18:00:09.000Z",
            //             "type": "DB INCR",
            //             "recent_status": "COMPLETED",
            //             "backup_history": [
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 }
            //             ],
            //         },
            //         "backup_trend": {
            //             "speed_trends": [
            //                 {
            //                     "date": "2023-02-19",
            //                     "input_speed": 2,
            //                     "output_speed": 3
            //                 },
            //                 {
            //                     "date": "2023-03-19",
            //                     "input_speed": 4,
            //                     "output_speed": 5
            //                 },{
            //                     "date": "2023-04-19",
            //                     "input_speed": 1,
            //                     "output_speed": 5
            //                 },
            //                 {
            //                     "date": "2023-05-19",
            //                     "input_speed": 3,
            //                     "output_speed": 6
            //                 },
            //                 {
            //                     "date": "2023-06-19",
            //                     "input_speed": 2,
            //                     "output_speed": 5
            //                 },
            //                 {
            //                     "date": "2023-07-19",
            //                     "input_speed": 2,
            //                     "output_speed": 3
            //                 },
            //                 {
            //                     "date": "2023-08-19",
            //                     "input_speed": 3,
            //                     "output_speed": 2
            //                 },
            //                 {
            //                     "date": "2023-09-19",
            //                     "input_speed": 1,
            //                     "output_speed": 1
            //                 },{
            //                     "date": "2023-10-19",
            //                     "input_speed": 3,
            //                     "output_speed": 3
            //                 },{
            //                     "date": "2023-11-19",
            //                     "input_speed": 6,
            //                     "output_speed":3
            //                 }
            //             ],
            //             "use_trends": [
            //                 {
            //                     "date": "1981-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },
            //                 {
            //                     "date": "1981-12-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1982-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1983-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1984-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1985-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1986-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1987-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1988-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1989-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1991-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 }
            //             ]
            //         }
            //     },
            //     {
            //         "database_information": {
            //             "name": "HIS",
            //             "version": "V1.0",
            //             "status": "open",
            //             "start_time": "2023-11-22T17:00:09.000Z",
            //             "end_time": "2023-11-22T18:00:09.000Z",
            //             "type": "DB INCR",
            //             "recent_status": "COMPLETED",
            //             "backup_history": [
            //                 {
            //                     "date": "1981-11-17",
            //                     "type": "增量备份",
            //                     "input_size": "2gb",
            //                     "output_size": "3gb",
            //                     "backup_duration": "50000"
            //                 }
            //             ],
            //         },
            //         "backup_trend": {
            //             "speed_trends": [
            //                 {
            //                     "date": "2023-02-19",
            //                     "input_speed": 2,
            //                     "output_speed": 3
            //                 },
            //                 {
            //                     "date": "2023-03-19",
            //                     "input_speed": 4,
            //                     "output_speed": 5
            //                 },{
            //                     "date": "2023-04-19",
            //                     "input_speed": 1,
            //                     "output_speed": 5
            //                 },
            //                 {
            //                     "date": "2023-05-19",
            //                     "input_speed": 3,
            //                     "output_speed": 6
            //                 },
            //                 {
            //                     "date": "2023-06-19",
            //                     "input_speed": 2,
            //                     "output_speed": 5
            //                 },
            //                 {
            //                     "date": "2023-07-19",
            //                     "input_speed": 2,
            //                     "output_speed": 3
            //                 },
            //                 {
            //                     "date": "2023-08-19",
            //                     "input_speed": 3,
            //                     "output_speed": 2
            //                 },
            //                 {
            //                     "date": "2023-09-19",
            //                     "input_speed": 1,
            //                     "output_speed": 1
            //                 },{
            //                     "date": "2023-10-19",
            //                     "input_speed": 3,
            //                     "output_speed": 3
            //                 },{
            //                     "date": "2023-11-19",
            //                     "input_speed": 6,
            //                     "output_speed":3
            //                 }
            //             ],
            //             "use_trends": [
            //                 {
            //                     "date": "1981-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },
            //                 {
            //                     "date": "1981-12-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1982-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1983-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1984-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1985-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1986-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1987-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1988-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1989-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 },{
            //                     "date": "1991-11-29",
            //                     "value": 20,
            //                     "unit": "%"
            //                 }
            //             ]
            //         }
            //     }
            // ]
        }
    },
    {
        url: '/mock/backup/edit',
        method: 'post',
        response: {
            error: '',
            status: 1,
            data: {
                isSuccess: true
            }
        }
    },
    {
        url: '/mock/backup/delete',
        method: 'post',
        response: {
            error: '',
            status: 1,
            data: {
                isSuccess: true
            }
        }
    }
]
