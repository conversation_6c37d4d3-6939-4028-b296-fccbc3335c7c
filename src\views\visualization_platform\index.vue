<script setup name="VisualizationPlatformIndedx">
import { GetKibana } from "@/api/modules/screen_management/screen";
import { useRouter } from "vue-router";
import storage from "@/utils/storage";
let url = storage.local.get("VITE_APP_ANALYSIS");
const router = useRouter();
const data = reactive({
  dataList: [],
  loading: false,
});
onMounted(() => {
  getData();
});
function getData() {
  data.loading = true;
  GetKibana()
    .then((res) => {
      // 名称排序
      const resData = res.data.sort((a, b) => {
        return a.dashboard_name > b.dashboard_name ? 1 : b.dashboard_name > a.dashboard_name ? -1 : 0;
      });
      let tagsList = [];
      let otherTag = { name: "其他", panle: [] };
      // 先处理标签，在处理数据
      resData.forEach((item) => {
        // 没用tag
        if (item.dashboard_tags.length > 0) {
          if (!tagsList[item.dashboard_tags[0].id]) {
            tagsList[item.dashboard_tags[0].id] = { ...item.dashboard_tags[0], panle: [] };
          }
          tagsList[item.dashboard_tags[0].id]["panle"].push(item);
        } else {
          otherTag["panle"].push(item);
        }
      });
      for (const key in tagsList) {
        data.dataList.push(tagsList[key]);
      }
      data.dataList.push(otherTag);
    })
    .finally(() => {
      data.loading = false;
    });
}

function iframe(item) {
  router.push({
    name: "analysis_iframe",
    query: {
      url:
        url +
        item.dashboard_id +
        "?embed=true&_g=(filters%3A!()%2CrefreshInterval%3A(pause%3A!t%2Cvalue%3A0)%2Ctime%3A(from%3Anow-24h%2Cto%3Anow))&show-top-menu=false&show-query-input=true&show-time-filter=true",
    },
  });
}
</script>

<template>
  <div>
    <page-main v-loading="data.loading" title="运营分析平台面板">
      <div class="tab-container">
        <div v-for="(item, key) in data.dataList" :key="key">
          <div>
            <el-icon size="18px"><svg-icon name="type" /></el-icon>
            <span class="text-20px font-bold pl-5">{{ item.name }}</span>
          </div>
          <div class="flex flex-wrap my-8px ml-20px text-14px">
            <template v-for="(items, index) in item.panle" :key="index">
              <div
                @click.stop="iframe(items)"
                class="mr-20px flex items-center cursor-pointer b-1 px-10px py-14px mb-14px b-gray rd-8px hover:b-[var(--g-sub-sidebar-menu-active-bg)] hover:text-[var(--g-sub-sidebar-menu-active-bg)]"
              >
                {{ items.dashboard_name }}
                {{ items.dashboard_description }}
              </div>
            </template>
          </div>
        </div>
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
// scss
</style>
