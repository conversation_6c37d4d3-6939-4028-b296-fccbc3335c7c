<script setup name="TemplateConfigurationMonitorTemplateDetail">
import useSettingsStore from "@/store/modules/settings";
import DetailForm from "./components/DetailForm/index.vue";
import eventBus from "@/utils/eventBus";
import { useTabbar } from "@/utils/composables";

const route = useRoute();
const router = useRouter();

const settingsStore = useSettingsStore();

const formRef = ref();

function onSubmit() {
  formRef.value.submit(() => {
    eventBus.emit("get-data-list");
    goBack();
  });
}

function onCancel() {
  goBack();
}

// 返回列表页
function goBack() {
  if (settingsStore.tabbar.enable && !settingsStore.tabbar.mergeTabs) {
    useTabbar().close({ name: "monitorTemplateList" });
  } else {
    router.push({ name: "monitor_template_list" });
  }
}
</script>

<template>
  <div>
    <page-main :title="route.name == 'createMonitorTemplate' ? '新增监控模板' : '编辑监控模板'">
      <el-row>
        <el-col :md="24" :lg="16">
          <DetailForm :id="route.params.id" ref="formRef" />
        </el-col>
      </el-row>
    </page-main>
    <fixed-action-bar>
      <el-button type="primary" size="large" @click="onSubmit">提交</el-button>
      <el-button size="large" @click="onCancel">取消</el-button>
    </fixed-action-bar>
  </div>
</template>

<style lang="scss" scoped>
// scss
</style>
