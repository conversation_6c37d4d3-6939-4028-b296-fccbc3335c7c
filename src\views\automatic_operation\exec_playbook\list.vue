<script setup name="ExampleList">
import eventBus from "@/utils/eventBus";
import { usePagination } from "@/utils/composables";
import {
  GetTaskHistory,
  DeletTask,
  batchDeleteTask,
} from "@/api/modules/auto_operation_maintenance/operationMaintenance";
import FormMode from "./components/FormMode/index.vue";
import { execAnsibleOperation } from "@/api/modules/ansible";
import { formatLocalTime } from "@/utils/dayjs";
import { goBack } from "@/utils";
import { ArrowLeftBold } from "@element-plus/icons-vue";
// 表单的封装好了的一些api
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
const router = useRouter();
// const route = useRoute()

const data = ref({
  loading: false,
  /**
   * 详情展示模式
   * router 路由跳转
   * dialog 对话框
   * drawer 抽屉
   */
  formMode: "router",
  // 详情
  formModeProps: {
    showMode: false,
  },
  pingStatus: false,
  // 搜索
  search: {
    keyword: "",
    status: "不限状态",
  },
  // 批量操作
  batch: {
    enable: false,
    selectionDataList: [],
  },
  // 列表数据
  dataList: [],
  serach_task: "",
  NameToPassword: [],
  Template: {
    execution_time: "",
    task_type: "",
    implemented_ip: "",
    task_describe: "",
    is_playbook: "",
    in_parameter: "",
    playbook_router: "",
    result: "",
    status: "",
    allList: [],
    isSearch: false,
  },
  content: {},
  options: [
    {
      value: "True",
      label: "执行成功",
    },
    {
      value: "False",
      label: "执行失败",
    },
  ],
});

onMounted(() => {
  getDataList();
  if (data.value.formMode === "router") {
    eventBus.on("get-data-list", () => {
      getDataList();
    });
  }
});

onBeforeUnmount(() => {
  if (data.value.formMode === "router") {
    eventBus.off("get-data-list");
  }
});

function dataFilter(dataList, params) {
  let list = [];
  dataList.forEach((element) => {
    list.push(element);
  });

  if (params.keyword != "" && params.keyword != undefined) {
    list = dataList.filter((item) => {
      return item
        ? item.implemented_ip.includes(params.keyword) ||
            item.task_type.includes(params.keyword) ||
            item.task_describe.includes(params.keyword)
        : true;
    });
  }

  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });
  return {
    list: pageList,
    total: list.length,
  };
}

const currentChangeAdd = () => {
  ElMessageBox.confirm("请确认执行的命令是否正确，是否包含危险命令？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      execAnsibleOperation({ data: data.value.search.keyWord }).then((res) => {
        if (res.data == "success") {
          ElMessage({
            type: "success",
            message: res.message,
          });
        } else {
          ElMessage.error(res.message);
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消",
      });
    });
};
function getDataList() {
  let params = getParams();
  data.value.search.keyword && (params.keyword = data.value.search.keyword);
  data.value.loading = true;
  GetTaskHistory({ data: data.value.search }).then((res) => {
    let temp_data = res.data;
    temp_data.forEach((item) => {
      let str = item.implemented_ip;
      str = str.replace(/[\[\]']/g, "");
      item.implemented_ip = str.split(",");
      item.dueTime = Date.parse(item.execution_time);
    });
    temp_data.sort(function (a, b) {
      return b.dueTime - a.dueTime;
    });
    data.value.allList = temp_data;
    paging();
  });
}
function paging() {
  let params = getParams();
  let res = dataFilter(data.value.allList, params);
  data.value.dataList = res.list;
  pagination.value.total = res.total;
  data.value.loading = false;
}
function createTask() {
  router.push({
    name: "create_task",
  });
}
function singleAnsible() {
  router.push({
    name: "Singleansible",
  });
}
// 每页数量切换
function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.value.isSearch == true) {
      searchData();
    } else {
      paging();
    }
  });
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.value.isSearch == true) {
      searchData();
    } else {
      paging();
    }
  });
}

// 字段排序
function sortChange(prop, order) {
  onSortChange(prop, order).then(() => paging());
}

function reconvert(str) {
  str = str.replace(/(\\u)(\w{1,4})/gi, function ($0) {
    return String.fromCharCode(parseInt(escape($0).replace(/(%5Cu)(\w{1,4})/g, "$2"), 16));
  });

  str = str.replace(/(&#x)(\w{1,4});/gi, function ($0) {
    return String.fromCharCode(parseInt(escape($0).replace(/(%26%23x)(\w{1,4})(%3B)/g, "$2"), 16));
  });

  str = str.replace(/(&#)(\d{1,6});/gi, function ($0) {
    return String.fromCharCode(parseInt(escape($0).replace(/(%26%23)(\d{1,6})(%3B)/g, "$2")));
  });
  str = str.replace(/[\r|\n|\t]/g, "");
  return str;
}

function execResult(row) {
  let details = JSON.parse(row.result);
  console.log(row.result);
  data.value.content = details.stdout;
  data.value.formModeProps.showMode = true;
}
function onDel(row) {
  let lists = [];
  lists.push(row.id);
  ElMessageBox.confirm(`确认删除吗？`)
    .then(() => {
      DeletTask(row.id).then(() => {
        getDataList();
        ElMessage.success({
          message: `删除成功`,
          center: true,
        });
      });
    })
    .catch(() => {});
}
function anyDel() {
  let list = data.value.batch.selectionDataList;
  let lists = [];
  for (let item of list) {
    lists.push(item.id);
  }
  if (list.length !== 0) {
    ElMessageBox.confirm(`确认删除吗？`)
      .then(() => {
        if (list.length == 0) {
          ElMessage.error("请先选择要删除的内容");
        } else {
          batchDeleteTask(lists).then(() => {
            getDataList();
            ElMessage.success({
              message: `删除成功`,
              center: true,
            });
          });
        }
      })
      .catch(() => {});
  } else {
    ElMessage.error("请先勾选要删除的内容");
  }
}
function queryData() {
  if (data.value.serach_task === "" || data.value.serach_task == null) {
    getDataList();
    data.value.isSearch = false;
  }
  //搜索
  searchData();
}
function searchData() {
  let list = [];
  data.value.allList.filter((item) => {
    if (item.task_describe.indexOf(data.value.serach_task) !== -1) {
      list.push(item);
    }
  });
  let params = getParams();
  data.value.dataList = dataFilter(list, params).list;
  pagination.value.total = dataFilter(list, params).total;
  data.value.isSearch = true;
}
</script>

<template>
  <div>
    <page-main title="执行记录">
      <template #title>
        <div class="flex items-center">
          <el-icon class="cursor-pointer" @click="goBack()"><ArrowLeftBold /></el-icon>
          <span class="ml-10px">执行记录</span>
        </div>
      </template>
      <search-bar>
        <el-form :model="data.search" size="default" label-width="100px" label-suffix="：">
          <el-row>
            <el-col :span="6">
              <el-form-item label="任务查询">
                <el-input
                  v-model="data.serach_task"
                  placeholder="请输入执行任务进行查询"
                  clearable
                  @keyup.enter="queryData"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="执行状态">
                <el-select v-model="data.search.status" placeholder="请选择执行状态">
                  <el-option
                    v-for="item in data.options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-form-item class="w-auto!">
              <el-button type="primary" @click="queryData()">
                <template #icon>
                  <el-icon>
                    <svg-icon name="ep:search" />
                  </el-icon>
                </template>
                筛选
              </el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </search-bar>
      <el-button type="danger" v-auth="['admin', 'publish.task_remove']" @click="anyDel">批量删除</el-button>
      <el-table
        ref="table"
        v-loading="data.loading"
        class="list-table"
        :data="data.dataList"
        :default-sort="{ prop: 'execution_time', order: 'descending' }"
        border
        stripe
        highlight-current-row
        @sort-change="sortChange"
        @selection-change="data.batch.selectionDataList = $event"
      >
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column type="selection" width="55" />
        <!-- <el-table-column prop="task_type" label="执行任务类型" /> -->
        <el-table-column prop="task_describe" label="执行任务" />
        <el-table-column prop="implemented_ip" label="执行ip" />
        <el-table-column prop="execution_time" label="执行时间">
          <template #default="scope">
            {{ formatLocalTime(scope.row.execution_time) }}
          </template>
        </el-table-column>

        <el-table-column prop="status" label="执行状态" width="100px">
          <template #default="scope">
            <div type="success" size="small" plain v-if="scope.row.status == 'True'">
              <el-tag type="success">执行成功</el-tag>
            </div>
            <div type="success" size="small" plain v-if="scope.row.status != 'True'">
              <el-tag type="danger">执行失败</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" plain @click="execResult(scope.row)">查看执行结果</el-button>
            <el-button
              type="danger"
              v-auth="['admin', 'publish.task_remove']"
              size="small"
              plain
              @click="onDel(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="pagination"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>

    <FormMode v-model="data.formModeProps.showMode" :content="data.content" />
  </div>
</template>

<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
}
</style>
