<script setup name="UserDetail">
import { getEnvViteApiBaseurlIp } from "@/utils/axios";
import { onMounted } from "@vue/runtime-core";
import VuePdfEmbed from "vue-pdf-embed";
const route = useRoute();
const state = reactive({ pdf: "" });

const options = {
  responseType: "blob",
};

onMounted(() => {
  state.pdf = `${getEnvViteApiBaseurlIp()}/static/weekly_report/${route.query.name}`;
});
</script>

<template>
  <div>
    <div class="vuePdfEmbed">
      <VuePdfEmbed :source="state.pdf" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.iframe,
iframe {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  // padding-bottom: 30px;
}

.vuePdfEmbed {
  flex: 1;
  display: flex;
  height: 100%;
  flex-direction: column;
}

.vuePdfEmbed {
  .page-tool {
    padding-left: 15px;
    padding-right: 15px;
    display: flex;
    align-items: center;
    background: rgb(66, 66, 66);
    color: white;
    border-radius: 19px;
    z-index: 100;
    cursor: pointer;
    width: 320px;
    align-items: center;
    margin: auto;
    justify-content: space-around;
  }

  .page-tool-item {
    padding: 8px 15px;
    padding-left: 10px;
    cursor: pointer;
  }
}
</style>
