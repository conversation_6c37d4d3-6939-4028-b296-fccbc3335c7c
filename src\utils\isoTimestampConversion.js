export function convertUTCtoLocalTime(utcTime) {
  // 将 UTC 时间字符串转换为 Date 对象
  const date = new Date(utcTime);

  // 获取各个部分
  const year = date.getUTCFullYear();  // 获取年份
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');  // 获取月份，补零
  const day = String(date.getUTCDate()).padStart(2, '0');  // 获取日期，补零
  const hours = String(date.getUTCHours()).padStart(2, '0');  // 获取小时，补零
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');  // 获取分钟，补零
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');  // 获取秒数，补零

  // 返回 "YYYY-MM-DD HH:mm:ss" 格式的 UTC 时间字符串
  return `${month}/${day} ${hours}:${minutes}`;
}