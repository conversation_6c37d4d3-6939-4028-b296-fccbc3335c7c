// @ts-nocheck
import storage from "@/utils/storage";
import dayjs from '@/utils/dayjs'

const useTaskJournalStore = defineStore(
  // 唯一ID
  "taskJournal",
  {
    state: () => ({
      taskList: JSON.parse(storage.local.get('taskList')) || [],//任务列表
    }),
    getters: {
      // ws任务列表
      getTaskList: (state) => {
        return state.taskList
      },

    },
    actions: {
      // 增加任务队列
      addTask(taskItem = {}) {
        const taskList = this.getTaskList;
        taskList.push(taskItem)
        this.editTask(taskList)
      },
      // 修改任务队列
      editTask(taskList) {
        storage.local.set('taskList', JSON.stringify(taskList))
        this.taskList = taskList
      },
      // 存储日志
      addTaskJournalList(wsUrl, item) {
        const list = this.getTaskJournalList(wsUrl);
        list.push({ content: item, time: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss') });
        storage.local.set(wsUrl, JSON.stringify(list));
      },
      // 修改某一个ws的任务状态
      editTaskwsStatus(wsUrl) {
        const taskList = toRaw(this.taskList)
        taskList.map((item) => {
          if (item.wsUrl == wsUrl) {
            item.wsStatus = 'finish'
          }
        })
        this.editTask(taskList)
      },
      // 获取日志--根据wsurl进行存储的
      getTaskJournalList(wsUrl) {
        return storage.local.get(wsUrl) ? JSON.parse(storage.local.get(wsUrl) || "") : [];
      },
      removeWslogCache(wsUrl) {
        storage.local.remove(wsUrl);
      }
    },
  }
);

export default useTaskJournalStore;
