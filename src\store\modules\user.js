import storage from "@/utils/storage";
import useTabbarStore from "./tabbar";
import useRouteStore from "./route";
import useMenuStore from "./menu";
import router from "@/router/index";
import { getUserAuth, editUserPassword, userLogout, userLogin } from "@/api/modules/user_api_management/users";
import useDateTimeStore from "./datetime";
const useUserStore = defineStore(
  // 唯一ID
  "user",
  {
    state: () => ({
      account: storage.local.get("account") || "",
      user: storage.local.get("user") || "",
      cookie: getCookie("user"),
      permissions: [],
      loginAttempts: storage.local.get("loginAttempts") || 0,
      lastAttemptTime: storage.local.get("lastAttemptTime") || null,
      isLocked: storage.local.get("isLocked") || false,
      lockingDuration: 3 * 60 * 1000, //锁定时长
    }),
    getters: {
      isLogin: (state) => {
        let retn = false;
        if (state.cookie !== "") {
          retn = true;
        }
        return retn;
      },
    },
    actions: {
      // 登录
      login(data) {
        return new Promise((resolve, reject) => {
          // 通过 mock 进行登录
          userLogin(data)
            .then((res) => {
              if (res.status_code == 200) {
                // 以前存进去就是个object，无法查看
                storage.local.set("user", JSON.stringify(res.data));
                storage.local.set("account", res.data.name);
                this.account = res.data.name;
                this.resetLoginAttempts();
              }
              resolve(res);
            })
            .catch((error) => {
              // 登录失败
              this.incrementLoginAttempts();
              if (this.isLocked) {
                alert("账户已锁定，请3分钟后重试");
              }
              reject(error);
            });
        });
      },
      // 登出
      logout() {
        return new Promise((resolve) => {
          const tabbarStore = useTabbarStore();
          const routeStore = useRouteStore();
          const menuStore = useMenuStore();
          const timeStore = useDateTimeStore();
          userLogout().then(() => {
            storage.local.remove("account");
            storage.local.remove("user");
            this.account = "";
            this.permissions = [];
            tabbarStore.clean();
            routeStore.removeRoutes();
            menuStore.setActived(0);
            timeStore.clear();
            resolve();
          });
        });
      },
      // 获取我的权限
      getPermissions() {
        return new Promise((resolve) => {
          // 通过 mock 获取权限
          getUserAuth({ name: storage.local.get("account") }).then((res) => {
            const { data } = res;

            this.permissions = JSON.parse(data).auth;
            resolve(this.permissions);
          });
        });
      },
      // 修改密码
      editPassword(data) {
        return new Promise((resolve) => {
          editUserPassword(data).then((res) => {
            if (res.status_code == 200) {
              resolve();
            }
          });
        });
      },
      // 计数登录次数
      incrementLoginAttempts() {
        if (this.isLocked) return;
        this.loginAttempts++;
        if (this.loginAttempts >= 5) {
          this.lockAccount();
        }
      },
      // 清空登录次数
      resetLoginAttempts() {
        this.loginAttempts = 0;
        this.lastAttemptTime = null;
        this.isLocked = false;
        storage.local.remove("loginAttempts");
        storage.local.remove("lastAttemptTime");
        storage.local.remove("isLocked");
      },
      // 锁定账户
      lockAccount() {
        this.isLocked = true;
        this.lastAttemptTime = new Date().getTime();
        storage.local.set("isLocked", this.isLocked);
        storage.local.set("lastAttemptTime", this.lastAttemptTime);
        setTimeout(() => {
          this.resetLoginAttempts();
        }, this.lockingDuration); // 3分钟后解锁
      },
    },
  }
);

export default useUserStore;
//查询名字为findCookieName的cookie值
function getCookie(cookieName) {
  //获取所有的Cookie,在strCookie是一个包含所有cookie的字符串。
  var strCookie = document.cookie;
  //以;为分隔符将所有的cookie进行分割。将获得的所有cookie切割成数组
  var arrCookie = strCookie.split("; ");
  //通过for循环进行遍历arrCookie数组。
  for (var i = 0; i < arrCookie.length; i++) {
    //通过=进行分割，将本次循环的cookie分割为名字（等于号前），值（等于号后面）
    var arr = arrCookie[i].split("=");
    //将本次循环的cookie名字与需要查找的cookie进行比较
    if (cookieName == arr[0]) {
      //返回指定cookie的值
      return arr[1];
    }
  }
  //未查找到指定的cookie返回空。
  return "";
}
