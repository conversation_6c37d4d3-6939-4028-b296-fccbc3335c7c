import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取用户权限
 * @param {*} data
 * @returns
 */
export function getUserAuth(data) {
  return api.get("/user/get_user_auth/");
}

/**
 * 修改用户密码
 * @param {*} data
 * @returns
 */
export function editUserPassword(data) {
  return api.post("/user/user_edit_password/", JSON.stringify(data));
}

/**
 * 用户退出登录
 * @returns
 */
export function userLogout() {
  return api.get("/user/login/user_logout/");
}

/**
 * 用户登录
 * @param {*} data
 * @returns
 */
export function userLogin(data) {
  return api.post("/user/login/user_login/", JSON.stringify(data));
}

/**
 * 获取所有用户列表
 * @param {*} model_name
 * @returns
 */
export function GetUsers() {
  return api.get(`/user_model/`);
}

/**
 *删除用户
 * @param {*} model_name
 * @param {*} object_id
 * @returns
 */
export function deleteUser(object_id) {
  const param = {
    "operation": 'delete',
    "value": object_id
  };
  return api.post(`/user_model/${object_id}/`, param);
}

/**
 * 修改用户
 * @param {*} object_id
 * @param {*} data
 * @returns
 */
export function updateUser(object_id, data) {
  const param = {
    "operation": 'update',
    "value": data
  };
  return api.post(`/user_model/${object_id}/`, JSON.stringify(param));
}

/**
 * 创建用户
 * @param {*} data
 * @returns
 */
export function createUser(data) {
  return api.post("/user_model/create/", JSON.stringify(data));
}

/**
 * 获取验证码
 * @returns
 */
export function getCaptchaImage() {
  return api.get("/user/login/get_captcha/");
}
export const login_url = {
  captcha: "/user/login/get_captcha/",
  download_pdf: "/weekly_report/download_file",
};