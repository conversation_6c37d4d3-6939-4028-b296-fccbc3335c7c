<script setup>
import { onMounted, reactive, ref, onUnmounted, watch } from "vue";
import { usePagination } from "@/utils/composables";
import { ArrowDown, Location, Remove } from "@element-plus/icons-vue";

import { GetModelListFilter } from "@/api/modules/basic_crud/crud";

import { ElMessage } from "element-plus";

import { showNotification } from "@/plugins/element-ui/index";
import ZabbixResult from "@/containers/zabbix_resulte_dialog/index.vue";
import {
  pageChangeNum,
  deleteResource,
  addZabbixForResource,
  showConfirmationDialog,
  goToPageAssetDetail,
} from "../components/utils";
import { ResourceTypeEnum } from "../cmdb_asset/components/constants";
import { getHostidByIp } from "@/api/modules/zabbix_api_management/zabbix";
import MoreOperations from "../components/more_operations.vue";
import CreateCluster from "@/views/homepage/cmdb_cluster/createCluster.vue";

const router = useRouter();
const route = useRoute();
const zabbixLoading = ref(false);
const zabbixResultDialog = ref(false);
const zabbixResultData = ref([]);

// 定义localStorage的key
const STORAGE_KEY = "cmdb_cluster_search_condition";

const data = reactive({
  batch: {
    enable: true,
    selectionDataList: [],
  },
  tableList: [],
  allList: [],
  search: {
    searchTag: "",
    isSearch: false,
  },
  tableListLoading: false,
  drawer: false,
  router_id: 0,
});

const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();

// 监听搜索条件变化，更新localStorage
watch(
  () => data.search,
  (newVal) => {
    if (newVal.searchTag) {
      localStorage.setItem(
        STORAGE_KEY,
        JSON.stringify({
          searchTag: newVal.searchTag,
          isSearch: newVal.isSearch,
        })
      );
    }
  },
  { deep: true }
);

onMounted(() => {
  // 从localStorage获取筛选条件
  const savedSearch = localStorage.getItem(STORAGE_KEY);
  if (savedSearch) {
    try {
      const parsedSearch = JSON.parse(savedSearch);
      data.search.searchTag = parsedSearch.searchTag || "";
      data.search.isSearch = parsedSearch.isSearch || false;
    } catch (e) {
      console.error("解析保存的筛选条件出错", e);
    }
  }

  getCluster();
});

// 组件卸载时清除localStorage
onUnmounted(() => {
  localStorage.removeItem(STORAGE_KEY);
});

function getCluster() {
  data.tableListLoading = true;
  GetModelListFilter("集群")
    .then((res) => {
      data.tableList = res.data;
      data.allList = res.data;

      // 如果有搜索条件，应用筛选
      if (data.search.isSearch && data.search.searchTag) {
        searchByName();
      } else {
        changePageNum(data.allList);
      }
    })
    .finally(() => {
      data.tableListLoading = false;
    });
}

function changePageNum(list) {
  let res = pageChangeNum(list, getParams());
  data.tableList = res.list;
  pagination.value.total = res.total;
}

function sizeChange(size) {
  onSizeChange(size).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}
// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => {
    if (data.search.isSearch == true) {
      searchByName();
    } else {
      changePageNum(data.allList);
    }
  });
}

function queryData() {
  if (data.search.searchTag == "") {
    getCluster();
  } else {
    searchByName();
  }
}
function searchByName() {
  const tag = data.search.searchTag.trim();
  let list = [];
  const ipRegex = /^[\d.]+$/;
  if (ipRegex.test(tag)) {
    // 使用 filter 和正则表达式进行筛选
    const regex = new RegExp(tag, "i");
    list = data.allList.filter((item) => {
      return regex.test(item.ip);
    });
  } else {
    list = data.allList.filter((item) => {
      return item.name.trim().toLowerCase().indexOf(data.search.searchTag.trim().toLowerCase()) != -1;
    });
  }

  changePageNum(list);
}

const drawerTitle = computed(() => {
  return data.router_id === 0 ? "新增集群" : "编辑集群";
});

function addCluster() {
  data.router_id = 0;
  data.drawer = true;
}

async function batchDel() {
  let delParam = [];
  data.batch.selectionDataList.forEach((item) => {
    delParam.push({
      relation_id: 0,
      zabbix_id: item.id,
    });
  });
  delCluster(delParam);
}

async function delCluster(delParam) {
  const result = await deleteResource(ResourceTypeEnum.cluster, delParam);
  if (!result) {
    return;
  }
  getCluster();
}
function editCluster(row) {
  data.router_id = row.id;
  data.drawer = true;
}

//单个集群安装监控
function install(item) {
  console.log(item);
  if (item.zabbix_template_name.length == 0) {
    ElMessage.error(item.name + "集群未添加监控模板,请配置！");
    return;
  }
  addZabbix([item]);
}

function installAll() {
  let list = [];
  list = data.allList.filter((item) => item.status == "offmonitor");
  if (list.length == 0) {
    ElMessage.error("请检查是否有未安装监控服务的设备");
    return;
  }
  let list_template = list.filter((item) => {
    return item.zabbix_template_name.length == 0;
  });
  if (list_template.length != 0) {
    let serverName = list_template.map((item) => item.name);
    showNotification("注意", "以下集群没有添加监控模板：[" + serverName.join(",") + "],请注意！", "error");
    return;
  }
  addZabbix(list);
}
//批量安装和单个安装监控公共部分代码
async function addZabbix(list) {
  let ip_information_list = [];
  list.forEach((item) => {
    ip_information_list.push({
      ip: item.ip,
      type: item.type,
      group_name: item.group_name,
      id: item.zabbix_id,
    });
  });
  let params = {
    resource_type_name: "cluster",
    ip_information_list,
  };
  const confirm = await showConfirmationDialog();
  if (confirm) {
    zabbixLoading.value = true;
    zabbixResultDialog.value = true;
    zabbixResultData.value = [];
    try {
      zabbixResultData.value = await addZabbixForResource(params);
    } catch (error) {}

    zabbixLoading.value = false;
    getCluster();
  } else {
    ElMessage.info("已取消监控安装!");
  }
}

// 跳转详情
function jumpAssetDetail(row, isMonitoring = false) {
  const queryParams = { item: "集群", id: row.id, ip: row.ip, relation_id: row.relation_id };
  goToPageAssetDetail(queryParams, isMonitoring);
}
</script>
<template>
  <div>
    <page-main title="集群">
      <div class="flex justify-between">
        <el-space wrap>
          <el-button type="primary" @click="addCluster">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:plus" />
              </el-icon>
            </template>
            新增集群
          </el-button>
          <el-button type="primary" @click="installAll">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:coordinate" />
              </el-icon>
            </template>
            一键安装所有
          </el-button>
          <el-button type="danger" :disabled="!data.batch.selectionDataList.length" @click="batchDel()">
            删除集群
          </el-button>
        </el-space>
        <el-space wrap>
          <div class="w-220px">
            <el-input
              placeholder="输入ip或名称,支持模糊查询"
              v-model="data.search.searchTag"
              clearable
              @keyup.enter="queryData"
            />
          </div>
          <el-button type="primary" @click="queryData()">
            <template #icon>
              <el-icon>
                <svg-icon name="ep:search" />
              </el-icon>
            </template>
            筛选
          </el-button>
        </el-space>
      </div>

      <el-table
        :data="data.tableList"
        border
        class="list-table"
        stripe
        highlight-current-row
        @selection-change="data.batch.selectionDataList = $event"
        v-loading="data.tableListLoading"
      >
        <el-table-column v-if="data.batch.enable" fixed type="selection" align="center" />
        <el-table-column label="集群名称" prop="name">
          <template #default="scoped">
            <el-link type="primary" underline @click="jumpAssetDetail(scoped.row)">
              {{ scoped.row.name }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="虚拟IP" prop="ip" />
        <el-table-column label="集群类型" prop="group_name" />
        <el-table-column label="节点" prop="extra_parameters">
          <template #default="scoped">
            <div class="flex flex-wrap justify-evenly">
              <el-link
                v-for="item in scoped.row.extra_parameters.cluster_ip_list"
                :key="item"
                class="m-4px"
                type="primary"
                disabled
              >
                {{ item }}
              </el-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="监控状态">
          <template #default="scoped">
            <el-tag v-if="scoped.row.status == 'online'">已启用</el-tag>
            <el-tag v-else-if="scoped.row.status == 'offmonitor'" type="info" effect="dark">未监控</el-tag>
            <el-tag v-else type="danger">停用的</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center">
          <template #default="scoped">
            <el-button type="primary" plain @click="editCluster(scoped.row)">编辑</el-button>
            <MoreOperations v-if="scoped.row.status == 'offmonitor'">
              <el-dropdown-menu>
                <el-dropdown-item
                  v-if="scoped.row.status == 'offmonitor'"
                  :icon="Location"
                  @click="install(scoped.row)"
                >
                  安装监控
                </el-dropdown-item>
              </el-dropdown-menu>
              <el-dropdown-item
                @click="jumpAssetDetail(scoped.row, true)"
                v-if="scoped.row.status == 'online' || scoped.row.status == 'offline'"
              >
                最新数据
              </el-dropdown-item>
            </MoreOperations>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </page-main>
    <ZabbixResult v-model="zabbixResultDialog" :stepData="zabbixResultData" :loading="zabbixLoading"></ZabbixResult>

    <el-drawer v-model="data.drawer" size="50%" :destroy-on-close="true" :close-on-click-modal="false">
      <template #header>
        <h4>{{ drawerTitle }}</h4>
      </template>
      <el-divider class="cancel_top" />
      <CreateCluster
        v-model="data.router_id"
        @closeDialog="data.drawer = false"
        @getResource="getCluster()"
      ></CreateCluster>
    </el-drawer>
  </div>
</template>
<style scoped>
.cancel_top {
  margin-top: 0px;
}
</style>
