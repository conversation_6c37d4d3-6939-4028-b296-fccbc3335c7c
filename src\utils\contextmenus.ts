import router from "@/router";
import ContextMenu from "@imengyu/vue3-context-menu";
export default function onContextMenu(e, item) {
  //prevent the browser's default menu
  e.preventDefault();
  console.log(e, item);

  //show our menu
  ContextMenu.showContextMenu({
    x: e.x,
    y: e.y,
    zIndex: 10000,
    items: [
      {
        label: "问题",
        onClick: () => {
          router.push({
            name: "problem_list",
            query: { ...item },
          });
        },
      },
      {
        label: "最新数据",
        onClick: () => {
          router.push({
            name: "latest",
            query: { ...item },
          });
        },
      },
    ],
  });
}
