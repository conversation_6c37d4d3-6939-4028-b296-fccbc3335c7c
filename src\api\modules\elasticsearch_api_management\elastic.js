import api from "@/plugins/axios/index";
import axios from "axios";

/**
 * 获取医院所有主机信息
 * @returns
 */
export function GetHospital() {
  return api.get("/centralized_monitoring_center/hospital");
}

/**
 * 获取所有日志
 * @param {*} data
 * @returns
 */
export function GetLog(data) {
  return api.post("/centralized_monitoring_center/log", JSON.stringify(data));
}

/**
 * 获取告警
 * @returns
 */
export function HospitalWarning() {
  return api.get("/centralized_monitoring_center/warning");
}

/**
 * 业务日志
 * @data [开始时间，结束时间]
 * @returns 
 */
export function BusinessLog(data) {
  return api.get(`/home_page/log/?business_name=${data.business_name}&start_time=${data.start_time}&end_time=${data.end_time}`);
}
