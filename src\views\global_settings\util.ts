import { getAllAnsibleHostIP } from "@/api/modules/configuration/oracleDb4bix";

export enum SourceMapEnum {
  elasticsearch = "elasticsearch",
  kibana = "kibana",
  zabbix = "zabbix",
  grafana = "grafana",
}
export const configStatus = {
  [SourceMapEnum.elasticsearch]: false,
  [SourceMapEnum.kibana]: false,
  [SourceMapEnum.zabbix]: false,
  [SourceMapEnum.grafana]: false,
};

//
function useGlobalConfig() {
  const ansibleHostList = ref([]);
  const activeStep = ref(0);

  // 获取所有ansible缓存的ip
  function getAllAnsibleHostIPList() {
    getAllAnsibleHostIP().then((res) => {
      res.data.forEach((item) => {
        ansibleHostList.value.push(item);
      });
    });
  }
  // 步骤
  const stepEnum = {
    source: 0,
    report: 1,
    import: 2,
  };
  const settingStep = [
    { lable: "数据源导入", value: stepEnum.source },
    { lable: "报告配置", value: stepEnum.report },
    { lable: "导入配置", value: stepEnum.import },
  ];

  return {
    getAllAnsibleHostIPList,
    ansibleHostList,
    stepEnum,
    settingStep,
    activeStep,
  };
}
export default useGlobalConfig;
