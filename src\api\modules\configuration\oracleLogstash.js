import api from "@/plugins/axios/index";

/**
 * 获取
 * @returns
 */
export function GetLogstashBindIp() {
  return api.get("/deal_config/logstash_config");
}

/**
 * 获取logstash配置文件
 * @returns
 */
export function GetLogstashConfig() {
  return api.patch("/deal_config/logstash_config");
}

/**
 * 绑定Logstash文件IP
 */
export function BindLogstashIp(data) {
  return api.post("/deal_config/logstash_config", JSON.stringify(data));
}

/**
 * 新增、删除、修改logstash文件内容
 * @param {*} data
 * @returns
 */
export function DealLogstashConfig(data) {
  return api.post("/deal_config/deal_logstash_config/", JSON.stringify(data));
}
