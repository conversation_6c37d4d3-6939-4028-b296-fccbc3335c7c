const Layout = () => import("@/layout/index.vue");
let children = [
  {
    path: "user_list",
    name: "user_list",
    component: () => import("@/views/user_manage/list.vue"),
    meta: {
      title: "账号管理",
      auth: ['admin', "user_list.browse"],
    },
    children: [
      {
        path: "create_user",
        name: "create_user",
        component: () => import("@/views/user_manage/detail.vue"),
        meta: {
          title: "新增用户",
          sidebar: false,
          activeMenu: "/user/user_list",
        },
      },
      {
        path: "edit_user",
        name: "edit_user",
        component: () => import("@/views/user_manage/detail.vue"),
        meta: {
          title: "编辑用户",
          sidebar: false,
          activeMenu: "/user/user_list",
        },
      },
    ],
  },
  {
    path: "campus",
    name: "campus",
    component: () => import("@/views/campus/index.vue"),
    meta: {
      title: "院区管理",
      auth: ['admin', "personal.browse"],
    },
  },
  {
    path: "runnerlog",
    name: "runnerlog",
    component: () => import("@/views/personal/runnerlog.vue"),
    meta: {
      title: "ansible日志",
      auth: ['admin', "runnerlog.browse"],
    },
  },
];

export default {
  path: "/user",
  component: Layout,
  redirect: "/user/user_list",
  name: "user",
  meta: {
    auth: ['admin', "user.browse"],
    title: "用户管理",
    icon: "user-icon",
  },
  children,
};
