<script setup>
// const router = useRouter()
import { Chart } from "@antv/g2";
import { onMounted, reactive } from "vue";
import { usePagination } from "@/utils/composables";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
// const route = useRoute()

const emit = defineEmits(["update:modelValue"]);
let myVisible = computed({
  get: function () {
    return props.modelValue;
  },
  set: function (val) {
    emit("update:modelValue", val);
  },
});
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
  },
});
const data = reactive({
  dataList: []
});
onMounted(() => {
  getData();
});

function getData() {

}
const tableData =
  [
    {
      date: '2016-05-03',
      name: '负载均衡服务器',
      ip: '***********',
      level: '灾难',
      message: '内存空间不足',
    },
    {
      date: '2016-05-02',
      name: '负载均衡服务器',
      ip: '***********',
      level: '灾难',
      message: '内存空间不足',
    },
    {
      date: '2016-05-04',
      name: '负载均衡服务器',
      ip: '***********',
      level: '警告',
      message: '内存空间不足',
    },
    {
      date: '2016-05-01',
      name: '负载均衡服务器',
      ip: '***********',
      level: '警告',
      message: '内存空间不足',
    },
    {
      date: '2016-05-08',
      name: '负载均衡服务器',
      ip: '***********',
      level: '灾难',
      message: '内存空间不足',
    },
    {
      date: '2016-05-06',
      name: '负载均衡服务器',
      ip: '***********',
      level: '警告',
      message: '内存空间不足',
    },
    {
      date: '2016-05-07',
      name: '负载均衡服务器',
      ip: '***********',
      level: '警告',
      message: '内存空间不足',
    },
  ]
  function sizeChange(size) {

}
function currentChange() {
  
}
</script>

<template>
  <div>
    <div class="vertical-line"></div>
    <span class="sub-title">日志</span>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column fixed prop="date" label="日期" />
      <el-table-column prop="name" label="服务器" />
      <el-table-column prop="ip" label="IP" />
      <el-table-column prop="level" label="等级" />
      <el-table-column prop="message" label="日志内容" />
    </el-table>
    <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
      :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
      background @size-change="sizeChange" @current-change="currentChange" />
  </div>
</template>

<style lang="scss" scoped>
// scss
.vertical-line {
  border-left: 4px solid #337ecc;
  /* 2px 宽度的加粗直线，黑色 */
  height: 13px;
  /* 设置高度 */
  display: inline-block;
}

.sub-title {
  display: inline-block;
  /* 设置为内联块级元素 */
  margin-left: 5px;
  /* 添加一些左边距以分隔线和文本 */
  font-size: 14px;
  font-weight: bold;
  padding-bottom: 5px;
}

.pagination {
  padding: 20px
}
</style>
