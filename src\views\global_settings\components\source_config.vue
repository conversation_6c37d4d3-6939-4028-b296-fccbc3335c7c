<template>
  <div class="data_source mt-16px" v-loading="formLoading">
    <el-divider content-position="left">
      <span class="setting_block_title">数据源配置</span>
    </el-divider>
    <el-row :gutter="10">
      <el-col :span="6">
        <el-card>
          <el-form
            :model="elasticsearchForm"
            label-width="150px"
            label-position="top"
            :rules="formRules"
            ref="elasticsearchFormRef"
          >
            <el-form-item label="日志数据源(es)" prop="ip">
              <el-input v-model="elasticsearchForm.ip" class="input_css" placeholder="请输入包含端口号的地址">
                <template #prepend>http://</template>
              </el-input>
            </el-form-item>

            <el-form-item label="登录账户(管理员)" prop="username">
              <el-input v-model="elasticsearchForm.username" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input type="password" show-password v-model="elasticsearchForm.password" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-form :model="kibanaForm" label-position="top" label-width="150px" :rules="formRules" ref="kibanaFormRef">
            <el-form-item label="分析报告数据源(kibana)" prop="ip">
              <el-input v-model="kibanaForm.ip" class="input_css">
                <template #prepend>http://</template>
                <template #append>/itoa</template>
              </el-input>
            </el-form-item>

            <el-form-item label="登录账户(管理员)" prop="username">
              <el-input v-model="kibanaForm.username" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input type="password" show-password v-model="kibanaForm.password" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-form ref="zabbixFormRef" :model="zabbixForm" label-position="top" label-width="150px" :rules="formRules">
            <el-form-item label="监控数据源(zabbix)" prop="ip">
              <el-input v-model="zabbixForm.ip" class="input_css">
                <template #prepend>http://</template>
                <template #append>/monitor</template>
              </el-input>
            </el-form-item>

            <el-form-item label="登录账户(管理员)" prop="username">
              <el-input v-model="zabbixForm.username" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input type="password" show-password v-model="zabbixForm.password" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-form
            ref="grafanaFormRef"
            :model="grafanaForm"
            label-position="top"
            label-width="100px"
            :rules="formRules"
          >
            <el-form-item label="大屏数据源(grafana)" prop="ip">
              <el-input v-model="grafanaForm.ip" class="input_css">
                <template #prepend>http://</template>
                <template #append>/tv</template>
              </el-input>
            </el-form-item>

            <el-form-item label="登录账户(管理员)" prop="username">
              <el-input v-model="grafanaForm.username" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input type="password" show-password v-model="grafanaForm.password" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <div class="ipconfig mt-16px"></div>

    <el-divider content-position="left">
      <span class="setting_block_title">监控配置</span>
    </el-divider>
    <el-card class="pt-18px">
      <el-form ref="form" label-width="190px" label-suffix="：" label-position="top">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Oracle数据库监控地址">
              <el-select v-model="dataSource.db4Ip" filterable placeholder="请选择绑定ip">
                <el-option label="本机" value="localhost" />
                <el-option v-for="item in ansibleHostList" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="logstash数据库采集地址">
              <el-select v-model="dataSource.logstashIp" filterable placeholder="请选择绑定ip">
                <el-option label="本机" value="localhost" />
                <el-option v-for="item in ansibleHostList" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <span class="text-red">{{ errorMessages }}</span>
  </div>
</template>

<script setup lang="ts">
import { BindDb4bixIp } from "@/api/modules/configuration/oracleDb4bix";
import useGlobalConfig, { SourceMapEnum } from "../util";
import { addEnvConfigInfo } from "@/api/modules/configuration/env_config";
import { BindLogstashIp } from "@/api/modules/configuration/oracleLogstash";
import {
  defaultEsConfig,
  defaultGrafanaConfig,
  defaultKibanaConfig,
  defaultZabbixConfig,
} from "@/constants/globalconfig";
const { ansibleHostList, getAllAnsibleHostIPList } = useGlobalConfig();
const formLoading = ref(false);
const dataSource = reactive({
  db4Ip: "localhost",
  logstashIp: "localhost",
});
const formRules = {
  ip: [{ required: true, message: "请输入IP", trigger: "blur" }],
  username: [{ required: true, message: "请输入账户", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
};
// 表单引用
const kibanaFormRef = ref(null);
const grafanaFormRef = ref(null);
const elasticsearchFormRef = ref(null);
const zabbixFormRef = ref(null);

// 表单数据和规则
const kibanaForm = ref({
  ip: defaultKibanaConfig.defaultip,
  username: defaultKibanaConfig.defalutaccount,
  password: defaultKibanaConfig.defaultpassword,
});
const elasticsearchForm = ref({
  ip: defaultEsConfig.defaultip,
  username: defaultEsConfig.defalutaccount,
  password: defaultEsConfig.defaultpassword,
});
const grafanaForm = ref({
  ip: defaultGrafanaConfig.defaultip,
  username: defaultGrafanaConfig.defalutaccount,
  password: defaultGrafanaConfig.defaultpassword,
});
const zabbixForm = ref({
  ip: defaultZabbixConfig.defaultip,
  username: defaultZabbixConfig.defalutaccount,
  password: defaultZabbixConfig.defaultpassword,
});
onMounted(() => {
  getAllAnsibleHostIPList();
});
const settingStatus = reactive({
  [SourceMapEnum.zabbix]: "",
  [SourceMapEnum.elasticsearch]: "",
  [SourceMapEnum.grafana]: "",
  [SourceMapEnum.kibana]: "",
});
const emits = defineEmits(["next-step"]);
const errorMessages = ref("");
function saveSourceConfig() {
  //    1.环境配置  2.db4配置  3.logstash配置

  Promise.all([
    kibanaFormRef.value.validate(),
    grafanaFormRef.value.validate(),
    elasticsearchFormRef.value.validate(),
    zabbixFormRef.value.validate(),
  ])
    .then(() => {
      formLoading.value = true;
      data_source_configuration()
        .then((res) => {
          // 检查是否有任何配置失败
          const allSuccessful = res.every((result) => result.status_code === 200);

          // 如果所有配置都成功，则继续下一步
          if (allSuccessful) {
            emits("next-step");
          } else {
            errorMessages.value =
              res
                .filter((item) => item.status_code !== 200) // 过滤出status_code不是200的项
                .map((item) => item.message) // 提取message属性
                .join("。") + "。"; // 将消息组合成一句，使用中文句号分隔
          }
        })
        .catch((error) => {
          console.error("Failed to configure:", error);
          // ElMessage.error('数据源配置失败', error.data['发生异常的请求体为'].type)
        })
        .finally(() => {
          formLoading.value = false;
        });
    })
    .catch(() => {
      ElMessage.error("请检查表单数据");
    });
}

// 数据源以及监控配置
async function data_source_configuration() {
  try {
    // 并发执行所有配置任务
    const results = await Promise.allSettled([
      addEnvConfigInfo({ type: SourceMapEnum.kibana, data_resource: kibanaForm.value }),
      addEnvConfigInfo({ type: SourceMapEnum.zabbix, data_resource: zabbixForm.value }),
      addEnvConfigInfo({ type: SourceMapEnum.grafana, data_resource: grafanaForm.value }),
      addEnvConfigInfo({ type: SourceMapEnum.elasticsearch, data_resource: elasticsearchForm.value }),
      BindDb4bixIp(dataSource.db4Ip),
      BindLogstashIp(dataSource.logstashIp),
    ]);
    // 处理每个结果
    const finalResults = results.map((result, index) => {
      if (result.status === "fulfilled") {
        return result.value;
      } else {
        return result.reason;
      }
    });

    return finalResults; // 返回结果供调用者使用
  } catch (error) {
    throw error; // 重新抛出错误，以便调用者可以处理
  }
}
defineExpose({ saveSourceConfig, formLoading });
</script>

<style scoped></style>
