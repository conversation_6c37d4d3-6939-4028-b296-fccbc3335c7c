const Layout = () => import("@/layout/index.vue");

let children = [
  {
    path: "MonitorPlat",
    name: "MonitorPlat",
    component: () => import("@/views/business_monitor/index.vue"),
    meta: {
      title: "业务监控平台",
      auth: ['admin', "MonitorPlat.browse"],
    },
  },
  {
    path: "bussiness_analysis",
    name: "bussiness_analysis",
    component: () => import("@/views/business_monitor/business_analysis.vue"),
    meta: {
      title: "业务分析查询",
      // link: url,
      auth: ['admin', "business_analysis.browse"],
    }
  },
  // {
  //   path: "MonitorTopology",
  //   name: "MonitorTopology",
  //   component: () => import("@/views/business_monitor/MonitorTopology.vue"),
  //   meta: {
  //     title: "集成平台监控",
  //     copyright: false,
  //     auth: ["admin", "MonitorTopology.browse"],
  //   },
  // },
  {
    path: "WarningMonitior",
    name: "WarningMonitior",
    component: () => import("@/views/business_monitor/warning.vue"),
    meta: {
      title: "告警列表",
      sidebar: false,
      // auth: ['admin', "monitor_plat.browse"],
    },
  },
  {
    path: "backup_list",
    name: "backup_list",
    component: () => import("@/views/backup/list.vue"),
    meta: {
      title: "备份监控",
      auth: ['admin', "backup_list.browse"],
    },
    children: [
      {
        path: "backup_information",
        name: "backup_information",
        component: () => import("@/views/backup/detail.vue"),
        meta: {
          title: "备份监控详情",
          sidebar: false,
          activeMenu: "/business_monitor/backup_list",
        },
      },
    ],
  },
  {
    path: "analysis_apm",
    name: "analysis_apm",
    component: () => import("@/views/visualization_platform/apm.vue"),
    meta: {
      title: "web性能监控",
      copyright: false,
      auth: ['admin', "analysis_apm.browse"],
    },
  },
  {
    path: "problem_list",
    name: "problem_list",
    component: () => import("@/views/problem_list/index.vue"),
    meta: {
      title: "问题清单",
      auth: ['admin', "problem_list.browse"],
    },

  },
  {
    path: "latest",
    name: "latest",
    component: () => import("@/views/problem_list/lastData.vue"),
    meta: {
      title: "最新数据",
      auth: ['admin', "latest.browse"],
    },
  },
];

export default {
  path: "/business_monitor",
  component: Layout,
  redirect: "/business_monitor/MonitorPlat",
  name: "business_monitor",
  meta: {
    auth: ['admin', "business_monitor.browse"],
    title: "监控平台",
    icon: "business_monitoring",
  },
  children,
};
