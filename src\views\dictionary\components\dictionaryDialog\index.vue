<script setup lang="ts">
import { apiDictionaryeAddUpdate } from "@/api/modules/dictionary";
import type { FormInstance, FormRules } from "element-plus";

const props = withDefaults(
    defineProps<{
        modelValue: boolean;
        id?: string | number;
        tree: any[];
    }>(),
    {
        modelValue: false,
        id: "",
    }
);

const emit = defineEmits(["update:modelValue", "success"]);
const myVisible = ref(props.modelValue);
const title = computed(() => (props.id === "" ? "新增字典" : "编辑字典"));

const formRef = ref<FormInstance>();
const form = ref({
    id: props.id,
    dictionary_name: "",
    dictionary_code: "",
    dictionary_value: [],
});
const formRules = ref<FormRules>({
    dictionary_name: [{ required: true, message: "请输入字典名称" }],
    dictionary_code: [{ required: true, message: "请输入字典编码" }],
});

onMounted(() => {
    if (props.id !== "") {
        const detail: any = props.tree.filter((item) => item.id == props.id)[0];

        form.value.id = detail.id;
        form.value.dictionary_name = detail.dictionary_name;
        form.value.dictionary_code = detail.dictionary_code;
        form.value.dictionary_value = detail.dictionary_value;
    }
});

function onSubmit() {
    if (form.value.id === "") {
        formRef.value &&
            formRef.value.validate((valid) => {
                form.value.id = null;
                if (valid) {
                    apiDictionaryeAddUpdate(form.value).then((res) => {
                        emit("success");
                        onCancel();
                    });
                }
            });
    } else {
        formRef.value &&
            formRef.value.validate((valid) => {
                if (valid) {
                    apiDictionaryeAddUpdate(form.value).then(() => {
                        emit("success");
                        onCancel();
                    });
                }
            });
    }
}

function onCancel() {
    myVisible.value = false;
}
</script>

<template>
    <el-dialog
        v-model="myVisible"
        :title="title"
        width="400px"
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
        @closed="emit('update:modelValue', false)"
    >
        <el-form ref="formRef" :model="form" :rules="formRules" label-width="80px">
            <el-form-item label="字典名称" prop="dictionary_name">
                <el-input v-model="form.dictionary_name" placeholder="请输入字典名称" clearable />
            </el-form-item>
            <el-form-item label="字典编码" prop="code">
                <el-input v-model="form.dictionary_code" placeholder="请输入字典编码" clearable />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button size="large" @click="onCancel">取消</el-button>
            <el-button type="primary" size="large" @click="onSubmit">确定</el-button>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-cascader) {
    width: 100%;
}
</style>
