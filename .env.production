NODE_ENV = production
# 页面标题
BaseIp = *************
VITE_APP_TITLE = 一体化运维平台
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = http://$BaseIp/api
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_SSH_WEBSOCKET = ws://$BaseIp/webssh/
VITE_CMDB_WEBSOCKET = ws://$BaseIp/ws/scheduled_task_result/collect 
VITE_KAFKA_BASEURL = http://$BaseIp:8000
VITE_SCHEDULE_WEBSOCKET = ws://$BaseIp/ws/scheduled_task_result/task_result
VITE_ROOM_BASEURL = http://$BaseIp/room
# 是否开启代理
VITE_OPEN_PROXY = true
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =

# 是否在打包时启用 Mock
VITE_BUILD_MOCK = false
# 是否在打包时生成 sourcemap
VITE_BUILD_SOURCEMAP = false
# 是否在打包时删除 console 代码
VITE_BUILD_DROP_CONSOLE = false
# 是否在打包时开启压缩，支持 gzip 和 brotli,不开起则为false
VITE_BUILD_COMPRESS = false
# 是否在打包时候生成PWA
VITE_BUILD_PWA = false
