const useMenuBadgeStore = defineStore(
    // 唯一ID
    'menuBadge',
    () => {
      const dot = ref(true)
      const number = ref(0)
      const text = ref('热门')

      function switchDot() {
        dot.value = !dot.value
      }
      function setNumber(val) {
        number.value = val
      }
      function setText(val) {
        text.value = val
      }

      return {
        dot,
        number,
        text,
        switchDot,
        setNumber,
        setText,
      }
    },
  )

  export default useMenuBadgeStore
