import { Toast } from "@/plugins/element-ui";
import useTaskJournalStore from "@/store/modules/taskJournal";



class WebSocketConnection {
  constructor(url, index) {
    //连接的url地址
    this.url = url;
    this.ws = new WebSocket(url);
    this.bindEvents();
  }
  //监听回调（连接成功、错误、断开、接收消息）
  bindEvents() {
    this.ws.onopen = () => {
      console.log(`${this.url} 连接成功.`);
    };
    const taskJournalStore = useTaskJournalStore();
    this.ws.onmessage = (message) => {
      // 收到信息后，将其存储在Pinia中
      const resulemsg = JSON.parse(message.data).message || ''
      taskJournalStore.addTaskJournalList(this.url, resulemsg);
      // TODO: 收到最后一条消息之后需要关闭连接
      if (resulemsg == EndFlagWord) {
        taskJournalStore.editTaskwsStatus(this.url)
        this.ws.close();
        Toast.success('执行结束，连接已关闭,具体信息请在作业执行日志查看.');

      }
    };
    this.ws.onerror = (error) => {
      console.error(`连接错误 ${this.url}: ${error}`);
    };
    this.ws.onclose = (e) => {
      console.log(e.code);
      console.log(`${this.url} 连接关闭`);
      taskJournalStore.editTaskwsStatus(this.url)//连接断了就断了把，只接改成已结束
      if (e.code !== 1000) {
        console.log(`Reconnecting ${this.url}...`);
        //重连 此处可以加个setTimeout 多少秒后再重连
        // this.reconnect();
      }
    };
  }
  //重连
  reconnect() {
    this.ws = new WebSocket(this.url);
    this.bindEvents();
  }
}
export default WebSocketConnection

// 结束标志符
export const EndFlagWord = 'task end'