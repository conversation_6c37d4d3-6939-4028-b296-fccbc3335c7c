<script setup>
import { onMounted, reactive } from "vue";
import History from "../history/index.vue";
const history = ref(false);
const props = defineProps({
  item: {
    type: Object,
  },
});
const data = reactive({
  histroy_list: [],
});
onMounted(() => {});
function warning(item) {
  history.value = true;
  data.histroy_list = item.histroy_list;
}
</script>

<template>
  <div>
    <el-timeline>
      <el-timeline-item v-for="(item, index) in props.item" :key="index" :type="'warning'" :hollow="true">
        <div style="display: flex; align-item: center; justify-content: space-between">
          <el-tag v-if="item.priority == '灾难'" type="danger">{{ item.priority }}</el-tag>
          <el-tag v-else-if="item.priority == '严重'" type="danger">{{ item.priority }}</el-tag>
          <el-tag v-else type="warning">{{ item.priority }}</el-tag>
          <span class="time">{{ item.lastchange }}</span>
          <div>
            <span style="color: #409eff; cursor: pointer" @click="warning(item)">历史告警</span>
          </div>
        </div>
        <div class="description">
          <span>
            <el-tag type="primary" class="name">{{ item.hostname }}</el-tag>
          </span>
          <span>
            <el-tag type="primary" class="ip" @click="$onContextMenu($event, item)">
              {{ item.hostip }}
            </el-tag>
          </span>
          {{ item.description }}
        </div>
      </el-timeline-item>
    </el-timeline>
    <History v-model="history" :item="data.histroy_list"></History>
  </div>
</template>

<style lang="scss" scoped>
.description {
  padding-top: 10px;
  .ip {
    margin-left: 5px;
  }
}
</style>
