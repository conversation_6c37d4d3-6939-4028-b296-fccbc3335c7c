<script setup>
import { onMounted, reactive, ref, computed, watch, nextTick } from "vue";
import { Line, Column, Pie, DualAxes, Heatmap } from "@antv/g2plot";
import { getUTCTimestampWithOffset } from "@/utils/dayjs";
import useDateTimeStore from "@/store/modules/datetime";
import { ElMessage } from "element-plus";
import api from "@/plugins/axios";
import AlertTimeline from "../AlertTimeline.vue";
import { useRoute } from 'vue-router';
import { getBusinessServers } from '@/api/modules/business_topo';
const timeStore = useDateTimeStore();
const route = useRoute();

// 定义props，不引用局部变量
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});

// 数据状态
const data = reactive({
  servers: [],
  alerts: [],
  timeSeries: {},
  loading: true,
  selectedServer: null // 当前选中的服务器
});

// 图表实例
const charts = reactive({
  timeSeries: null,
  trendComparison: null,
  heatmapLoad: null,
  memoryLineChart: null,  // 新增内存折线图
  networkLineChart: null,  // 新增网络流量折线图
  diskIoChart: null
});

// 定义resizeTimer变量，用于窗口大小变化时的延迟处理
let resizeTimer = null;

// 当前选中的指标和分析模式
const currentMetric = ref('cpu');
const compareMode = ref('yesterday'); // yesterday, beforeYesterday, lastWeek

// 图表配置
const chartConfig = {
  height: 320,
  padding: [30, 20, 50, 80],
  point: {
    size: 3,
    shape: 'circle',
    style: {
      stroke: '#fff',
      lineWidth: 1
    },
  },
};

// 定义指标配置
const metricConfig = {
  cpu: {
    name: 'CPU使用率',
    unit: '%',
    color: ['#1890ff', '#f5222d', '#faad14', '#52c41a', '#722ed1'],
    threshold: 90,
  },
  memory: {
    name: '内存使用率',
    unit: '%',
    color: ['#52c41a', '#fa8c16', '#fadb14', '#eb2f96', '#722ed1'],
    threshold: 90,
  },
  network: {
    name: '网络流量',
    unit: 'Mbps',
    color: ['#13c2c2', '#fa541c', '#a0d911', '#f759ab', '#9254de'],
    threshold: 800,
  },

};

// 获取服务器监控数据
async function fetchData(serverId = props.id) {
  try {
    data.loading = true;

    // 从route.query中获取id
    const id = serverId || route.query.id;
    
    // 使用时间store获取时间范围
    const startTime = getUTCTimestampWithOffset(timeStore.begin);
    const endTime = getUTCTimestampWithOffset(timeStore.over);
    
    // 构建请求参数，加上8小时的秒数(8*3600=28800)
    const params = {
      id: id,
      start_time: startTime + 28800,
      end_time: endTime + 28800
    };
    
    // 调用接口获取数据
    // const response = await api.post('/server/monitor', JSON.stringify(params), { baseURL: '/mock/' });
    const response = await getBusinessServers(params)
    const responseData = response.data;
    // 处理服务器列表数据
    data.servers = responseData.servers || [];
    
    // 默认选中第一个服务器
    if (data.servers.length > 0 && !data.selectedServer) {
      data.selectedServer = data.servers[0].name; // 修改为使用name作为selectedServer
    }
    
    // 处理告警数据 - 统一使用alerts属性
    data.alerts = responseData.alerts || [];
    
    // 处理时间序列数据
    data.timeSeries = responseData.timeSeries || {};
    
    // 统计数据
    data.statistics = {
      totalServers: data.servers.length,
      avgCpu: 0,
      avgMemory: 0,
      avgNetwork: 0
    };
    // 更新图表
    nextTick(() => {
      initCharts();
    });
  } catch (error) {
    console.error('获取数据失败:', error);
    ElMessage.error('获取监控数据失败');
  } finally {
    data.loading = false;
  }
}

// 监听route.query.id变化
watch(() => route.query.id, (newId) => {
  if (newId && newId !== props.id) {
    // 如果路由ID变化且不等于当前props.id，则更新数据
    fetchData(newId);
  }
}, { immediate: true });

// 监听ID变化
watch(() => props.id, (newValue) => {
  if (newValue) {
    fetchData();
  }
});

// 监听指标变化
watch(currentMetric, (newMetric) => {
  // 只重新渲染时间段对比趋势图表，不影响其他图表
  renderTrendComparisonChart();
});

// 监听对比模式变化
watch(compareMode, (newMode) => {
  renderTrendComparisonChart();
});


// 监听时间变化
watch(() => [timeStore.begin, timeStore.over], (newTimeRange) => {
  // 获取新的时间范围
  const startTime = getUTCTimestampWithOffset(timeStore.begin);
  const endTime = getUTCTimestampWithOffset(timeStore.over);
  
  
  // 重新获取数据
  fetchData();
});

// 监听选中服务器变化
watch(() => data.selectedServer, (newServer) => {
  if (newServer) {
    // 重新渲染趋势对比图表
    renderTrendComparisonChart();
  }
});



// 获取服务器状态类型
function getServerStatusType(status) {
  if (status === 'danger') {
    return 'danger';
  } else if (status === 'warning') {
    return 'warning';
  } else {
    return 'success';
  }
}

// 获取服务器状态文本
function getServerStatusText(status) {
  if (status === 'danger') {
    return '危险';
  } else if (status === 'warning') {
    return '警告';
  } else {
    return '正常';
  }
}

// 初始化图表
function initCharts() {
  // 初始化图表实例
  charts.timeSeries = null;
  charts.memoryLineChart = null;
  charts.networkLineChart = null;
  charts.diskIoChart = null;
  charts.heatmapLoad = null;
  charts.trendComparison = null;
  
  // 注册窗口大小变化事件，用于图表自适应
  window.addEventListener('resize', handleResize);
  
  // 初始渲染所有图表
  renderCharts();
}

// 渲染所有图表
function renderCharts() {
  // 渲染CPU时间序列图表
  renderTimeSeriesChart();
  
  // 渲染内存使用率折线图
  renderMemoryLineChart();
  
  // 渲染网络流量折线图
  renderNetworkLineChart();
  
  // 渲染热力图
  renderHeatmapLoadChart();
  
  // 渲染趋势对比图
  renderTrendComparisonChart();
}

// 处理窗口大小变化
function handleResize() {
  // 延迟执行以减少频繁调用
  if (resizeTimer) clearTimeout(resizeTimer);
  resizeTimer = setTimeout(() => {
    // 重新渲染所有图表
    renderCharts();
  }, 300);
}

// 日期格式化辅助函数
function formatChartTime(text) {
  // 原始格式为 "2025-05-26 08:00"，提取月日和时间
  const parts = text.split(' ');
  if (parts.length === 2) {
    const dateParts = parts[0].split('-');
    if (dateParts.length === 3) {
      return `${dateParts[1]}-${dateParts[2]} ${parts[1]}`;
    }
  }
  return text;
}

// 计算平均值辅助函数
function getAverage(data) {
  if (!data || data.length === 0) return 0;
  const sum = data.reduce((acc, item) => acc + item.value, 0);
  return sum / data.length;
}

// 渲染时间序列图表
function renderTimeSeriesChart() {
  const container = document.getElementById('timeSeriesChart');
  if (!container) return;
  
  // 确保销毁之前的图表实例
  if (charts.timeSeries) {
    try {
      charts.timeSeries.destroy();
      charts.timeSeries = null;
    } catch (error) {
      console.error('销毁时间序列图表失败:', error);
    }
  }
  
  // 清空容器内容，防止残留旧图表
  container.innerHTML = '';
  
  let chartData = [];
  
  // 从时间Store获取时间范围
  const startTime = new Date(timeStore.begin);
  const endTime = new Date(timeStore.over) || new Date();
  
  // 为每个服务器生成时间序列数据
  data.servers.forEach(server => {
    if (data.timeSeries[server.name] && data.timeSeries[server.name].cpu) {
      const timePoints = data.timeSeries[server.name].cpu;
      
      timePoints.forEach(point => {
        // 提取月-日 时:分
        const parts = point.time.split(' ');
        let displayTime = point.time;
        if (parts.length === 2) {
          const dateParts = parts[0].split('-');
          if (dateParts.length === 3) {
            displayTime = `${dateParts[1]}-${dateParts[2]} ${parts[1]}`;
          }
        }
        
        chartData.push({
          time: displayTime,
          originalTime: point.time,
          value: point.value * 100,
          server: server.name
        });
      });
    }
  });
  
  if (chartData.length > 0) {
    // 按时间排序数据
    chartData.sort((a, b) => new Date(a.originalTime) - new Date(b.originalTime));
    
    try {
      charts.timeSeries = new Line(container, {
        data: chartData,
        height: chartConfig.height,
        padding: [30, 30, 70, 100],
        xField: 'time',
        yField: 'value',
        seriesField: 'server',
        smooth: true,
        xAxis: {
          type: 'cat',
          title: {
            text: '时间',
          },
          label: {
            autoHide: false,
            autoRotate: true,
            style: {
              fontSize: 11,
              textAlign: 'center'
            }
          }
        },
        yAxis: {
          title: {
            text: metricConfig.cpu.name,
          },
          label: {
            formatter: (v) => `${v}${metricConfig.cpu.unit}`
          }
        },
        legend: {
          position: 'top',
        },
        point: chartConfig.point,
        tooltip: {
          formatter: (datum) => {
            return {
              name: datum.server,
              value: datum.value.toFixed(2) + metricConfig.cpu.unit
            };
          }
        },
        annotations: [
          {
            type: 'line',
            start: ['min', metricConfig.cpu.threshold],
            end: ['max', metricConfig.cpu.threshold],
            style: {
              stroke: '#ff4d4f',
              lineDash: [4, 4],
              lineWidth: 2,
            },
          }
        ]
      });
      
      charts.timeSeries.render();
    } catch (error) {
      console.error('渲染时间序列图表失败:', error);
    }
  }
}

// 渲染趋势对比图
function renderTrendComparisonChart() {
  // 获取容器元素
  const chartDom = document.getElementById('trendComparisonChart');
  if (!chartDom) return;

  // 先确保销毁之前的图表实例
  if (charts.trendComparison) {
    try {
      charts.trendComparison.destroy();
      charts.trendComparison = null;
    } catch (error) {
      console.error('销毁趋势对比图表失败:', error);
    }
  }

  // 清空容器内容，防止残留旧图表
  chartDom.innerHTML = '';

  if (!data.selectedServer || !currentMetric.value || !data.timeSeries) {
    return;
  }
  
  // 获取选中服务器的数据
  const serverData = data.timeSeries[data.selectedServer];
  
  if (!serverData || !serverData[currentMetric.value]) return;
  
  // 获取当前指标的数据
  const metricData = serverData[currentMetric.value];
  
  if (!metricData || metricData.length === 0) return;

  // 获取比较模式对应的数据键
  const compareKey = compareMode.value; // yesterday, beforeYesterday, lastWeek
  const compareLabel = compareMode.value === 'yesterday' ? '昨天' : 
                      compareMode.value === 'beforeYesterday' ? '前天' : '上周';
  
  // 先按时间排序当前数据，确保时间连续性
  const sortedData = [...metricData].sort((a, b) => new Date(a.time) - new Date(b.time));
  
  // 准备图表数据 - 当前数据
  const currentData = sortedData.map(item => {
    // 提取时间部分 (HH:MM)
    const timePart = item.time.split(' ')[1];
    // 提取日期部分 (MM-DD)
    const datePart = item.time.split(' ')[0].substring(5); // 去掉年份，只保留月-日
    
    return {
      timeKey: timePart, // 用于匹配比较数据
      time: `${datePart} ${timePart}`, // 显示用的格式化时间
      originalTime: item.time,
      value: item.value * 100,
      type: '当前'
    };
  });
  
  // 准备图表数据 - 对比数据
  const compareData = sortedData.map(item => {
    // 提取时间部分 (HH:MM)
    const timePart = item.time.split(' ')[1];
    // 提取日期部分 (MM-DD)
    const datePart = item.time.split(' ')[0].substring(5); // 去掉年份，只保留月-日
    
    // 从数据点直接获取历史比较值
    const compareValue = item[compareKey] || 0;
    
    return {
      timeKey: timePart,
      time: `${datePart} ${timePart}`, // 使用当前数据对应的日期
      originalTime: item.time,
      value: compareValue * 100, // 使用对应的比较数据，确保有值
      type: compareLabel
    };
  });
  
  // 计算环比变化 - 根据timeKey匹配当前与比较数据
  const columnData = [];
  
  // 对每个时间点计算环比变化
  sortedData.forEach((item, index) => {
    const currValue = item.value * 100;
    const prevValue = (item[compareKey] || 0) * 100;
    
    // 只计算有效的环比变化
    if (prevValue > 0) {
      const changeValue = parseFloat(((currValue - prevValue) / prevValue * 100).toFixed(2));
      
      // 提取时间信息
      const timePart = item.time.split(' ')[1];
      const datePart = item.time.split(' ')[0].substring(5);
      
      columnData.push({
        time: `${datePart} ${timePart}`,
        timeKey: timePart,
        change: changeValue,
        changeType: changeValue < 0 ? '下降' : '上升'  // 添加changeType字段用于颜色映射
      });
    }
  });

  // 用于图表展示的数据
  const lineData = [...currentData, ...compareData];

  // 创建图表实例
  try {
    charts.trendComparison = new DualAxes(chartDom, {
      data: [lineData, columnData],
      xField: 'time',
      yField: ['value', 'change'],
      geometryOptions: [
        // 第一个几何图形：线图
        {
          geometry: 'line',
          seriesField: 'type',
          smooth: true,
          color: ({ type }) => {
            if (type === '当前') return '#1890ff';
            return '#fa8c16';
          },
          lineStyle: ({ type }) => {
            if (type === '当前') {
              return {
                lineWidth: 2,
                lineDash: []
              };
            }
            return {
              lineWidth: 2,
              lineDash: [4, 4]
            };
          },
          point: {
            shape: ({ type }) => {
              return type === '当前' ? 'circle' : 'square';
            },
            size: 4,
            style: ({ type }) => {
              if (type === '当前') {
                return {
                  fill: '#1890ff',
                  stroke: '#fff',
                  lineWidth: 1
                };
              }
              return {
                fill: '#fa8c16',
                stroke: '#fff',
                lineWidth: 1
              };
            }
          }
        },
        // 第二个几何图形：柱状图
        {
          geometry: 'column',
          // 完全使用自定义颜色映射，不依赖内部逻辑
          seriesField: 'changeType',  // 添加一个seriesField，强制图表基于这个字段应用颜色
          color: ({ changeType }) => {
            return changeType === '上升' ? '#f5222d' : '#52c41a';  // 上升红色，下降绿色
          },
          columnWidthRatio: 0.4,
          label: false, // 移除柱状图上的文字标签，以减少拥挤
        }
      ],
      legend: {
        position: 'top', // 只保留一个图例，放在顶部
        custom: true,
        items: [
          { name: '当前', value: '当前', marker: { style: { fill: '#1890ff' } } },
          { name: compareLabel, value: compareLabel, marker: { style: { fill: '#fa8c16', r: 4, symbol: 'square' } } },
          { name: '上升', value: '上升', marker: { style: { fill: '#f5222d' } } },
          { name: '下降', value: '下降', marker: { style: { fill: '#52c41a' } } }
        ]
      },
      xAxis: {
        title: {
          text: '时间',
        },
        label: {
          autoHide: true, // 设为true，自动隐藏部分标签避免拥挤
          autoRotate: true,
          style: {
            fontSize: 10
          }
        },
        // 显示时间轴
        grid: {
          line: {
            style: {
              stroke: '#e8e8e8',
              lineWidth: 1,
              lineDash: [4, 4]
            }
          }
        },
        line: {
          style: {
            stroke: '#e8e8e8',
            lineWidth: 1
          }
        },
        tickLine: {
          style: {
            stroke: '#e8e8e8',
            lineWidth: 1
          }
        }
      },
      yAxis: {
        value: {
          title: {
            text: metricConfig[currentMetric.value].name,
          },
          grid: {
            line: {
              style: {
                stroke: '#e8e8e8',
                lineWidth: 1,
                lineDash: [4, 4]
              }
            }
          },
          // 优化Y轴显示
          tickCount: 5,
          nice: true
        },
        change: {
          title: {
            text: '环比变化率(%)',
          },
          grid: null,
          min: -50,
          max: 50,
          tickCount: 5
        }
      },
      // 配置tooltip
      tooltip: {
        showMarkers: true,
        shared: true,
        showCrosshairs: true,
        crosshairs: {
          type: 'x',
        },
        domStyles: {
          'g2-tooltip-title': {
            fontWeight: 'bold',
            marginBottom: '8px'
          }
        },
        formatter: (datum) => {
          if (datum.type) {
            return { name: `${datum.type}${metricConfig[currentMetric.value].name}`, value: datum.value.toFixed(2) + '%' };
          } else if (datum.change !== undefined) {
            const prefix = datum.change >= 0 ? '+' : '';
            // 添加箭头表示趋势方向
            const trend = datum.change >= 0 ? '↑' : '↓';
            const color = datum.change >= 0 ? 'red' : 'green';
            return { 
              name: '环比变化', 
              value: `<span style="color:${color}">${trend} ${prefix}${datum.change}%</span>` 
            };
          }
          return { name: '', value: '' };
        }
      },
      annotations: {
        value: [
          // 添加平均值标注线
          {
            type: 'line',
            start: ['min', getAverage(currentData)],
            end: ['max', getAverage(currentData)],
            style: {
              stroke: '#1890ff',
              lineDash: [4, 4],
              lineWidth: 1,
            },
            text: {
              content: `当前平均值: ${getAverage(currentData).toFixed(2)}${metricConfig[currentMetric.value].unit}`,
              position: 'end',
              style: {
                textAlign: 'end',
                fontSize: 12,
                fill: '#1890ff',
              },
            },
          },
          {
            type: 'line',
            start: ['min', getAverage(compareData)],
            end: ['max', getAverage(compareData)],
            style: {
              stroke: '#fa8c16',
              lineDash: [4, 4],
              lineWidth: 1,
            },
            text: {
              content: `${compareLabel}平均值: ${getAverage(compareData).toFixed(2)}${metricConfig[currentMetric.value].unit}`,
              position: 'end',
              style: {
                textAlign: 'end',
                fontSize: 12,
                fill: '#fa8c16',
              },
            },
          }
        ]
      },
      interactions: [{ type: 'element-active' }]
    });
    
    charts.trendComparison.render();
  } catch (error) {
    console.error('渲染服务器时间段对比趋势图失败:', error);
  }
}

// 渲染服务器负载分布热力图
function renderHeatmapLoadChart() {
  const container = document.getElementById('heatmapLoadChart');
  if (!container || !data.timeSeries) {
    console.warn('HeatmapLoadChart: Container not found or no timeSeries data.');
    return;
  }
  
  // 确保销毁之前的图表实例
  if (charts.heatmapLoad) {
    try {
      charts.heatmapLoad.destroy();
      charts.heatmapLoad = null;
    } catch (error) {
      console.error('销毁热力图表失败:', error);
    }
  }
  
  // 清空容器内容，防止残留旧图表
  container.innerHTML = '';
  
  // 改为使用时间序列中的disks数据展示磁盘IO队列
  const diskIoData = [];
  
  // 处理每个服务器的每个磁盘，从timeSeries中获取数据
  Object.keys(data.timeSeries).forEach(serverName => {
    const serverData = data.timeSeries[serverName];
    if (serverData && serverData.disks) {
      // 遍历每个磁盘
      Object.keys(serverData.disks).forEach(diskName => {
        // 获取磁盘数据
        const diskData = serverData.disks[diskName];
        
        if (diskData && diskData.length > 0) {
          // 使用所有数据，不额外截断
          diskData.forEach(point => {
            diskIoData.push({
              disk: `${serverName}: ${diskName}`,
              time: point.time,
              value: point.value,
              category: getDiskIoCategory(point.value)
            });
          });
        }
      });
    }
  });
  
  // 根据IO值获取类别
  function getDiskIoCategory(value) {
    const val = parseFloat(value);
    if (val < 0.05) return 'low';
    if (val < 0.10) return 'medium-low';
    if (val < 0.15) return 'medium';
    if (val < 0.20) return 'medium-high';
    return 'high';
  }
  
  // 排序磁盘IO数据，确保时间顺序正确
  diskIoData.sort((a, b) => new Date(a.time) - new Date(b.time));
  
  // 使用热力图显示磁盘IO队列
  if (diskIoData.length > 0) {
    try {
      charts.heatmapLoad = new Heatmap(container, {
        data: diskIoData,
        height: chartConfig.height * 1.5, // 增加高度以适应更多磁盘行
        padding: [30, 30, 50, 240], // 增加左侧padding以适应更长的标签
        xField: 'time',
        yField: 'disk',
        colorField: 'value',
        color: ({ value }) => {
          // 使用参考图的绿色渐变色系
          if (value < 0.05) {
            return '#2C6E31'; // 深绿色 - 低IO
          } else if (value < 0.10) {
            return '#2D8134'; // 绿色 - 中低IO
          } else if (value < 0.15) {
            return '#3BAA3E'; // 浅绿色 - 中IO
          } else if (value < 0.20) {
            return '#7BD039'; // 黄绿色 - 中高IO
          } else if (value < 0.25) {
            return '#FFCE54'; // 黄色 - 高IO
          } else {
            return '#FF6B6B'; // 红色 - 超高IO
          }
        },
        shape: 'rect',
        heatmapStyle: {
          stroke: '#e8e8e8',
          lineWidth: 1,
        },
        meta: {
          value: {
            formatter: (val) => `${val.toFixed(2)}`
          }
        },
        xAxis: {
          title: {
            text: '时间',
          },
          label: {
            autoHide: false,
            autoRotate: true,
            formatter: formatChartTime,
            style: {
              fontSize: 10,
              fill: '#333'
            }
          },
          // 移除动态调整刻度数，显示所有数据点
          // tickCount: Math.min(24, Math.max(6, diskIoData.length > 0 ? Math.ceil(diskIoData.length / 8) : 8)),
          line: {
            style: {
              stroke: '#eee',
            }
          },
        },
        yAxis: {
          title: {
            text: '设备IO平均队列长度',
            style: {
              fontSize: 12,
            }
          },
          label: {
            style: {
              fontSize: 10,
              fill: '#333'
            },
            // 处理长文本，使其截断显示
            formatter: (text) => {
              const maxLength = 22;
              if (text.length > maxLength) {
                return text.substring(0, maxLength) + '...';
              }
              return text;
            }
          },
          line: {
            style: {
              stroke: '#eee',
            }
          },
        },
        tooltip: {
          showTitle: true,
          title: '磁盘IO队列',
          formatter: (datum) => {
            return {
              name: datum.disk,
              value: `IO队列: ${datum.value.toFixed(2)}`
            };
          }
        },
        interactions: [
          { type: 'element-active' }
        ],
        legend: {
          position: 'bottom-left',
        },
        theme: {
          backgroundColor: '#ffffff'
        }
      });
      
      charts.heatmapLoad.render();
    } catch (error) {
      console.error('渲染热力图表失败:', error);
    }
  }
}

// 渲染内存使用率折线图
function renderMemoryLineChart() {
  const container = document.getElementById('memoryLineChart');
  if (!container) return;
  
  // 确保销毁之前的图表实例
  if (charts.memoryLineChart) {
    try {
      charts.memoryLineChart.destroy();
      charts.memoryLineChart = null;
    } catch (error) {
      console.error('销毁内存折线图表失败:', error);
    }
  }
  
  // 清空容器内容，防止残留旧图表
  container.innerHTML = '';
  
  let chartData = [];
  
  // 为每个服务器生成时间序列数据 - 内存数据
  data.servers.forEach(server => {
    if (data.timeSeries[server.name] && data.timeSeries[server.name].memory) {
      const timePoints = data.timeSeries[server.name].memory;
      
      timePoints.forEach(point => {
        // 提取月-日 时:分
        const parts = point.time.split(' ');
        let displayTime = point.time;
        if (parts.length === 2) {
          const dateParts = parts[0].split('-');
          if (dateParts.length === 3) {
            displayTime = `${dateParts[1]}-${dateParts[2]} ${parts[1]}`;
          }
        }
        
        chartData.push({
          time: displayTime,
          originalTime: point.time,
          value: point.value * 100,
          server: server.name
        });
      });
    }
  });
  
  if (chartData.length > 0) {
    // 按时间排序数据
    chartData.sort((a, b) => new Date(a.originalTime) - new Date(b.originalTime));
    
    try {
      charts.memoryLineChart = new Line(container, {
        data: chartData,
        height: chartConfig.height,
        padding: [30, 30, 70, 100],
        xField: 'time',
        yField: 'value',
        seriesField: 'server',
        smooth: true,
        xAxis: {
          type: 'cat',
          title: {
            text: '时间',
          },
          label: {
            autoHide: false,
            autoRotate: true,
            style: {
              fontSize: 11,
              textAlign: 'center'
            }
          }
        },
        yAxis: {
          title: {
            text: metricConfig.memory.name,
          },
          label: {
            formatter: (v) => `${v}${metricConfig.memory.unit}`
          }
        },
        legend: {
          position: 'top',
        },
        point: chartConfig.point,
        tooltip: {
          formatter: (datum) => {
            return {
              name: datum.server,
              value: datum.value.toFixed(2) + metricConfig.memory.unit
            };
          }
        },
        annotations: [
          {
            type: 'line',
            start: ['min', metricConfig.memory.threshold],
            end: ['max', metricConfig.memory.threshold],
            style: {
              stroke: '#ff4d4f',
              lineDash: [4, 4],
              lineWidth: 2,
            },
          }
        ]
      });
      
      charts.memoryLineChart.render();
    } catch (error) {
      console.error('渲染内存折线图表失败:', error);
    }
  }
}

// 渲染网络流量折线图
function renderNetworkLineChart() {
  const container = document.getElementById('networkLineChart');
  if (!container) return;
  
  // 确保销毁之前的图表实例
  if (charts.networkLineChart) {
    try {
      charts.networkLineChart.destroy();
      charts.networkLineChart = null;
    } catch (error) {
      console.error('销毁网络折线图表失败:', error);
    }
  }
  
  // 清空容器内容，防止残留旧图表
  container.innerHTML = '';
  
  let chartData = [];
  
  // 为每个服务器生成时间序列数据 - 网络流量数据
  data.servers.forEach(server => {
    if (data.timeSeries[server.name] && data.timeSeries[server.name].network) {
      const timePoints = data.timeSeries[server.name].network;
      
      timePoints.forEach(point => {
        // 提取月-日 时:分
        const parts = point.time.split(' ');
        let displayTime = point.time;
        if (parts.length === 2) {
          const dateParts = parts[0].split('-');
          if (dateParts.length === 3) {
            displayTime = `${dateParts[1]}-${dateParts[2]} ${parts[1]}`;
          }
        }
        
        chartData.push({
          time: displayTime,
          originalTime: point.time,
          value: point.value,  // 直接使用原始值，不进行百分比转换
          server: server.name
        });
      });
    }
  });
  
  if (chartData.length > 0) {
    // 按时间排序数据
    chartData.sort((a, b) => new Date(a.originalTime) - new Date(b.originalTime));
    
    try {
      charts.networkLineChart = new Line(container, {
        data: chartData,
        height: chartConfig.height,
        padding: [30, 30, 70, 100],
        xField: 'time',
        yField: 'value',
        seriesField: 'server',
        smooth: true,
        xAxis: {
          type: 'cat',
          // 移除动态调整刻度数，显示所有数据点
          // tickCount: Math.min(20, Math.max(8, Math.ceil(chartData.length / 10))),
          title: {
            text: '时间',
          },
          label: {
            autoHide: false,
            autoRotate: true,
            style: {
              fontSize: 11,
              textAlign: 'center'
            }
          }
        },
        yAxis: {
          title: {
            text: metricConfig.network.name,
          },
          // 移除固定刻度，让图表自动确定合适的刻度范围
          // min: 0,
          // max: 100,
          label: {
            formatter: (v) => `${v}${metricConfig.network.unit}`
          }
        },
        legend: {
          position: 'top',
        },
        point: chartConfig.point,
        tooltip: {
          formatter: (datum) => {
            return {
              name: datum.server,
              value: datum.value.toFixed(2) + metricConfig.network.unit
            };
          }
        },
      });
      
      charts.networkLineChart.render();
    } catch (error) {
      console.error('渲染网络折线图表失败:', error);
    }
  }
}
</script>

<template>
  <div class="server-monitor-container" v-loading="data.loading">
    <!-- 服务器简要信息卡片 -->
    <div class="server-cards-wrapper">
      <div class="server-mini-cards">
        <el-card 
          v-for="server in data.servers" 
          
          class="server-mini-card" 
          shadow="hover"
          :class="{'status-warning': server.status === 'warning', 'status-danger': server.status === 'danger'}"
        >
          <div class="server-mini-header">
            <span class="server-title">{{ server.name }}</span>
            <el-tag :type="getServerStatusType(server.status)" size="small" effect="dark" class="status-tag">
              {{ getServerStatusText(server.status) }}
            </el-tag>
          </div>
          <div class="server-mini-info">
            <div class="server-mini-item">
              <div class="server-icon">
            <el-icon ><svg-icon name="server" /></el-icon>
              </div>
              <div class="server-basic">
                <div class="server-ip">{{ server.ip }}</div>
                <div class="server-system">{{ server.system }}</div>
              </div>
            </div>
            <div class="server-mini-metrics">
              <div class="mini-metric">
                <div class="metric-header">
                  <span class="metric-name">CPU</span>
                  <span class="metric-value" :class="{'warning': server.metrics.cpu > 0.7, 'danger': server.metrics.cpu > 0.9}">
                    {{ (server.metrics.cpu * 100).toFixed(2) }}%
                  </span>
                </div>
                <el-progress 
                  :percentage="server.metrics.cpu * 100" 
                  :color="server.metrics.cpu * 100 > 90 ? '#F56C6C' : server.metrics.cpu * 100 > 70 ? '#E6A23C' : '#67C23A'"
                  :stroke-width="6"
                  :show-text="false"
                  class="metric-progress"
                />
              </div>
              <div class="mini-metric">
                <div class="metric-header">
                  <span class="metric-name">内存</span>
                  <span class="metric-value" :class="{'warning': server.metrics.memory > 70, 'danger': server.metrics.memory > 90}">
                    {{ (server.metrics.memory).toFixed(2) }}%
                  </span>
                </div>
                <el-progress 
                  :percentage="server.metrics.memory" 
                  :color="server.metrics.memory > 90 ? '#F56C6C' : server.metrics.memory > 70 ? '#E6A23C' : '#67C23A'"
                  :stroke-width="6"
                  :show-text="false"
                  class="metric-progress"
                />
              </div>
              <div class="mini-metric">
                <div class="metric-header">
                  <span class="metric-name">网络</span>
                  <span class="metric-value">{{ server.metrics.network.toFixed(2) }} Mbps</span>
                </div>
              </div>

              <div class="server-footer">
                <span class="uptime-label">运行时间: {{ server.uptime }}</span>
                <span class="processes-label">进程数: {{ server.processes || 0 }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 时间序列图表 -->
      <el-card class="chart-card full-width" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>CPU指标时间序列分析</span>
            <div class="chart-subtitle">基于选定的时间范围进行分析</div>
          </div>
        </template>
        <div id="timeSeriesChart" class="chart-container"></div>
      </el-card>

      <!-- 内存使用率折线图 -->
      <el-card class="chart-card full-width" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>内存使用率时间序列分析</span>
            <div class="chart-subtitle">基于选定的时间范围进行分析</div>
          </div>
        </template>
        <div class="chart-description">
          <p>
            内存使用率折线图展示了各服务器随时间变化的内存消耗情况，可以发现内存使用异常或泄漏问题。
          </p>
        </div>
        <div id="memoryLineChart" class="chart-container"></div>
      </el-card>

      <!-- 网络流量折线图 -->
      <el-card class="chart-card full-width" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>网络流量时间序列分析</span>
            <div class="chart-subtitle">基于选定的时间范围进行分析</div>
          </div>
        </template>
        <div class="chart-description">
          <p>
            网络流量折线图展示了各服务器的网络吞吐量变化，可以发现网络异常或流量激增问题。
          </p>
        </div>
        <div id="networkLineChart" class="chart-container"></div>
      </el-card>

      <!-- 服务器磁盘IO分布热力图 -->
      <el-card class="chart-card full-width" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>磁盘IO队列</span>
            <div class="chart-subtitle">展示所有服务器磁盘的IO平均队列长度</div>
          </div>
        </template>
        <div class="chart-description">
          <p>
            颜色深浅表示磁盘IO队列长度:
            <span class="color-dot very-low"></span> 低负载 (&lt;0.05) 
            <span class="color-dot low"></span> 中低负载 (0.05-0.10)
            <span class="color-dot medium"></span> 中等负载 (0.10-0.15)
            <span class="color-dot medium-high"></span> 中高负载 (0.15-0.20)
            <span class="color-dot high"></span> 高负载 (&gt;0.20)
          </p>
        </div>
        <div id="heatmapLoadChart" class="chart-container dark-chart"></div>
      </el-card>

      <!-- 时间段对比趋势图 -->
      <el-card class="chart-card full-width" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>服务器时间段对比趋势</span>
            <div class="chart-controls">
              <span class="control-label">选择服务器:</span>
              <el-select 
                v-model="data.selectedServer" 
                placeholder="选择服务器" 
                size="small" 
                style="width: 200px; margin-right: 15px;"
              >
                <el-option 
                  v-for="server in data.servers" 
                  
                  :label="server.name" 
                  :value="server.name" 
                />
              </el-select>
              <span class="control-label">指标类型:</span>
              <el-radio-group v-model="currentMetric" size="small" style="margin-right: 15px;">
                <el-radio-button label="cpu">CPU</el-radio-button>
                <el-radio-button label="memory">内存</el-radio-button>
                <el-radio-button label="network">网络</el-radio-button>
              </el-radio-group>
              <span class="control-label">对比类型:</span>
              <el-radio-group v-model="compareMode" size="small">
                <el-radio-button label="yesterday">与昨天</el-radio-button>
                <el-radio-button label="beforeYesterday">与前天</el-radio-button>
                <el-radio-button label="lastWeek">与上周</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <div class="chart-description">
          <p>双线对比图表：实线表示当前所选服务器的<strong>{{ metricConfig[currentMetric].name }}</strong>指标值，虚线表示历史对比时间段的指标值。</p>
          <p>柱状图表示环比变化： <span class="trend-dot increase"></span> 增长（红色）对比历史有上升趋势 <span class="trend-dot decrease"></span> 下降（绿色）对比历史有下降趋势</p>
          <p>水平线表示各时间段的指标平均值，可直观对比不同时段的性能变化趋势。</p>
        </div>
        <div id="trendComparisonChart" class="chart-container"></div>
      </el-card>
    </div>

    <!-- 告警时间线 -->
    <AlertTimeline :alerts="data.alerts" />
  </div>
</template>

<style lang="scss" scoped>
.server-monitor-container {
  padding: 20px;
  background-color: #f5f7fa;
}

// 过滤器样式
.filter-card {
  margin-bottom: 20px;
  border-radius: 8px;
  
  .filter-header {
    margin-bottom: 15px;
    
    .filter-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      
      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
  }
  
  .filter-content {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    
    .filter-item {
      display: flex;
      align-items: center;
      margin-right: 20px;
      
      .filter-label {
        margin-right: 10px;
        color: #606266;
        white-space: nowrap;
      }
    }
  }
}

// 服务器迷你卡片
.server-cards-wrapper {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
  padding: 10px 0;
  
  /* 滚动条样式 */
  &::-webkit-scrollbar {
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

.server-mini-cards {
  display: flex;
  flex-wrap: nowrap;
  gap: 15px;
  min-width: 100%;
  padding: 5px;
  
  .server-mini-card {
    min-width: 300px;
    max-width: 350px;
    flex-shrink: 0;
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #52c41a, #52c41a);
      z-index: 1;
    }
    
    &.status-warning::before {
      background: linear-gradient(90deg, #faad14, #fa8c16);
    }
    
    &.status-danger::before {
      background: linear-gradient(90deg, #f5222d, #ff4d4f);
    }
    
    .server-mini-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 12px;
      margin-bottom: 12px;
      
      .server-title {
        font-weight: 600;
        font-size: 15px;
        color: #262626;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
      }
      
      .status-tag {
        padding: 0 8px;
        border-radius: 12px;
        font-weight: 600;
      }
    }
    
    .server-mini-info {
      .server-mini-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        .server-icon {
          margin-right: 10px;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #f0f5ff;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #1890ff;
          font-size: 18px;
        }
        
        .server-basic {
          .server-ip {
            color: #262626;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
          }
          
          .server-system {
            color: #8c8c8c;
            font-size: 12px;
          }
        }
      }
      
      .server-mini-metrics {
        .mini-metric {
          margin-bottom: 10px;
          
          .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            
            .metric-name {
              color: #595959;
              font-size: 13px;
              font-weight: 500;
            }
            
            .metric-value {
              font-weight: 600;
              font-size: 13px;
              color: #262626;
              
              &.warning {
                color: #faad14;
              }
              
              &.danger {
                color: #f5222d;
              }
            }
          }
          
          .metric-progress {
            margin-bottom: 4px;
          }
        }
        
        .server-footer {
          display: flex;
          justify-content: space-between;
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px dashed #f0f0f0;
          
          .uptime-label, .processes-label {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }
  }
}

// 图表容器
.charts-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
  
  .full-width {
    grid-column: span 2;
  }
  
  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
    
    .full-width {
      grid-column: span 1;
    }
  }
}

.chart-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
    
    span {
      font-weight: 600;
      font-size: 16px;
      color: #262626;
      position: relative;
      padding-left: 12px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: linear-gradient(to bottom, #1890ff, #096dd9);
        border-radius: 2px;
      }
    }
  }
  
  .chart-subtitle {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 4px;
  }
  
  .chart-controls {
    display: flex;
    align-items: center;
    
    .control-label {
      margin-right: 8px;
      font-size: 14px;
      color: #606266;
    }
  }
  
  .chart-container {
    height: 320px;
    padding: 16px 0;
  }
}

.chart-description {
  color: #8c8c8c;
  font-size: 12px;
  margin: 0 20px 10px;
  padding: 8px 12px;
  border-left: 3px solid #1890ff;
  background-color: #f6f8fa;
  border-radius: 4px;
  
  p {
    margin: 5px 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .color-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px 0 8px;
    
    &.good { background-color: #30BF78; }
    &.success { background-color: #73D13D; }
    &.warning { background-color: #faad14; }
    &.danger { background-color: #f5222d; }
    
    // 磁盘IO队列特定颜色
    &.very-low { background-color: #2C6E31; }
    &.low { background-color: #2D8134; }
    &.medium { background-color: #3BAA3E; }
    &.medium-high { background-color: #7BD039; }
    &.high { background-color: #FF6B6B; }
  }
  
  .trend-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px 0 8px;
    
    &.increase { background-color: #f5222d; }
    &.decrease { background-color: #52c41a; }
  }
}

.dark-chart {
  background-color: #ffffff;
  border-radius: 4px;
}
</style> 