<script setup>
import { reactive, ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  businessData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:visible', 'submit', 'cancel']);

// 表单数据
const formData = reactive({
  productName: '',  // 产品名称
  serviceName: '',  // 服务名称
  groupName: '',    // 业务分组
  instanceName: '', // 实例名称
  serverUrl: '',    // 服务地址
  port: '',         // 运行端口
  ip: '',           // IP地址
  type: 'service',  // 类型，默认为业务服务
  installDirForDisplay: '', // 安装目录
  currentVersion: '', // 当前版本
  
  // 服务器
  clusterNodes: [],
  
  // 关联数据库
  relationDatabaseList: [],
  
  // 关联服务
  relationServiceList: []
});

// 服务器节点表单
const serverForm = reactive({
  name: '',
  ip: '',
  type: 'service'
});

// 关联数据库表单
const databaseForm = reactive({
  serviceName: '',
  ip: '',
  port: '',
  database_type: 'oracle'
});

// 关联服务表单
const serviceForm = reactive({
  serviceName: '',
  ip: '',
  port: ''
});

// 类型选项
const typeOptions = [
  { label: '业务服务', value: 'service' },
  { label: 'Oracle数据库', value: 'oracle' },
  { label: 'PostgreSQL数据库', value: 'pg' },
  { label: 'Nginx中间件', value: 'nginx' },
  { label: 'IIS中间件', value: 'iis' }
];

// 数据库类型选项
const dbTypeOptions = [
  { label: 'Oracle', value: 'oracle' },
  { label: 'PostgreSQL', value: 'pg' }
];

// 表单校验规则
const rules = {
  productName: [
    { required: true, message: '请输入产品名称', trigger: 'blur' }
  ],
  serviceName: [
    { required: true, message: '请输入服务名称', trigger: 'blur' }
  ],
  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址', trigger: 'blur' }
  ],
  port: [
    { pattern: /^\d+$/, message: '端口必须是数字', trigger: 'blur' }
  ]
};

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = ref(props.visible);

// 监听IP和端口变化，自动更新服务地址
watch([() => formData.ip, () => formData.port], ([newIp, newPort]) => {
  if (newIp && newPort) {
    // 根据类型选择协议
    let protocol = 'http://';
    if (formData.type === 'oracle') {
      protocol = 'oracle://';
    } else if (formData.type === 'pg') {
      protocol = 'postgresql://';
    }
    
    // 构建服务地址
    formData.serverUrl = `${protocol}${newIp}:${newPort}`;
  }
}, { immediate: true });

// 监听类型变化，更新服务地址协议
watch(() => formData.type, (newType) => {
  if (formData.ip && formData.port) {
    let protocol = 'http://';
    if (newType === 'oracle') {
      protocol = 'oracle://';
    } else if (newType === 'pg') {
      protocol = 'postgresql://';
    }
    
    // 更新服务地址协议部分
    formData.serverUrl = `${protocol}${formData.ip}:${formData.port}`;
  }
});

// 添加服务器节点
function addServerNode() {
  if (!serverForm.name || !serverForm.ip) {
    return;
  }
  
  formData.clusterNodes.push({
    name: serverForm.name,
    ip: serverForm.ip,
    type: serverForm.type,
    problem: [],  // 空问题列表
    status: 'success'  // 默认状态为成功
  });
  
  // 清空表单
  serverForm.name = '';
  serverForm.ip = '';
  serverForm.type = 'service';
}

// 添加关联数据库
function addDatabase() {
  if (!databaseForm.serviceName || !databaseForm.ip) {
    return;
  }
  
  formData.relationDatabaseList.push({
    serviceName: databaseForm.serviceName,
    ip: databaseForm.ip,
    port: databaseForm.port || (databaseForm.database_type === 'oracle' ? '1521' : '5432'),
    database_type: databaseForm.database_type
  });
  
  // 清空表单
  databaseForm.serviceName = '';
  databaseForm.ip = '';
  databaseForm.port = '';
  databaseForm.database_type = 'oracle';
}

// 添加关联服务
function addService() {
  if (!serviceForm.serviceName || !serviceForm.ip) {
    return;
  }
  
  formData.relationServiceList.push({
    serviceName: serviceForm.serviceName,
    ip: serviceForm.ip,
    port: serviceForm.port
  });
  
  // 清空表单
  serviceForm.serviceName = '';
  serviceForm.ip = '';
  serviceForm.port = '';
}

// 删除服务器节点
function removeServerNode(index) {
  formData.clusterNodes.splice(index, 1);
}

// 删除关联数据库
function removeDatabase(index) {
  formData.relationDatabaseList.splice(index, 1);
}

// 删除关联服务
function removeService(index) {
  formData.relationServiceList.splice(index, 1);
}

// 提交表单
function submitForm() {
  if (!formRef.value) return;
  
  formRef.value.validate((valid) => {
    if (valid) {
      // 处理端口和URL
      if (formData.port && !formData.serverUrl.includes(':')) {
        // 如果有端口但URL中不包含端口，则添加端口
        const protocol = formData.serverUrl.startsWith('http') ? '' : 'http://';
        formData.serverUrl = `${protocol}${formData.ip}:${formData.port}`;
      }
      
      // 构建业务对象
      const businessData = {
        ...formData,
        status: 'success',  // 默认状态为成功
        problem: []         // 空问题列表
      };
      
      // 发送数据到父组件
      emit('submit', businessData);
      // 关闭对话框
      closeDialog();
    }
  });
}

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  
  // 当对话框打开且有业务数据时，填充表单
  if (newVal && props.businessData) {
    fillFormWithBusinessData();
  }
});

// 监听businessData变化
watch(() => props.businessData, (newVal) => {
  if (newVal && dialogVisible.value) {
    fillFormWithBusinessData();
  }
}, { deep: true });

// 填充表单数据
function fillFormWithBusinessData() {
  if (!props.businessData) return;
  
  console.log('填充表单数据:', props.businessData);
  
  // 填充基本信息
  formData.productName = props.businessData.productName || '';
  formData.serviceName = props.businessData.serviceName || '';
  formData.groupName = props.businessData.groupName || '';
  formData.instanceName = props.businessData.instanceName || '';
  formData.serverUrl = props.businessData.serverUrl || '';
  formData.ip = props.businessData.ip || '';
  
  // 从 serverUrl 中提取端口号
  if (props.businessData.serverUrl) {
    const urlMatch = props.businessData.serverUrl.match(/:(\d+)$/);
    if (urlMatch && urlMatch[1]) {
      formData.port = urlMatch[1];
    } else {
      formData.port = props.businessData.port || '';
    }
  } else {
    formData.port = props.businessData.port || '';
  }
  
  formData.type = props.businessData.type || 'service';
  formData.installDirForDisplay = props.businessData.installDirForDisplay || '';
  formData.currentVersion = props.businessData.currentVersion || '';
  
  // 填充集群节点
  formData.clusterNodes = [];
  if (props.businessData.clusterNodes && Array.isArray(props.businessData.clusterNodes)) {
    formData.clusterNodes = [...props.businessData.clusterNodes];
  } else if (props.businessData.cluster && Array.isArray(props.businessData.cluster)) {
    formData.clusterNodes = [...props.businessData.cluster];
  }
  
  // 填充关联数据库
  formData.relationDatabaseList = [];
  if (props.businessData.relationDatabaseList && Array.isArray(props.businessData.relationDatabaseList)) {
    formData.relationDatabaseList = [...props.businessData.relationDatabaseList];
  }
  
  // 填充关联服务
  formData.relationServiceList = [];
  if (props.businessData.relationServiceList && Array.isArray(props.businessData.relationServiceList)) {
    formData.relationServiceList = [...props.businessData.relationServiceList];
  }
}

// 关闭对话框
function closeDialog() {
  dialogVisible.value = false;
  emit('update:visible', false);
  
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  
  // 清空服务器和关联列表
  formData.clusterNodes = [];
  formData.relationDatabaseList = [];
  formData.relationServiceList = [];
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="props.businessData ? '编辑业务' : '新增业务'"
    width="750px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="business-form"
    >
      <!-- 基本信息部分 -->
      <div class="form-section">
        <div class="section-title">
          <i class="el-icon-document"></i>
          <span>基本信息</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="formData.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="服务名称" prop="serviceName">
              <el-input v-model="formData.serviceName" placeholder="请输入服务名称" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="业务分组" prop="groupName">
              <el-input v-model="formData.groupName" placeholder="请输入业务分组" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="实例名称" prop="instanceName">
              <el-input v-model="formData.instanceName" placeholder="请输入实例名称" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="IP地址" prop="ip">
              <el-input v-model="formData.ip" placeholder="请输入IP地址" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="运行端口" prop="port">
              <el-input v-model="formData.port" placeholder="请输入端口号" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择类型">
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="服务地址" prop="serverUrl">
              <el-input v-model="formData.serverUrl" placeholder="自动生成，也可手动修改" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="当前版本" prop="currentVersion">
              <el-input v-model="formData.currentVersion" placeholder="请输入当前版本" />
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="安装目录" prop="installDirForDisplay">
              <el-input v-model="formData.installDirForDisplay" placeholder="请输入安装目录" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      
      <!-- 服务器部分 -->
      <div class="form-section">
        <div class="section-title">
          <i class="el-icon-s-grid"></i>
          <span>服务器</span>
        </div>
        
        <div class="add-item-form">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-input v-model="serverForm.name" placeholder="服务器名称" />
            </el-col>
            
            <el-col :span="8">
              <el-input v-model="serverForm.ip" placeholder="IP地址" />
            </el-col>
            
            <el-col :span="5">
              <el-select v-model="serverForm.type" placeholder="类型">
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            
            <el-col :span="3">
              <el-button type="primary" @click="addServerNode" :disabled="!serverForm.name || !serverForm.ip">
                添加
              </el-button>
            </el-col>
          </el-row>
        </div>
        
        <!-- 已添加的服务器列表 -->
        <div class="added-items" v-if="formData.clusterNodes.length > 0">
          <el-table :data="formData.clusterNodes" style="width: 100%" border>
            <el-table-column prop="name" label="服务器名称" />
            <el-table-column prop="ip" label="IP地址" />
            <el-table-column prop="type" label="类型">
              <template #default="scope">
                {{ typeOptions.find(item => item.value === scope.row.type)?.label || scope.row.type }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeServerNode(scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="no-data" v-else>
          <el-empty description="暂无服务器" :image-size="60" />
        </div>
      </div>
      
      <!-- 关联数据库部分 -->
      <div class="form-section">
        <div class="section-title">
          <i class="el-icon-coin"></i>
          <span>关联数据库</span>
        </div>
        
        <div class="add-item-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input v-model="databaseForm.serviceName" placeholder="数据库名称" />
            </el-col>
            
            <el-col :span="6">
              <el-input v-model="databaseForm.ip" placeholder="IP地址" />
            </el-col>
            
            <el-col :span="4">
              <el-input v-model="databaseForm.port" placeholder="端口号" />
            </el-col>
            
            <el-col :span="5">
              <el-select v-model="databaseForm.database_type" placeholder="数据库类型">
                <el-option
                  v-for="item in dbTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            
            <el-col :span="3">
              <el-button type="primary" @click="addDatabase" :disabled="!databaseForm.serviceName || !databaseForm.ip">
                添加
              </el-button>
            </el-col>
          </el-row>
        </div>
        
        <!-- 已添加的关联数据库列表 -->
        <div class="added-items" v-if="formData.relationDatabaseList.length > 0">
          <el-table :data="formData.relationDatabaseList" style="width: 100%" border>
            <el-table-column prop="serviceName" label="数据库名称" />
            <el-table-column prop="ip" label="IP地址" />
            <el-table-column prop="port" label="端口号" />
            <el-table-column prop="database_type" label="数据库类型">
              <template #default="scope">
                {{ dbTypeOptions.find(item => item.value === scope.row.database_type)?.label || scope.row.database_type }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeDatabase(scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="no-data" v-else>
          <el-empty description="暂无关联数据库" :image-size="60" />
        </div>
      </div>
      
      <!-- 关联服务部分 -->
      <div class="form-section">
        <div class="section-title">
          <i class="el-icon-share"></i>
          <span>关联服务</span>
        </div>
        
        <div class="add-item-form">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-input v-model="serviceForm.serviceName" placeholder="服务名称" />
            </el-col>
            
            <el-col :span="8">
              <el-input v-model="serviceForm.ip" placeholder="IP地址" />
            </el-col>
            
            <el-col :span="5">
              <el-input v-model="serviceForm.port" placeholder="端口号" />
            </el-col>
            
            <el-col :span="3">
              <el-button type="primary" @click="addService" :disabled="!serviceForm.serviceName || !serviceForm.ip">
                添加
              </el-button>
            </el-col>
          </el-row>
        </div>
        
        <!-- 已添加的关联服务列表 -->
        <div class="added-items" v-if="formData.relationServiceList.length > 0">
          <el-table :data="formData.relationServiceList" style="width: 100%" border>
            <el-table-column prop="serviceName" label="服务名称" />
            <el-table-column prop="ip" label="IP地址" />
            <el-table-column prop="port" label="端口号" />
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeService(scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="no-data" v-else>
          <el-empty description="暂无关联服务" :image-size="60" />
        </div>
      </div>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.business-form {
  max-height: 65vh;
  overflow-y: auto;
  padding-right: 10px;
}

.form-section {
  background-color: #fff;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

.section-title i {
  margin-right: 8px;
  color: #1890ff;
  font-size: 18px;
}

.add-item-form {
  margin-bottom: 20px;
  background-color: #f8f9fc;
  padding: 16px;
  border-radius: 4px;
}

.added-items {
  margin-top: 16px;
}

.no-data {
  display: flex;
  justify-content: center;
  padding: 30px 0;
  background-color: #f8f9fc;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-table th.el-table__cell) {
  background-color: #f8f9fc;
}

:deep(.el-input__inner) {
  border-radius: 4px;
}

:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 12px;
}

/* 滚动条样式 */
.business-form::-webkit-scrollbar {
  width: 6px;
}

.business-form::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.business-form::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}
</style> 