const Layout = () => import("@/layout/index.vue");
let children = [
  {
    path: "room",
    name: "room",
    component: () => import("@/views/3droom/room.vue"),
    meta: {
      title: "3D机房",
      cache: true,
      auth: ["admin", "room.browse"],
      copyright: false,
    },
  },
  {
    path: "screen_player",
    name: "screen_player",
    component: () => import("@/views/player/playerList.vue"),
    meta: {
      title: "数字大屏",
      copyright: false,
      auth: ["admin", "screen_player.browse"],
    },
  },
  {
    path: "player_visualization",
    name: "player_visualization",
    component: () => import("@/views/large_screen/index.vue"),
    meta: {
      title: "大屏可视化配置",
      auth: ["admin", "screen_player.browse"],
      sidebar: false,
      activeMenu: "/data_analysis/screen_player",
    },
  },
  {
    path: "player_iframe",
    name: "player_iframe",
    component: () => import("@/views/large_screen/dashbord.vue"),
    meta: {
      title: "大屏可视化面板",
      copyright: false,
      sidebar: false,
      auth: ["admin", "screen_player.browse"],
      activeMenu: "/data_analysis/screen_player",
    },
  },
  {
    path: "player_list",
    name: "player_list",
    component: () => import("@/views/large_screen/playerList.vue"),
    meta: {
      title: "大屏播放列表",
      copyright: false,
      sidebar: false,
      auth: ["admin", "screen_player.browse"],
      activeMenu: "/data_analysis/screen_player",
    },
  },
];
export default {
  path: "/data_analysis",
  component: Layout,
  redirect: "/data_analysis/room",
  name: "data_analysis",
  meta: {
    auth: ["admin", "data_analysis.browse"],
    title: "可视化",
    icon: "business_monitoring",
  },
  children,
};
