import Mock from 'mockjs'

// 真实服务器数据
const realServers = [
  {
    id: 1,
    name: 'CDSS（业务应用）-Linux-************',
    ip: '************',
    status: 'warning',
    system: 'CentOS Linux 7',
    cpuModel: 'Intel(R) Xeon(R) CPU',
    totalMemory: 31.26,
    uptime: '309天21小时19分钟',
    zabbixId: '10001',
    processes: 186,
    metrics: {
      cpu: 0.62,
      memory: 52.56,
      network: 15.6  // 修改为Mbps值
    },
  },
  {
    id: 2,
    name: 'OA-Windows-***********',
    ip: '***********',
    status: 'success',
    system: 'Windows Server 2016 Standard',
    cpuModel: 'Intel(R) Xeon(R) Gold 6330 CPU @ 2.00GHz',
    totalMemory: 64,
    uptime: '388天15小时37分钟',
    zabbixId: '10002',
    processes: 124,
    metrics: {
      cpu: 0.33,
      memory: 6.80,
      network: 8.5  // 修改为Mbps值
    },
  },
  {
    id: 3,
    name: 'MySQL数据库-Linux-************',
    ip: '************',
    status: 'success',
    system: 'CentOS Linux 7',
    cpuModel: 'Intel(R) Xeon(R) CPU E5-2680 v3 @ 2.50GHz',
    totalMemory: 128,
    uptime: '156天08小时42分钟',
    zabbixId: '10003',
    processes: 142,
    metrics: {
      cpu: 0.48,
      memory: 68.20,
      network: 22.8  // 修改为Mbps值
    },
  },
  {
    id: 4,
    name: 'SQL Server-Windows-************',
    ip: '************',
    status: 'danger',
    system: 'Windows Server 2019 Standard',
    cpuModel: 'Intel(R) Xeon(R) CPU E5-2690 v4 @ 2.60GHz',
    totalMemory: 96,
    uptime: '32天03小时15分钟',
    zabbixId: '10004',
    processes: 215,
    metrics: {
      cpu: 0.92,
      memory: 85.32,
      network: 32.4  // 修改为Mbps值
    },
  }
];

// 告警数据
const alerts = [
  {
    "lastchange": "2025-05-26 11:27:01",
    "hostip": "************",
    "hostname": "CDSS（业务应用）-Linux-************",
    "hostid": "10101",
    "priority": "严重",
    "description": "设备连续3次无法Ping通，请检查设备状态！",
    "status": "未解决",
    "histroy_list": [
      {
        "eventid": "85881",
        "r_eventid": 0,
        "startime": "2025-05-26 11:27:01",
        "endtime": "2025-05-26 11:27:45",
        "duration": [
          "0天",
          "0小时",
          "0分钟",
          "44秒"
        ],
        "status": "已解决",
        "acknowledged": "不",
        "tags": [
          {
            "tag": "Application",
            "value": "Status"
      }
    ]
  },
  {
        "eventid": "85845",
        "r_eventid": 85880,
        "startime": "2025-05-26 10:31:03",
        "endtime": "2025-05-26 11:26:15",
        "duration": [
          "0天",
          "0小时",
          "55分钟",
          "12秒"
        ],
        "status": "已解决",
        "acknowledged": "不",
        "tags": [
          {
            "tag": "Application",
            "value": "Status"
          }
        ]
      }
    ]
  },
  {
    "lastchange": "2025-05-15 11:23:42",
    "hostip": "***********",
    "hostname": "OA-Windows-***********",
    "hostid": "10102",
    "priority": "警告",
    "description": "Nginx:无法获取Stub状态页面 (or no data for 30m)",
    "status": "未解决",
    "histroy_list": []
  },
  {
    "lastchange": "2025-05-14 16:24:22",
    "hostip": "************",
    "hostname": "SQL Server-Windows-************",
    "hostid": "10104",
    "priority": "严重",
    "description": "服务连续3次无法访问，请检查！",
    "status": "未解决",
    "histroy_list": []
  },
  {
    "lastchange": "2025-05-20 18:45:12",
    "hostip": "************",
    "hostname": "MySQL数据库-Linux-************",
    "hostid": "10103",
    "priority": "严重",
    "description": "MySQL服务异常重启",
    "status": "已解决",
    "histroy_list": [
      {
        "eventid": "85886",
        "r_eventid": 0,
        "startime": "2025-05-20 13:45:12",
        "endtime": "2025-05-20 18:45:12",
        "duration": [
          "0天",
          "5小时",
          "0分钟",
          "0秒"
        ],
        "status": "已解决",
        "acknowledged": "不",
        "tags": [
          {
            "tag": "服务名",
            "value": "MySQL"
          },
          {
            "tag": "告警类型",
            "value": "服务可用性"
          }
        ]
      }
    ]
  },
  {
    "lastchange": "2025-05-20 14:20:33",
    "hostip": "************",
    "hostname": "CDSS（业务应用）-Linux-************",
    "hostid": "10101",
    "priority": "警告",
    "description": "CPU负载异常增高",
    "status": "已解决",
    "histroy_list": [
      {
        "eventid": "85882",
        "r_eventid": 0,
        "startime": "2025-05-20 11:15:33",
        "endtime": "2025-05-20 14:20:33",
        "duration": [
          "0天",
          "3小时",
          "5分钟",
          "0秒"
        ],
        "status": "已解决",
        "acknowledged": "不",
        "tags": [
          {
            "tag": "服务名",
            "value": "CDSS应用"
          },
          {
            "tag": "告警类型",
            "value": "CPU性能"
          }
        ]
      }
    ]
  }
];

// 生成服务器时间序列数据
function generateTimeSeriesData() {
  const timeSeriesData = {};
  
  // 为每个服务器生成数据，使用服务器名称作为key
  realServers.forEach(server => {
    // 基础指标数据
    const serverData = {
      cpu: generateMetricTimeSeries(server.metrics.cpu, 0.15),
      memory: generateMetricTimeSeries(server.metrics.memory, 3.5),
      network: generateNetworkTimeSeries(server.metrics.network), // 使用专用的网络流量生成函数
      disks: {}
    };
    
    // 为每个服务器添加模拟的磁盘IO数据
    if (server.name === 'CDSS（业务应用）-Linux-************') {
      serverData.disks['sda'] = generateDiskIOTimeSeries(0.15, 0.08);
      serverData.disks['sdb'] = generateDiskIOTimeSeries(0.22, 0.1);
      serverData.disks['sdc'] = generateDiskIOTimeSeries(0.08, 0.05);
    } else if (server.name === 'OA-Windows-***********') {
      serverData.disks['C:'] = generateDiskIOTimeSeries(0.12, 0.07);
      serverData.disks['D:'] = generateDiskIOTimeSeries(0.18, 0.09);
    } else if (server.name === 'MySQL数据库-Linux-************') {
      serverData.disks['sda'] = generateDiskIOTimeSeries(0.25, 0.12);
      serverData.disks['sdb'] = generateDiskIOTimeSeries(0.32, 0.15);
      serverData.disks['sdc'] = generateDiskIOTimeSeries(0.28, 0.13);
      serverData.disks['sdd'] = generateDiskIOTimeSeries(0.19, 0.09);
    } else if (server.name === 'SQL Server-Windows-************') {
      serverData.disks['C:'] = generateDiskIOTimeSeries(0.14, 0.08);
      serverData.disks['D:'] = generateDiskIOTimeSeries(0.29, 0.14);
      serverData.disks['E:'] = generateDiskIOTimeSeries(0.35, 0.16);
    }
    
    timeSeriesData[server.name] = serverData;
  });
  
  return timeSeriesData;
}

// 生成指标的时间序列数据（CPU和内存）
function generateMetricTimeSeries(baseValue, variance) {
  const now = new Date();
  const data = [];
  
  // 只生成两天的数据
  for (let i = 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(now.getDate() - i);
    
    // 生成一天内24小时的数据
    for (let hour = 0; hour < 24; hour++) {
      // 根据时间段调整数值，模拟真实工作负载
      let hourFactor = 1.0;
      if (hour >= 9 && hour <= 18) { // 工作时间负载高
        hourFactor = 1.2;
      } else if (hour >= 0 && hour <= 5) { // 凌晨负载低
        hourFactor = 0.7;
      }
      
      // 确保生成的值不会超过100%（如果baseValue是百分比）
      let adjustedBase = baseValue;
      let adjustedVariance = variance;
      
      // 如果基值已经很高，则减小方差，防止超过100%
      if (baseValue * hourFactor > 0.8) {
        adjustedVariance = Math.min(variance, (1 - baseValue * hourFactor) * 0.8);
      }
      
      // 产生随机波动，但保证不超过100%
      const randomFactor = Math.random() * adjustedVariance * 2 - adjustedVariance;
      let hourValue = adjustedBase * hourFactor + randomFactor;
      
      // 最终确保值在合理范围内
      hourValue = Math.max(0, Math.min(hourValue, 1)); // 确保在0-1之间
      
      // 格式化时间为YYYY-MM-DD HH:mm格式
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const formattedHour = String(hour).padStart(2, '0');
      
      // 生成历史比较数据
      const yesterday = generateComparisonValue(hourValue, 1);
      const beforeYesterday = generateComparisonValue(hourValue, 2);
      const lastWeek = generateComparisonValue(hourValue, 7);
      
      data.push({
        time: `${year}-${month}-${day} ${formattedHour}:00`,
        value: Number(hourValue.toFixed(4)),
        yesterday: yesterday,
        beforeYesterday: beforeYesterday,
        lastWeek: lastWeek
      });
    }
  }
  
  return data;
}

// 专门用于生成磁盘IO数据的函数，不包含历史比较数据
function generateDiskIOTimeSeries(baseValue, variance) {
  const now = new Date();
  const data = [];
  
  // 只生成两天的数据
  for (let i = 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(now.getDate() - i);
    
    // 生成一天内24小时的数据
    for (let hour = 0; hour < 24; hour++) {
      // 根据时间段调整数值，模拟真实工作负载
      let hourFactor = 1.0;
      if (hour >= 9 && hour <= 18) { // 工作时间负载高
        hourFactor = 1.2;
      } else if (hour >= 0 && hour <= 5) { // 凌晨负载低
        hourFactor = 0.7;
      }
      
      // 确保生成的值不会超过100%（如果baseValue是百分比）
      let adjustedBase = baseValue;
      let adjustedVariance = variance;
      
      // 如果基值已经很高，则减小方差，防止超过100%
      if (baseValue * hourFactor > 0.8) {
        adjustedVariance = Math.min(variance, (1 - baseValue * hourFactor) * 0.8);
      }
      
      // 产生随机波动，但保证不超过100%
      const randomFactor = Math.random() * adjustedVariance * 2 - adjustedVariance;
      let hourValue = adjustedBase * hourFactor + randomFactor;
      
      // 最终确保值在合理范围内
      hourValue = Math.max(0, Math.min(hourValue, 1)); // 确保在0-1之间
      
      // 格式化时间为YYYY-MM-DD HH:mm格式
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const formattedHour = String(hour).padStart(2, '0');
      
      // 磁盘IO数据不包含历史比较数据
      data.push({
        time: `${year}-${month}-${day} ${formattedHour}:00`,
        value: Number(hourValue.toFixed(4))
      });
    }
  }
  
  return data;
}

// 专门用于生成网络流量数据的函数
function generateNetworkTimeSeries(baseValue) {
  const now = new Date();
  const data = [];
  
  // 只生成两天的数据
  for (let i = 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(now.getDate() - i);
    
    // 生成一天内24小时的数据
    for (let hour = 0; hour < 24; hour++) {
      // 根据时间段调整数值，模拟真实工作负载
      let hourFactor = 1.0;
      if (hour >= 9 && hour <= 18) { // 工作时间负载高
        hourFactor = 1.3;
      } else if (hour >= 0 && hour <= 5) { // 凌晨负载低
        hourFactor = 0.6;
      }
      
      // 产生随机波动，范围为基础值的±30%
      const variance = baseValue * 0.3;
      const randomFactor = Math.random() * variance * 2 - variance;
      
      // 计算当前小时的流量值
      let hourValue = baseValue * hourFactor + randomFactor;
      
      // 模拟流量突发情况（5%的概率）
      const isSurge = Math.random() < 0.05;
      if (isSurge) {
        // 突发流量为正常值的3-10倍
        const surgeFactor = 3 + Math.random() * 7;
        hourValue = hourValue * surgeFactor;
      }
      
      // 确保值在合理范围内 (5-45 Mbps)
      hourValue = Math.max(5, Math.min(hourValue, 45));
      
      // 格式化时间为YYYY-MM-DD HH:mm格式
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const formattedHour = String(hour).padStart(2, '0');
      
      // 生成历史比较数据
      const yesterday = generateNetworkComparisonValue(hourValue, 1);
      const beforeYesterday = generateNetworkComparisonValue(hourValue, 2);
      const lastWeek = generateNetworkComparisonValue(hourValue, 7);
      
      data.push({
        time: `${year}-${month}-${day} ${formattedHour}:00`,
        value: Number(hourValue.toFixed(2)),
        yesterday: yesterday,
        beforeYesterday: beforeYesterday,
        lastWeek: lastWeek
      });
    }
  }
  
  return data;
}

// 生成网络流量历史比较值
function generateNetworkComparisonValue(currentValue, daysOffset) {
  // 计算随机波动，较小的扰动比例
  const randomFactor = 1 + (Math.random() * 0.3 - 0.15); // ±15%的随机波动
  
  // 根据天数不同应用不同的基础倍数
  let baseFactor;
  if (daysOffset === 1) baseFactor = 0.9; // 昨天比今天低约10%
  else if (daysOffset === 2) baseFactor = 0.85; // 前天比今天低约15%
  else if (daysOffset === 7) baseFactor = 0.8; // 上周比今天低约20%
  else baseFactor = 1.0;
  
  // 应用基础倍数和随机因子，确保值在合理范围内
  const comparisonValue = Math.max(5, Math.min(currentValue * baseFactor * randomFactor, 45));
  
  return Number(comparisonValue.toFixed(2));
}

// 生成历史比较值
function generateComparisonValue(currentValue, daysOffset) {
  // 计算随机波动，较小的扰动比例
  const randomFactor = 1 + (Math.random() * 0.2 - 0.1); // ±10%的随机波动
  
  // 根据天数不同应用不同的基础倍数
  let baseFactor;
  if (daysOffset === 1) baseFactor = 0.95; // 昨天比今天低约5%
  else if (daysOffset === 2) baseFactor = 0.9; // 前天比今天低约10%
  else if (daysOffset === 7) baseFactor = 0.85; // 上周比今天低约15%
  else baseFactor = 1.0;
  
  // 应用基础倍数和随机因子，确保值在0-1之间
  const comparisonValue = Math.min(Math.max(currentValue * baseFactor * randomFactor, 0), 1);
  
  return Number(comparisonValue.toFixed(4));
}

export default [
  {
    url: '/mock/server/monitor',
    method: 'post',
    response: {
      error: '',
      status: 1,
      data: {
        // 服务器列表
        servers: realServers,
        // 告警信息（当前告警和历史记录）
        alerts: alerts,
        // 时间序列数据
        timeSeries: generateTimeSeriesData()
      }
    }
  }
]