// @ts-nocheck
<script setup name="NginxIndex">
// 导入模拟数据代替API调用
import { getNginxList, getNginxConfig, saveNginx } from "@/api/modules/nginx_management";
import useDateTimeStore from "@/store/modules/datetime";
import { useRouter, useRoute } from "vue-router";
import { reactive, onMounted, watch, ref, onUnmounted, nextTick } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import BasicInformation from "./basicInformation.vue";
import ServerBlockManager from "./ServerBlockManager.vue";
import NginxLog from "./log.vue";
import {Expand
} from '@element-plus/icons-vue'
// 添加行样式类缓存Map
const lineClassCache = new Map();

// 添加一个变量来防止初始加载时重复调用API
const initialLoadComplete = ref(false);
const configLoadInProgress = ref(false);

// localStorage键名常量
const STORAGE_KEY_ACTIVE_TAB = 'nginx_active_tab';
const STORAGE_KEY_SELECTED_NODE = 'nginx_selected_node';

const timeStore = useDateTimeStore();
const router = useRouter();
const route = useRoute();

const img = new URL("../../../assets/images/select.png", import.meta.url).href;
const serverBlockManagerRef = ref(null);
const upstreamManagerRef = ref(null);
const data = reactive({
  loading: false,
  contentLoading: false,
  time: useDateTimeStore().time,
  businessList: [],
  searchTerm: '',
  selectedNode: null,
  overview: {
    proxy_host: 0,
    redirect_host: 0,
    stream_host: 0,
    not_find_host: 0,
  },
  nginxConfig: '',
  nginxConfigFiles: [],
  serverBlocks: [],
  upstreams: [],
  currentConfigIndex: 0,
  configModified: false,
  showSaveConfirmDialog: false,
  configValidationErrors: [], // 添加配置验证错误数组
  pendingChanges: false,
  hasSyntaxErrors: false,  // 添加语法错误标记
  activeTabName: '', // 业务选择标签页的激活ID
  activeDetailTab: 'basic-info', // 服务器详情标签页的激活ID
  activeAnchor: 'basic-info', // 当前激活的锚点
  // 差异对比对话框
  showDiffDialog: false,
  diffFiles: [],
  currentDiffIndex: 0,
  originalFiles: [], // 用于存储原始配置文件
});

onMounted(() => {
  data.time = useDateTimeStore().time;
  // 从localStorage获取保存的状态
  restoreStateFromStorage();
  getData();

  // 添加滚动事件监听
  setTimeout(() => {
    const contentArea = document.querySelector('.server-content-area');
    if (contentArea) {
      contentArea.addEventListener('scroll', updateActiveAnchorOnScroll);
    }
  }, 500);
});

onUnmounted(() => {
  // 移除滚动事件监听
  const contentArea = document.querySelector('.server-content-area');
  if (contentArea) {
    contentArea.removeEventListener('scroll', updateActiveAnchorOnScroll);
  }
});

// 从localStorage恢复状态
function restoreStateFromStorage() {
  try {
    // 恢复活动标签页
    const savedActiveTab = localStorage.getItem(STORAGE_KEY_ACTIVE_TAB);
    if (savedActiveTab) {
      data.activeTabName = savedActiveTab;
    }

    // 恢复选中的节点
    const savedNodeString = localStorage.getItem(STORAGE_KEY_SELECTED_NODE);
    if (savedNodeString) {
      const savedNode = JSON.parse(savedNodeString);
      // 暂存savedNode，等待数据加载完成后使用
      data.savedNode = savedNode;
    }
  } catch (error) {
    console.error('恢复缓存的状态时出错:', error);
    // 出错时清除可能损坏的存储
    localStorage.removeItem(STORAGE_KEY_ACTIVE_TAB);
    localStorage.removeItem(STORAGE_KEY_SELECTED_NODE);
  }
}

// 保存状态到localStorage
function saveStateToStorage() {
  try {
    // 保存活动标签页
    if (data.activeTabName) {
      localStorage.setItem(STORAGE_KEY_ACTIVE_TAB, data.activeTabName);
    }

    // 保存选中的节点
    if (data.selectedNode) {
      // 只保存必要的信息，避免存储过大的对象
      const nodeToSave = {
        id: data.selectedNode.id,
        ip: data.selectedNode.ip,
        name: data.selectedNode.name,
        status: data.selectedNode.status
      };
      localStorage.setItem(STORAGE_KEY_SELECTED_NODE, JSON.stringify(nodeToSave));
    }
  } catch (error) {
    console.error('保存状态到localStorage时出错:', error);
  }
}

// 滚动时更新激活的锚点
function updateActiveAnchorOnScroll() {
  // 所有锚点ID
  const sections = ['basic-info', 'server-blocks', 'upstreams', 'nginx-logs'];

  // 查找当前可见的section
  let activeSection = '';
  let minDistance = Infinity;

  for (const id of sections) {
    const element = document.getElementById(id);
    if (!element) continue;

    const rect = element.getBoundingClientRect();
    // 计算元素顶部到可视区域顶部的距离
    const distance = Math.abs(rect.top - 100);

    // 找出最接近顶部的元素
    if (distance < minDistance) {
      minDistance = distance;
      activeSection = id;
    }
  }

  // 更新激活的锚点
  if (activeSection && data.activeAnchor !== activeSection) {
    data.activeAnchor = activeSection;
  }
}

watch(timeStore.$state, (newValue, oldValue) => {
  data.time = newValue.time;
});

// 添加监听activeTabName变化的函数
watch(() => data.activeTabName, (newTabName, oldTabName) => {
  if (newTabName && newTabName !== oldTabName) {
    // 保存新的activeTabName到localStorage
    localStorage.setItem(STORAGE_KEY_ACTIVE_TAB, newTabName);
    
    // 如果是首次加载过程中，则不需要再次调用
    if (!initialLoadComplete.value) {
      return;
    }
    
    // 从formattedData中找到对应的IP节点
    const currentBusiness = formatTreeData(data.businessList).find(item => item.id === newTabName);
    // 如果找到对应IP且有子业务，自动选择第一个
    if (currentBusiness && currentBusiness.children && currentBusiness.children.length > 0) {
      // 检查是否有保存的节点，且该节点属于当前选中的标签页
      const savedNode = data.savedNode;
      if (savedNode && currentBusiness.children.some(child => child.id === savedNode.id)) {
        // 找到保存的节点
        const savedNodeObj = currentBusiness.children.find(child => child.id === savedNode.id);
        if (savedNodeObj) {
          nextTick(() => {
            handleNodeClick(savedNodeObj);
            // 使用后清除临时存储
            delete data.savedNode;
          });
          return;
        }
      }
      
      // 如果没有找到保存的节点或不匹配，则选择第一个
      nextTick(() => {
        handleNodeClick(currentBusiness.children[0]);
      });
    }
  }
});

// 修改formatTreeData函数，让其不再重复选择第一个业务，而是仅返回格式化的数据
function formatTreeData(businessList) {
  if (!businessList || businessList.length === 0) return [];

  const formattedData = businessList.map(ipNode => {
    // 以IP为根节点
    return {
      id: `business_${ipNode.ip}`,  // 添加唯一ID
      label: ipNode.ip,
      ip: ipNode.ip,
      num: ipNode.business_name_list?.length || 0,
      children: (ipNode.business_name_list || []).map(business => ({
        id: `server_${ipNode.ip}_${business.name}`,  // 添加唯一ID
        label: business.name,
        ip: ipNode.ip,
        status: business.config?.nginx_status === 'Active' ? '运行中' : '已停止',
        data: {
          ...business.config,
          ip: ipNode.ip,
          name: business.name,
          status: business.config?.nginx_status === 'Active' ? '运行中' : '已停止'
        }
      }))
    };
  }).filter(item => {
    if (!data.searchTerm) return true;
    const searchLower = data.searchTerm.toLowerCase();

    // IP地址匹配
    if (item.label.toLowerCase().includes(searchLower)) return true;

    // 检查子业务是否匹配
    const matchingChildren = item.children.filter(child =>
      child.label.toLowerCase().includes(searchLower) ||
      child.ip.toLowerCase().includes(searchLower)
    );

    if (matchingChildren.length > 0) {
      // 仅保留匹配的子项
      item.children = matchingChildren;
      return true;
    }

    return false;
  });

  // 设置默认激活的标签页
  if (formattedData.length > 0) {
    // 首先检查是否有从localStorage恢复的值
    const savedActiveTab = data.activeTabName;
    
    // 检查是否在当前数据中存在
    const savedTabExists = savedActiveTab && formattedData.some(item => item.id === savedActiveTab);
    
    // 如果存在已保存的有效标签页，使用它
    if (savedTabExists) {
      // 已经设置过了，不需要再设置
    } else {
      // 没有有效的保存值，设置为第一个IP的ID
      data.activeTabName = formattedData[0].id;
    }
  }

  return formattedData;
}

// 修改getData函数，使其在加载完数据后尝试恢复选中的业务
function getData() {
  data.loading = true;
  getNginxList()
    .then((res) => {
      // 直接使用新的数据结构
      data.businessList = res.data || [];
      data.loading = false;
      
      // 格式化数据
      const formattedData = formatTreeData(data.businessList);
      
      // 设置标记，确保watch不会重复调用
      initialLoadComplete.value = false;
      
      // 检查是否有已保存的节点可以恢复
      const savedNode = data.savedNode;
      
      if (savedNode && savedNode.ip) {
        // 查找保存的业务所在的标签页
        const businessTab = formattedData.find(tab => 
          tab.children.some(child => child.id === savedNode.id || 
            (child.ip === savedNode.ip && child.label === savedNode.name)));
        
        if (businessTab) {
          // 设置正确的标签页
          data.activeTabName = businessTab.id;
          
          // 查找要恢复的具体业务节点
          const nodeToSelect = businessTab.children.find(child => 
            child.id === savedNode.id || 
            (child.ip === savedNode.ip && child.label === savedNode.name));
          
          if (nodeToSelect) {
            // 找到了保存的节点，选择它
            nextTick(() => {
              handleNodeClick(nodeToSelect);
              // 使用后清除临时存储
              delete data.savedNode;
              
              // 完成后设置加载完成标记
              setTimeout(() => {
                initialLoadComplete.value = true;
              }, 500);
            });
            return;
          }
        }
      }
      
      // 如果没有找到保存的节点或不需要恢复，选择第一个业务
      if (formattedData.length > 0 && formattedData[0].children && formattedData[0].children.length > 0) {
        nextTick(() => {
          handleNodeClick(formattedData[0].children[0]);
          // 在处理完成后设置加载完成标记
          setTimeout(() => {
            initialLoadComplete.value = true;
          }, 500);
        });
      } else {
        // 没有数据，直接设置加载完成
        initialLoadComplete.value = true;
      }
    })
    .catch(error => {
      data.loading = false;
      initialLoadComplete.value = true; // 出错时也需要设置标记
    });
}

// 处理节点点击
function handleNodeClick(node) {
  if (node && node.ip) {
    // 防止重复调用
    if (configLoadInProgress.value) {
      return;
    }
    
    data.selectedNode = {
      id: node.id,
      ip: node.ip,
      name: node.label,
      status: node.status,
      ...node.data
    };
    
    // 保存选择到localStorage
    saveStateToStorage();
    
    // 重置服务器详情标签页到基本信息
    data.activeDetailTab = 'basic-info';
    // 获取服务器配置
    getServerConfig(node.ip);
  }
}

// 获取服务器配置
const getServerConfig = async (serverIp) => {
  try {
    // 设置锁，防止重复调用
    configLoadInProgress.value = true;
    data.contentLoading = true;
    // 清空现有配置文件，避免显示旧数据
    data.nginxConfigFiles = [];
    data.currentConfigIndex = 0;
    data.serverBlocks = [];
    data.upstreams = [];
    // 清空原始文件记录
    data.originalFiles = [];

    const res = await getNginxConfig({ ip: serverIp })
  
    // 修复：正确处理API返回的数据结构
    if (Array.isArray(res.data.data)) {
    
      // 从正确的响应格式中获取配置文件数组
      if (res.data.data.length > 0) {
        // 保存配置文件数据 - 先转换为普通数组再保存，避免嵌套的Proxy对象
        /** @type {NginxConfigFile[]} */
        data.nginxConfigFiles = JSON.parse(JSON.stringify(res.data.data));
        // 备份原始配置 - 在修改任何内容前
        backupOriginalConfig(res.data.data);
        logConfigFiles(data.nginxConfigFiles);
        if (data.nginxConfigFiles[0] && data.nginxConfigFiles[0].content) {
          data.nginxConfig = data.nginxConfigFiles[0].content;
        }
        // 解析反向代理和负载均衡配置
        parseServerConfigData(data.nginxConfigFiles);
      } else {
        console.warn("res.data.data数组为空");
      }
    } else if (res.data && Array.isArray(res.data)) {
      // 数据直接是数组
      if (res.data.length > 0) {
        /** @type {NginxConfigFile[]} */
        data.nginxConfigFiles = JSON.parse(JSON.stringify(res.data));
        // 备份原始配置 - 在修改任何内容前
        backupOriginalConfig(res.data);
        logConfigFiles(data.nginxConfigFiles);
        if (data.nginxConfigFiles[0] && data.nginxConfigFiles[0].content) {
          data.nginxConfig = data.nginxConfigFiles[0].content;
        }

        // 解析反向代理和负载均衡配置
        parseServerConfigData(data.nginxConfigFiles);
      } else {
        console.warn("配置文件数组为空");
      }
    } else {
      console.error("无法识别的配置数据格式:", res.data);
    }
    data.contentLoading = false;
  } catch (e) {
    console.error("获取配置数据出错:", e);
    data.contentLoading = false;
  } finally {
    // 解除锁
    configLoadInProgress.value = false;
  }
}

// 解析配置文件中的反向代理和负载均衡数据
function parseServerConfigData(configFiles) {
  // 清空现有数据
  data.serverBlocks = [];
  data.upstreams = [];

  if (!configFiles || configFiles.length === 0) {
    return;
  }

  // 遍历所有配置文件
  configFiles.forEach(file => {
    if (!file || !file.content) {
      return;
    }

    // 添加文件路径检查
    if (!file.file_path) {
      return;
    }

    // 解析server块
    /** @type {ServerBlock[]} */
    const serverBlocksInFile = parseServerBlocks(file.content);
    if (serverBlocksInFile && serverBlocksInFile.length > 0) {
      // 为server块添加来源信息
      serverBlocksInFile.forEach(block => {
        if (block) {
          // 显式添加file_path属性
          block.file_path = file.file_path;
          data.serverBlocks.push(block);
        }
      });
    }

    // 解析upstream块
    /** @type {Upstream[]} */
    const upstreamsInFile = parseUpstreams(file.content);
    if (upstreamsInFile && upstreamsInFile.length > 0) {
      // 为upstream块添加来源信息
      upstreamsInFile.forEach(upstream => {
        if (upstream) {
          // 显式添加file_path属性
          upstream.file_path = file.file_path;
          data.upstreams.push(upstream);
        }
      });
    }
  });

}

// 解析Nginx配置中的server块
/**
 * @param {string} configText 配置文本
 * @returns {ServerBlock[]} 解析出的server块数组
 */
function parseServerBlocks(configText) {
  if (!configText) return [];

  /** @type {ServerBlock[]} */
  const serverBlocks = [];
  const lines = configText.split('\n');
  let inServerBlock = false;
  /** @type {ServerBlock|null} */
  let currentBlock = null;
  let braceCount = 0;
  let blockContent = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // 检测server块开始
    // 支持注释的server块
    const serverStartRegex = /^\s*(#\s*)?server\s*\{/;
    if (serverStartRegex.test(trimmedLine)) {
      inServerBlock = true;
      braceCount = 1;
      blockContent = line + '\n';

      // 检测是否被注释
      const isCommented = /^\s*#/.test(trimmedLine);

      // 创建新的server块对象 - 使用时间戳和随机数生成唯一ID
      currentBlock = {
        id: `server_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`,
        name: `Server ${serverBlocks.length + 1}`,
        listen: '',
        server_name: '',
        locations: [],
        enabled: !isCommented,
        config: '',
        startLine: i,
        endLine: null,
        file_path: '' // 添加file_path属性，稍后会被设置
      };

      continue;
    }

    // 在server块内
    if (inServerBlock) {
      blockContent += line + '\n';

      // 检查大括号数量
      braceCount += (line.match(/{/g) || []).length;
      braceCount -= (line.match(/}/g) || []).length;

      // 提取listen和server_name
      if (trimmedLine.startsWith('listen')) {
        const listenMatch = trimmedLine.match(/listen\s+([^;]+)/);
        if (listenMatch && currentBlock) currentBlock.listen = listenMatch[1].trim();
      }

      if (trimmedLine.startsWith('server_name')) {
        const serverNameMatch = trimmedLine.match(/server_name\s+([^;]+)/);
        if (serverNameMatch && currentBlock) {
          currentBlock.server_name = serverNameMatch[1].trim();
          // 使用server_name作为块名称
          currentBlock.name = `${currentBlock.server_name} (${currentBlock.listen || 'default'})`;
        }
      }

      // 检测server块结束
      if (braceCount === 0) {
        inServerBlock = false;
        if (currentBlock) {
          currentBlock.endLine = i;
          currentBlock.config = blockContent;

          // 如果没有设置名称，使用服务器地址
          if (!currentBlock.name || currentBlock.name === `Server ${serverBlocks.length + 1}`) {
            currentBlock.name = `${currentBlock.server_name || 'localhost'}:${currentBlock.listen || '80'}`;
          }

          // 提取location块
          currentBlock.locations = extractLocations(blockContent);

          serverBlocks.push(currentBlock);
          currentBlock = null;
          blockContent = '';
        }
      }
    }
  }

  return serverBlocks;
}

// 提取location块
/**
 * @param {string} serverConfig 服务器配置内容
 * @returns {Location[]} location块数组
 */
function extractLocations(serverConfig) {
  /** @type {Location[]} */
  const locations = [];
  const locationRegex = /(?:^|\n)\s*(#\s*)?location\s+([^{]+)\s*{([^}]*)}/gm;
  let match;
  let index = 0;

  while ((match = locationRegex.exec(serverConfig)) !== null) {
    const isCommented = !!match[1];
    const path = match[2].trim();
    const locationConfig = match[0];
    const content = match[3];

    // 提取proxy_pass或root
    const proxyPassMatch = content.match(/proxy_pass\s+([^;]+);/);
    const rootMatch = content.match(/root\s+([^;]+);/);

    locations.push({
      id: `loc_${index++}`,
      path,
      proxy_pass: proxyPassMatch ? proxyPassMatch[1].trim() : undefined,
      root: rootMatch ? rootMatch[1].trim() : undefined,
      enabled: !isCommented,
      config: locationConfig
    });
  }

  return locations;
}

// 解析Nginx配置中的upstream块
/**
 * @param {string} configText 配置文本
 * @returns {Upstream[]} 解析出的upstream块数组
 */
function parseUpstreams(configText) {
  if (!configText) return [];

  /** @type {Upstream[]} */
  const upstreams = [];
  const lines = configText.split('\n');
  let inUpstreamBlock = false;
  /** @type {Upstream|null} */
  let currentUpstream = null;
  let braceCount = 0;
  let blockContent = '';
  let blockStart = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // 检测upstream块开始
    // 支持注释的upstream块
    const upstreamStartRegex = /^\s*(#\s*)?upstream\s+([^\s{]+)\s*\{/;
    const upstreamMatch = upstreamStartRegex.exec(trimmedLine);

    if (upstreamMatch) {
      inUpstreamBlock = true;
      braceCount = 1;
      blockContent = line + '\n';
      blockStart = i;

      // 检测是否被注释
      const isCommented = /^\s*#/.test(trimmedLine);

      // 获取upstream名称 - 确保提取正确的名称
      const upstreamName = upstreamMatch[2];

      // 创建新的upstream对象，确保使用原始名称
      currentUpstream = {
        id: `upstream_${upstreams.length + 1}`,
        name: upstreamName, // 保留原始名称很重要
        servers: [],
        strategy: 'round-robin',
        enabled: !isCommented,
        config: '',
        startLine: i,
        endLine: null,
        file_path: '' // 添加file_path属性，稍后会被设置
      };

      continue;
    }

    // 在upstream块内
    if (inUpstreamBlock) {
      blockContent += line + '\n';

      // 检查大括号数量
      braceCount += (line.match(/{/g) || []).length;
      braceCount -= (line.match(/}/g) || []).length;

      // 提取server行
      if (trimmedLine.includes('server ') && currentUpstream) {
        const isCommented = /^\s*#/.test(trimmedLine);
        const serverMatch = trimmedLine.match(/server\s+([^;]+)/);

        if (serverMatch) {
          const serverInfo = serverMatch[1].trim();

          // 解析服务器参数
          const weightMatch = serverInfo.match(/weight=(\d+)/);
          const maxFailsMatch = serverInfo.match(/max_fails=(\d+)/);
          const failTimeoutMatch = serverInfo.match(/fail_timeout=([^;\s]+)/);
          const backupMatch = /backup/.test(serverInfo);
          const downMatch = /down/.test(serverInfo);

          // 提取服务器地址
          const address = serverInfo.replace(/\s+weight=\d+|\s+max_fails=\d+|\s+fail_timeout=[^;\s]+|\s+backup|\s+down/g, '');

          currentUpstream.servers.push({
            address,
            weight: weightMatch ? parseInt(weightMatch[1]) : 1,
            maxFails: maxFailsMatch ? parseInt(maxFailsMatch[1]) : undefined,
            failTimeout: failTimeoutMatch ? failTimeoutMatch[1] : undefined,
            backup: backupMatch,
            down: downMatch,
            enabled: !isCommented,
            line: i
          });
        }
      }

      // 解析负载均衡算法 - 改进策略解析
      if (currentUpstream) {
        // 检查ip_hash策略
        if (trimmedLine.includes('ip_hash')) {
          currentUpstream.strategy = 'ip_hash';
        } 
        // 检查least_conn策略
        else if (trimmedLine.includes('least_conn')) {
          currentUpstream.strategy = 'least_conn';
        } 
        // 检查hash策略 - 保留完整参数
        else if (trimmedLine.includes('hash')) {
          const hashMatch = trimmedLine.match(/hash\s+([^;]+)/);
          if (hashMatch) {
            currentUpstream.strategy = `hash ${hashMatch[1]}`;
          } else {
            currentUpstream.strategy = 'hash';
          }
        } 
        // 检查random策略
        else if (trimmedLine.includes('random')) {
          currentUpstream.strategy = 'random';
        }
      }

      // 检测块结束
      if (braceCount === 0) {
        inUpstreamBlock = false;
        if (currentUpstream) {
          currentUpstream.endLine = i;
          currentUpstream.config = blockContent;
          upstreams.push(currentUpstream);
          currentUpstream = null;
        }
      }
    }
  }

  return upstreams;
}



// 记录配置文件信息
function logConfigFiles(configFiles) {
  configFiles.forEach((file, index) => {

  });
}




/**
 * 处理upstream块启用/禁用，生成正确的配置文本
 * @param {Array} upstreams 所有upstream块
 * @param {Object} toggledUpstream 被切换的upstream块
 * @param {boolean} enabled 是否启用
 * @param {Array} configFiles 配置文件数组
 * @returns {string} 更新后的配置文本
 */
function handleUpstreamToggle(upstreams, toggledUpstream, enabled, configFiles) {
  // 参数验证
  if (!toggledUpstream || !toggledUpstream.file_path) {
    return null;
  }

  // 查找配置文件
  const targetFile = toggledUpstream.file_path;
  const fileIndex = data.nginxConfigFiles.findIndex(f =>
    f && typeof f === 'object' && 'file_path' in f && f.file_path === targetFile
  );

  if (fileIndex === -1) {
    return null;
  }

  // 获取文件当前内容
  const currentContent = data.nginxConfigFiles[fileIndex].content;

  // 找到要修改的upstream块
  const regexPattern = new RegExp(`(\\s*upstream\\s+${toggledUpstream.name}\\s*{[\\s\\S]*?}\\s*)`, 'gm');

  let match = regexPattern.exec(currentContent);
  if (!match) {
    return null;
  }

  let blockContent = match[1];

  // 根据目标状态修改块内容
  let updatedBlockContent;

  if (enabled) {
    // 启用：移除注释
    if (blockContent.trimStart().startsWith('#')) {
      updatedBlockContent = blockContent.replace(/^\s*#\s*/gm, '');
    } else {
      updatedBlockContent = blockContent;
    }
  } else {
    // 禁用：添加注释
    if (!blockContent.trimStart().startsWith('#')) {
      // 添加注释前先保存原始内容
      // 为每行添加注释
      updatedBlockContent = blockContent.replace(/^(\s*)/gm, '$1# ');
    } else {
      updatedBlockContent = blockContent;
    }
  }

  // 自动修复空行问题：规范化空行
  // 1. 首先提取块结构
  const blockRegex = /(^\s*(?:#\s*)?upstream\s+[^\{]+\{)([\s\S]*?)(^\s*(?:#\s*)?})/gm;
  updatedBlockContent = updatedBlockContent.replace(blockRegex, (match, startLine, blockBody, endLine) => {
    // 2. 处理块内部内容
    let normalizedBody = blockBody
      // 移除多余空行，保留一个换行
      .replace(/\n\s*\n+/g, '\n')
      // 确保每行有正确的缩进(保留注释前缀)
      .replace(/\n\s*(?:#\s*)?(\S)/g, '\n    $1');

    // 3. 重新组合块
    return `${startLine}${normalizedBody}${endLine}`;
  });

  // 将修改后的块替换到文件内容中
  const updatedContent = currentContent.replace(blockContent, updatedBlockContent);

  // 更新配置文件内容
  data.nginxConfigFiles[fileIndex].content = updatedContent;

  // 如果当前正在查看这个文件，更新显示内容
  if (fileIndex === data.currentConfigIndex) {
    data.nginxConfig = updatedContent;
  }

  // 标记配置已修改
  data.configModified = true;

  // 返回更新后的内容
  return updatedContent;
}

// 同步所有组件的配置状态
function syncComponentStates() {
  // 移除配置文件编辑器的相关代码
  
  // 确保服务器块管理器能看到最新的状态
  if (serverBlockManagerRef.value && typeof serverBlockManagerRef.value.parseServerBlocks === 'function') {
    try {
      // 传递最新的服务器块数据
      serverBlockManagerRef.value.parseServerBlocks(data.serverBlocks);
    } catch (error) {
    }
  }

  // 确保上游块管理器能看到最新的状态
  if (upstreamManagerRef.value && typeof upstreamManagerRef.value.parseUpstreams === 'function') {
    try {
      // 传递最新的上游块数据
      upstreamManagerRef.value.parseUpstreams(data.upstreams);
    } catch (error) {
    }
  }

  // 标记配置为已修改状态
  data.configModified = true;
}

// 当用户点击保存按钮时
function saveConfigChanges() {

  // 获取差异文件并显示对话框
  showConfigDifferences();
}

// 配置差异比较函数 - 简化比对逻辑
function showConfigDifferences() {
  // 比较配置文件差异
  const diffFiles = [];
  
  // 检查是否有配置文件
  if (!data.nginxConfigFiles || data.nginxConfigFiles.length === 0) {
    ElMessage.warning("没有可比较的配置文件");
    return;
  }
  
  // 检查是否有原始文件
  if (!data.originalFiles || data.originalFiles.length === 0) {
    ElMessage.warning("没有原始文件用于比较，将直接保存当前编辑器内容");
    // 如果没有原始文件，直接保存
    saveConfig();
    return;
  }
  
  
  // 遍历所有配置文件，查找有变化的文件
  for (let i = 0; i < data.nginxConfigFiles.length; i++) {
    const currentFile = data.nginxConfigFiles[i];
    if (!currentFile || !currentFile.file_path) continue;
    
    // 查找对应的原始文件
    const originalFileIndex = data.originalFiles.findIndex(f => f.file_path === currentFile.file_path);
    
    // 如果找不到原始文件，说明这是新增的文件
    if (originalFileIndex === -1) {
      diffFiles.push({
        file_path: currentFile.file_path,
        oldContent: '',
        newContent: currentFile.content || '',
        diffType: 'new'
      });
    } else {
      // 获取原始文件内容
      const originalFile = data.originalFiles[originalFileIndex];
      
      // 直接比较字符串内容
      const originalContent = originalFile.content || '';
      const currentContent = currentFile.content || '';
      
      // 只有内容不同的文件才添加到比对列表
      if (originalContent !== currentContent) {
       
        diffFiles.push({
          file_path: currentFile.file_path,
          oldContent: originalContent,
          newContent: currentContent,
          diffType: 'modified'
        });
      } 
    }
  }
  
  // 检查是否有被删除的文件
  for (let i = 0; i < data.originalFiles.length; i++) {
    const originalFile = data.originalFiles[i];
    if (!originalFile || !originalFile.file_path) continue;
    
    // 查找对应的当前文件
    const currentFileExists = data.nginxConfigFiles.some(f => f.file_path === originalFile.file_path);
    
    // 如果当前文件中找不到，说明被删除了
    if (!currentFileExists) {
     
      diffFiles.push({
        file_path: originalFile.file_path,
        oldContent: originalFile.content || '',
        newContent: '',
        diffType: 'deleted'
      });
    }
  }
  
  // 如果没有找到任何变更
  if (diffFiles.length === 0) {
    ElMessage.info("没有检测到配置文件变更，无需保存");
    return;
  }
  
  // 保存差异文件并显示对话框
  data.diffFiles = diffFiles;
  data.currentDiffIndex = 0;
  data.showDiffDialog = true;
}


// 保存配置 - 直接使用编辑器中的内容，不做任何修改
function saveConfig() {
  if (!data.selectedNode?.ip) {
    ElMessage.warning("未选择服务器");
    return;
  }

  // 设置loading状态
  data.loading = true;
  
  // 先保存IP地址，防止在异步回调中变为undefined
  const serverIp = data.selectedNode.ip;
  
  // 创建一个新数组来防止引用污染
  const configFilesToSave = data.nginxConfigFiles.map(file => ({
    file_path: file.file_path,
    content: file.content
  }));
 
   // 设置内容区域为加载状态，使所有组件都重新加载
      data.contentLoading = true;
  // 发送到服务器
  saveNginx({
    'ip_address': serverIp,
    'nginx_data': configFilesToSave
  }).then((res) => {
      // 更新原始文件备份 - 使用深拷贝确保不影响编辑器中的内容
      backupOriginalConfig(configFilesToSave);
      
      // 清除修改标记
      data.configModified = false;
      
      // 重新获取配置数据，完整刷新所有组件
      getServerConfig(serverIp);
    
  }).finally(() => {
    // 关闭整体加载状态
    data.loading = false;
    
    // 移除配置文件编辑器的loading状态设置
  });
}

// 备份原始配置文件 - 独立复制内容确保不影响编辑器
function backupOriginalConfig(configFiles) {
  // 重置原始文件数组
  data.originalFiles = [];

  // 逐个文件备份，确保完全独立
  if (Array.isArray(configFiles)) {
    configFiles.forEach(file => {
      if (file && typeof file === 'object' && file.file_path && file.content !== undefined) {
        // 手动创建新对象，避免JSON序列化可能引起的格式变化
        const fileCopy = {
          file_path: String(file.file_path),
          content: String(file.content)
        };
        data.originalFiles.push(fileCopy);
      }
    });
  }
}

// 完全重写的计算行差异类的辅助函数
function getLineClass(diffFile, line, lineIndex, side) {
  // 对于新文件，所有新内容都标记为绿色；对于删除的文件，所有旧内容都标记为红色
  if (diffFile.diffType === 'new' && side === 'new') {
    return 'diff-new';
  } else if (diffFile.diffType === 'deleted' && side === 'old') {
    return 'diff-old';
  } else if (diffFile.diffType === 'modified') {
    // 这是关键部分 - 只对修改的文件进行更精细的行比较
    
    // 1. 准备辅助函数
    
    // 完全规范化内容（移除所有空白字符）以进行实质内容比较
    const normalizeContent = (text) => {
      return text.replace(/\s+/g, '');
    };
    
    // 判断是否是注释行
    const isCommentLine = (text) => {
      return text.trim().startsWith('#');
    };
    
    // 获取注释行的实际内容（去掉#号）
    const getCommentContent = (text) => {
      return text.trim().replace(/^#\s*/, '');
    };
    
    // 2. 获取源数据数组
    const oldLines = (diffFile.oldContent || '').split('\n');
    const newLines = (diffFile.newContent || '').split('\n');
    
    // 3. 提前规范化所有行用于内容比较
    const normalizedOldLines = oldLines.map(l => normalizeContent(l));
    const normalizedNewLines = newLines.map(l => normalizeContent(l));
   
    
    if (side === 'old') {
      // 旧内容，检查这一行是否在新内容中有对应或被修改
      const normalizedLine = normalizeContent(line);
      
      // 如果是空行或只有空白字符，不标记
      if (normalizedLine === '') {
        return '';
      }
      
      // 检查是否是注释行
      if (isCommentLine(line)) {
        const commentContent = normalizeContent(getCommentContent(line));
        
        // 情况1: 如果相同的注释在新内容中也存在，不标记
        const sameCommentInNew = newLines.some(nl => 
          isCommentLine(nl) && normalizeContent(getCommentContent(nl)) === commentContent
        );
        if (sameCommentInNew) {
          return '';
        }
        
        // 情况2: 如果注释内容在新内容中存在但不是作为注释，则这是取消注释的情况
        const uncommentedInNew = newLines.some(nl => 
          !isCommentLine(nl) && normalizeContent(nl).includes(commentContent)
        );
        if (uncommentedInNew) {
          return 'diff-old';
        }
        
        // 情况3: 如果注释内容在新内容中不存在，则是删除了这行注释
        return 'diff-old';
      } else {
        // 非注释行
        
        // 情况1: 如果完全相同的行（规范化后）在新内容中存在，不标记
        if (normalizedNewLines.includes(normalizedLine)) {
          return '';
        }
        
        // 情况2: 如果被注释了（内容相同但添加了#）
        const commentedInNew = newLines.some(nl => 
          isCommentLine(nl) && normalizeContent(getCommentContent(nl)) === normalizedLine
        );
        if (commentedInNew) {
          return 'diff-old';
        }
        
        // 情况3: 如果内容在新文件中不存在，即被删除或修改
        return 'diff-old';
      }
    } else { // side === 'new'
      // 新内容，检查这一行是否在旧内容中有对应或是新增的
      const normalizedLine = normalizeContent(line);
      
      // 如果是空行或只有空白字符，不标记
      if (normalizedLine === '') {
        return '';
      }
      
      // 检查是否是注释行
      if (isCommentLine(line)) {
        const commentContent = normalizeContent(getCommentContent(line));
        
        // 情况1: 如果相同的注释在旧内容中也存在，不标记
        const sameCommentInOld = oldLines.some(ol => 
          isCommentLine(ol) && normalizeContent(getCommentContent(ol)) === commentContent
        );
        if (sameCommentInOld) {
          return '';
        }
        
        // 情况2: 如果注释内容在旧内容中存在但不是作为注释，则这是添加注释的情况
        const uncommentedInOld = oldLines.some(ol => 
          !isCommentLine(ol) && normalizeContent(ol).includes(commentContent)
        );
        if (uncommentedInOld) {
          return 'diff-new';
        }
        
        // 情况3: 如果注释内容在旧内容中不存在，则是添加了这行注释
        return 'diff-new';
      } else {
        // 非注释行
        
        // 情况1: 如果完全相同的行（规范化后）在旧内容中存在，不标记
        if (normalizedOldLines.includes(normalizedLine)) {
          return '';
        }
        
        // 情况2: 如果是被取消注释的行（内容相同但在旧内容中有#）
        const commentedInOld = oldLines.some(ol => 
          isCommentLine(ol) && normalizeContent(getCommentContent(ol)) === normalizedLine
        );
        if (commentedInOld) {
          return 'diff-new';
        }
        
        // 情况3: 如果内容在旧文件中不存在，即新增或修改
        return 'diff-new';
      }
    }
  }
  
  // 默认不应用样式
  return '';
}

// 添加方法在对话框关闭时清除缓存
function clearLineClassCache() {
  lineClassCache.clear();
}

// 修改切换文件的方法，使用nextTick确保当前渲染周期完成
function showPreviousDiff() {
  if (data.currentDiffIndex > 0) {
    nextTick(() => {
      data.currentDiffIndex--;
    });
  }
}

function showNextDiff() {
  if (data.currentDiffIndex < data.diffFiles.length - 1) {
    nextTick(() => {
      data.currentDiffIndex++;
    });
  } else {
    // 最后一个文件，保存配置
    confirmSaveConfig();
  }
}

// 确认保存配置
function confirmSaveConfig() {
  // 关闭对话框
  data.showDiffDialog = false;

  // 然后保存配置
  saveConfig();
}



// 简单的语法检查函数，作为备用
function basicSyntaxCheck(content) {
  const errors = [];

  // 检查括号是否匹配
  let braceCount = 0;
  const lines = content.split('\n');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    // 忽略注释
    if (line.trim().startsWith('#')) continue;

    // 计算括号
    for (const char of line) {
      if (char === '{') braceCount++;
      if (char === '}') braceCount--;

      // 如果出现负数，说明右括号多于左括号
      if (braceCount < 0) {
        errors.push(`行 ${i + 1}: 右括号多于左括号`);
        braceCount = 0; // 重置以继续检查
      }
    }
  }

  // 最终检查括号匹配情况
  if (braceCount > 0) {
    errors.push(`打开的花括号数量多于关闭的花括号，缺少 ${braceCount} 个 }`);
  } else if (braceCount < 0) {
    errors.push(`关闭的花括号数量多于打开的花括号，多余 ${Math.abs(braceCount)} 个 }`);
  }

  return errors;
}

// 更新配置文件函数
function updateConfiguration(type, eventData) {
  // 处理配置文件更新
  if (type === 'config_files') {
    // 简化配置文件处理，由于已移除编辑器组件
    if (eventData && eventData.config && Array.isArray(eventData.config)) {
      // 更新配置文件
      data.nginxConfigFiles = eventData.config;
      
      // 标记配置已修改
      data.configModified = true;
      
      // 重新解析配置以更新serverBlocks和upstreams数据
      parseServerConfigData(data.nginxConfigFiles);
    }
  } else if (type === 'server-blocks') {
    // 处理反向代理配置更新
    if (eventData) {

      // 保存服务器块数据 - 直接使用ServerBlockManager传来的数据
      if (eventData.blocks) {
        data.serverBlocks = eventData.blocks;
      }

      // 处理更新的配置文件
      if (eventData.config && Array.isArray(eventData.config)) {
        // 更新配置文件但不重新解析
        updateConfigFiles(eventData.config, true); // 传递preserveServerBlockData=true
      }
    }
  } else if (type === 'upstreams') {

    // 处理启用/禁用上游的操作
    if (eventData.action === 'toggle' && eventData.toggledUpstream) {
      const toggledUpstream = eventData.toggledUpstream;

      // 如果处理函数存在，调用它
      if (typeof handleUpstreamToggle === 'function') {
        const updatedContent = handleUpstreamToggle(eventData.upstreams, toggledUpstream, eventData.enabled, data.nginxConfigFiles);
      } 
    }

    // 使用通用的配置文件更新逻辑
    if (eventData.upstreams) {

      // 检查eventData.upstreams的结构
      if (Array.isArray(eventData.upstreams) && eventData.upstreams.length > 0) {
        // 检查第一个元素是否有file_path和content字段 (来自buildUpdatedConfig)
        if (eventData.upstreams[0] && 'file_path' in eventData.upstreams[0] && 'content' in eventData.upstreams[0]) {
          // 这是配置块数据，需要更新配置文件并重新解析
          updateConfigFiles(eventData.upstreams);
          // 不要直接设置data.upstreams，让parseServerConfigData来处理
          return;
        } else {
      
          // 保留现有upstream的ID以保持引用一致性
          data.upstreams = eventData.upstreams.map(upstream => {
            // 查找现有的同名upstream
            const existingUpstream = data.upstreams.find(u => u && u.name === upstream.name);
            if (existingUpstream) {
              // 如果找到同名upstream，保留其id
              return { ...upstream, id: existingUpstream.id };
            }
            return upstream;
          });
       
        }
      } 
    }

    // 处理更新的配置文件
    if (eventData.config && Array.isArray(eventData.config)) {
      // 更新配置文件
      updateConfigFiles(eventData.config);
    }
  }

  // 标记配置已修改
  data.configModified = true;
}

// 辅助函数：更新配置文件
function updateConfigFiles(configFiles, preserveServerBlockData = false) {
  if (!Array.isArray(configFiles) || configFiles.length === 0) return;

  // 更新标志
  let hasUpdatedFiles = false;

  // 遍历每个更新的文件，并更新 nginxConfigFiles
  configFiles.forEach(fileConfig => {
    // 检查文件是否包含必要的属性
    if (!fileConfig || typeof fileConfig !== 'object' || !('file_path' in fileConfig) || !('content' in fileConfig)) {
      return;
    }

    const filePath = String(fileConfig.file_path);
    const content = String(fileConfig.content);

    // 查找现有文件的索引
    const fileIndex = data.nginxConfigFiles.findIndex(f =>
      f && typeof f === 'object' && 'file_path' in f && f.file_path === filePath
    );

    if (fileIndex >= 0) {
      // 更新现有文件内容
      const oldContent = data.nginxConfigFiles[fileIndex].content;
      data.nginxConfigFiles[fileIndex].content = content;


      // 如果当前正在查看这个文件，更新显示内容
      if (fileIndex === data.currentConfigIndex) {
        data.nginxConfig = content;
      }

      hasUpdatedFiles = true;
    } else {
      // 添加新文件

      data.nginxConfigFiles.push({
        file_path: filePath,
        content: content
      });

      hasUpdatedFiles = true;
    }
  });

  // 配置已修改，需要刷新
  data.configModified = true;

  // 如果有文件更新，同步所有组件的配置状态
  if (hasUpdatedFiles) {
    // 只在需要时重新解析服务器块数据
    if (!preserveServerBlockData) {
      // 重新解析配置以更新serverBlocks和upstreams数据
      parseServerConfigData(data.nginxConfigFiles);
    }
    syncComponentStates();
  }
}



// 添加新函数：滚动到指定锚点
function scrollToAnchor(anchor) {
  const element = document.getElementById(anchor);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    // 更新当前激活的锚点
    data.activeAnchor = anchor;
  }
}

// 检查组件是否被修改
function isComponentsModified() {
  // 检查服务器块管理器
  // @ts-ignore - 忽略类型检查问题
  const serverBlockModified = serverBlockManagerRef.value &&
    // @ts-ignore - 忽略类型检查问题
    typeof serverBlockManagerRef.value.isConfigModified === 'function' &&
    // @ts-ignore - 忽略类型检查问题
    serverBlockManagerRef.value.isConfigModified();

  // 检查上游块管理器
  // @ts-ignore - 忽略类型检查问题
  const upstreamModified = upstreamManagerRef.value &&
    // @ts-ignore - 忽略类型检查问题
    typeof upstreamManagerRef.value.isConfigModified === 'function' &&
    // @ts-ignore - 忽略类型检查问题
    upstreamManagerRef.value.isConfigModified();

  // 移除对配置文件编辑器的检查
  return serverBlockModified || upstreamModified;
}



// 添加跳转函数
function handleNavigate(type) {
  if (type === 'bind') {
    router.push('/data_analysis/biz_hub');
  } else if (type === 'add') {
    router.push('/cmdb/middlewareInformation');
  }
}

</script>

<template>
  <div>
  <div class="nginx-manager">
    <div class="page-main">
      <div class="nginx-container">

        <el-tabs v-model="data.activeTabName" class="server-tabs" type="card">
          <!-- 加载状态显示 -->
          <div v-if="data.loading" class="loading-tabs-placeholder">
            <el-skeleton :rows="3" animated />
          </div>

          <!-- 服务器标签页 -->
          <template v-else>
            <el-tab-pane v-for="business in formatTreeData(data.businessList)" :key="business.id"
              :label="business.label" :name="business.id">
              <template #label>
                <div class="business-tab-label">
                  <el-icon class="business-icon"><svg-icon name="nginx" /></el-icon>
                  <span>{{ business.label }}</span>
                  <el-tag size="small" type="info" class="count-tag">{{ business.num }}</el-tag>
                </div>
              </template>

              <!-- 业务下的服务器列表 -->
              <div class="server-list">
                <el-card v-for="server in business.children" :key="server.id" class="server-card"
                  :class="{ 'active-server': data.selectedNode && data.selectedNode.id === server.id }"
                  @click="handleNodeClick(server)">
                  <div class="server-card-content">
                    <div class="server-info">
                      <el-icon class="server-icon"><svg-icon name="business" /></el-icon>
                      <div class="server-details">
                        <span class="server-name">{{ server.label }}</span>
                        <span class="server-ip">{{ server.ip }}</span>
                      </div>
                    </div>
                    <div class="server-status">
                      <el-tag size="small" :type="server.data.status === '运行中' ? 'success' : 'danger'">
                        {{ server.data.status }}
                      </el-tag>
                    </div>
                  </div>
                </el-card>
              </div>
            </el-tab-pane>
          </template>
        </el-tabs>

        <!-- 服务器详情内容区域 -->
        <div class="server-content-area">
          <el-skeleton :rows="10" animated v-if="data.contentLoading && data.selectedNode?.ip" />

          <!-- 没有选择服务器的提示 -->
          <div v-else-if="!data.selectedNode?.ip || Object.keys(data.selectedNode).length === 0"
            :class="['empty-container', data.businessList.length === 0 ? 'empty-fullscreen' : 'empty-compact']">
            <div class="empty-content">
              <div class="empty-illustration">
                <svg-icon name="nginx" class="nginx-icon" />
                <div class="pulse-circle"></div>
              </div>
              <div class="empty-text-container">
                <h1 class="empty-title" v-if="data.businessList.length === 0">请添加绑定nginx服务器</h1>
                <h1 class="empty-title" v-else>请选择服务器</h1>
                <p class="empty-description" v-if="data.businessList.length === 0">
                  暂无可管理的服务器，请先进行绑定
                </p>
                <p class="empty-description" v-else>
                  请从上方选择一个业务进行查看，如没有业务请先去绑定
                </p>
                <div class="arrow-hint" @click="handleNavigate(data.businessList.length === 0 ? 'add' : 'bind')">
                  <svg viewBox="0 0 24 24" class="arrow-icon">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"></path>
                  </svg>
                  <span v-if="data.businessList.length === 0">添加Nginx服务器</span>
                  <span v-else>业务绑定</span>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="server-details">

            <!-- 新的紧凑型悬浮导航按钮 -->
            <div class="compact-floating-nav">
              <el-popover
                placement="top"
                :width="120"
                trigger="click"
                popper-class="nav-popover"
              >
                <template #reference>
                  <el-button circle type="primary" :icon="Expand" class="nav-trigger-btn" size="large">
                  </el-button>
                </template>
                <div class="compact-nav-menu">
                  <div class="compact-nav-item" :class="{ 'active': data.activeAnchor === 'basic-info' }"
                    @click="scrollToAnchor('basic-info')">
                    <el-icon><svg-icon name="info" /></el-icon>
                    <span>基本信息</span>
                  </div>
                  <div class="compact-nav-item" :class="{ 'active': data.activeAnchor === 'server-blocks' }"
                    @click="scrollToAnchor('server-blocks')">
                    <el-icon><svg-icon name="server" /></el-icon>
                    <span>反向代理</span>
                  </div>
                  <div class="compact-nav-item" :class="{ 'active': data.activeAnchor === 'nginx-logs' }"
                    @click="scrollToAnchor('nginx-logs')">
                    <el-icon><svg-icon name="log" /></el-icon>
                    <span>日志</span>
                  </div>
                </div>
              </el-popover>
            </div>

            <!-- 基本信息区块 -->
            <div id="basic-info" class="content-section">
              <div class="section-header">
                <h2 class="section-title">基本信息</h2>
              </div>
              <BasicInformation :item="data.selectedNode || {}" :overview="data.overview || {}"
                @config-changed="(config) => updateConfiguration('overview', config)" />
            </div>

            <!-- 移除配置文件编辑器区块 -->

            <!-- 反向代理区块 -->
            <div id="server-blocks" class="content-section">
              <div class="section-header">
                <h2 class="section-title">反向代理</h2>
              </div>
              <el-skeleton :rows="6" animated v-if="data.contentLoading" />
              <ServerBlockManager v-else :serverBlocks="data.serverBlocks" :serverIp="data.selectedNode.ip"
                :nginxConfig="data.nginxConfigFiles"
                @config-changed="(configData) => updateConfiguration('server-blocks', configData)"
                ref="serverBlockManagerRef" />
            </div>

            <!-- 负载均衡区块 -->
            <!-- <div id="upstreams" class="content-section">
              <div class="section-header">
                <h2 class="section-title">负载均衡</h2>
              </div>
              <el-skeleton :rows="6" animated v-if="data.contentLoading" />
              <UpstreamManager v-else :upstreams="data.upstreams" :serverIp="data.selectedNode.ip"
                :nginxConfig="data.nginxConfigFiles"
                @config-changed="(configData) => updateConfiguration('upstreams', configData)"
                ref="upstreamManagerRef" />
            </div> -->

            <!-- 日志区块 -->
            <div id="nginx-logs" class="content-section">
              <div class="section-header">
                <h2 class="section-title">日志</h2>
              </div>
              <NginxLog :item="data.selectedNode" :overview="data.overview" />
            </div>

            <!-- 添加保存配置按钮 -->
            <div v-if="data.configModified || isComponentsModified()" class="save-config-container">
              <div class="button-group">
                <el-button type="primary" size="large" :loading="data.contentLoading"
                  :disabled="data.hasSyntaxErrors || data.pendingChanges" @click="saveConfigChanges">
                  <el-icon><svg-icon name="ep:select" /></el-icon>
                  <span>保存配置</span>
                </el-button>
              </div>
              <div v-if="data.hasSyntaxErrors" class="save-error-hint">
                <el-icon><svg-icon name="ep:warning" /></el-icon>
                <span>配置文件存在语法错误，请修复后再保存</span>
              </div>
              <div v-else-if="data.pendingChanges" class="save-error-hint warning">
                <el-icon><svg-icon name="ep:info" /></el-icon>
                <span>配置已修改但尚未应用! 请先点击"应用配置"按钮来保存更改</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 差异对比对话框 -->
  <el-dialog v-model="data.showDiffDialog" :title="`配置文件差异 (${data.currentDiffIndex + 1}/${data.diffFiles.length})`"
    :width="'95%'" class="diff-dialog" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false"
    @closed="clearLineClassCache">
    <template v-if="data.diffFiles && data.diffFiles.length > 0 && data.diffFiles[data.currentDiffIndex]">
      <div class="diff-content">
        <!-- 文件信息头部 -->
        <div class="diff-header">
          <span class="file-path">{{ data.diffFiles[data.currentDiffIndex].file_path }}</span>
          <span class="file-status" :class="{
            'new': data.diffFiles[data.currentDiffIndex].diffType === 'new',
            'modified': data.diffFiles[data.currentDiffIndex].diffType === 'modified',
            'deleted': data.diffFiles[data.currentDiffIndex].diffType === 'deleted'
          }">
            {{ data.diffFiles[data.currentDiffIndex].diffType === 'new' ? '新增' :
              data.diffFiles[data.currentDiffIndex].diffType === 'modified' ? '修改' : '删除' }}
          </span>
          <span class="diff-counter">{{ data.currentDiffIndex + 1 }}/{{ data.diffFiles.length }}</span>
        </div>
        
        <!-- 简单的差异图例样式 -->
        <div class="diff-legend">
          <div class="legend-item"><span class="color-sample old"></span> 已变更/删除的内容</div>
          <div class="legend-item"><span class="color-sample new"></span> 新增/修改的内容</div>
          <div class="legend-note">注：只有变更部分会高亮显示</div>
        </div>
        
        <!-- 差异内容区域 -->
        <div class="diff-split-view">
          <!-- 左侧：原始内容 -->
          <div class="diff-pane diff-left">
            <div class="diff-header-bar">
              <span class="pane-title">原始内容</span>
            </div>
            <div class="line-content">
              <table class="line-table">
                <tbody>
                  <template v-if="data.diffFiles[data.currentDiffIndex].diffType !== 'new'">
                    <tr v-for="(line, index) in data.diffFiles[data.currentDiffIndex].oldContent.split('\n')"
                      :key="`old-${index}`"
                      :class="getLineClass(data.diffFiles[data.currentDiffIndex], line, index, 'old')">
                      <td class="line-number">{{ index + 1 }}</td>
                      <td class="line-text">{{ line }}</td>
                    </tr>
                  </template>
                  <tr v-else>
                    <td class="line-number">-</td>
                    <td class="line-text empty-content">（新增文件，无原始内容）</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 右侧：修改后内容 -->
          <div class="diff-pane diff-right">
            <div class="diff-header-bar">
              <span class="pane-title">修改后内容</span>
            </div>
            <div class="line-content">
              <table class="line-table">
                <tbody>
                  <template v-if="data.diffFiles[data.currentDiffIndex].diffType !== 'deleted'">
                    <tr v-for="(line, index) in data.diffFiles[data.currentDiffIndex].newContent.split('\n')"
                      :key="`new-${index}`"
                      :class="getLineClass(data.diffFiles[data.currentDiffIndex], line, index, 'new')">
                      <td class="line-number">{{ index + 1 }}</td>
                      <td class="line-text">{{ line }}</td>
                    </tr>
                  </template>
                  <tr v-else>
                    <td class="line-number">-</td>
                    <td class="line-text empty-content">（文件已删除，无新内容）</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <div class="diff-dialog-footer">
        <div>
          <el-button @click="data.showDiffDialog = false">取消</el-button>
        </div>
        <div>
          <el-button @click="showPreviousDiff" :disabled="data.currentDiffIndex === 0">
            上一个文件
          </el-button>
          <el-button @click="showNextDiff" type="primary">
            {{ data.currentDiffIndex < data.diffFiles.length - 1 ? '下一个文件' : '确认保存' }} </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.nginx-manager {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color);

  .page-main {
    flex: 1;
    overflow: hidden;
    padding: 16px;
  }

  .nginx-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--el-bg-color-overlay);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-light);

    .header-title {
      display: flex;
      align-items: center;
      gap: 12px;

      h1 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .el-icon {
        font-size: 24px;
        color: var(--el-color-primary);
      }
    }
  }

  .server-tabs {
    flex-shrink: 0;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }

    .business-tab-label {
      display: flex;
      align-items: center;
      gap: 8px;

      .business-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: var(--el-color-success);
      }

      .count-tag {
        font-size: 11px;
        padding: 0 6px;
        height: 18px;
        line-height: 18px;
        border-radius: 9px;
      }
    }
  }

  .loading-tabs-placeholder {
    padding: 20px;
  }

  .server-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    padding: 16px;
    background-color: var(--el-fill-color-light);
  }

  .server-card {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    }

    &.active-server {
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
    }

    .server-card-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .server-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .server-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
        border-radius: 6px;
      }

      .server-details {
        display: flex;
        flex-direction: column;

        .server-name {
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .server-ip {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .server-content-area {
    flex: 1;
    overflow-y: auto;
    background-color: var(--el-bg-color);
    padding: 20px;
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* 当没有数据时，占满整个屏幕 */
  .empty-fullscreen {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  /* 当有数据但未选择服务器时，使用自然大小 */
  .empty-compact {
    padding: 3rem 0;
    height: auto;
    min-height: 300px;
  }

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    max-width: 500px;
    padding: 2rem;
  }

  .empty-illustration {
    position: relative;
    width: 160px;
    height: 160px;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .nginx-icon {
      width: 90px;
      height: 90px;
      color: var(--el-color-primary);
      z-index: 2;
      filter: drop-shadow(0 8px 16px rgba(24, 144, 255, 0.5));
      animation: float 3s ease-in-out infinite;
    }

    .pulse-circle {
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: radial-gradient(circle, var(--el-color-primary-light-8) 0%, rgba(236, 245, 255, 0) 70%);
      opacity: 0.8;
      z-index: 1;
      animation: pulse 3s ease-in-out infinite;
    }
  }

  .empty-text-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    animation: slideUp 0.5s ease 0.2s both;
  }

  .empty-title {
    margin: 0;
    font-size: 32px;
    font-weight: 600;
    color: var(--el-color-primary);
    line-height: 1.2;
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .empty-description {
    margin: 0;
    font-size: 18px;
    color: var(--el-text-color-secondary);
    line-height: 1.6;
    max-width: 320px;
  }

  .arrow-hint {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 24px;
    padding: 14px 24px;
    background-color: var(--el-color-primary-light-9);
    border-radius: 30px;
    color: var(--el-color-primary-dark-2);
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.2);
    transition: all 0.3s ease;
    animation: pulse-light 2s infinite;
    border: 2px solid var(--el-color-primary-light-5);

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(var(--el-color-primary-rgb), 0.3);
    }

    .arrow-icon {
      width: 24px;
      height: 24px;
      fill: currentColor;
      transform: rotate(-90deg);
    }
  }

  // 悬浮式锚点导航
  .floating-anchor-navigation {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--el-bg-color-overlay);
    border-radius: 12px;
    margin-bottom: 20px;
    padding: 8px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    }

    .nav-wrapper {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      gap: 12px;
      padding: 4px;
      scrollbar-width: thin;

      &::-webkit-scrollbar {
        height: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: var(--el-border-color);
        border-radius: 2px;
      }
    }

    .nav-item {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      white-space: nowrap;
      cursor: pointer;
      color: var(--el-text-color-regular);
      transition: all 0.3s ease;

      .el-icon {
        font-size: 16px;
      }

      &:hover {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }

      &.active {
        background-color: var(--el-color-primary);
        color: white;
        box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.35);
        transform: translateY(-1px);
      }
    }
  }

  // 内容区块样式
  .content-section {
    background-color: var(--el-bg-color-overlay);
    border-radius: 12px;
    margin-bottom: 30px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

    .section-header {
      margin-bottom: 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      padding-bottom: 10px;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
        position: relative;
        padding-left: 15px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 4px;
          bottom: 4px;
          width: 4px;
          background-color: var(--el-color-primary);
          border-radius: 2px;
        }
      }
    }
  }

  // 为锚点添加顶部内边距，防止被导航栏遮挡
  #basic-info,
  #server-blocks,
  #upstreams,
  #nginx-logs {
    scroll-margin-top: 80px;
  }

  // 保存配置按钮样式
  .save-config-container {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    z-index: 100;

    .button-group {
      display: flex;
      gap: 10px;
      margin-bottom: 8px;
    }

    .el-button {
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      transition: all 0.2s ease;
      min-width: 140px;

      &:not(:disabled):hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }

    .save-error-hint {
      margin-top: 8px;
      background-color: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &.warning {
        background-color: var(--el-color-warning-light-9);
        color: var(--el-color-warning-dark-2);
        font-weight: bold;
      }

      .el-icon {
        margin-right: 6px;
        font-size: 16px;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }

  100% {
    transform: scale(0.8);
    opacity: 0.7;
  }
}

@keyframes pulse-light {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.8;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0px);
  }
}

/* 差异对比对话框样式 */
.diff-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
    max-height: 80vh;
    overflow-y: auto;
  }

  .diff-content {
    display: flex;
    flex-direction: column;
  }

  .diff-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    background-color: #1e1e1e;
    color: #eee;

    .file-path {
      flex-grow: 1;
      font-weight: bold;
      font-family: monospace;
    }

    .file-status {
      margin: 0 16px;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;

      &.new {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
        font-weight: bold;
      }

      &.modified {
        background-color: rgba(255, 193, 7, 0.2);
        color: #ffc107;
        font-weight: bold;
      }

      &.deleted {
        background-color: rgba(220, 53, 69, 0.2);
        color: #dc3545;
        font-weight: bold;
      }
    }

    .diff-counter {
      font-size: 12px;
      color: #aaa;
    }
  }
  
  /* 简单的差异图例样式 */
  .diff-legend {
    display: flex;
    padding: 8px 16px;
    background-color: #2a2a2a;
    border-bottom: 1px solid #444;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #ddd;
      
      .color-sample {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        
        &.old {
          background-color: rgba(255, 100, 100, 0.2);
          border-left: 3px solid #ff6464;
        }
        
        &.new {
          background-color: rgba(40, 167, 69, 0.2);
          border-left: 3px solid #28a745;
        }
      }
    }
    
    .legend-note {
      width: 100%;
      text-align: center;
      margin-top: 6px;
      font-size: 11px;
      color: #aaa;
      font-style: italic;
    }
  }
  
  /* 变更类型指示器样式 */
  .diff-change-types {
    display: flex;
    flex-direction: column;
    padding: 8px 16px;
    background-color: #2a2a2a;
    border-bottom: 1px solid #444;
    
    .change-type-list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 8px;
      
      .change-type-tag {
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: 500;
        background-color: #444;
        color: #ddd;
        
        /* 特定类型的标签样式 */
        &.new-file, &.server-added, &.location-added, &.upstream-added, &.http-added, &.events-added, &.blocks-added {
          background-color: rgba(40, 167, 69, 0.2);
          color: #4cca65;
        }
        
        &.deleted-file, &.server-removed, &.location-removed, &.upstream-removed, &.http-removed, &.events-removed, &.blocks-removed {
          background-color: rgba(220, 53, 69, 0.2);
          color: #f15b6c;
        }
        
        &.comment-changes {
          background-color: rgba(255, 193, 7, 0.2);
          color: #ffd54f;
        }
        
        &.content-modified {
          background-color: rgba(23, 162, 184, 0.2);
          color: #5ad3ea;
        }
      }
    }
    
    .change-type-legend {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      font-size: 11px;
      color: #aaa;
      padding: 6px 0;
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .color-block {
          display: inline-block;
          width: 14px;
          height: 14px;
          border-radius: 2px;
          
          &.added {
            background-color: rgba(40, 167, 69, 0.25);
            border-left: 3px solid #28a745;
          }
          
          &.deleted {
            background-color: rgba(220, 53, 69, 0.25);
            border-left: 3px solid #dc3545;
          }
          
          &.comment {
            background-color: rgba(255, 193, 7, 0.25);
            border-left: 3px solid #ffc107;
          }
          
          &.format {
            background-color: rgba(23, 162, 184, 0.15);
            border-left: 3px solid #17a2b8;
          }
        }
      }
    }
  }

  .diff-split-view {
    display: flex;
    height: calc(80vh - 120px);
    background-color: #1e1e1e;
    color: #eee;

    .diff-pane {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      &.diff-left {
        border-right: 1px solid #444;
      }

      .diff-header-bar {
        background-color: #333;
        padding: 8px 12px;
        border-bottom: 1px solid #444;

        .pane-title {
          font-weight: bold;
          font-size: 14px;
        }
      }

      .line-content {
        flex: 1;
        overflow: auto;

        .line-table {
          width: 100%;
          border-collapse: collapse;
          font-family: 'Courier New', Courier, monospace;
          font-size: 13px;

          tr {
            /* 旧版本内容样式 - 红色 */
            &.diff-old {
              background-color: rgba(255, 100, 100, 0.2);
              border-left: 3px solid #ff6464;
            }

            /* 新版本内容样式 - 绿色 */
            &.diff-new {
              background-color: rgba(40, 167, 69, 0.2);
              border-left: 3px solid #28a745;
            }

            .line-number {
              width: 40px;
              text-align: right;
              padding: 0 8px;
              color: #888;
              border-right: 1px solid #444;
              user-select: none;
            }

            .line-text {
              padding: 0 8px;
              white-space: pre;
              overflow-x: hidden;

              &.empty-content {
                color: #888;
                font-style: italic;
                padding: 12px 8px;
              }
            }
          }
        }
      }
    }
  }

  .diff-dialog-footer {
    display: flex;
    justify-content: space-between;
    background-color: #333;
    padding: 10px;

    .el-button {
      margin-left: 8px;
    }
  }
}

/* 新的紧凑型悬浮导航样式 */
.compact-floating-nav {
  position: fixed;
  right: 30px;
  bottom: 80px;
  z-index: 999;
  
  .nav-trigger-btn {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
    width: 50px;
    height: 50px;
  }
}

/* 弹出菜单项样式 */
.compact-nav-menu {
  .compact-nav-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 4px;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: var(--el-fill-color-light);
    }
    
    &.active {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
    
    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
    
    span {
      font-size: 14px;
    }
  }
}

/* 自定义弹出框样式 */
:deep(.nav-popover) {
  padding: 8px !important;
  min-width: 120px;
}
</style>