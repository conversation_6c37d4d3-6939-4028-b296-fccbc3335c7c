<script setup lang="ts">
import { apiDictionaryeAddUpdate } from "@/api/modules/dictionary";
import type { FormInstance, FormRules } from "element-plus";

const props = withDefaults(
    defineProps<{
        modelValue: boolean;
        dictionaryId: string | number;
        id?: string | number;
        tree: any[];
    }>(),
    {
        modelValue: false,
        dictionaryId: "",
        id: "",
    }
);

const emit = defineEmits(["update:modelValue", "success"]);
const myVisible = ref(props.modelValue);
const title = computed(() => (props.id === "" ? "新增字典项" : "编辑字典项"));

const formRef = ref<FormInstance>();
const form = ref({
    dictionaryId: props.dictionaryId,
    id: props.id,
    key: "",
    value: "",
});
const formRules = ref<FormRules>({
    dictionaryId: [{ required: true, message: "请选择所属字典" }],
    key: [{ required: true, message: "请输入字典项名称" }],
    value: [{ required: true, message: "请输入字典项键值" }],
});
const currentDic = computed(() => {
    return props.tree.filter((item) => item.id == props.dictionaryId)[0];
});
onMounted(() => {
    if (props.id !== "") {
        const currentItem = currentDic.value.dictionary_value.filter((item) => {
            return item.id == props.id;
        })[0];
        form.value.key = currentItem.key;
        form.value.value = currentItem.value;
    }
});

function onSubmit() {
    let params = { ...currentDic.value };
    if (form.value.id === "") {
        params.dictionary_value = [
            ...currentDic.value.dictionary_value,
            { key: form.value.key, value: form.value.value },
        ];
        formRef.value &&
            formRef.value.validate((valid) => {
                if (valid) {
                    apiDictionaryeAddUpdate(params).then(() => {
                        emit("success");
                        onCancel();
                    });
                }
            });
    } else {
        params.dictionary_value = [...currentDic.value.dictionary_value];
        params.dictionary_value.forEach((item) => {
            if (item.id == form.value.id) {
                item.key = form.value.key;
                item.value = form.value.value;
            }
        });
        formRef.value &&
            formRef.value.validate((valid) => {
                if (valid) {
                    apiDictionaryeAddUpdate(params).then(() => {
                        emit("success");
                        onCancel();
                    });
                }
            });
    }
}

function onCancel() {
    myVisible.value = false;
}
</script>

<template>
    <el-dialog
        v-model="myVisible"
        :title="title"
        width="400px"
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
        @closed="emit('update:modelValue', false)"
    >
        <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
            <el-form-item label="所属字典" prop="dictionaryId">
                <el-cascader
                    v-model="form.dictionaryId"
                    :options="tree"
                    :props="{ value: 'id', label: 'dictionary_name', emitPath: false, checkStrictly: true }"
                    :show-all-levels="false"
                    placeholder="请选择所属字典"
                    readonly
                    disabled
                />
            </el-form-item>
            <el-form-item label="字典项名称" prop="key">
                <el-input v-model="form.key" placeholder="请输入字典项名称" clearable />
            </el-form-item>
            <el-form-item label="字典项键值" prop="value">
                <el-input v-model="form.value" placeholder="请输入字典项键值" clearable />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button size="large" @click="onCancel">取消</el-button>
            <el-button type="primary" size="large" @click="onSubmit">确定</el-button>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-cascader) {
    width: 100%;
}
</style>
