const Layout = () => import("@/layout/index.vue");
const IframeLayout = () => import("@/layout/iframe.vue");
let children = [
  {
    path: "analysis_platform",
    name: "analysis_platform",
    component: () => import("@/views/visualization_platform/index.vue"),
    meta: {
      title: "运维分析平台",
      // link: url,
      cache: true,
      auth: ['admin', "analysis_platform.browse"],
    },

    children: [
      {
        path: "analysis_iframe",
        name: "analysis_iframe",
        component: () => import("@/views/visualization_platform/dashbord.vue"),
        meta: {
          title: "运维分析平台面板",
          copyright: false,
          cache: true,
          sidebar: false,
          activeMenu: "/analysis/analysis_platform",
        },
      },
    ],
  },
];

export default {
  path: "/analysis",
  redirect: "/analysis/analysis_platform",
  component: Layout,
  name: "analysis",
  meta: {
    auth: ['admin', "analysis.browse"],
    title: "分析平台",
    icon: "analysis",
  },
  children,
};
