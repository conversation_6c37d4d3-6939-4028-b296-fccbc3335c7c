import Mock from 'mockjs'

const AllList = []
for (let i = 0; i < 50; i++) {
  AllList.push(Mock.mock({
    id: '@id',
    title: '@ctitle(10, 20)'
  }))
}
export default [
  {
    url: '/mock/resource/auto_cmdb',
    method: 'get',
    response:
    {
      "status_code": 200,
      "message": "成功获取资源",
      "data": [
        {
          "title": "基础信息",
          "unique": "base",
          "type": "nomal",
          "data": {
            "databaseName": {
              "name_zh": "名称",
              "value": "Oracle测试数据库"
            },
            "ip": {
              "name_zh": "IP",
              "value": "*************"
            },
            "databaseServer": {
              "name_zh": "服务器",
              "value": ""
            },
            "databaseSoft": {
              "name_zh": "安装软件",
              "value": "oracle"
            },
            "type": {
              "name_zh": "二级配置项",
              "value": "数据库"
            }
          }
        },
        {
          "data": {
            "cdb": {
              "value": "否",
              "name_zh": "是否容器数据库"
            },
            "name": {
              "value": "WEBEMR",
              "name_zh": "名称"
            },
            "version": {
              "value": "********.0",
              "name_zh": "数据库版本"
            },
            "archiver": {
              "value": "STARTED",
              "name_zh": "归档进程"
            },
            "log_mode": {
              "value": "ARCHIVELOG",
              "name_zh": "日志模式"
            },
            "host_name": {
              "value": "************",
              "name_zh": "主机"
            },
            "active_state": {
              "value": "正常",
              "name_zh": "实例状态"
            },
            "startup_time": {
              "value": "2024-04-18T10:15:09",
              "name_zh": "实例启用时间"
            },
            "version_time": {
              "value": "2024-03-29T14:25:57",
              "name_zh": "版本时间"
            },
            "instance_name": {
              "value": "WEBEMR",
              "name_zh": "实例"
            },
            "database_status": {
              "value": "ACTIVE",
              "name_zh": "数据库状态"
            },
            "instance_number": {
              "value": 1,
              "name_zh": "实例编号"
            },
            "ASM_Proxy_Instance": {
              "value": "FALSE",
              "name_zh": "是否ASM代理"
            }
          },
          "type": "nomal",
          "title": "数据库基础信息",
          "unique": "database"
        },
        {
          "data": {
            "tableData": [

              {
                "name": "undo_retention",
                "value": "900",
                "isdefault": "TRUE"
              },
              {
                "name": "undo_tablespace",
                "value": "UNDOTBS1",
                "isdefault": "FALSE"
              },
              {
                "name": "unified_audit_common_systemlog",
                "value": null,
                "isdefault": "TRUE"
              },
              {
                "name": "unified_audit_sga_queue_size",
                "value": "1048576",
                "isdefault": "TRUE"
              },
              {
                "name": "unified_audit_systemlog",
                "value": null,
                "isdefault": "TRUE"
              },
              {
                "name": "uniform_log_timestamp_format",
                "value": "TRUE",
                "isdefault": "TRUE"
              },
              {
                "name": "use_dedicated_broker",
                "value": "FALSE",
                "isdefault": "TRUE"
              },
              {
                "name": "use_large_pages",
                "value": "TRUE",
                "isdefault": "TRUE"
              },
              {
                "name": "user_dump_dest",
                "value": "/data/app/oracle/product/19.3.0/dbhome_1/rdbms/log",
                "isdefault": "TRUE"
              },
              {
                "name": "wallet_root",
                "value": null,
                "isdefault": "TRUE"
              },
              {
                "name": "workarea_size_policy",
                "value": "AUTO",
                "isdefault": "TRUE"
              },
              {
                "name": "xml_db_events",
                "value": "enable",
                "isdefault": "TRUE"
              },
              {
                "name": "xml_handling_of_invalid_chars",
                "value": "raise_error",
                "isdefault": "TRUE"
              }
            ],
            "tableColumn": {
              "name": "参数",
              "value": "值",
              "isdefault": "是否默认值"
            }
          },
          "type": "table",
          "title": "参数配置信息",
          "unique": "parameters"
        },
        {
          "data": {
            "tableData": [
              {
                "mb": 200,
                "group": 1,
                "status": "活跃",
                "thread": 1,
                "archived": "NO",
                "sequence": 34,
                "blocksize": 512
              },
              {
                "mb": 200,
                "group": 2,
                "status": "休眠",
                "thread": 1,
                "archived": "YES",
                "sequence": 32,
                "blocksize": 512
              },
              {
                "mb": 200,
                "group": 3,
                "status": "休眠",
                "thread": 1,
                "archived": "YES",
                "sequence": 33,
                "blocksize": 512
              }
            ],
            "tableColumn": {
              "mb": "日志大小(MB)",
              "group": "组别",
              "status": "活跃状态",
              "thread": "线程编号",
              "archived": "归档状态",
              "sequence": "序列号",
              "blocksize": "块大小"
            }
          },
          "type": "table",
          "title": "redologs线程信息",
          "unique": "redologs"
        },
        {
          "data": {
            "tableData": [
              {
                "isdba": "否",
                "con_id": 0,
                "con_name": "WEBEMR",
                "oracle_home": "/data/app/oracle/product/19.3.0/dbhome_1",
                "current_user": "ZLMONITOR",
                "database_role": "PRIMARY"
              }
            ],
            "tableColumn": {
              "isdba": "dba权限",
              "con_id": "容器ID",
              "con_name": "连接容器",
              "oracle_home": "oracle安装路径",
              "current_user": "当前用户",
              "database_role": "数据库角色"
            }
          },
          "type": "table",
          "title": "用户环境",
          "unique": "userenv"
        },
        {
          "data": {
            "tableData": [
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 112,
                "username": "BASECONFIG"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 115,
                "username": "CSFORM"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 120,
                "username": "CSFORMH"
              },
              {
                "created": "2024-03-29T14:32:04",
                "user_id": 110,
                "username": "HR"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 111,
                "username": "MAAI"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 118,
                "username": "MAAIH"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 116,
                "username": "MEDDOC"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 121,
                "username": "MEDDOCH"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 113,
                "username": "QCRULE"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 117,
                "username": "SCORES"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 122,
                "username": "SCORESH"
              },
              {
                "created": "2024-04-17T14:10:12",
                "user_id": 124,
                "username": "SOE"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 114,
                "username": "ZLEMR"
              },
              {
                "created": "2024-03-29T14:53:51",
                "user_id": 119,
                "username": "ZLEMRH"
              },
              {
                "created": "2024-04-09T11:51:32",
                "user_id": 123,
                "username": "ZLMONITOR"
              }
            ],
            "tableColumn": {
              "created": "创建时间",
              "user_id": "序号",
              "username": "用户名"
            }
          },
          "type": "table",
          "title": "用户信息",
          "unique": "users"
        },
        {
          "data": {
            "tableData": [
              {
                "name": "WEBEMR",
                "con_id": 0,
                "guid_hex": "",
                "tablespaces": {
                  "data": {
                    "tableData": [

                      {
                        "name": "UNDOTBS1",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 730,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/undotbs01.dbf"
                      },
                      {
                        "name": "UNDOTBS1",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 30720,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/undotbs1.dbf"
                      },
                      {
                        "name": "USERS",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 18,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/users01.dbf"
                      },
                      {
                        "name": "ZLEMRBUSHISDATA",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 100,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/ZlemrBusHisData01.DBF"
                      },
                      {
                        "name": "ZLEMRCONTENTHISDATA",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 100,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/ZlemrContentHisData01.DBF"
                      },
                      {
                        "name": "ZL_BASIC_CONFIG",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 3600,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/zl_basic_config01.dbf"
                      },
                      {
                        "name": "ZL_MAAI",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 160,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/zl_maai01.dbf"
                      },
                      {
                        "name": "ZL_QCRULE",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 100,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/zl_qcrule01.dbf"
                      },
                      {
                        "name": "ZL_SCORES",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 100,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/ZL_SCORES01.DBF"
                      },
                      {
                        "name": "ZL_ZLEMR",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 160,
                        "datafile_name": "/data/app/oracle/oradata/WEBEMR/zl_zlemr01.DBF"
                      }
                    ],
                    "tableColumn": {
                      "name": "表空间名称",
                      "bigfile": "大文件",
                      "size_mb": "表空间大小(MB)",
                      "datafile_name": "文件路径"
                    }
                  },
                  "type": "table",
                  "title": "表空间",
                  "unique": "tablespaces"
                },
                "temp_tablespaces": {
                  "data": {
                    "tableData": [
                      {
                        "name": "TEMP",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 139,
                        "tempfile_name": "/data/app/oracle/oradata/WEBEMR/temp01.dbf"
                      },
                      {
                        "name": "TEMP",
                        "con_id": 0,
                        "bigfile": "NO",
                        "size_mb": 30720,
                        "tempfile_name": "/data/app/oracle/oradata/WEBEMR/temp02.dbf"
                      }
                    ],
                    "tableColumn": {
                      "name": "表空间名称",
                      "bigfile": "大文件",
                      "size_mb": "表空间大小(MB)",
                      "datafile_name": "文件路径"
                    }
                  },
                  "type": "table",
                  "title": "临时表空间",
                  "unique": "temp_tablespaces"
                }
              }
            ],
            "tableColumn": {
              "name": "PDB容器名",
              "con_id": "PDB_ID",
              "tablespaces": "表空间",
              "temp_tablespaces": "临时表空间"
            }
          },
          "type": "nestedtable",
          "title": "表空间信息",
          "unique": "pdbs_tablespaces_temptablespaces"
        }
      ]
    }

  }, {
    url: "/mock/server/auto_cmdb",
    method: "get",
    response: {
      "status_code": 200,
      "message": "成功获取资源",
      "data": [
        {
          "title": "基础信息",
          "unique": "base",
          "type": "nomal",
          "data": {
            "databaseName": {
              "name_zh": "名称",
              "value": "本机100服务器"
            },
            "ip": {
              "name_zh": "IP",
              "value": "***************"
            },
            "databaseSoft": {
              "name_zh": "安装软件",
              "value": "nginx"
            },
            "type": {
              "name_zh": "二级配置项",
              "value": "中间件"
            }
          }
        },
        {
          "data": {
            "tableData": [
              {
                "key": "nginx_file_path",
                "value": "/etc/nginx/nginx.conf"
              },
              {
                "key": "nginx_version",
                "value": "1.18.0"
              },
              {
                "key": "listening_ports_list",
                "value": [
                  "80"
                ]
              },
              {
                "key": "access_log",
                "value": "/var/log/nginx/access.log"
              },
              {
                "key": "error_log",
                "value": "/var/log/nginx/error.log"
              },
              {
                "key": "nginx_status",
                "value": "Active"
              },
              {
                "key": "include_path",
                "value": "/etc/nginx/conf.d"
              },
              {
                "key": "cluster_list",
                "value": []
              }
            ],
            "tableColumn": {
              "key": "属性",
              "value": "值"
            }
          },
          "type": "table",
          "title": "配置文件-nginx.conf",
          "unique": "config_data"
        },
        {
          "title": "配置文件夹",
          "unique": "cannot_parse_file_data",
          "type": "table",
          "data": {
            "tableColumn": {
              "file_name": "文件名",
              "file_content": "文件内容"
            },
            "tableData": [
              {
                "file_name": "/etc/nginx/mime.types",
                "file_content": "\ntypes {\n    text/html                                        html htm shtml;\n    text/css                                         css;\n    text/xml                                         xml;\n    image/gif                                        gif;\n    image/jpeg                                       jpeg jpg;\n    application/javascript                           js;\n    application/atom+xml                             atom;\n    application/rss+xml                              rss;\n\n    text/mathml                                      mml;\n    text/plain                                       txt;\n    text/vnd.sun.j2me.app-descriptor                 jad;\n    text/vnd.wap.wml                                 wml;\n    text/x-component                                 htc;\n\n    image/png                                        png;\n    image/svg+xml                                    svg svgz;\n    image/tiff                                       tif tiff;\n    image/vnd.wap.wbmp                               wbmp;\n    image/webp                                       webp;\n    image/x-icon                                     ico;\n    image/x-jng                                      jng;\n    image/x-ms-bmp                                   bmp;\n\n    font/woff                                        woff;\n    font/woff2                                       woff2;\n\n    application/java-archive                         jar war ear;\n    application/json                                 json;\n    application/mac-binhex40                         hqx;\n    application/msword                               doc;\n    application/pdf                                  pdf;\n    application/postscript                           ps eps ai;\n    application/rtf                                  rtf;\n    application/vnd.apple.mpegurl                    m3u8;\n    application/vnd.google-earth.kml+xml             kml;\n    application/vnd.google-earth.kmz                 kmz;\n    application/vnd.ms-excel                         xls;\n    application/vnd.ms-fontobject                    eot;\n    application/vnd.ms-powerpoint                    ppt;\n    application/vnd.oasis.opendocument.graphics      odg;\n    application/vnd.oasis.opendocument.presentation  odp;\n    application/vnd.oasis.opendocument.spreadsheet   ods;\n    application/vnd.oasis.opendocument.text          odt;\n    application/vnd.openxmlformats-officedocument.presentationml.presentation\n                                                     pptx;\n    application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\n                                                     xlsx;\n    application/vnd.openxmlformats-officedocument.wordprocessingml.document\n                                                     docx;\n    application/vnd.wap.wmlc                         wmlc;\n    application/x-7z-compressed                      7z;\n    application/x-cocoa                              cco;\n    application/x-java-archive-diff                  jardiff;\n    application/x-java-jnlp-file                     jnlp;\n    application/x-makeself                           run;\n    application/x-perl                               pl pm;\n    application/x-pilot                              prc pdb;\n    application/x-rar-compressed                     rar;\n    application/x-redhat-package-manager             rpm;\n    application/x-sea                                sea;\n    application/x-shockwave-flash                    swf;\n    application/x-stuffit                            sit;\n    application/x-tcl                                tcl tk;\n    application/x-x509-ca-cert                       der pem crt;\n    application/x-xpinstall                          xpi;\n    application/xhtml+xml                            xhtml;\n    application/xspf+xml                             xspf;\n    application/zip                                  zip;\n\n    application/octet-stream                         bin exe dll;\n    application/octet-stream                         deb;\n    application/octet-stream                         dmg;\n    application/octet-stream                         iso img;\n    application/octet-stream                         msi msp msm;\n\n    audio/midi                                       mid midi kar;\n    audio/mpeg                                       mp3;\n    audio/ogg                                        ogg;\n    audio/x-m4a                                      m4a;\n    audio/x-realaudio                                ra;\n\n    video/3gpp                                       3gpp 3gp;\n    video/mp2t                                       ts;\n    video/mp4                                        mp4;\n    video/mpeg                                       mpeg mpg;\n    video/quicktime                                  mov;\n    video/webm                                       webm;\n    video/x-flv                                      flv;\n    video/x-m4v                                      m4v;\n    video/x-mng                                      mng;\n    video/x-ms-asf                                   asx asf;\n    video/x-ms-wmv                                   wmv;\n    video/x-msvideo                                  avi;\n}\n"
              },
              {
                "file_name": "/etc/nginx/nginx.conf",
                "file_content": "\nuser  nginx;\nworker_processes  1;\n\nerror_log  /var/log/nginx/error.log warn;\npid        /var/run/nginx.pid;\n\n\nevents {\n    worker_connections  1024;\n}\n\n\nhttp {\n    include       /etc/nginx/mime.types;\n    default_type  application/octet-stream;\n\n    log_format  main  '$remote_addr - $remote_user [$time_local] \"$request\" '\n                      '$status $body_bytes_sent \"$http_referer\" '\n                      '\"$http_user_agent\" \"$http_x_forwarded_for\"';\n\n    access_log  /var/log/nginx/access.log  main;\n\n    sendfile        on;\n    #tcp_nopush     on;\n\n    keepalive_timeout  65;\n\n    #gzip  on;\n\n    include /etc/nginx/conf.d/*.conf;\n}\n"
              },
              {
                "file_name": "/etc/nginx/conf.d/zlly.conf",
                "file_content": "server {\n    # 监听 80端口\n    listen       80;\n    # 本机\n    server_name  ***************;\n    index index.html index.php;\n    root    /usr/share;\n\t\n\t#zabbix\n    location /monitor{\n\t\talias /usr/share/zabbix/;\n\t\tindex index.html index.php;\n        set $args \"enter=guest\";\n        try_files $uri $uri/ /index.php$is_args$args;\n\t}\n    location ~ /monitor/.+\\.php.*$ {\n\tif ($fastcgi_script_name ~ /monitor/(.+\\.php.*)$) {\n\t\tset $valid_fastcgi_script_name $1;\n\t}\n        fastcgi_pass UNIX:/var/opt/rh/rh-php72/run/php-fpm/zabbix.sock;\n        fastcgi_param  SCRIPT_FILENAME  /usr/share/zabbix/$valid_fastcgi_script_name;\n        include fastcgi_params;\n    }\n\t\n\t#itop\n    location /itsm{\n\t\talias /var/www/html/web;\n\t\tindex index.html index.php;\n\t}\n    location ~ /itsm/.+\\.php.*$ {\n        rewrite /itsm/(.+\\.php) /$1 break;\n        fastcgi_pass 127.0.0.1:9000;\n        fastcgi_param  SCRIPT_FILENAME  /var/www/html/web/$fastcgi_script_name;\n        include fastcgi_params;\n    }\n    location /itil{\n\t\talias /var/www/html/web;\n\t\tindex index.html index.php;\n\t}\n    location ~ /itil/.+\\.php.*$ {\n        rewrite /itil/(.+\\.php) /$1 break;\n        fastcgi_pass 127.0.0.1:9000;\n        fastcgi_param  SCRIPT_FILENAME  /var/www/html/web/$fastcgi_script_name;\n        include fastcgi_params;\n    }\n\t\n\t#kibana\n    location /itoa {\n        proxy_pass http://***************:5601/itoa;\n        #rewrite ^/itoa/(.*) /$1 break;\n        proxy_set_header   Host $host;\n   } \n   \n   #grafana\n    location /tv {\n        proxy_pass http://***************:3000/;\n        add_header 'Access-Control-Allow-Origin' '*';\n        add_header Access-Control-Allow-Methods GET,POST,OPTIONS,DELETE;\n        add_header 'Access-Control-Allow-Headers' 'userId,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';\n        rewrite ^/tv/(.*) /$1 break;\n        proxy_set_header   Host $host;\n   }    \nlocation /getLicense {\n        proxy_pass http://***************:8081;\n        add_header 'Access-Control-Allow-Origin' '*';\n        proxy_set_header X-Real-IP  $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n   }\n    location /api {\n        proxy_pass http://***************:8099;\n        add_header 'Access-Control-Allow-Origin' '*';\n        proxy_set_header X-Real-IP  $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n   }\n    location /ws {\n        proxy_pass http://***************:8099;  # 后端服务器的地址和端口号\n        proxy_http_version 1.1;  # 使用HTTP 1.1协议版本\n        proxy_set_header Upgrade $http_upgrade;  # 传递升级请求头\n        proxy_set_header Connection \"upgrade\";  # 传递连接升级请求头\n   }\n    location /webssh {\n        proxy_pass http://***************:8099;  # 后端服务器的地址和端口号\n        proxy_http_version 1.1;  # 使用HTTP 1.1协议版本\n        proxy_set_header Upgrade $http_upgrade;  # 传递升级请求头\n        proxy_set_header Connection \"upgrade\";  # 传递连接升级请求头\n   }\n    location /static {\n        proxy_pass http://***************:8099;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n   }\n}\n"
              },
              {
                "file_name": "/etc/nginx/conf.d/default.conf",
                "file_content": "server {\n    listen       80;\n    server_name  localhost;\n\n    #charset koi8-r;\n    #access_log  /var/log/nginx/host.access.log  main;\n\n    location / {\n        root   /usr/share/nginx/html;\n        index  index.html index.htm;\n    }\n\n    #error_page  404              /404.html;\n\n    # redirect server error pages to the static page /50x.html\n    #\n    error_page   500 502 503 504  /50x.html;\n    location = /50x.html {\n        root   /usr/share/nginx/html;\n    }\n\n    # proxy the PHP scripts to Apache listening on 127.0.0.1:80\n    #\n    #location ~ \\.php$ {\n    #    proxy_pass   http://127.0.0.1;\n    #}\n\n    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000\n    #\n    #location ~ \\.php$ {\n    #    root           html;\n    #    fastcgi_pass   127.0.0.1:9000;\n    #    fastcgi_index  index.php;\n    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;\n    #    include        fastcgi_params;\n    #}\n\n    # deny access to .htaccess files, if Apache's document root\n    # concurs with nginx's one\n    #\n    #location ~ /\\.ht {\n    #    deny  all;\n    #}\n}\n\n"
              }
            ]
          }
        }
      ]
    }
  }]