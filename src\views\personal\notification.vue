<route>
{
    name: 'personalNotification',
    meta: {
        title: "通知中心"
    }
}
</route>

<script setup>
import useWarningListStore from "@/store/modules/warningList";
import { reactive } from "vue";
import storage from "@/utils/storage";
const data = reactive({
    dataList: [],
});
onMounted(() => {
    getData();
});
function getData() {
  if (storage.local.get("warningList")) {
        data.dataList = JSON.parse(storage.local.get("warningList"));
    }
}
watch(useWarningListStore().$state, (newValue, oldValue) => {
  getData();
});
function deleteList(row) {
    let filteredData = data.dataList.filter((item) => {
        return item.message !== row.message || item.status !== row.status;
    });
    useWarningListStore().message = filteredData;
    storage.local.set("warningList", JSON.stringify(filteredData));
    getData();
}
</script>

<template>
    <div>
        <page-main title="消息通知">
            <el-table :data="data.dataList" style="width: 100%">
                <el-table-column prop="message" label="消息" />
                <el-table-column label="状态"><template #default="scope"><el-tag v-if="scope.row.status === 'success'"
                            type="success">执行成功</el-tag><el-tag v-else
                            type="danger">执行失败</el-tag></template></el-table-column>
                <el-table-column label="操作"><template #default="scope"><el-button @click="deleteList(scope.row)"
                            type="danger">删除</el-button></template></el-table-column>
                <!-- <el-table-column label="操作"
          ><template #default
            ><el-button @click="" type="primary">标记为已处理</el-button></template
          ></el-table-column
        > -->
            </el-table>
        </page-main>
    </div>
</template>
