<template>
  <div>
    <div v-show="data.showTable" v-loading="data.latestloading">
      <search-bar :showMore="true" @toggle="changeIsUnfold" :unfold="true">
        <el-form size="default" label-width="100px" label-suffix="：">
          <el-row gutter="10">
            <!-- 剩下两个不确定是否需要 fixme: 暂时不需要,做不需要修改处理-->
            <el-col :span="8">
              <el-form-item label="主机">
                <el-select
                  filterable
                  collapse-tags
                  multiple
                  clearable
                  :reserve-keyword="false"
                  v-model="data.search.host"
                  collapse-tags-tooltip
                  placeholder="主机"
                >
                  <el-option-group v-for="group in data.hostOption" :key="group.groupid" :label="group.name">
                    <el-option
                      v-for="groups in group.hosts"
                      :key="groups.hostid"
                      :label="groups.name"
                      :value="groups.hostid"
                    />
                  </el-option-group>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="主机群组">
                <el-select
                  filterable
                  collapse-tags
                  multiple
                  clearable
                  v-model="data.search.host_group"
                  placeholder="主机群组"
                  :loading="data.hostGroupLoading"
                >
                  <el-option
                    v-for="hostgroups in data.hostGroupOption"
                    :key="hostgroups.groupid"
                    :label="hostgroups.name"
                    :value="hostgroups.groupid"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="名称">
                <el-input
                  v-model="data.search.name"
                  placeholder="请输入名称，支持模糊查询"
                  clearable
                  @keydown.enter="search()"
                />
              </el-form-item>
            </el-col>
            <!--  -->
          </el-row>
          <el-form-item>
            <el-button type="primary" @click="search()">
              <template #icon>
                <el-icon>
                  <svg-icon name="i-ep:search" />
                </el-icon>
              </template>
              筛选
            </el-button>
          </el-form-item>
          <el-row v-if="isUnfold">
            <el-col>
              <el-form-item label="标签">
                <div>
                  <el-checkbox-group
                    v-model="data.search.tag"
                    size="small"
                    @change="tagClick()"
                    v-loading="data.targetLoading"
                  >
                    <template v-for="(item, index) in data.tagList">
                      <el-checkbox-button
                        :key="item.target"
                        :value="item"
                        :label="item.target"
                        class="target_checkbox"
                        v-if="item.target != ''"
                      >
                        {{ item.target }}
                        <span class="ml-3px">({{ item.count }})</span>
                      </el-checkbox-button>
                    </template>
                  </el-checkbox-group>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </search-bar>
      <el-table
        :data="data.dataList"
        border
        style="width: 100%"
        :header-cell-style="{ background: 'var(--el-color-primary-light-8)' }"
      >
        <el-table-column label="主机" prop="host_name">
          <template #default="scoped">
            <el-text
              tag="ins"
              @click="
                $onContextMenu($event, {
                  hostname: scoped.row.host_name,
                  hostid: scoped.row.hostid,
                  hostip: scoped.row.hostip,
                })
              "
            >
              {{ scoped.row.host_name }}
            </el-text>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" />
        <!-- <el-table-column prop="key_" label="监控项" /> -->
        <el-table-column prop="lastclock" label="最近检查记录" />
        <el-table-column label="最新数据" show-overflow-tooltip>
          <template #default="scope">{{ scope.row.lastvalue + scope.row.units }}</template>
        </el-table-column>
        <el-table-column width="150px" align="center" label="标签">
          <template #default="scope">
            <el-tooltip :content="scope.row.application" placement="top-start">
              <el-tag class="text-tag" v-if="scope.row.application">{{ scope.row.application }}</el-tag>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column width="100px" align="center" label="操作">
          <template #default="scope">
            <div v-if="checkValue(scope.row.lastvalue)">
              <el-button type="primary" plain @click="look(scope.row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="[100]"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
    <div v-show="data.showChart">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>{{ data.tabelTitle }}</span>
            <el-button type="primary" style="float: right" @click="switchTable">
              <template #icon>
                <el-icon>
                  <svg-icon name="ep:money" />
                </el-icon>
              </template>
              返回表格
            </el-button>
          </div>
        </template>

        <div id="trendRef" style="width: 100%; height: 100%" />
        <el-row style="padding-top: 20px">
          <div>
            <div class="green-block"></div>
            <div class="data-wrapper">
              <div class="data-text">
                {{ "最新数据：" + (data.echart.lastData.value || 0) + (data.echart.lastData.unit || "") }}
              </div>
            </div>
          </div>
          <div>
            <div class="red-block"></div>
            <div class="data-wrapper">
              <div class="data-text">
                {{ "最大值：" + (data.echart.maxData.value || 0) + (data.echart.maxData.unit || "") }}
              </div>
            </div>
          </div>
          <div>
            <div class="yello-block"></div>
            <div class="data-wrapper">
              <div class="data-text">
                {{ "最小值：" + (data.echart.minData.value || 0) + (data.echart.minData.unit || "") }}
              </div>
            </div>
          </div>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getHostidByIp,
  getLastData,
  getTrendData,
  getZabbixMonitor,
  getZabbixLatestTarget,
} from "@/api/modules/zabbix_api_management/zabbix";
import { usePagination } from "@/utils/composables";

import { Line } from "@antv/g2plot";
import useDateTimeStore from "@/store/modules/datetime";

import { Toast } from "@/plugins/element-ui";
import { getUTCTimestampWithOffset } from "@/utils/dayjs";

import { _unit_conversion, compareByx, findMinMaxOriginalDataArrays, loading, checkValue } from "./util";
import { loadingHide } from "@/components/SpinkitLoading";

const timeStore = useDateTimeStore();
const { pagination, onSizeChange, onCurrentChange } = usePagination();
const isUnfold = ref(true);
var trendG2;

const route = useRoute();
const data = reactive({
  dataList: [],
  tagList: [],
  showTable: true,
  showChart: false,
  echart: { maxData: {}, minData: {}, lastData: {} },
  trendData: [],
  param: {},
  search: {
    name: "",
    tag: [],
    host_group: [],
    host: [],
  },
  hostGroupOption: [],
  hostOption: [],
  tabelTitle: "",
  latestloading: false,
  targetLoading: false,
  hostGroupLoading: false,
  timeStamp: [],
  routeHostip: "",
  zabbix_id: "",
});

const props = defineProps({
  hostip: { type: String, default: "" },
  zabbix_id: { type: String, default: "" },
  hostid: { type: String, default: "" },
});
const unit = computed(() => {
  return data.trendData[0].unit || data.trendData[0].original_unit;
});
let hasInitialized = ref(false);
pagination.value.size = 100;

// 封装接口调用的异步函数
const fetchData = async () => {
  if (data.routeHostip && data.zabbix_id) {
    try {
      const resGetHostidByIp = await getHostidByIp({ ip: data.routeHostip, resource_type_id: data.zabbix_id });
      if (resGetHostidByIp.status_code === 200) {
        data.search.host = resGetHostidByIp.data.hostid ? [resGetHostidByIp.data.hostid] : [];
        data.search.host_group = resGetHostidByIp.data.groupid ? [resGetHostidByIp.data.groupid] : [];
      }
      if (!resGetHostidByIp.data.hostid) {
        Toast.warning("被监控主机群组不存在");
      }
    } catch (error) {
      console.error("Error fetching host ID by IP:", error);
    }
  }

  // 使用最新的 data.search.host 进行其他接口调用
  await Promise.all([getZabbixMonitorData(), getData(), getTargetList()]);

  data.timeStamp = timeStore.getUtcUnixTimestamp();
};

const initializeData = async () => {
  if (!hasInitialized.value) {
    data.routeHostip = props.hostip;
    data.zabbix_id = props.zabbix_id;
    data.search.host = props.hostid ? [props.hostid] : [];
    console.log("initializeData", !data.routeHostip, !data.zabbix_id, !props.hostid);

    await fetchData(); // 调用接口
    hasInitialized.value = true; // 标记为已初始化
  }
};
onBeforeMount(() => {
  console.log("onBeforeMount", route.name, props);
  if (props.hostip && props.zabbix_id) {
    initializeData();
  } else if (route.name == "latest" && !props.hostip && !props.zabbix_id) {
    //最新数据页面可能没有参数
    initializeData();
  } else if (route.name == "latest" && props.hostip && props.hostid) {
    // 最新数据页可能是主机id
    initializeData();
  }
});
onMounted(() => {});

watch(
  () => [props.hostip, props.zabbix_id], // 监听 hostip 和 zabbix_id
  async ([newHostip, newZabbixId]) => {
    console.log("watch");

    // 如果尚未初始化，并且新属性有效，则进行初始化
    if (!hasInitialized.value && newHostip && newZabbixId) {
      await initializeData();
    }
  },
  { immediate: false } // 不在 watch 创建时立即执行
);

watch(timeStore.$state, (newValue, oldValue) => {
  data.timeStamp = [getUTCTimestampWithOffset(newValue.begin), getUTCTimestampWithOffset(newValue.over)];
  dayPickerChange();
});

// 搜索的展开
function changeIsUnfold(boolean) {
  isUnfold.value = boolean;
}
// 搜索
function search() {
  data.search.tag = [];
  getData();
  getTargetList();
}

// 每页条数改变
function sizeChange(size) {
  onSizeChange(size).then(() => {
    search();
  });
}

// 获取最新数据
function getData() {
  data.latestloading = true;
  let tags = [];
  data.search.tag.map((item) => {
    tags.push({ tag: "Application", value: item.target, operator: "1" });
  });
  const search = data.search.name ? { name: data.search.name } : {};

  const paramer = {
    hostids: data.search.host,
    groupids: data.search.host_group,
    tags,
    search,
    limit: pagination.value.size,
    offset: pagination.value.size * (pagination.value.page - 1),
  };
  getLastData(paramer)
    .then((res: any) => {
      data.dataList = res.data.data;
    })
    .finally(() => {
      data.latestloading = false;
    });
}

/**
 * 获取target标签列表
 */
function getTargetList() {
  data.targetLoading = true;
  const search = data.search.name ? { name: data.search.name } : {};
  const paramer = { hostids: data.search.host, groupids: data.search.host_group, search };
  getZabbixLatestTarget(paramer)
    .then((res) => {
      data.tagList = res.data || [];

      pagination.value.total = res.data.reduce((sum, e) => sum + Number(e.count || 0), 0);
    })
    .finally(() => {
      data.targetLoading = false;
    });
}

// 当前页码切换（翻页）
function currentChange(page) {
  onCurrentChange(page).then(() => {
    getData();
  });
}

// 查看图形
function look(row) {
  loading();
  data.showChart = true;
  data.showTable = false;
  data.param = {
    itemid: row.itemid,
    time_begin: data.timeStamp[0],
    time_end: data.timeStamp[1],
  };
  data.tabelTitle = row.name;
  getTrend();
}

// 获取历史数据
function getTrend() {
  getTrendData(data.param).then((res: any) => {
    data.trendData = res.data.history_data;
    if (data.trendData.length == 0) {
      ElMessage.error("暂无历史趋势数据");
      data.showTable = true;
      data.showChart = false;
      loadingHide();
    } else {
      initChart2();
    }
  });
}
// 时间切换
function dayPickerChange() {
  data.param["time_begin"] = data.timeStamp[0];
  data.param["time_end"] = data.timeStamp[1];
  if (data.showChart) {
    loading();
    trendG2 && trendG2.destroy();
    getTrend();
  }
}
// 初始化图表
function initChart2() {
  data.trendData.forEach((element) => {
    element.value = Number(element.value);
    element.displayValue = Number(element.value);
    element.original_data = Number(element.original_data);
  });
  // 时间排序
  data.trendData.sort(compareByx);
  // 调用函数并输出结果
  const { maxArray, minArray } = findMinMaxOriginalDataArrays(data.trendData);
  // 获取最大值和最小值
  data.echart.maxData = maxArray;
  data.echart.minData = minArray;
  data.echart.lastData = data.trendData[data.trendData.length - 1];

  trendG2 = new Line("trendRef", {
    data: data.trendData,
    xField: "clock",
    yField: "value",
    yAxis: {
      label: {
        formatter: (val) => {
          console.log(unit.value, val, 222222);

          return _unit_conversion(unit.value, val);
        },
      },
      min: data.echart.minData.value,
    },
    xAxis: {
      label: {
        autoRotate: true,
      },
    },
    tooltip: {
      fields: ["original_data", "unit", "value", "clock", "displayValue"],
      formatter: (datum) => {
        // 这里显示的后端计算后的值，带单位
        const { value, unit, displayValue } = datum;
        return { name: "值是", value: `${displayValue} ${unit}` };
      },
    },
    annotations: [
      {
        type: "text",
        position: ["min", "mean"],
        content: "平均值",
        offsetY: -4,
        style: {
          textBaseline: "bottom",
        },
      },
      {
        type: "line",
        start: ["min", "mean"],
        end: ["max", "mean"],
        style: {
          stroke: "red",
          lineDash: [2, 2],
        },
      },
    ],
    interactions: [{ type: "marker-active" }, { type: "brush-x" }], // 启用缩放功能
    legend: {
      position: "top",
    },
    smooth: true,
  });
  trendG2.render();
  loadingHide();
}

// 返回表格
const switchTable = () => {
  trendG2 && trendG2.destroy();
  (data.showTable = true), (data.showChart = false);
};

// 获取zabbix分组下的zabbix群组列表
function getZabbixMonitorData() {
  data.hostGroupLoading = true;
  getZabbixMonitor()
    .then((res) => {
      data.hostGroupOption = res.data || [];
      data.hostOption = data.hostGroupOption;
    })
    .finally(() => {
      data.hostGroupLoading = false;
    });
}

// 搜索标签tag点击
function tagClick() {
  getData();
  if (data.search.tag.length == 0) {
    pagination.value.total = data.tagList.reduce((sum, e) => sum + Number(e.count || 0), 0);
  } else {
    pagination.value.total = data.search.tag.reduce((sum, e) => sum + Number(e.count || 0), 0);
  }
}
</script>

<style lang="scss" scoped>
@import "./style.scss";
</style>
