## 中联联壹科技开发

# 项目框架来源：fantastic-admin 专业版

# 参考地址： https://fantastic-admin.gitee.io/pro-example/#/home

# 文档地址：https://fantastic-admin.gitee.io/guide/intro.html

# 采用 vue3+js/ts 混编模式开发

# 所使用插件：

# uno/

# 目录树

# ├─.husky

# │ └─\_

# ├─.vscode vscode 配置

# ├─plop-templates 代码文件自动生成

# ├─public 公用文件

# ├─scripts 脚本

# ├─src

# │ ├─api 接口

# │ ├─assets 资源

# │ │ ├─fonts 字体

# │ │ ├─icons 图表

# │ │ ├─images 图片

# │ │ ├─sprites 雪碧图

# │ │ └─styles 样式

# │ ├─components 组件

# │ ├─constants 常量

# │ ├─containers 容器

# │ ├─iconify 图标

# │ ├─layout 布局组件

# │ ├─locales 国际化语言包

# │ │ └─lang

# │ ├─menu

# │ │ └─modules

# │ ├─mock

# │ ├─plugins 插件

# │ ├─pwa

# │ ├─router 路由

# │ ├─store 缓存

# │ ├─utils 工具

# │ └─views 视图

# │ ├─automatic_operation

# │ │ ├─exec_playbook

# │ │ │ └─components

# │ │ │ ├─DetailForm

# │ │ │ └─FormMode

# │ │ └─publish

# │ ├─backup

# │ ├─business_monitor

# │ │ └─components

# │ │ └─ServerDrawer

# │ ├─configuration_management

# │ │ ├─db4bix_config

# │ │ │ └─components

# │ │ │ └─DetailForm

# │ │ ├─logstash_config

# │ │ │ └─components

# │ │ │ └─DetailForm

# │ │ └─zabbix_config

# │ ├─function_list

# │ │ └─playbook_management

# │ ├─homepage

# │ │ ├─cmdb_business

# │ │ ├─cmdb_cluster

# │ │ ├─cmdb_database

# │ │ ├─cmdb_middleware

# │ │ ├─cmdb_network_equipment

# │ │ └─cmdb_server

# │ │ └─ssh

# │ ├─host_information

# │ ├─kafka

# │ │ ├─monitor

# │ │ └─theme

# │ ├─large_screen

# │ ├─model_configuration

# │ │ ├─cmdb_business_soft

# │ │ └─cmdb_soft

# │ ├─monitor

# │ ├─nginx

# │ │ └─components

# │ ├─personal

# │ ├─player

# │ ├─problem_list

# │ ├─resource_list

# │ │ ├─business_application

# │ │ │ └─components

# │ │ │ └─DetailForm

# │ │ ├─database

# │ │ │ └─components

# │ │ │ └─DetailForm

# │ │ ├─last_data

# │ │ ├─master

# │ │ │ └─components

# │ │ │ └─DetailForm

# │ │ ├─middleware

# │ │ │ └─components

# │ │ │ └─DetailForm

# │ │ ├─network_equipment

# │ │ │ └─components

# │ │ │ └─DetailForm

# │ │ └─server_information

# │ │ └─components

# │ │ ├─DetailForm

# │ │ ├─editHost

# │ │ └─Information

# │ ├─scheduled_task

# │ ├─template_configuration

# │ │ ├─cmdb_template

# │ │ │ └─components

# │ │ │ ├─DetailForm

# │ │ │ └─FormMode

# │ │ ├─host_template

# │ │ │ └─components

# │ │ │ └─DetailForm

# │ │ ├─monitor_template

# │ │ │ └─components

# │ │ │ ├─DetailForm

# │ │ │ └─FormMode

# │ │ └─oracle_template

# │ │ └─components

# │ │ ├─DetailForm

# │ │ └─FormMode

# │ ├─user_manage

# │ │ └─components

# │ │ ├─DetailForm

# │ │ └─FormMode

# │ ├─visualization_platform

# │ └─weekly_report

# │ ├─dailyReport

# │ └─weeklyReport

# │ └─components

# │ ├─DetailForm

# │ └─FormMode

# └─vite

# └─plugins
