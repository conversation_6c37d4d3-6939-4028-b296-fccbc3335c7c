const Layout = () => import("@/layout/index.vue");
import kafka from "../modules/kafka";
let children = [
  {
    path: "analysis_apm",
    name: "analysis_apm",
    component: () => import("@/views/visualization_platform/apm.vue"),
    meta: {
      title: "web性能监控",
      copyright: false,
      auth: ["admin", "analysis_apm.browse"],
    },
  },
  {
    path: "MonitorTopology",
    name: "MonitorTopology",
    component: () => import("@/views/business_monitor/MonitorTopology.vue"),
    meta: {
      title: "集成平台监控",
      copyright: false,
      auth: ["admin", "MonitorTopology.browse"],
    },
  },
  {
    path: "nginx_list",
    name: "nginx_list",
    component: () => import("@/views/nginx/main.vue"),
    meta: {
      title: "集群监控",
      copyright: false,
      auth: ["admin", "nginx_list.browse"],
    },
  },
  {
    path: "nginx_analysis",
    name: "nginx_analysis",
    component: () => import("@/views/nginx/nginxAnalysis.vue"),
    meta: {
      title: "集群监控分析",
      sidebar: false,
      activeMenu: "/special_monitoring/nginx_list",
      auth: ["admin", "nginx_list.browse"],
    },
  },
  kafka,
];
export default {
  path: "/special_monitoring",
  component: Layout,
  redirect: "/special_monitoring/analysis_apm",
  name: "special_monitoring",
  meta: {
    auth: ["admin", "special_monitoring.browse"],
    title: "专项监控",
    icon: "business_monitoring",
  },
  children,
};
