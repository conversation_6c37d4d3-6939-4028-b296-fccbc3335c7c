<template>
  <el-table :data="data.dataList" style="width: 100%">
    <el-table-column label="资源">
      <template #default="scope">
        <el-select v-model="scope.row.source" placeholder="选择资源">
          <el-option v-for="item in props.nodes" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="端口">
      <template #default="scope"><el-input v-model="scope.row.label" /></template>
    </el-table-column>
    <el-table-column label="指向">
      <template #default="scope">
        <el-select v-model="scope.row.target" placeholder="选择指向">
          <el-option v-for="item in props.nodes" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <el-button type="danger" :disabled="isDisabled" title="自动发现主机不能使用" @click="delPort(scope.$index)">
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-button class="mt-4" style="width: 100%" @click="addPort()" title="自动发现主机不能使用" :disabled="isDisabled">
    新增
  </el-button>
  <div class="flex justify-center items-center mt-30px h-50px button-btn">
    <el-button type="primary" @click="submit()" size="large" title="自动发现主机不能使用" :disabled="isDisabled">
      确定
    </el-button>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["savePort"]);
const data = reactive({
  dataList: [] as any,
});
const props = defineProps({
  nodes: {
    type: Array,
    default: [],
  },
  currentNode: {
    type: Object,
    default: {},
  },
  edges: {
    type: Array,
    default: [],
  },
});
const isDisabled = computed(() => {
  console.log(props.currentNode.id, isNaN(props.currentNode.id));

  return isNaN(props.currentNode.id);
});
watchEffect(() => {
  data.dataList = [];
  // 过滤当前节点开始的端口线
  props.edges.forEach((element) => {
    if (element.source == props.currentNode.id || element.target == props.currentNode.id) {
      data.dataList.push(element);
    }
  });
});

function addPort() {
  data.dataList.push({
    source: props.currentNode.id,
    target: "",
    label: "",
  });
}

function delPort(index) {
  data.dataList.splice(index, 1);
}

function submit() {
  // 过滤” 非“ 当前节点开始的端口线，用于接下来的合并修改
  let params = props.edges.filter((item) => item.source != props.currentNode.id && item.target != props.currentNode.id);
  params = [...params, ...data.dataList];
  emit("savePort", params);
}
</script>

<style scoped lang="scss">
.button-btn {
  background-color: var(--g-app-bg);
  box-shadow: 0 0 1px 0 var(--g-box-shadow-color);
  transition: all 0.3s, var(--el-transition-box-shadow);
}
</style>
