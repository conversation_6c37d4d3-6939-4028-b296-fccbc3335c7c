<script setup>
import { cmdbGetAllBusinessInfo, cmdbGetBusinessMessage, cmdbBusinessDelMessage, getBusinessLog } from "@/api/modules/cmdb/business";

import { license_info } from "@/store/modules/license";
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { ResourceTypeEnum } from "../cmdb_asset/components/constants";
import CreateForm from "./components/createForm.vue";

// 定义localStorage的key
const STORAGE_KEY = 'cmdb_business_search_condition';

const router = useRouter();

const route = useRoute();

const menuRef = ref();
const businessID = ref(0);
let showDrawer = ref(false);
const data = reactive({
  title: "",
  businessName: "",
  dialog: false,
  drawer: false,
  allMenuList: [],
  businessList: [],
  dataList: [],
  allDataList: [],
  search: {
    searchName: "",
  },
  bussniess: {
    businessList: [],
  },
  menu: [],
  parentId: 0,
});

// 监听搜索条件变化，更新localStorage
watch(() => data.search, (newVal) => {
  if (newVal.searchName) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify({
      searchName: newVal.searchName
    }));
  }
}, { deep: true });

onMounted(() => {
  // 从localStorage获取筛选条件
  const savedSearch = localStorage.getItem(STORAGE_KEY);
  if (savedSearch) {
    try {
      const parsedSearch = JSON.parse(savedSearch);
      data.search.searchName = parsedSearch.searchName || '';
    } catch (e) {
      console.error('解析保存的筛选条件出错', e);
    }
  }
  
  getAllBusinessInfo();
});

// 组件卸载时清除localStorage
onUnmounted(() => {
  localStorage.removeItem(STORAGE_KEY);
});

//获取所有业务类型信息
function getAllBusinessInfo() {
  data.menu = [];
  cmdbGetAllBusinessInfo().then((res) => {
    res.data.forEach((element, index) => {
      data.menu.push({
        id: element.id,
        parentId: 0,
        title: element.name,
        icon: "ri:pages-line",
        children: [
          {
            id: index + 1,
            parentId: element.id,
            title: ResourceTypeEnum.server,
            num: element.serverNum,
          },
          {
            id: index + 2,
            parentId: element.id,
            title: ResourceTypeEnum.database,
            num: element.databaseNum,
          },
          {
            id: index + 3,
            parentId: element.id,
            title: ResourceTypeEnum.middleware,
            num: element.middlewareNum,
          },
          {
            id: index + 4,
            parentId: element.id,
            title: ResourceTypeEnum.businessApp,
            num: element.businessNum,
          },
        ],
      });
    });
    data.allMenuList = data.menu;
    if (data.menu.length > 0) {
      data.parentId = data.menu[0].id;
      getBusinessDetail(data.menu[0].id);
    }
  });
}

function editBussiness(item) {
  businessID.value = item.id;
  showDrawer.value = true;
}

//删除业务

function deleteBusiness(item) {
  ElMessageBox.confirm("是否确认删除该业务？", "注意", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      cmdbBusinessDelMessage([item.id]).then((res) => {
        getAllBusinessInfo();
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "删除取消",
      });
    });
}

//筛选业务
function queryData() {
  if (data.search.searchName == "") {
    getAllBusinessInfo();
    return;
  }
  data.menu = data.allMenuList.filter((item) => {
    return item.title.toLowerCase().indexOf(data.search.searchName.toLowerCase()) != -1;
  });
}

//通过节点信息获取业务类型详情。二级节点筛选类型
async function handleNodeClick(nodeData) {
  if (nodeData.parentId == 0) {
    data.parentId = nodeData.id;
    await getBusinessDetail(nodeData.id);
    return;
  } else {
    if (nodeData.parentId != data.parentId) {
      data.parentId = nodeData.parentId;
      await getBusinessDetail(nodeData.parentId);
    }

    data.dataList = data.allDataList.filter((item) => {
      return item.type == nodeData.title;
    });
  }
}

//获取业务类型详情信息
async function getBusinessDetail(id) {
  await cmdbGetBusinessMessage(id).then((res) => {
    data.dataList = res.data;
    data.allDataList = res.data;
  });
}

//创建或修改完业务后，刷新信息
function handleRequestCompleted() {
  getAllBusinessInfo();
}
function resetBusinessId() {
  businessID.value = 0;
}
function businessLog() {
  getBusinessLog().then((res) => {
    if (res.status_code == 200) {
      ElMessage({
        message: res.message,
        type: 'success',
      })
    } else {
      ElMessage.error(res.message)
    }
  })
}
</script>
<template>
  <div class="absolute-container">
    <div class="page-main">
      <LayoutContainer left-side-width="300px" hide-left-side-toggle>
        <template #leftSide>
          <el-scrollbar class="tree">
            <div>
              <div class="div-class">
                <span class="tree-title">业务列表</span>
                <div class="add_class">
                  <el-button-group>
                    <el-button @click="showDrawer = true" type="primary" plain size="small">新增</el-button>
                    <el-button @click="businessLog" type="primary" plain size="small">日志采集</el-button>
                  </el-button-group>
                </div>
              </div>
            </div>

            <el-divider class="eldivider-title" />
            <ElTree :data="data.menu" :default-expand-all="false" ref="menuRef" @node-click="handleNodeClick">
              <template #default="{ node, data }">
                <div class="custom-tree-node">
                  <div class="label" :title="data.title">
                    <el-icon v-if="data.icon" size="16px" style="margin-right: 5px">
                      <svg-icon :name="data.icon" />
                    </el-icon>
                    <div class="text">
                      {{ data.title }}
                    </div>
                  </div>
                  <div class="actions" v-if="node.level == 1">
                    <el-button-group>
                      <el-button type="info" plain size="small" @click.stop="editBussiness(data)">
                        <template #icon>
                          <el-icon>
                            <svg-icon name="i-ep:edit" />
                          </el-icon>
                        </template>
                      </el-button>
                      <el-button type="danger" plain size="small" @click.stop="deleteBusiness(data)">
                        <template #icon>
                          <el-icon>
                            <svg-icon name="i-ep:delete" />
                          </el-icon>
                        </template>
                      </el-button>
                    </el-button-group>
                  </div>
                  <div class="actions" style="display: inline" v-if="node.level == 2">
                    {{ data.num }}
                  </div>
                </div>
              </template>
            </ElTree>
          </el-scrollbar>
        </template>
        <div>
          <div class="grid grid-cols-2">
            <el-form :model="data.search" @submit.native.prevent>
              <el-form-item label="查询业务:">
                <el-input class="w-280" placeholder="请输入业务名称" clearable v-model="data.search.searchName"
                  @keyup.enter.native="queryData()" />
              </el-form-item>
            </el-form>
            <div class="float-right ml-10">
              <el-button type="primary" class="add" @click="queryData()">
                <template #icon>
                  <el-icon>
                    <svg-icon name="ep:search" />
                  </el-icon>
                </template>
                筛选
              </el-button>
              <!-- <el-button @click="showDrawer = true" type="primary" plain>
                <template #icon>
                  <el-icon>
                    <svg-icon name="ep:plus" />
                  </el-icon>
                </template>
                新增业务
              </el-button> -->
            </div>
          </div>
          <div>
            <el-table border :data="data.dataList" class="list-table" stripe highlight-current-row>
              <el-table-column label="主机名称" prop="name" />
              <el-table-column label="IP地址" prop="ip" />
              <el-table-column label="类型" prop="type">
                <template #default="scoped">
                  <el-tag v-if="scoped.row.type == '服务器'">
                    {{ scoped.row.type }}
                  </el-tag>
                  <el-tag v-else-if="scoped.row.type == '数据库'" type="success">
                    {{ scoped.row.type }}
                  </el-tag>
                  <el-tag v-else-if="scoped.row.type == '中间件'" type="warning">
                    {{ scoped.row.type }}
                  </el-tag>
                  <el-tag v-else type="info">
                    {{ scoped.row.type }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="安装软件" prop="install_software" />
            </el-table>
          </div>
        </div>
      </LayoutContainer>
    </div>
    <CreateForm v-model="showDrawer" :businessId="businessID" @close-drawer="resetBusinessId"
      @requestCompleted="handleRequestCompleted"></CreateForm>
  </div>
</template>

<style lang="scss" scoped>
.add_class {
  margin-left: 75px;
}

.tree-title {
  font-size: 16px;
  /* 设置字体大小 */
  font-weight: bold;
  color: #333;
  /* 设置文本颜色 */
  text-transform: uppercase;
  /* 将文本转换为大写 */
}

.eldivider-title {
  margin: 10px 0;
}

.div-class {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
}

.absolute-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 0;
  }

  .page-main {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;

    .flex-container {
      position: static;
    }
  }
}

.flex-container {
  :deep(.left-side) {
    display: flex;
    flex-direction: column;
    height: 100%;

    .btns {
      display: inline-flex;
      width: 100%;
      margin-bottom: var(--container-padding);

      .add {
        width: 100%;
      }
    }

    .tree {
      flex: 1;
      overflow-y: auto;

      .el-tree {
        .el-tree-node__content {
          height: 40px;
        }

        .is-current>.el-tree-node__content {
          background-color: var(--el-color-primary-light-9);
        }

        .custom-tree-node {
          flex: 1;
          position: relative;
          width: 0;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .label {
            display: inline-flex;
            align-items: center;
            width: calc(100% - 10px);
            color: var(--el-text-color-primary);

            .text {
              @include text-overflow;
            }
          }

          &:hover {
            .actions {
              display: block;
            }
          }

          .actions {
            display: none;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);

            .el-button {
              padding: 5px 8px;
            }
          }
        }
      }
    }
  }
}
</style>
