<script setup>
import { onMounted, reactive } from "vue";
import { usePagination } from "@/utils/composables";
import { ElMessage, ElMessageBox } from "element-plus";
import { searchBackup, downloadBackup, delBackup } from "@/api/modules/script_managment/index";
import { deepClone } from "@/utils/";
import { getEnvViteApiBaseurl } from "@/utils/axios";
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination();
let baseURL = getEnvViteApiBaseurl();
const data = reactive({
  dataList: [],
  allList: [],
  batch: {
    enable: true,
    selectionDataList: [],
  },
  search: {
    searchName: "",
  },
  selectTaskObject: {},
  file: {},
  deployDialog: false,
});
onMounted(() => {
  getData();
});
//获取所有功能包信息
function getData() {
  searchBackup().then((res) => {
    data.allList = res.data;
    paging(data.allList);
  });
}
//分页
function paging(list) {
  let params = getParams();
  let dataList = data_Filter(list, params);
  data.dataList = dataList.list;
  pagination.value.total = dataList.total;
}
//查询功能包
function queryData() {
  if (data.search.searchName == "") {
    getData();
    return;
  }
  data.dataList = data.allList.filter((item) => {
    return item.file_name.toLowerCase().includes(data.search.searchName.toLowerCase());
  });
  paging(data.dataList);
}
//筛选数据
function data_Filter(dataList, params) {
  let list = deepClone(dataList);

  let pageList = list.filter((item, index) => {
    return index >= params.from && index < params.from + params.limit;
  });

  pageList.forEach((item) => {
    item.params = item.params;
  });
  return {
    list: pageList,
    total: list.length,
  };
}
function delDeploy() {
  let list = [];
  ElMessageBox.confirm("是否删除选中备份包？", "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    data.batch.selectionDataList.forEach((item) => {
      list.push(item.file_name);
    });
    delBackup(list).then((res) => {
      if (res.status_code == 200) {
        if (res.data.length !== 0) {
          ElMessage({
            message: `删除失败的备份有${res.data.join("，")}`,
            type: "error",
          });
        }
        getData();
      }
    });
  });
}
function batchDownload() {
  ElMessageBox.confirm("是否要下载选中备份包？", "注意", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    data.batch.selectionDataList.forEach((item) => {
      fetch(baseURL + "/script/download_backup_file/?file_name=" + item.file_name)
        .then((response) => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", item.file_name);
          document.body.appendChild(link);
          link.click();
          link.remove();
        })
        .catch((error) => {
          ElMessageBox.alert("下载失败，请稍后再试", "提示", { type: "error" });
        });
    });
  });
}
</script>
<template>
  <div>
    <div class="flex justify-between">
      <el-space wrap>
        <el-button :disabled="!data.batch.selectionDataList.length" type="primary" @click="batchDownload">
          批量下载
        </el-button>
        <el-button :disabled="!data.batch.selectionDataList.length" type="danger" @click="delDeploy">
          批量删除
        </el-button>
      </el-space>
      <el-space>
        <div class="w-220px">
          <el-input v-model="data.search.searchName" placeholder="输入脚本名称" clearable @keyup.enter="queryData" />
        </div>
        <el-button type="primary" @click="queryData()">
          <template #icon>
            <el-icon>
              <svg-icon name="ep:search" />
            </el-icon>
          </template>
          筛选
        </el-button>
      </el-space>
    </div>

    <el-divider />
    <div v-if="data.dataList.length != 0">
      <el-table
        :data="data.dataList"
        border
        class="list-table"
        stripe
        highlight-current-row
        @selection-change="data.batch.selectionDataList = $event"
      >
        <el-table-column v-if="data.batch.enable" type="selection" align="center" fixed />
        <el-table-column label="备份名称" prop="file_name" />
        <el-table-column label="文件类型" prop="file_type" />
        <el-table-column label="创建时间" prop="creation_time" />
      </el-table>
      <el-pagination
        :current-page="pagination.page"
        :total="pagination.total"
        :page-size="pagination.size"
        :page-sizes="pagination.sizes"
        :layout="pagination.layout"
        :hide-on-single-page="false"
        class="paginationTable"
        background
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
    <div v-if="data.dataList.length == 0">
      <el-empty :image-size="120" />
    </div>
  </div>
</template>
<style scoped lang="scss">
.text {
  max-width: 80%;
  @include text-overflow;
}

.button_css {
  float: right;
  position: absolute;
  top: 20px;
  right: 10px;
}

.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70vh;
}

.page-content {
  flex-direction: column;
  align-items: center;
}

.form_css {
  margin-bottom: 1px;
}
</style>
