<script setup>
import { onMounted, reactive } from "vue";
import Components from "./component.vue";
const data = reactive({
  loading: false,
  tabs: '服务器',
});
onMounted(() => {

});


</script>

<template>
  <div>
    <page-main>
      <el-tabs v-model="data.tabs" type="card" class="demo-tabs">
        <el-tab-pane label="服务器" name="服务器">
          <Components v-if="data.tabs === '服务器'" :item="data.tabs"></Components>
        </el-tab-pane>
        <el-tab-pane label="数据库" name="数据库">
          <Components v-if="data.tabs === '数据库'" :item="data.tabs"></Components>
        </el-tab-pane>
      </el-tabs>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
.demo-tabs>.el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
