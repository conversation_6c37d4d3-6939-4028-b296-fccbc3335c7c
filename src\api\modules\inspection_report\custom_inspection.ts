// src/api/inspectionReportApi.ts

import api from "@/plugins/axios/index";
import { InspectionReport, CreateInspectionReportRequest } from "./types";

/**
 * 获取所有巡检模板名称
 * @returns Promise<InspectionReport[]>
 */
export function getAllInspectionReports() {
  return api.get<InspectionReport[]>("/inspection_report/list/");
}

/**
 * 批量删除巡检报告
 * @param ids 报告ID列表
 * @returns Promise<{ success: boolean, message?: string }>
 */
export function deleteInspectionReports(ids: number[]) {
  return api.post<{ success: boolean; message?: string }>("/inspection_report/delete_inspection_report_file/", ids);
}

/**
 * 下载指定ID的巡检报告文件
 * @param reportId 日报ID
 * @returns Promise<Blob>
 */
export function downloadInspectionReport(reportId: number) {
  return api.get<Blob>(`/inspection_report/download_inspection_file/?inspection_report_id=${reportId}`, {
    responseType: "blob",
  });
}

/**
 * 创建自定义巡检报告
 * @param data 新日报的数据
 * @returns Promise<{ id: number, success: boolean, message?: string }>
 */
export function createInspectionReport(data: CreateInspectionReportRequest) {
  return api.post<{ id: number; success: boolean; message?: string }>(
    "/inspection_report/create_inspection_report/",
    data
  );
}
