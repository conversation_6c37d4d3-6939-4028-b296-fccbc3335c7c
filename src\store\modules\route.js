import { deepClone, resolveRoutePath } from "@/utils";
import { systemRoutes } from "@/router/routes";
import { license_info } from "./license";
import useSettingsStore from "./settings";
import useUserStore from "./user";
import useTabbarStore from "./tabbar";
import api from "@/plugins/axios";
import { licenseInfoOption, licenseInfo, excludingFeatures } from "@/constants/auth";

function hasPermission(permissions, route) {
  let permission = [];
  permission = JSON.parse(JSON.parse(permissions).replace(/'/g, '"'));
  let isAuth = false;
  if (route.meta && route.meta.auth) {
    isAuth = permission.some((auth) => {
      if (typeof route.meta.auth == "string") {
        return route.meta.auth === auth;
      } else {
        return route.meta.auth.some((routeAuth) => {
          return routeAuth === auth;
        });
      }
    });
    // 先看版本设置，是否有需要排除的权限，目前只有基础版需要权限排除
    // 后续可以添加其他版本配置，由于后端给的版本名字是中文，所以多了层对比
    let excludingAuth = [];
    excludingAuth = excludingFeatures[license_info.value.license.PackageNames];
    // // 如果是基础版需要排除的直接覆盖掉用户的权限
    if (excludingAuth && excludingAuth.length > 0) {
      if (typeof route.meta.auth == "string") {
        if (excludingAuth.includes(route.meta.auth)) {
          isAuth = false;
        }
      } else {
        if (
          route.meta.auth.some((routeAuth) => {
            return excludingAuth.includes(routeAuth);
          })
        ) {
          isAuth = false;
        }
      }
    }
  } else {
    isAuth = true;
  }
  return isAuth;
}

function filterAsyncRoutes(routes, permissions) {
  const res = [];
  routes.forEach((route) => {
    let tmpRoute = deepClone(route);
    if (hasPermission(permissions, tmpRoute)) {
      if (tmpRoute.children) {
        tmpRoute.children = filterAsyncRoutes(tmpRoute.children, permissions);
        tmpRoute.children.length && res.push(tmpRoute);
      } else {
        res.push(tmpRoute);
      }
    }
  });
  return res;
}

function formatBackRoutes(routes, views = import.meta.glob("../../views/**/*.vue")) {
  return routes.map((route) => {
    switch (route.component) {
      case "Layout":
        route.component = () => import("@/layout/index.vue");
        break;
      case "IframeLayout":
        route.component = () => import("@/layout/iframe.vue");
        break;
      default:
        if (route.component) {
          route.component = views[`../../views/${route.component}`];
        }
    }
    if (route.children) {
      route.children = formatBackRoutes(route.children, views);
    }
    return route;
  });
}

// 将多层嵌套路由处理成两层，保留顶层和最子层路由，中间层级将被拍平
function flatAsyncRoutes(routes) {
  if (routes.children) {
    routes.children = flatAsyncRoutesRecursive(
      routes.children,
      [
        {
          path: routes.path,
          title: routes.meta.title,
          i18n: routes.meta.i18n,
          hide: !routes.meta.breadcrumb && routes.meta.breadcrumb === false,
        },
      ],
      routes.path
    );
  }
  return routes;
}
function flatAsyncRoutesRecursive(routes, breadcrumb, baseUrl) {
  let res = [];
  routes.forEach((route) => {
    if (route.children) {
      let childrenBaseUrl = resolveRoutePath(baseUrl, route.path);
      let tmpBreadcrumb = deepClone(breadcrumb);
      if (route.meta.breadcrumb !== false) {
        tmpBreadcrumb.push({
          path: childrenBaseUrl,
          title: route.meta.title,
          i18n: route.meta.i18n,
          hide: !route.meta.breadcrumb && route.meta.breadcrumb === false,
        });
      }
      let tmpRoute = deepClone(route);
      tmpRoute.path = childrenBaseUrl;
      tmpRoute.meta.breadcrumbNeste = tmpBreadcrumb;
      delete tmpRoute.children;
      res.push(tmpRoute);
      let childrenRoutes = flatAsyncRoutesRecursive(route.children, tmpBreadcrumb, childrenBaseUrl);
      childrenRoutes.map((item) => {
        // 如果 path 一样则覆盖，因为子路由的 path 可能设置为空，导致和父路由一样，直接注册会提示路由重复
        if (res.some((v) => v.path == item.path)) {
          res.forEach((v, i) => {
            if (v.path == item.path) {
              res[i] = item;
            }
          });
        } else {
          res.push(item);
        }
      });
    } else {
      let tmpRoute = deepClone(route);
      tmpRoute.path = resolveRoutePath(baseUrl, tmpRoute.path);
      // 处理面包屑导航
      let tmpBreadcrumb = deepClone(breadcrumb);
      tmpBreadcrumb.push({
        path: tmpRoute.path,
        title: tmpRoute.meta.title,
        i18n: tmpRoute.meta.i18n,
        hide: !tmpRoute.meta.breadcrumb && tmpRoute.meta.breadcrumb === false,
      });
      tmpRoute.meta.breadcrumbNeste = tmpBreadcrumb;
      res.push(tmpRoute);
    }
  });
  return res;
}

const useRouteStore = defineStore(
  // 唯一ID
  "route",
  {
    state: () => ({
      isGenerate: false,
      routes: [],
      currentRemoveRoutes: [],
    }),
    getters: {
      // 扁平化路由（将三级及以上路由数据拍平成二级）
      flatRoutes: (state) => {
        const settingsStore = useSettingsStore();
        let routes = [];
        if (state.routes) {
          if (settingsStore.app.routeBaseOn !== "filesystem") {
            state.routes.map((item) => {
              routes.push(...deepClone(item.children));
            });
            routes.map((item) => flatAsyncRoutes(item));
          } else {
            routes.push(...deepClone(state.routes));
          }
        }
        return routes;
      },
      flatSystemRoutes: () => {
        let routes = [...systemRoutes];
        routes.map((item) => flatAsyncRoutes(item));
        return routes;
      },
    },
    actions: {
      // 根据权限动态生成路由（前端生成）
      generateRoutesAtFront(asyncRoutes) {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve) => {
          const settingsStore = useSettingsStore();
          const userStore = useUserStore();
          const tabbarStore = useTabbarStore();
          let accessedRoutes;
          // 如果权限功能开启，则需要对路由数据进行筛选过滤
          // console.log(asyncRoutes, 666);
          if (settingsStore.app.enablePermission) {
            const permissions = await userStore.getPermissions();
            // console.log(permissions, 3333);
            accessedRoutes = filterAsyncRoutes(asyncRoutes, permissions);
          } else {
            accessedRoutes = deepClone(asyncRoutes);
          }
          // console.log(accessedRoutes, 2222);
          // 设置 routes 数据
          this.isGenerate = true;
          this.routes = accessedRoutes.filter((item) => item.children.length != 0);
          // console.log(this.routes, 1111);
          // 加载常驻标签页
          if (settingsStore.tabbar.enable) {
            tabbarStore.initPermanentTab();
          }
          resolve();
        });
      },
      // 根据权限动态生成路由（后端获取）
      generateRoutesAtBack() {
        return new Promise((resolve) => {
          api
            .get("route/list", {
              baseURL: "/mock/",
            })
            .then(async (res) => {
              const settingsStore = useSettingsStore();
              const userStore = useUserStore();
              const tabbarStore = useTabbarStore();
              let asyncRoutes = formatBackRoutes(res.data);
              let accessedRoutes;
              // 如果权限功能开启，则需要对路由数据进行筛选过滤
              if (settingsStore.app.enablePermission) {
                const permissions = await userStore.getPermissions();
                accessedRoutes = filterAsyncRoutes(asyncRoutes, permissions);
              } else {
                accessedRoutes = deepClone(asyncRoutes);
              }
              // 设置 routes 数据
              this.isGenerate = true;
              this.routes = accessedRoutes.filter((item) => item.children.length != 0);
              // 初始化常驻标签页
              if (settingsStore.tabbar.enable) {
                tabbarStore.initPermanentTab();
              }
              resolve();
            });
        });
      },
      // 根据权限动态生成路由（文件系统生成）
      generateRoutesAtFilesystem(asyncRoutes) {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve) => {
          const settingsStore = useSettingsStore();
          const userStore = useUserStore();
          const tabbarStore = useTabbarStore();
          let accessedRoutes;
          // 如果权限功能开启，则需要对路由数据进行筛选过滤
          if (settingsStore.app.enablePermission) {
            const permissions = await userStore.getPermissions();
            accessedRoutes = filterAsyncRoutes(asyncRoutes, permissions);
          } else {
            accessedRoutes = deepClone(asyncRoutes);
          }
          // 设置 routes 数据
          this.isGenerate = true;
          this.routes = accessedRoutes.filter((item) => item.children.length != 0);
          // 加载常驻标签页
          if (settingsStore.tabbar.enable) {
            tabbarStore.initPermanentTab();
          }
          resolve();
        });
      },
      // 记录 accessRoutes 路由，用于登出时删除路由
      setCurrentRemoveRoutes(routes) {
        this.currentRemoveRoutes = routes;
      },
      // 清空动态路由
      removeRoutes() {
        this.isGenerate = false;
        this.routes = [];
        this.currentRemoveRoutes.forEach((removeRoute) => {
          removeRoute();
        });
        this.currentRemoveRoutes = [];
      },
    },
  }
);

export default useRouteStore;
