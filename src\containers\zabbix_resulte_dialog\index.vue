<script setup lang="ts">
import { onMounted, reactive } from "vue";
// 前后端状态对应
// 前端'wait' | 'process' | 'finish' | 'error' | 'success'
// 后端 success cancel error alarm
const statusOption = { success: "success", cancel: "wait", error: "error", alarm: "alarm" };

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  stepData: {
    type: Array,
    default: [],
  },
  loading: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["update:modelValue"]);
const modelValue = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

function handleClose() {
  modelValue.value = false;
}

// 获取描述信息
// 后端 success cancel error alarm
function getStepsDescription(step) {
  let message = "";
  if (step.alarm.length > 0) {
    return step.alarm.join(",");
  }
  switch (step.status) {
    case "success":
      message = "执行成功";
      break;
    case "cancel":
      message = "取消执行";
      break;
    case "error":
      message = "执行失败";
      break;
    case "alarm":
      message = "出现告警";
      break;

    default:
      break;
  }
  return message;
}
</script>
<template>
  <div>
    <el-dialog
      v-model="modelValue"
      title="监控安装执行流程"
      :destroy-on-close="true"
      lock-scroll
      :close-on-click-modal="false"
    >
      <div class="h-70vh overflow-auto">
        <div
          class="flex justify-center h-[calc(100%-40px)] overflow-auto"
          v-loading="props.loading"
          element-loading-text="监控安装中，请稍等."
        >
          <el-steps finish-status="success" direction="vertical" :space="100" class="w-100%">
            <el-step
              v-for="step in props.stepData"
              :key="step.step"
              :title="step.step_name"
              :description="getStepsDescription(step)"
              :status="statusOption[step.status]"
              class="w-100%"
            ></el-step>
          </el-steps>
        </div>
        <div class="flex justify-center">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped></style>
<style>
.el-step__head.is-alarm {
  color: var(--el-color-warning);
  border-color: var(--el-color-warning);
}
.el-step__title.is-alarm {
  color: var(--el-color-warning);
}
.el-step__description.is-alarm {
  color: var(--el-color-warning);
}

.el-step__main {
  width: calc(100% - 24px);
  padding: 0;
}
</style>
